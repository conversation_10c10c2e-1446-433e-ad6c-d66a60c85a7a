import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../blocs/demopos/pos_bloc.dart';
import '../../../blocs/demopos/pos_event.dart';

class DemoCartHeader extends StatelessWidget {
  final String? tableNumber;
  final String orderId;
  final String orderType;

  const DemoCartHeader({
    super.key,
    this.tableNumber,
    required this.orderId,
    required this.orderType,
  });

  @override
  Widget build(BuildContext context) {

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                'Demo Order',
                style: GoogleFonts.dmSans(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),

              IconButton(
                icon: const Icon(Icons.clear_all, color: Colors.red),
                onPressed: () {
                  context.read<DemoPOSBloc>().add(ClearCart());
                },
              ),
            ],
          ),
          Row(
            children: [
              //hint to swipe left to duplicate right to customize show icons with text
              const Icon(Icons.arrow_back_ios, color: Colors.white),

              Text(
                'To Customize',
                style: GoogleFonts.dmSans(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              //hint to swipe left to duplicate right to customize show icons with text

              Text(
                'To Duplicate',
                style: GoogleFonts.dmSans(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Icon(Icons.arrow_forward_ios, color: Colors.white),
            ],
          )
        ],
      ),
    );
  }
}
