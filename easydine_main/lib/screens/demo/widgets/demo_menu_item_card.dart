import 'package:easydine_main/blocs/customization/customization_modal.dart';
import 'package:easydine_main/widgets/quantity_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import '../../../blocs/demopos/pos_bloc.dart';
import '../../../blocs/demopos/pos_event.dart';
import '../../../blocs/demopos/pos_state.dart';
import '../../../models/menuItem.dart';

class DemoMenuItemCard extends StatefulWidget {
  final MenuItem item;

  const DemoMenuItemCard({super.key, required this.item});

  @override
  State<DemoMenuItemCard> createState() => _DemoMenuItemCardState();
}

class _DemoMenuItemCardState extends State<DemoMenuItemCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(
      CurvedAnimation(
        parent: _scaleController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  void _showCustomizationModal(BuildContext context, MenuItem item) {
    showModalBottomSheet(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ItemCustomizationBottomSheet(item: item),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;
    final item = widget.item;

    return BlocBuilder<DemoPOSBloc, DemoPOSState>(
      builder: (context, state) {
        final cartItem = state.cartItems
            .where((cartItem) => cartItem.id == widget.item.id)
            .toList();
        final quantity = cartItem.isNotEmpty ? cartItem.first.quantity : 0;

        return GestureDetector(
          onTapDown: (_) => _scaleController.forward(),
          onTapUp: (_) {
            _scaleController.reverse();
            // Note: DemoPOSBloc doesn't have server sync yet
            // You can add similar functionality to DemoPOSBloc if needed
            context.read<DemoPOSBloc>().add(AddToCart(
                  item,
                  id: item.id,
                  name: item.name,
                  price: item.price,
                ));
          },
          onTapCancel: () => _scaleController.reverse(),
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) => Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            ),
            child: Card(
              color: Colors.white24,
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(3.w),
              ),
              child: Stack(
                children: [
                  Padding(
                    padding: EdgeInsets.all(3.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Text(
                            widget.item.name,
                            style: GoogleFonts.dmSans(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 3.5.sp,
                            ),
                          ),
                        ),
                        SizedBox(height: 1.w),
                        Text(
                          '\$${widget.item.price.toStringAsFixed(2)}',
                          style: GoogleFonts.dmSans(
                            color: Colors.amber.shade700,
                            fontWeight: FontWeight.bold,
                            fontSize: 3.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Positioned(
                  //   top: 8,
                  //   right: 8,
                  //   child: QuantitySelector(
                  //     initialQuantity: quantity,
                  //     onQuantityChanged: (newQuantity) {
                  //       if (newQuantity > 0) {
                  //         if (quantity == 0) {
                  //           context.read<POSBloc>().add(AddToCart(
                  //             widget.item,
                  //             id: widget.item.id,
                  //             name: widget.item.name,
                  //             price: widget.item.price,
                  //           ));
                  //         } else {
                  //           context.read<POSBloc>().add(
                  //             UpdateCartItemQuantity(
                  //               id: widget.item.id,
                  //               quantity: newQuantity,
                  //             ),
                  //           );
                  //         }
                  //       } else {
                  //         context.read<POSBloc>().add(
                  //           RemoveFromCart(widget.item.id),
                  //         );
                  //       }
                  //     },
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
