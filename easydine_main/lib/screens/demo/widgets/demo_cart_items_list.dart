import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../blocs/demopos/pos_bloc.dart';
import '../../../blocs/demopos/pos_state.dart';
import 'demo_cart_item_tile.dart';


class DemoCartItemsList extends StatelessWidget {
  const DemoCartItemsList({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DemoPOSBloc, DemoPOSState>(
      builder: (context, state) {
        if (state.cartItems.isEmpty) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.transparent,
              
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 48,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Your cart is empty',
                    style: GoogleFonts.dmSans(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: state.cartItems.length,
          itemBuilder: (context, index) {
            return DemoCartItemTile(item: state.cartItems[index]);
          },
        );
      },
    );
  }
}