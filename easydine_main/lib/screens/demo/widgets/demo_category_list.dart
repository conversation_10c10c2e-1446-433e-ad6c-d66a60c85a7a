import 'package:easydine_main/services/menu_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../blocs/demopos/pos_bloc.dart';
import '../../../blocs/demopos/pos_event.dart';
import '../../../blocs/demopos/pos_state.dart';
import 'demo_category_Card.dart';

class DemoCategoryList extends StatefulWidget {
  const DemoCategoryList({super.key});

  @override
  State<DemoCategoryList> createState() => _DemoCategoryListState();
}

class _DemoCategoryListState extends State<DemoCategoryList> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollLeft() {
    if (!_scrollController.hasClients) return;
    final newOffset = _scrollController.offset - 200;
    _scrollController.animateTo(
      newOffset < 0 ? 0 : newOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _scrollRight() {
    if (!_scrollController.hasClients) return;
    _scrollController.animateTo(
      _scrollController.offset + 200,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DemoPOSBloc, DemoPOSState>(
      builder: (context, state) {
        // Get all categories and add "All" at the beginning
        final List<String> categories = ['All', ...MenuService.getAllCategories()];
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Row(
                children: [
                  Text(
                    "Categories",
                    style: GoogleFonts.poppins(
                      fontSize: 24,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  // Search Bar
                  Expanded(
                    flex: 2,
                    child: Container(
                      height: 40,
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.15),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: Focus(
                          onFocusChange: (hasFocus) {
                            setState(() {
                              // Add visual feedback when focused
                            });
                          },
                          child: TextField(
                            controller: _searchController,
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                            keyboardAppearance: Brightness.dark,
                            cursorColor: Colors.white,
                            decoration: InputDecoration(
                              fillColor: Colors.transparent,
                              filled: true,
                              isDense: true,
                              hintText: 'Search menu items...',
                              hintStyle: GoogleFonts.poppins(
                                color: Colors.white.withOpacity(0.4),
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                              prefixIcon: AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                padding: const EdgeInsets.all(8),
                                child: Icon(
                                  Icons.search_rounded,
                                  color: Colors.white.withOpacity(0.6),
                                  size: 20,
                                ),
                              ),
                              suffixIcon: state.searchQuery.isNotEmpty
                                  ? Container(
                                      margin: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(50),
                                      ),
                                      child: IconButton(
                                        padding: EdgeInsets.zero,
                                        icon: Icon(
                                          Icons.close_rounded,
                                          color: Colors.white.withOpacity(0.6),
                                          size: 18,
                                        ),
                                        onPressed: () {
                                          _searchController.clear();
                                          context
                                              .read<DemoPOSBloc>()
                                              .add(const SearchMenuItems(''));
                                        },
                                      ),
                                    )
                                  : null,
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 10,
                              ),
                              enabledBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                            ),
                            onChanged: (value) {
                              context
                                  .read<DemoPOSBloc>()
                                  .add(SearchMenuItems(value));
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _scrollLeft,
                    icon: const Icon(
                      Icons.arrow_circle_left,
                      color: Colors.white,
                    ),
                  ),
                  IconButton(
                    onPressed: _scrollRight,
                    icon: const Icon(
                      Icons.arrow_circle_right,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: MediaQuery.of(context).orientation == Orientation.portrait
                  ? MediaQuery.of(context).size.height * 0.1
                  : MediaQuery.of(context).size.height * 0.15,
              width: double.infinity,
              child: ListView.builder(
                padding: EdgeInsets.only(left: 12),
                controller: _scrollController,
                scrollDirection: Axis.horizontal,
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final categoryName = categories[index];
                  final categoryItems = categoryName == 'All' 
                      ? MenuService.getAllMenuItems()
                      : MenuService.getMenuItems(categoryName);
                  return DemoCategoryCard(
                    category: {
                      'name': categoryName,
                      'icon': _getCategoryIcon(categoryName),
                      'itemCount': categoryItems.length.toString(),
                    },
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  String _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'all':
        return '🍽️';
      case 'pizza':
        return '🍕';
      case 'burgers':
        return '🍔';
      case 'drinks':
        return '🥤';
      case 'desserts':
        return '🍰';
      case 'sides':
        return '🍟';
      case 'beverages':
        return '🍹';
      case 'appetizers':
        return '🫕';
      case 'entrees':
        return '🍲';
      case 'salads':
        return '🥗';
      default:
        return '🍽️';
    }
  }
}
