import 'package:easydine_main/services/order_id_generator.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../blocs/demopos/pos_bloc.dart';
import '../../../blocs/demopos/pos_state.dart';
import 'demo_cart_header.dart';
import 'demo_cart_items_list.dart';
import 'demo_cart_total.dart';
import 'demo_extra_options_row.dart';

class DemoCartDrawer extends StatelessWidget {
  final String tableNumber;
  final String orderType;

  const DemoCartDrawer({
    super.key,
    required this.tableNumber,
    required this.orderType,
  });

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        double drawerWidth = orientation == Orientation.portrait
            ? MediaQuery.of(context).size.width * 0.5
            : MediaQuery.of(context).size.width * 0.3;
        
        return BackdropFilter(
          filter: ui.ImageFilter.blur(sigmaX: 12, sigmaY: 12),
          child: Drawer(
            backgroundColor: Colors.transparent,
            width: drawerWidth,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white24,
                border: Border(
                  left: BorderSide(color: Colors.grey.shade800),
                ),
              ),
              child: BlocBuilder<DemoPOSBloc, DemoPOSState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      DemoCartHeader(orderType: orderType ,tableNumber: tableNumber, orderId: OrderIdGenerator.generateOrderId(orderType: orderType, tableNumber: tableNumber, priority: 1)),
                      const Divider(color: Colors.grey),
                      const Expanded(child: DemoCartItemsList()),
                      const Divider(color: Colors.grey),
                      DemoExtraOptionsRow(context),
                      const Divider(color: Colors.grey),
                      DemoCartTotal(
                        orderId: OrderIdGenerator.generateOrderId(orderType: orderType, tableNumber: tableNumber, priority: 1),
                        orderType: orderType,
                        tableNumber: tableNumber,
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
