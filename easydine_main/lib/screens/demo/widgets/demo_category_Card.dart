import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../blocs/demopos/pos_bloc.dart';
import '../../../blocs/demopos/pos_event.dart';
import '../../../blocs/demopos/pos_state.dart';
class DemoCategoryCard extends StatelessWidget {
  final Map<String, String> category;

  const DemoCategoryCard({
    super.key,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DemoPOSBloc, DemoPOSState>(
      builder: (context, state) {
        final isSelected = category["name"] == state.selectedCategory;
        return GestureDetector(
          onTap: () => context.read<DemoPOSBloc>().add(CategorySelected(category["name"]!)),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.12,
            width: MediaQuery.of(context).orientation == Orientation.portrait
                ? MediaQuery.of(context).size.width * 0.12
                : MediaQuery.of(context).size.width * 0.1,
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              color: isSelected ? Color.fromRGBO(44,191,90,1) : Colors.white24,
              // borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.shade300,
                width: isSelected ? 0 : 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: Color.fromRGBO(0,0,0,0.6),
                        blurRadius: 9,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : [],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category["icon"]!,
                  style:  GoogleFonts.dmSans(fontSize: 20,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  category["name"]!,
                  style: GoogleFonts.dmSans(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: isSelected ? Colors.white : Colors.white,
                  ),
                ),
                Text(
                  '${category["itemCount"]} items',
                  style: GoogleFonts.dmSans(
                    fontSize: 14,
                    color: isSelected ? Colors.white : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
