import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'dart:ui' as ui;
import '../../widgets/app_bar.dart';
import '../../widgets/tiled_background.dart';
import '../../router/router_constants.dart';

class BulkOrdersPage extends StatefulWidget {
  const BulkOrdersPage({super.key});

  @override
  State<BulkOrdersPage> createState() => _BulkOrdersPageState();
}

class _BulkOrdersPageState extends State<BulkOrdersPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String _selectedEventType = 'Birthday';
  bool _isGridView = true;

  final List<String> _eventTypes = [
    'Birthday',
    'Marriage',
    'Wedding',
    'Corporate Event',
    'Anniversary',
    'Graduation',
    'Baby Shower',
    'Holiday Party',
  ];

  final List<Map<String, dynamic>> _sampleBulkOrders = [
    {
      'id': 'BLK001',
      'eventType': 'Birthday',
      'customerName': '<PERSON>',
      'eventDate': '2024-01-15',
      'guestCount': 25,
      'totalAmount': 750.00,
      'specialPrice': 650.00,
      'status': 'Confirmed',
      'items': [
        {'name': 'Birthday Cake', 'quantity': 1, 'price': 150.00},
        {'name': 'Pizza Margherita', 'quantity': 5, 'price': 200.00},
        {'name': 'Soft Drinks', 'quantity': 25, 'price': 125.00},
        {'name': 'Party Decorations', 'quantity': 1, 'price': 75.00},
      ],
      'notes': 'Chocolate cake with custom message',
    },
    {
      'id': 'BLK002',
      'eventType': 'Marriage',
      'customerName': 'Sarah & Mike Johnson',
      'eventDate': '2024-02-20',
      'guestCount': 150,
      'totalAmount': 4500.00,
      'specialPrice': 3800.00,
      'status': 'Pending',
      'items': [
        {'name': 'Wedding Cake', 'quantity': 1, 'price': 800.00},
        {'name': 'Catered Dinner', 'quantity': 150, 'price': 3000.00},
        {'name': 'Champagne Toast', 'quantity': 150, 'price': 450.00},
        {'name': 'Floral Arrangements', 'quantity': 10, 'price': 250.00},
      ],
      'notes': 'Vegetarian options required for 30 guests',
    },
    {
      'id': 'BLK003',
      'eventType': 'Corporate Event',
      'customerName': 'Tech Solutions Inc.',
      'eventDate': '2024-01-25',
      'guestCount': 80,
      'totalAmount': 2400.00,
      'specialPrice': 2100.00,
      'status': 'In Progress',
      'items': [
        {'name': 'Business Lunch', 'quantity': 80, 'price': 1600.00},
        {'name': 'Coffee & Pastries', 'quantity': 80, 'price': 400.00},
        {'name': 'AV Equipment', 'quantity': 1, 'price': 400.00},
      ],
      'notes': 'Presentation setup required',
    },
  ];

  List<Map<String, dynamic>> get _filteredOrders {
    return _sampleBulkOrders
        .where((order) => order['eventType'] == _selectedEventType)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      key: _scaffoldKey,
      extendBodyBehindAppBar: true,
      appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreateBulkOrderDialog,
        backgroundColor: Colors.orange.shade600,
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          'New Bulk Order',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Stack(
        children: [
          const TiledBackground(),
          BackdropFilter(
            filter: ui.ImageFilter.blur(sigmaX: 12, sigmaY: 12),
            child: SafeArea(
              child: Column(
                children: [
                  _buildHeader(isLandscape),
                  _buildFilterAndViewControls(),
                  Expanded(
                    child: _isGridView
                        ? _buildGridView(isLandscape)
                        : _buildListView(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(bool isLandscape) {
    return Container(
      padding: EdgeInsets.all(isLandscape ? 16 : 20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.shopping_cart_outlined,
              color: Colors.orange.shade600,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Bulk Orders Management',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: isLandscape ? 24 : 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Manage event orders with special pricing',
                  style: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: isLandscape ? 14 : 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterAndViewControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withOpacity(0.2)),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedEventType,
                  dropdownColor: Colors.grey.shade800,
                  style: GoogleFonts.poppins(color: Colors.white),
                  icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                  items: _eventTypes.map((String eventType) {
                    return DropdownMenuItem<String>(
                      value: eventType,
                      child: Text(eventType),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedEventType = newValue;
                      });
                    }
                  },
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => setState(() => _isGridView = true),
                  icon: Icon(
                    Icons.grid_view,
                    color:
                        _isGridView ? Colors.orange.shade400 : Colors.white70,
                  ),
                ),
                IconButton(
                  onPressed: () => setState(() => _isGridView = false),
                  icon: Icon(
                    Icons.list,
                    color:
                        !_isGridView ? Colors.orange.shade400 : Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView(bool isLandscape) {
    final filteredOrders = _filteredOrders;

    if (filteredOrders.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      padding: const EdgeInsets.all(20),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: isLandscape ? 3 : 2,
        childAspectRatio: isLandscape ? 1.2 : 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: filteredOrders.length,
      itemBuilder: (context, index) {
        return _buildOrderCard(filteredOrders[index], true);
      },
    );
  }

  Widget _buildListView() {
    final filteredOrders = _filteredOrders;

    if (filteredOrders.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: filteredOrders.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildOrderCard(filteredOrders[index], false),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 64,
            color: Colors.white.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No ${_selectedEventType.toLowerCase()} orders found',
            style: GoogleFonts.poppins(
              color: Colors.white.withOpacity(0.7),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create a new bulk order to get started',
            style: GoogleFonts.poppins(
              color: Colors.white.withOpacity(0.5),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order, bool isGridView) {
    final savings = order['totalAmount'] - order['specialPrice'];
    final savingsPercentage = ((savings / order['totalAmount']) * 100).round();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: isGridView
                ? _buildGridCardContent(order, savings, savingsPercentage)
                : _buildListCardContent(order, savings, savingsPercentage),
          ),
        ),
      ),
    );
  }

  Widget _buildGridCardContent(
      Map<String, dynamic> order, double savings, int savingsPercentage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            _buildEventTypeChip(order['eventType']),
            const Spacer(),
            _buildStatusChip(order['status']),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          order['customerName'],
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          'Order #${order['id']}',
          style: GoogleFonts.poppins(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(Icons.people, color: Colors.white70, size: 16),
            const SizedBox(width: 4),
            Text(
              '${order['guestCount']} guests',
              style: GoogleFonts.poppins(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(Icons.calendar_today, color: Colors.white70, size: 16),
            const SizedBox(width: 4),
            Text(
              order['eventDate'],
              style: GoogleFonts.poppins(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
        const Spacer(),
        if (savings > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Save \$${savings.toStringAsFixed(2)} ($savingsPercentage%)',
              style: GoogleFonts.poppins(
                color: Colors.green.shade400,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 8),
        ],
        Row(
          children: [
            if (order['totalAmount'] != order['specialPrice']) ...[
              Text(
                '\$${order['totalAmount'].toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  color: Colors.white54,
                  fontSize: 12,
                  decoration: TextDecoration.lineThrough,
                ),
              ),
              const SizedBox(width: 8),
            ],
            Text(
              '\$${order['specialPrice'].toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                color: Colors.orange.shade400,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildListCardContent(
      Map<String, dynamic> order, double savings, int savingsPercentage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      _buildEventTypeChip(order['eventType']),
                      const SizedBox(width: 8),
                      _buildStatusChip(order['status']),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    order['customerName'],
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'Order #${order['id']}',
                    style: GoogleFonts.poppins(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (order['totalAmount'] != order['specialPrice']) ...[
                  Text(
                    '\$${order['totalAmount'].toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(
                      color: Colors.white54,
                      fontSize: 14,
                      decoration: TextDecoration.lineThrough,
                    ),
                  ),
                ],
                Text(
                  '\$${order['specialPrice'].toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    color: Colors.orange.shade400,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (savings > 0)
                  Text(
                    'Save $savingsPercentage%',
                    style: GoogleFonts.poppins(
                      color: Colors.green.shade400,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Icon(Icons.people, color: Colors.white70, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    '${order['guestCount']} guests',
                    style: GoogleFonts.poppins(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Row(
              children: [
                Icon(Icons.calendar_today, color: Colors.white70, size: 16),
                const SizedBox(width: 4),
                Text(
                  order['eventDate'],
                  style: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          'Items: ${(order['items'] as List).map((item) => item['name']).join(', ')}',
          style: GoogleFonts.poppins(
            color: Colors.white70,
            fontSize: 12,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (order['notes'] != null && order['notes'].isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Notes: ${order['notes']}',
            style: GoogleFonts.poppins(
              color: Colors.white60,
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildEventTypeChip(String eventType) {
    Color chipColor;
    IconData chipIcon;

    switch (eventType.toLowerCase()) {
      case 'birthday':
        chipColor = Colors.pink.shade400;
        chipIcon = Icons.cake;
        break;
      case 'marriage':
      case 'wedding':
        chipColor = Colors.purple.shade400;
        chipIcon = Icons.favorite;
        break;
      case 'corporate event':
        chipColor = Colors.blue.shade400;
        chipIcon = Icons.business;
        break;
      case 'anniversary':
        chipColor = Colors.red.shade400;
        chipIcon = Icons.celebration;
        break;
      default:
        chipColor = Colors.orange.shade400;
        chipIcon = Icons.event;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(chipIcon, color: chipColor, size: 14),
          const SizedBox(width: 4),
          Text(
            eventType,
            style: GoogleFonts.poppins(
              color: chipColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color statusColor;
    IconData statusIcon;

    switch (status.toLowerCase()) {
      case 'confirmed':
        statusColor = Colors.green.shade400;
        statusIcon = Icons.check_circle;
        break;
      case 'pending':
        statusColor = Colors.orange.shade400;
        statusIcon = Icons.schedule;
        break;
      case 'in progress':
        statusColor = Colors.blue.shade400;
        statusIcon = Icons.hourglass_empty;
        break;
      case 'completed':
        statusColor = Colors.teal.shade400;
        statusIcon = Icons.done_all;
        break;
      case 'cancelled':
        statusColor = Colors.red.shade400;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.grey.shade400;
        statusIcon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, color: statusColor, size: 14),
          const SizedBox(width: 4),
          Text(
            status,
            style: GoogleFonts.poppins(
              color: statusColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateBulkOrderDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey.shade900,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'Create New Bulk Order',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'This feature will allow you to create custom bulk orders for events with special pricing and menu options.',
          style: GoogleFonts.poppins(
            color: Colors.white70,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to POS with bulk order mode
              context.goNamed(RouterConstants.pos, queryParameters: {
                'tableNumber': 'BULK',
                'orderId': 'BLK${DateTime.now().millisecondsSinceEpoch}',
                'orderType': 'bulk',
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
            ),
            child: Text(
              'Create Order',
              style: GoogleFonts.poppins(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
