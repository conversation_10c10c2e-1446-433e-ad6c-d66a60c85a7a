import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import '../../dialogs/signature_pad_dialog.dart';
import '../../models/checklistItem.dart';
import '../../router/router_constants.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/pdf_service.dart';
import '../../models/checklist_report.dart';

class DailyChecklistPage extends StatefulWidget {
  const DailyChecklistPage({super.key});

  @override
  State<DailyChecklistPage> createState() => _DailyChecklistPageState();
}

class _DailyChecklistPageState extends State<DailyChecklistPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isLoading = true;

  // Add local state for signatures
  Map<String, List<Offset?>> _signatures = {};

  final List<ChecklistItem> _items = [
    ChecklistItem(
      title: 'Opening Checks',
      subtitle: 'Verify all opening tasks are completed',
      icon: Icons.store,
      subitems: [
        'Check front door is unlocked',
        'Turn on all lights',
        'Check POS system is online',
        'Count float in register',
        'Ensure staff areas are clean'
      ],
    ),
    ChecklistItem(
      title: 'Chilled Storage Checks',
      subtitle: 'Check all chilled storage units',
      icon: Icons.inventory_2,
      subitems: [
        'Fridge 1: Temp below 5°C',
        'Fridge 2: Temp below 5°C',
        'Walk-in cooler: Temp below 5°C',
        'Check for expired items',
        'Ensure proper food storage hierarchy'
      ],
    ),
    ChecklistItem(
      title: 'Cooking Temperature Checks',
      subtitle: 'Check all cooking temperatures',
      icon: Icons.local_fire_department,
      subitems: [
        'Grill: 200°C minimum',
        'Fryer oil: 180°C',
        'Oven: 180°C minimum',
        'Check probe thermometer calibration',
        'Record temperatures in log'
      ],
    ),
    ChecklistItem(
      title: 'Cooling Checks',
      subtitle: 'Complete all cooling checks',
      icon: Icons.ac_unit,
      subitems: [
        'Hot food cooled within 2 hours',
        'Food stored in shallow containers',
        'Proper air circulation around food',
        'Food covered properly',
        'Cooling log completed'
      ],
    ),
    ChecklistItem(
      title: 'Reheating Temperature Checks',
      subtitle: 'Complete all reheating checks',
      icon: Icons.whatshot,
      subitems: [
        'Food reheated to 75°C minimum',
        'Record temperatures in log',
        'Use probe thermometer correctly',
        'Reheat food only once',
        'Discard food if not hot enough'
      ],
    ),
  ];

  // Check if all tasks and signatures are completed
  bool get _allTasksCompleted {
    // First check if all items are completed
    if (!_items.every((item) => item.isCompleted)) {
      return false;
    }

    // Next check if all items have signatures
    for (var item in _items) {
      if (!_isItemSigned(item.title)) {
        return false;
      }
    }

    return true;
  }

  // Check if an item is signed
  bool _isItemSigned(String itemTitle) {
    return _signatures.containsKey(itemTitle) &&
        _signatures[itemTitle]!.isNotEmpty;
  }

  // Add signature
  void _updateSignature(String itemTitle, List<Offset?> points) {
    setState(() {
      _signatures[itemTitle] = points;
    });
    _saveSignatureToPrefs(itemTitle, points);
  }

  // Clear signature
  void _clearSignature(String itemTitle) {
    setState(() {
      _signatures[itemTitle] = [];
    });
    _clearSignatureFromPrefs(itemTitle);
  }

  // Save signature to SharedPreferences
  Future<void> _saveSignatureToPrefs(
      String itemTitle, List<Offset?> points) async {
    final prefs = await SharedPreferences.getInstance();
    // Convert points to string representation
    final pointsString =
    points.map((p) => '${p?.dx},${p?.dy}').join('|').toString();
    await prefs.setString('signature_$itemTitle', pointsString);
  }

  // Clear signature from SharedPreferences
  Future<void> _clearSignatureFromPrefs(String itemTitle) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('signature_$itemTitle');
  }

  // Load signatures from SharedPreferences
  Future<void> _loadSignatures() async {
    final prefs = await SharedPreferences.getInstance();
    Map<String, List<Offset?>> loadedSignatures = {};

    for (var item in _items) {
      final pointsString = prefs.getString('signature_${item.title}');
      if (pointsString != null && pointsString.isNotEmpty) {
        final points = pointsString.split('|').map((point) {
          if (point == 'null') return null;

          final coords = point.split(',');
          return Offset(double.parse(coords[0]), double.parse(coords[1]));
        }).toList();
        loadedSignatures[item.title] = points;
      } else {
        loadedSignatures[item.title] = [];
      }
    }

    setState(() {
      _signatures = loadedSignatures;
    });
  }

  // Helper method to check if a subitem is completed
  bool _isSubitemCompleted(String itemTitle, String subitem) {
    final prefs = SharedPreferences.getInstance().then((prefs) {
      // Try string value first
      final stringValue = prefs.getString('subitem_${itemTitle}_$subitem');
      if (stringValue != null) {
        return stringValue == 'true';
      } else {
        try {
          return prefs.getBool('subitem_${itemTitle}_$subitem') ?? false;
        } catch (e) {
          return false;
        }
      }
    });

    // Default to false while waiting for the future
    return false;
  }

  // Store progress in state
  double _overallProgress = 0.0;

  // Update progress calculation
  Future<void> _updateProgress() async {
    double totalSubitems = 0;
    double completedSubitems = 0;
    final prefs = await SharedPreferences.getInstance();

    for (var item in _items) {
      if (item.subitems != null) {
        totalSubitems += item.subitems!.length;

        // Count completed subitems
        for (var subitem in item.subitems!) {
          // Try string value first
          final stringValue = prefs.getString('subitem_${item.title}_$subitem');
          bool isCompleted = false;

          if (stringValue != null) {
            isCompleted = stringValue == 'true';
          } else {
            try {
              isCompleted =
                  prefs.getBool('subitem_${item.title}_$subitem') ?? false;
            } catch (e) {
              isCompleted = false;
            }
          }

          if (isCompleted) {
            completedSubitems += 1;
          }
        }
      }
    }

    setState(() {
      _overallProgress =
      totalSubitems > 0 ? completedSubitems / totalSubitems : 0.0;
    });
  }

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add a timeout to ensure we don't get stuck in loading state
    Future.delayed(const Duration(seconds: 5), () {
      if (_isLoading && mounted) {
        print('Loading timeout reached, forcing UI update');
        setState(() {
          _isLoading = false;
        });
        _animationController.forward();
      }
    });

    // Load checklist data
    _loadChecklist().then((_) {
      return _updateProgress();
    }).then((_) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _animationController.forward();
      }
    }).catchError((error) {
      print('Error loading checklist: $error');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadChecklist() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCheckDate = prefs.getString('last_check_date');
      final today = DateTime.now().toIso8601String().split('T')[0];
      final wasSkipped = prefs.getBool('checklist_skipped') ?? false;

      if (lastCheckDate != today ||
          (wasSkipped && !(prefs.getBool('checklist_completed') ?? false))) {
        // Reset checklist if it's a new day or was skipped but not completed
        if (lastCheckDate != today) {
          await prefs.setString('last_check_date', today);

          // Clear previous signatures for a new day
          for (var item in _items) {
            await _clearSignatureFromPrefs(item.title);
          }
        }

        // Load saved states for items and subitems
        for (var item in _items) {
          item.isCompleted = false;

          // Load saved subitems completion states
          if (item.subitems != null) {
            final allSubitemsCompleted = await _areAllSubitemsCompleted(item);
            item.isCompleted = allSubitemsCompleted;
          }
        }
      } else {
        // Load previous session state
        for (var item in _items) {
          final allSubitemsCompleted = await _areAllSubitemsCompleted(item);
          final hasSignature = _isItemSigned(item.title);

          // An item is only considered complete if all subitems are done AND it has a signature
          item.isCompleted = allSubitemsCompleted && hasSignature;
        }
      }

      // Load signatures
      await _loadSignatures();
    } catch (e) {
      print('Error in _loadChecklist: $e');
      // Initialize with default values in case of error
      for (var item in _items) {
        item.isCompleted = false;
      }
      _signatures = {};
    }
  }

  Future<void> _saveChecklistItem(ChecklistItem item) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('checklist_${item.title}', item.isCompleted);
  }

  Future<bool> _areAllSubitemsCompleted(ChecklistItem item) async {
    try {
      if (item.subitems == null || item.subitems!.isEmpty) {
        return false;
      }

      final prefs = await SharedPreferences.getInstance();

      for (var subitem in item.subitems!) {
        // Try string value first
        final stringValue = prefs.getString('subitem_${item.title}_$subitem');
        bool isCompleted = false;

        if (stringValue != null) {
          isCompleted = stringValue == 'true';
        } else {
          try {
            isCompleted =
                prefs.getBool('subitem_${item.title}_$subitem') ?? false;
          } catch (e) {
            isCompleted = false;
          }
        }

        if (!isCompleted) {
          return false;
        }
      }

      return true;
    } catch (e) {
      print('Error in _areAllSubitemsCompleted: $e');
      return false;
    }
  }

  void _proceedToHome() async {
    if (_allTasksCompleted) {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];

      // Save checklist completion status
      await prefs.setString('last_check_date', today);
      await prefs.setBool('checklist_skipped', false);
      await prefs.setBool('checklist_completed', true);

      // Create checklist report
      final report = ChecklistReport(
        date: today,
        checklistItems: Map.fromEntries(
          await Future.wait(
            _items.map((item) async {
              final subitems = item.subitems ?? [];
              final subitemStatus = Map.fromEntries(
                await Future.wait(
                  subitems.map((subitem) async {
                    // Try to get string value first
                    final stringValue =
                    prefs.getString('subitem_${item.title}_$subitem');
                    final status = stringValue == 'true' ||
                        (stringValue == null &&
                            (prefs.getBool('subitem_${item.title}_$subitem') ??
                                false));
                    return MapEntry(subitem, status);
                  }),
                ),
              );
              return MapEntry(
                  item.title, Map<String, bool>.from(subitemStatus));
            }),
          ),
        ),
        signatures: _signatures,
        completedBy: 'Staff Name', // Replace with actual staff name
        completionTime: DateTime.now(),
      );

      // Generate and save PDF
      try {
        final file = await PDFService.generateChecklistReport(report);

        // Get existing reports
        final reports = prefs.getStringList('checklist_reports') ?? [];

        // Check if a report for today already exists and remove it
        final todayFileName = 'checklist_$today.pdf';
        reports.removeWhere((path) => path.endsWith(todayFileName));

        // Add the new report
        reports.add(file.path);
        await prefs.setStringList('checklist_reports', reports);
      } catch (e) {
        print('Error generating PDF: $e');
      }

      if (mounted) {
        GoRouter.of(context).goNamed(RouterConstants.home);
      }
    }
  }

  // Skip button handler
  void _skipChecklist() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('checklist_skipped', true);
    await prefs.setBool('checklist_completed', false);

    if (mounted) {
      GoRouter.of(context).goNamed(RouterConstants.home);
    }
  }

  void _openChecklistDetail(ChecklistItem item) async {
    final prefs = await SharedPreferences.getInstance();
    Map<String, bool> subitemStatus = {};

    // Load saved subitem statuses
    if (item.subitems != null) {
      for (var subitem in item.subitems!) {
        // Try to get string value first
        final stringValue = prefs.getString('subitem_${item.title}_$subitem');
        if (stringValue != null) {
          // Convert string value to boolean
          subitemStatus[subitem] = stringValue == 'true';
        } else {
          // Fallback to boolean for backward compatibility
          try {
            subitemStatus[subitem] =
                prefs.getBool('subitem_${item.title}_$subitem') ?? false;
          } catch (e) {
            // If there's an error (like type mismatch), default to false
            subitemStatus[subitem] = false;
          }
        }
      }
    }

    bool? result = await showDialog<bool>(
      context: context,
      builder: (context) => ChecklistDetailDialog(
        item: item,
        subitemStatus: subitemStatus,
        signature: _signatures[item.title] ?? [],
        onSignatureSaved: (points) {
          _updateSignature(item.title, points);
        },
      ),
    );

    if (result == true) {
      // Update the checklist item status
      bool allSubitemsCompleted = await _areAllSubitemsCompleted(item);
      bool hasSignature = _isItemSigned(item.title);

      setState(() {
        // Only mark as completed if all subitems are done AND has a signature
        item.isCompleted = allSubitemsCompleted && hasSignature;
        _saveChecklistItem(item);
      });

      // Update the progress
      await _updateProgress();
    }
  }

  // Calculate real progress based on completed subitems and signatures
  Future<double> _calculateRealProgress() async {
    double totalSubitems = 0;
    double completedSubitems = 0;
    final prefs = await SharedPreferences.getInstance();

    for (var item in _items) {
      if (item.subitems != null) {
        totalSubitems += item.subitems!.length;

        // Count completed subitems
        for (var subitem in item.subitems!) {
          // Try string value first
          final stringValue = prefs.getString('subitem_${item.title}_$subitem');
          bool isCompleted = false;

          if (stringValue != null) {
            isCompleted = stringValue == 'true';
          } else {
            try {
              isCompleted =
                  prefs.getBool('subitem_${item.title}_$subitem') ?? false;
            } catch (e) {
              isCompleted = false;
            }
          }

          if (isCompleted) {
            completedSubitems += 1;
          }
        }
      }
    }

    if (totalSubitems == 0) return 0.0;
    return completedSubitems / totalSubitems;
  }

  // Collect all failed items across all checklists
  Future<Map<String, List<String>>> _collectFailedItems() async {
    final prefs = await SharedPreferences.getInstance();
    Map<String, List<String>> failedItems = {};

    for (var item in _items) {
      List<String> failedSubitems = [];

      if (item.subitems != null) {
        for (var subitem in item.subitems!) {
          // Try string value first
          final stringValue = prefs.getString('subitem_${item.title}_$subitem');
          bool isFailed = false;

          if (stringValue != null) {
            isFailed = stringValue == 'false';
          } else {
            try {
              // For backward compatibility, we don't have a direct "failed" state
              // in the old boolean model, so we can't determine failures
              continue;
            } catch (e) {
              continue;
            }
          }

          if (isFailed) {
            failedSubitems.add(subitem);
          }
        }
      }

      if (failedSubitems.isNotEmpty) {
        failedItems[item.title] = failedSubitems;
      }
    }

    return failedItems;
  }

  void _showFailedItemsReport() async {
    final failedItems = await _collectFailedItems();

    if (failedItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No failed items to report'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => FailedItemsReportDialog(
        failedItems: failedItems,
        onSendReport: _sendReportToManager,
      ),
    );
  }

  Future<void> _sendReportToManager(
      Map<String, List<String>> failedItems, String additionalNotes) async {
    // Here you would implement the actual reporting logic
    // This could be sending an email, creating a notification, or saving to a database

    // For now, we'll just show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Report sent to manager successfully'),
        backgroundColor: Colors.green,
      ),
    );

    // You could also save that the report was sent
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(
        'report_sent_${DateTime.now().toIso8601String().split('T')[0]}', true);
  }

  @override
  Widget build(BuildContext context) {
    // Check for landscape mode
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final overallProgress =
        _items.where((item) => item.isCompleted).length / _items.length;
    final screenSize = MediaQuery.of(context).size;

    return WillPopScope(
      onWillPop: () async => false, // Prevent back navigation
      child: Scaffold(
        body: _isLoading
            ? Center(child: CircularProgressIndicator())
            : Stack(
          children: [
            // Kitchen-themed background
            Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/kitchen_background.jpg'),
                  fit: BoxFit.cover,
                  colorFilter: ColorFilter.mode(
                    Colors.black.withOpacity(0.7),
                    BlendMode.darken,
                  ),
                ),
              ),
            ),
            // Gradient overlay header
            Container(
              height: isLandscape
                  ? MediaQuery.of(context).size.height * 0.25
                  : MediaQuery.of(context).size.height * 0.14,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF1E88E5),
                    Color(0xFF0D47A1),
                  ],
                  stops: [0.3, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header section
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child:
                  _buildHeader(context, isLandscape, overallProgress),
                ),
                // Grid layout for checklist items
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.85),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black38,
                          blurRadius: 15,
                          offset: Offset(0, -5),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: isLandscape ? 16 : 24,
                        ),
                        child: _buildGridLayout(
                            context, isLandscape, screenSize),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            // Bottom buttons
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: _buildBottomButtons(context, isLandscape),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(
      BuildContext context, bool isLandscape, double overallProgress) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white24,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.restaurant_menu,
                color: Colors.white,
                size: isLandscape ? 30 : 36,
              ),
            ),
            SizedBox(width: MediaQuery.of(context).size.width * 0.01),
            Text(
              'Kitchen Checklist',
              style: GoogleFonts.poppins(
                fontSize: isLandscape ? 24 : 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
        SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Daily Safety Checks',
                    style: GoogleFonts.poppins(
                      fontSize: isLandscape ? 14 : 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.white70,
                    ),
                  ),
                  SizedBox(height: 8),
                  Stack(
                    children: [
                      Container(
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.white24,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      AnimatedFractionallySizedBox(
                        duration: Duration(milliseconds: 300),
                        widthFactor: overallProgress,
                        child: Container(
                          height: 8,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Color(0xFF43A047),
                                Color(0xFF66BB6A),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(4),
                            boxShadow: [
                              BoxShadow(
                                color: Color(0xFF43A047).withOpacity(0.3),
                                blurRadius: 8,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  Text(
                    '${(overallProgress * 100).toInt()}% Complete',
                    style: GoogleFonts.poppins(
                      fontSize: isLandscape ? 12 : 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 16),
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white10,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                DateTime.now().toString().split(' ')[0],
                style: GoogleFonts.poppins(
                  fontSize: isLandscape ? 12 : 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.white70,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGridLayout(
      BuildContext context, bool isLandscape, Size screenSize) {
    // Calculate grid dimensions based on screen size
    final crossAxisCount = isLandscape ? 4 : (screenSize.width > 600 ? 3 : 1);

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: isLandscape ? 1.8 : 1.4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _items.length,
      itemBuilder: (context, index) {
        final item = _items[index];
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            final delay = index * 0.1;
            final value = _animation.value > delay
                ? (_animation.value - delay) / (1 - delay)
                : 0.0;

            return Transform.scale(
              scale: 0.8 + (0.2 * value),
              child: Opacity(
                opacity: value,
                child: child,
              ),
            );
          },
          child: GestureDetector(
            onTap: () => _openChecklistDetail(item),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: item.isCompleted
                      ? [
                    Color(0xFF43A047).withOpacity(0.2),
                    Color(0xFF66BB6A).withOpacity(0.2)
                  ]
                      : [Colors.white12, Colors.white10],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: item.isCompleted
                      ? Color(0xFF43A047).withOpacity(0.5)
                      : Colors.white24,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 8,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Background pattern
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(15),
                      child: CustomPaint(
                        painter: GridPatternPainter(
                          color: item.isCompleted
                              ? Color(0xFF43A047).withOpacity(0.05)
                              : Colors.white.withOpacity(0.03),
                        ),
                      ),
                    ),
                  ),
                  // Content
                  Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: item.isCompleted
                                    ? Color(0xFF43A047).withOpacity(0.1)
                                    : Color(0xFF1E88E5).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: item.isCompleted
                                      ? Color(0xFF43A047).withOpacity(0.3)
                                      : Color(0xFF1E88E5).withOpacity(0.3),
                                ),
                              ),
                              child: Icon(
                                item.icon,
                                color: item.isCompleted
                                    ? Color(0xFF43A047)
                                    : Color(0xFF1E88E5),
                                size: 24,
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                item.title,
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                  color: item.isCompleted
                                      ? Color(0xFF66BB6A)
                                      : Colors.white,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        Spacer(),
                        Text(
                          item.subtitle,
                          style: GoogleFonts.poppins(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white10,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.checklist,
                                    size: 10,
                                    color: Colors.white70,
                                  ),
                                  SizedBox(width: 4),
                                  Text(
                                    '${item.subitems?.length ?? 0} tasks',
                                    style: GoogleFonts.poppins(
                                      fontSize: 10,
                                      color: Colors.white70,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              width: 28,
                              height: 28,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: item.isCompleted
                                    ? Color(0xFF43A047)
                                    : Colors.white24,
                              ),
                              child: Icon(
                                item.isCompleted
                                    ? Icons.check
                                    : Icons.arrow_forward,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Signature indicator
                  if (_isItemSigned(item.title))
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 3,
                        ),
                        decoration: BoxDecoration(
                          color: Color(0xFF43A047).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Color(0xFF43A047).withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.draw,
                              size: 10,
                              color: Color(0xFF43A047),
                            ),
                            SizedBox(width: 2),
                            Text(
                              'Signed',
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                color: Color(0xFF43A047),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomButtons(BuildContext context, bool isLandscape) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _allTasksCompleted
            ? ElevatedButton(
          onPressed: _proceedToHome,
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(0xFF43A047),
            foregroundColor: Colors.white,
            padding:
            EdgeInsets.symmetric(vertical: isLandscape ? 12 : 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 8,
            shadowColor: Color(0xFF43A047).withOpacity(0.5),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle_outline,
                  size: isLandscape ? 18 : 20),
              SizedBox(width: 8),
              Text(
                'Kitchen Ready',
                style: GoogleFonts.poppins(
                  fontSize: isLandscape ? 14 : 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        )
            : Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: _showFailedItemsReport,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                      vertical: isLandscape ? 12 : 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 8,
                  shadowColor: Colors.deepOrange.withOpacity(0.5),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.report_problem_outlined,
                        size: isLandscape ? 16 : 18, color: Colors.white),
                    SizedBox(width: 8),
                    Text(
                      'Report Issues',
                      style: GoogleFonts.poppins(
                        fontSize: isLandscape ? 12 : 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(width: MediaQuery.of(context).size.width * 0.25),
            Expanded(
              child: TextButton(
                onPressed: _skipChecklist,
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                      vertical: isLandscape ? 12 : 16),
                  backgroundColor: Colors.black26,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(color: Colors.grey.withOpacity(0.3)),
                  ),
                ),
                child: Text(
                  'Skip for now',
                  style: GoogleFonts.poppins(
                    fontSize: isLandscape ? 12 : 14,
                    color: Colors.grey[400],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class ChecklistDetailDialog extends StatefulWidget {
  final ChecklistItem item;
  final Map<String, bool> subitemStatus;
  final List<Offset?> signature;
  final Function(List<Offset?>) onSignatureSaved;

  const ChecklistDetailDialog({
    super.key,
    required this.item,
    required this.subitemStatus,
    required this.signature,
    required this.onSignatureSaved,
  });

  @override
  State<ChecklistDetailDialog> createState() => _ChecklistDetailDialogState();
}

class _ChecklistDetailDialogState extends State<ChecklistDetailDialog> {
  late Map<String, String> _subitemStatus;
  late List<Offset?> _signature;
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize subitem status
    _subitemStatus = {};
    for (var subitem in widget.item.subitems ?? []) {
      _subitemStatus[subitem] =
      widget.subitemStatus[subitem] == true ? 'checked' : 'not_checked';
    }

    // Initialize signature
    _signature = List.from(widget.signature);

    // Calculate initial progress
    _calculateProgress();
  }

  void _calculateProgress() {
    if (widget.item.subitems!.isEmpty) {
      setState(() {
        _progress = 0.0;
      });
      return;
    }

    int totalItems = widget.item.subitems!.length;
    int completedItems = 0;

    for (var subitem in widget.item.subitems!) {
      if (_subitemStatus[subitem] == 'checked') {
        completedItems++;
      }
    }

    setState(() {
      _progress = totalItems > 0 ? completedItems / totalItems : 0.0;
    });
  }

  void _toggleSubitemStatus(String subitem) {
    setState(() {
      if (_subitemStatus[subitem] == 'checked') {
        _subitemStatus[subitem] = 'not_checked';
      } else {
        _subitemStatus[subitem] = 'checked';
      }
    });

    _calculateProgress();
    _saveSubitemStatus(subitem);
  }

  Future<void> _saveSubitemStatus(String subitem) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      'subitem_${widget.item.title}_$subitem',
      _subitemStatus[subitem] == 'checked' ? 'true' : 'false',
    );
  }

  void _markAllFailed() {
    setState(() {
      for (var subitem in widget.item.subitems ?? []) {
        _subitemStatus[subitem] = 'failed';
      }
    });

    _calculateProgress();
    _saveAllSubitemStatus();
  }

  Future<void> _saveAllSubitemStatus() async {
    final prefs = await SharedPreferences.getInstance();

    for (var subitem in widget.item.subitems ?? []) {
      await prefs.setString(
        'subitem_${widget.item.title}_$subitem',
        _subitemStatus[subitem] == 'checked' ? 'true' : 'false',
      );
    }
  }

  void _openSignaturePad() async {
    final List<Offset?>? result = await showDialog<List<Offset?>>(
      context: context,
      builder: (context) => SignaturePadDialog(
        itemTitle: widget.item.title,
        onSignatureSaved: (points) {
          // This callback will be called when the signature is saved
          setState(() {
            _signature = points;
          });
          widget.onSignatureSaved(points);
        },
      ),
    );

    // No need to update state here as it's already done in the onSignatureSaved callback
  }

  @override
  Widget build(BuildContext context) {
    // Check for landscape mode
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    // Adjust dialog size based on orientation
    final dialogWidth = isLandscape
        ? MediaQuery.of(context).size.width * 0.6
        : MediaQuery.of(context).size.width * 0.9;

    final dialogHeight = isLandscape
        ? MediaQuery.of(context).size.height * 0.8
        : MediaQuery.of(context).size.height * 0.7;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: dialogWidth,
        constraints: BoxConstraints(
          maxHeight: dialogHeight,
        ),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black38,
              blurRadius: 15,
              offset: Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF1E88E5),
                    Color(0xFF0D47A1),
                  ],
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isLandscape ? 8 : 12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      widget.item.icon,
                      color: Colors.white,
                      size: isLandscape ? 20 : 24,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.item.title,
                          style: GoogleFonts.poppins(
                            fontSize: isLandscape ? 18 : 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          widget.item.subtitle,
                          style: GoogleFonts.poppins(
                            fontSize: isLandscape ? 14 : 16,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Progress Bar
            Padding(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Progress',
                    style: GoogleFonts.poppins(
                      fontSize: isLandscape ? 14 : 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 8),
                  Stack(
                    children: [
                      Container(
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.white12,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      AnimatedFractionallySizedBox(
                        duration: Duration(milliseconds: 300),
                        widthFactor: _progress,
                        child: Container(
                          height: 8,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Color(0xFF43A047),
                                Color(0xFF66BB6A),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(4),
                            boxShadow: [
                              BoxShadow(
                                color: Color(0xFF43A047).withOpacity(0.3),
                                blurRadius: 8,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  Text(
                    '${(_progress * 100).toInt()}% Complete',
                    style: GoogleFonts.poppins(
                      fontSize: isLandscape ? 12 : 14,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            // Subitems List
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.symmetric(
                  horizontal: isLandscape ? 16 : 20,
                  vertical: isLandscape ? 8 : 12,
                ),
                itemCount: widget.item.subitems?.length ?? 0,
                itemBuilder: (context, index) {
                  final subitem = widget.item.subitems![index];
                  final status = _subitemStatus[subitem] ??
                      'not_checked'; // Default status

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            subitem,
                            style: GoogleFonts.poppins(
                              fontSize: isLandscape ? 14 : 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            // Failed button
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  _subitemStatus[subitem] =
                                  _subitemStatus[subitem] == 'failed'
                                      ? 'not_checked'
                                      : 'failed';
                                });
                                _calculateProgress();
                                _saveSubitemStatus(subitem);
                              },
                              icon: Icon(
                                Icons.cancel,
                                color: status == 'failed'
                                    ? Colors.red
                                    : Colors.white,
                                size: isLandscape ? 6.w : 3.w,
                              ),
                            ),
                            // Checked button
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  _subitemStatus[subitem] =
                                  _subitemStatus[subitem] == 'checked'
                                      ? 'not_checked'
                                      : 'checked';
                                });
                                _calculateProgress();
                                _saveSubitemStatus(subitem);
                              },
                              icon: Icon(
                                Icons.check_circle,
                                color: status == 'checked'
                                    ? Color(0xFF43A047)
                                    : Colors.white,
                                size: isLandscape ? 6.w : 3.w,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            // Signature Area
            Padding(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Signature',
                    style: GoogleFonts.poppins(
                      fontSize: isLandscape ? 14 : 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 8),
                  GestureDetector(
                    onTap: _openSignaturePad,
                    child: Container(
                      height: isLandscape ? 60 : 80,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: _signature.isEmpty
                          ? Center(
                        child: Text(
                          'Tap to sign',
                          style: GoogleFonts.poppins(
                            color: Colors.grey[500],
                            fontSize: isLandscape ? 14 : 16,
                          ),
                        ),
                      )
                          : CustomPaint(
                        painter: SignaturePainter(points: _signature),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Action Buttons
            Padding(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(false);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.red,
                      padding:
                      EdgeInsets.symmetric(vertical: isLandscape ? 12 : 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 4,
                    ),
                    child: Text(
                      'Cancel',
                      style: GoogleFonts.poppins(
                        fontSize: isLandscape ? 14 : 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      GoRouter.of(context).pop(true);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF43A047),
                      foregroundColor: Colors.white,
                      padding:
                      EdgeInsets.symmetric(vertical: isLandscape ? 12 : 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 4,
                    ),
                    child: Text(
                      'Done',
                      style: GoogleFonts.poppins(
                        fontSize: isLandscape ? 14 : 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FailedItemsReportDialog extends StatefulWidget {
  final Map<String, List<String>> failedItems;
  final Function(Map<String, List<String>>, String) onSendReport;

  const FailedItemsReportDialog({
    required this.failedItems,
    required this.onSendReport,
  });

  @override
  State<FailedItemsReportDialog> createState() =>
      _FailedItemsReportDialogState();
}

class _FailedItemsReportDialogState extends State<FailedItemsReportDialog> {
  final TextEditingController _notesController = TextEditingController();

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: isLandscape
            ? MediaQuery.of(context).size.width * 0.6
            : MediaQuery.of(context).size.width * 0.9,
        padding: EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.report_problem_outlined,
                    color: Colors.red[700],
                    size: 24,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Report Failed Items',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        'Send a report to the manager',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 24),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red[100]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Failed Items:',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.red[700],
                    ),
                  ),
                  SizedBox(height: 12),
                  ...widget.failedItems.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            entry.key,
                            style: GoogleFonts.poppins(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          ...entry.value.map((item) {
                            return Padding(
                              padding:
                              const EdgeInsets.only(left: 16.0, top: 4.0),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.remove_circle_outline,
                                    size: 16,
                                    color: Colors.red[400],
                                  ),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      item,
                                      style: GoogleFonts.poppins(
                                        fontSize: 14,
                                        color: Colors.black54,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ),
            ),
            SizedBox(height: 24),
            Text(
              'Additional Notes',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: TextField(
                controller: _notesController,
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: 'Add any additional information here...',
                  hintStyle: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[400],
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                ),
              ),
            ),
            SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    widget.onSendReport(
                        widget.failedItems, _notesController.text);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[700],
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Send Report',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class SignaturePainter extends CustomPainter {
  final List<Offset?> points;

  SignaturePainter({required this.points});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Color(0xFF1E88E5)
      ..strokeCap = StrokeCap.round
      ..strokeWidth = 3.0;

    for (int i = 0; i < points.length - 1; i++) {
      if (points[i] != null &&
          points[i + 1] != null &&
          points[i] != Offset.infinite &&
          points[i + 1] != Offset.infinite) {
        // Scale points to fit the canvas
        final p1 = Offset(
          points[i]!.dx * size.width,
          points[i]!.dy * size.height,
        );
        final p2 = Offset(
          points[i + 1]!.dx * size.width,
          points[i + 1]!.dy * size.height,
        );
        canvas.drawLine(p1, p2, paint);
      }
    }
  }

  @override
  bool shouldRepaint(SignaturePainter oldDelegate) {
    return oldDelegate.points != points;
  }
}

class GridPatternPainter extends CustomPainter {
  final Color color;

  GridPatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = 0.5;

    // Draw horizontal lines
    double y = 0;
    while (y < size.height) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
      y += 20;
    }

    // Draw vertical lines
    double x = 0;
    while (x < size.width) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
      x += 20;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
