import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../widgets/stats_card_home.dart';

Widget buildEnhancedStatsBar(BuildContext context, List<Map<String, dynamic>> filteredTables) {

  final totalTables = filteredTables.length;
  final availableTables =
      filteredTables.where((t) => t['status'] == 'Available').length;
  final occupiedTables =
      filteredTables.where((t) => t['status'] == 'Occupied').length;
  final reservedTables =
      filteredTables.where((t) => t['status'] == 'Reserved').length;

  // Calculate total and average seats
  final totalSeats =
  filteredTables.fold(0, (sum, table) => sum + (table['seats'] as int));
  final avgSeats =
  totalTables > 0 ? (totalSeats / totalTables).toStringAsFixed(1) : '0';

  // Calculate occupancy rate
  final occupancyRate = totalTables > 0
      ? ((occupiedTables / totalTables) * 100).toStringAsFixed(1)
      : '0';

  return Container(
    padding: const EdgeInsets.symmetric(vertical: 16),
    child: Column(
      children: [
        // Primary Stats
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white24,
            borderRadius: BorderRadius.circular(15),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              buildStatCard(
                title: 'Total Tables',
                value: totalTables.toString(),
                icon: Icons.table_bar,
                context: context,
                color: Colors.blue.shade400,
              ),
              buildStatCard(
                title: 'Available',
                value: availableTables.toString(),
                context: context,
                icon: Icons.check_circle_outline,
                color: Colors.green,
              ),
              buildStatCard(
                title: 'Occupied',
                context: context,
                value: occupiedTables.toString(),
                icon: Icons.people,
                color: Colors.red,
              ),
              buildStatCard(
                title: 'Reserved',
                context: context,
                value: reservedTables.toString(),
                icon: Icons.event_seat,
                color: Colors.orange,
              ),
            ],
          ),
        ),
      ],
    ),
  );
}