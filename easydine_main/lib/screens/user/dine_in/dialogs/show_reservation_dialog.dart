//
// import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
//
// void showReservationDialog(
//     BuildContext context,
//     Map<String, dynamic> table,
//     List<Map<String, dynamic>> allTables,
//     List<Map<String, dynamic>> filteredTables,
//     ) {
//   final timeController = TextEditingController();
//   final nameController = TextEditingController();
//   final phoneController = TextEditingController();
//   final guestsController = TextEditingController();
//   final notesController = TextEditingController();
//
//   showDialog(
//     context: context,
//     builder: (context) => AlertDialog(
//       backgroundColor: Colors.grey[900],
//       title: Text(
//         'Reserve Table ${table['id']}',
//         style: GoogleFonts.poppins(color: Colors.white),
//       ),
//       content: SingleChildScrollView(
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             TextField(
//               controller: nameController,
//               style: TextStyle(color: Colors.white),
//               decoration: InputDecoration(
//                 labelText: 'Customer Name',
//                 labelStyle: TextStyle(color: Colors.white70),
//                 enabledBorder: UnderlineInputBorder(
//                   borderSide: BorderSide(color: Colors.white30),
//                 ),
//               ),
//             ),
//             SizedBox(height: 16),
//             TextField(
//               controller: phoneController,
//               style: TextStyle(color: Colors.white),
//               keyboardType: TextInputType.phone,
//               decoration: InputDecoration(
//                 labelText: 'Phone Number',
//                 labelStyle: TextStyle(color: Colors.white70),
//                 enabledBorder: UnderlineInputBorder(
//                   borderSide: BorderSide(color: Colors.white30),
//                 ),
//               ),
//             ),
//             SizedBox(height: 16),
//             InkWell(
//               onTap: () async {
//                 TimeOfDay? selectedTime = await showTimePicker(
//                   context: context,
//                   initialTime: TimeOfDay.now(),
//                 );
//                 if (selectedTime != null) {
//                   timeController.text = selectedTime.format(context);
//                 }
//               },
//               child: IgnorePointer(
//                 child: TextField(
//                   controller: timeController,
//                   style: TextStyle(color: Colors.white),
//                   decoration: InputDecoration(
//                     labelText: 'Reservation Time',
//                     labelStyle: TextStyle(color: Colors.white70),
//                     enabledBorder: UnderlineInputBorder(
//                       borderSide: BorderSide(color: Colors.white30),
//                     ),
//                     suffixIcon:
//                     Icon(Icons.access_time, color: Colors.white70),
//                   ),
//                 ),
//               ),
//             ),
//             SizedBox(height: 16),
//             TextField(
//               controller: guestsController,
//               style: TextStyle(color: Colors.white),
//               keyboardType: TextInputType.number,
//               decoration: InputDecoration(
//                 labelText: 'Number of Guests',
//                 labelStyle: TextStyle(color: Colors.white70),
//                 enabledBorder: UnderlineInputBorder(
//                   borderSide: BorderSide(color: Colors.white30),
//                 ),
//               ),
//             ),
//             SizedBox(height: 16),
//             TextField(
//               controller: notesController,
//               style: TextStyle(color: Colors.white),
//               maxLines: 2,
//               decoration: InputDecoration(
//                 labelText: 'Special Notes',
//                 labelStyle: TextStyle(color: Colors.white70),
//                 enabledBorder: UnderlineInputBorder(
//                   borderSide: BorderSide(color: Colors.white30),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//       actions: [
//         TextButton(
//           onPressed: () => Navigator.pop(context),
//           child: Text('Cancel'),
//         ),
//         ElevatedButton(
//           style: ElevatedButton.styleFrom(
//             backgroundColor: Color(0xFF2CBF5A),
//           ),
//           onPressed: () {
//             if (nameController.text.isEmpty || timeController.text.isEmpty) {
//               ScaffoldMessenger.of(context).showSnackBar(
//                 SnackBar(content: Text('Please fill in required fields')),
//               );
//               return;
//             }
//
//             parentSetState(() {
//               final index =
//               allTables.indexWhere((t) => t['id'] == table['id']);
//               if (index != -1) {
//                 allTables[index]['status'] = 'Reserved';
//                 allTables[index]['reservationDetails'] = {
//                   'name': nameController.text,
//                   'phone': phoneController.text,
//                   'time': timeController.text,
//                   'guests': guestsController.text,
//                   'notes': notesController.text,
//                 };
//                 filteredTables = List.from(allTables);
//               }
//             });
//             Navigator.pop(context); // Close reservation dialog
//             Navigator.pop(context); // Close table details dialog
//           },
//           child: Text('Reserve'),
//         ),
//       ],
//     ),
//   );
// }
