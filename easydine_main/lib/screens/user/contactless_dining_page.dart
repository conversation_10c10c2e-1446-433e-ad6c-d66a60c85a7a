import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../widgets/tiled_background.dart';
import '../../widgets/app_bar.dart';

class ContactlessDiningPage extends StatefulWidget {
  const ContactlessDiningPage({super.key});

  @override
  State<ContactlessDiningPage> createState() => _ContactlessDiningPageState();
}

class _ContactlessDiningPageState extends State<ContactlessDiningPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool isContactlessEnabled = true;
  String selectedQRType = 'Dynamic';

  // Define consistent colors
  final cardBackground = Colors.white.withOpacity(0.08);
  final cardBorder = Colors.white.withOpacity(0.1);
  final highlightColor = Color(0xFF2CBF5A);
  final accentColor = Color(0xFF4A90E2);

  @override
  Widget build(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      extendBodyBehindAppBar: true,
      key: _scaffoldKey,
      appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
      body: Stack(
        children: [
          const TiledBackground(),
          SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(isLandscape ? 24 : 16),
              child: isLandscape ? _buildLandscapeLayout() : _buildPortraitLayout(),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _generateNewQRCodes,
        backgroundColor: highlightColor,
        icon: const Icon(Icons.qr_code, color: Colors.white),
        label: Text(
          'Generate QR Codes',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildLandscapeLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildStatusCard(),
              const SizedBox(height: 20),
              _buildQRSettings(),
            ],
          ),
        ),
        const SizedBox(width: 20),
        // Right column
        Expanded(
          flex: 2,
          child: Column(
            children: [
              _buildTablesList(),
              const SizedBox(height: 20),
              _buildAnalytics(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPortraitLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        _buildStatusCard(),
        const SizedBox(height: 20),
        _buildQRSettings(),
        const SizedBox(height: 20),
        _buildTablesList(),
        const SizedBox(height: 20),
        _buildAnalytics(),
      ],
    );
  }

  Widget _buildHeader() {
    return Text(
      'Contactless Dining Management',
      style: GoogleFonts.poppins(
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildCard({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: cardBackground,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: cardBorder),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: child,
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return _buildCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Status',
              style: GoogleFonts.poppins(
                fontSize: 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: Colors.black12,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white10),
              ),
              child: SwitchListTile(
                title: Text(
                  'Contactless Dining',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
                subtitle: Text(
                  isContactlessEnabled ? 'Active' : 'Inactive',
                  style: GoogleFonts.poppins(
                    color: isContactlessEnabled ? highlightColor : Colors.grey,
                  ),
                ),
                value: isContactlessEnabled,
                activeColor: highlightColor,
                onChanged: (value) {
                  setState(() => isContactlessEnabled = value);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQRSettings() {
    return _buildCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'QR Code Settings',
              style: GoogleFonts.poppins(
                fontSize: 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: Colors.black12,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white10),
              ),
              child: Theme(
                data: Theme.of(context).copyWith(
                  canvasColor: Colors.black87,
                ),
                child: DropdownButtonFormField<String>(
                  value: selectedQRType,
                  decoration: InputDecoration(
                    labelText: 'QR Code Type',
                    labelStyle: GoogleFonts.poppins(color: Colors.white70),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.transparent,
                  ),
                  style: GoogleFonts.poppins(color: Colors.white),
                  dropdownColor: Colors.black87,
                  items: ['Static', 'Dynamic'].map((String type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    setState(() => selectedQRType = value!);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTablesList() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return _buildCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Table QR Codes',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.download_outlined, color: highlightColor),
                  onPressed: () => _downloadAllQRCodes(),
                  tooltip: 'Download All QR Codes',
                ),
              ],
            ),
            const SizedBox(height: 16),
            isLandscape
                ? _buildGridTableList()
                : _buildListTableList(),
          ],
        ),
      ),
    );
  }

  Widget _buildGridTableList() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.black12,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white10),
          ),
          child: InkWell(
            onTap: () => _showQRDetails(index + 1),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(Icons.qr_code_2, color: accentColor),
                      const SizedBox(width: 8),
                      Text(
                        'Table ${index + 1}',
                        style: GoogleFonts.poppins(color: Colors.white),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.download, color: highlightColor, size: 20),
                        onPressed: () => _downloadQR(index + 1),
                        tooltip: 'Download QR',
                      ),
                      IconButton(
                        icon: Icon(Icons.print, color: accentColor, size: 20),
                        onPressed: () => _printQR(index + 1),
                        tooltip: 'Print QR',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildListTableList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5,
      itemBuilder: (context, index) {
        return ListTile(
          leading: const Icon(Icons.qr_code_2, color: Colors.white),
          title: Text(
            'Table ${index + 1}',
            style: GoogleFonts.poppins(color: Colors.white),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.download, color: Colors.green),
                onPressed: () => _downloadQR(index + 1),
                tooltip: 'Download QR',
              ),
              IconButton(
                icon: const Icon(Icons.print, color: Colors.blue),
                onPressed: () => _printQR(index + 1),
                tooltip: 'Print QR',
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnalytics() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    
    return _buildCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Analytics',
              style: GoogleFonts.poppins(
                fontSize: 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            isLandscape
                ? _buildLandscapeAnalytics()
                : _buildPortraitAnalytics(),
          ],
        ),
      ),
    );
  }

  Widget _buildLandscapeAnalytics() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Expanded(child: _buildAnalyticItem('Total Scans', '1,234')),
        Expanded(child: _buildAnalyticItem('Active Tables', '8')),
        Expanded(child: _buildAnalyticItem('Success Rate', '98%')),
        Expanded(child: _buildAnalyticItem('Daily Orders', '45')),
      ],
    );
  }

  Widget _buildPortraitAnalytics() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildAnalyticItem('Total Scans', '1,234'),
        _buildAnalyticItem('Active Tables', '8'),
        _buildAnalyticItem('Success Rate', '98%'),
      ],
    );
  }

  Widget _buildAnalyticItem(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black12,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white10),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: highlightColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.poppins(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  void _generateNewQRCodes() {
    // Implement QR code generation logic
  }

  void _downloadQR(int tableNumber) {
    // Implement QR code download logic
  }

  void _printQR(int tableNumber) {
    // Implement QR code printing logic
  }

  void _downloadAllQRCodes() {
    // Implement bulk download logic
  }

  void _showQRDetails(int tableNumber) {
    // Implement QR details dialog
  }
}
