import 'package:easydine_main/widgets/app_bar.dart';
import 'package:easydine_main/widgets/tiled_background.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../blocs/settings/settings_bloc.dart';
import '../../router/router_constants.dart';

class SettingsPage extends StatelessWidget {
  SettingsPage({Key? key}) : super(key: key);

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        children: [
          Icon(icon, color: Colors.white70),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
    String? subtitle,
  }) {
    return Card(
      color: Colors.white24,
      child: ListTile(
        leading: Icon(icon, color: Colors.white),
        title: Text(title, style: TextStyle(color: Colors.white)),
        subtitle: subtitle != null
            ? Text(subtitle, style: TextStyle(color: Colors.white70))
            : null,
        trailing: Icon(Icons.chevron_right, color: Colors.white70),
        onTap: onTap,
      ),
    );
  }

  Widget _buildExpandableSettingsCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required bool value,
    required Function(bool) onChanged,
    required List<Widget> expandedSettings,
    String? subtitle,
  }) {
    return ExpansionCard(
      color: Colors.black45,
      child: Column(
        children: [
          ListTile(
            leading: Icon(icon, color: Colors.white),
            title: Text(title, style: const TextStyle(color: Colors.white)),
            subtitle: subtitle != null
                ? Text(subtitle, style: const TextStyle(color: Colors.white70))
                : null,
            trailing: Switch(
              value: value,
              onChanged: onChanged,
              activeColor: Colors.green,
            ),
          ),
          if (value) ...[
            const Divider(color: Colors.white24),
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Column(
                children: expandedSettings,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSettingRow({
    required String label,
    required Widget control,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70),
          ),
          control,
        ],
      ),
    );
  }

  Widget _buildFeatureSettings(BuildContext context, SettingsState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Feature Settings', Icons.featured_play_list),

        // Contactless Dining Settings
        _buildExpandableSettingsCard(
          context,
          title: 'Contactless Dining',
          icon: Icons.qr_code_scanner_outlined,
          value: state.contactlessDineInEnabled,
          subtitle: 'Enable QR code-based ordering system',
          onChanged: (value) {
            context
                .read<SettingsBloc>()
                .add(ToggleContactlessDineIn(isEnabled: value));
          },
          expandedSettings: [
            _buildSettingRow(
              label: 'Auto-accept orders',
              control: Switch(
                value: state.autoAcceptQrOrders ?? false,
                onChanged: (value) {
                  context
                      .read<SettingsBloc>()
                      .add(UpdateQrOrderSettings(autoAccept: value));
                },
              ),
            ),
            _buildSettingRow(
              label: 'Table verification',
              control: Switch(
                value: state.requireTableVerification ?? true,
                onChanged: (value) {
                  context
                      .read<SettingsBloc>()
                      .add(UpdateQrOrderSettings(tableVerification: value));
                },
              ),
            ),
            _buildSettingRow(
              label: 'Order notification sound',
              control: DropdownButton<String>(
                value: state.qrOrderSound ?? 'default',
                dropdownColor: Colors.grey[900],
                style: const TextStyle(color: Colors.white),
                items: ['default', 'bell', 'chime', 'none']
                    .map((e) => DropdownMenuItem(
                          value: e,
                          child: Text(e),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    context
                        .read<SettingsBloc>()
                        .add(UpdateQrOrderSettings(sound: value));
                  }
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Rush Orders Settings
        _buildExpandableSettingsCard(
          context,
          title: 'Rush Orders',
          icon: Icons.speed_outlined,
          value: state.rushOrderEnabled,
          subtitle: 'Enable priority order processing',
          onChanged: (value) {
            context.read<SettingsBloc>().add(ToggleRushOrder(isEnabled: value));
          },
          expandedSettings: [
            _buildSettingRow(
              label: 'Rush order fee',
              control: TextButton(
                child: Text(
                  '\$${state.rushOrderFee?.toStringAsFixed(2) ?? '5.00'}',
                  style: const TextStyle(color: Colors.white),
                ),
                onPressed: () => _showFeeDialog(context),
              ),
            ),
            _buildSettingRow(
              label: 'Priority level',
              control: DropdownButton<int>(
                value: state.rushOrderPriority ?? 1,
                dropdownColor: Colors.grey[900],
                style: const TextStyle(color: Colors.white),
                items: [1, 2, 3]
                    .map((e) => DropdownMenuItem(
                          value: e,
                          child: Text('Level $e'),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    context
                        .read<SettingsBloc>()
                        .add(UpdateRushOrderSettings(priority: value));
                  }
                },
              ),
            ),
            _buildSettingRow(
              label: 'Maximum preparation time',
              control: TextButton(
                child: Text(
                  '${state.rushOrderMaxTime ?? 20} min',
                  style: const TextStyle(color: Colors.white),
                ),
                onPressed: () => _showTimeDialog(context),
              ),
            ),
          ],
        ),

        const Divider(color: Colors.white24),
      ],
    );
  }

  Future<void> _showFeeDialog(BuildContext context) async {
    final controller = TextEditingController(
      text: (context.read<SettingsBloc>().state.rushOrderFee ?? 5.00)
          .toStringAsFixed(2),
    );

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text('Set Rush Order Fee',
            style: TextStyle(color: Colors.white)),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            prefixText: '\$',
            labelText: 'Fee Amount',
            labelStyle: TextStyle(color: Colors.white70),
          ),
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            child: const Text('Cancel'),
            onPressed: () => Navigator.pop(context),
          ),
          TextButton(
            child: const Text('Save'),
            onPressed: () {
              final fee = double.tryParse(controller.text);
              if (fee != null) {
                context
                    .read<SettingsBloc>()
                    .add(UpdateRushOrderSettings(fee: fee));
              }
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  Future<void> _showTimeDialog(BuildContext context) async {
    final controller = TextEditingController(
      text: (context.read<SettingsBloc>().state.rushOrderMaxTime ?? 20)
          .toString(),
    );

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text('Set Maximum Preparation Time',
            style: TextStyle(color: Colors.white)),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            suffixText: 'minutes',
            labelText: 'Time Limit',
            labelStyle: TextStyle(color: Colors.white70),
          ),
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            child: const Text('Cancel'),
            onPressed: () => Navigator.pop(context),
          ),
          TextButton(
            child: const Text('Save'),
            onPressed: () {
              final time = int.tryParse(controller.text);
              if (time != null) {
                context
                    .read<SettingsBloc>()
                    .add(UpdateRushOrderSettings(maxTime: time));
              }
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      key: scaffoldKey,
      extendBodyBehindAppBar: true,
      appBar: WaiterAppBar(scaffoldKey: scaffoldKey),
      body: Stack(
        children: [
          TiledBackground(),
          SafeArea(
            child: BlocBuilder<SettingsBloc, SettingsState>(
              builder: (context, state) {
                return isLandscape
                    ? _buildLandscapeLayout(context, state)
                    : _buildPortraitLayout(context, state);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(BuildContext context, SettingsState state) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Column - Feature Settings & Order Processing
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader(
                    'Feature Settings', Icons.featured_play_list),

                // Contactless Dining Settings
                _buildExpandableSettingsCard(
                  context,
                  title: 'Contactless Dining',
                  icon: Icons.qr_code_scanner_outlined,
                  value: state.contactlessDineInEnabled,
                  subtitle: 'Enable QR code-based ordering system',
                  onChanged: (value) {
                    context
                        .read<SettingsBloc>()
                        .add(ToggleContactlessDineIn(isEnabled: value));
                  },
                  expandedSettings: [
                    _buildSettingRow(
                      label: 'Auto-accept orders',
                      control: Switch(
                        value: state.autoAcceptQrOrders ?? false,
                        onChanged: (value) {
                          context
                              .read<SettingsBloc>()
                              .add(UpdateQrOrderSettings(autoAccept: value));
                        },
                      ),
                    ),
                    _buildSettingRow(
                      label: 'Table verification',
                      control: Switch(
                        value: state.requireTableVerification ?? true,
                        onChanged: (value) {
                          context.read<SettingsBloc>().add(
                              UpdateQrOrderSettings(tableVerification: value));
                        },
                      ),
                    ),
                    _buildSettingRow(
                      label: 'Order notification sound',
                      control: DropdownButton<String>(
                        value: state.qrOrderSound ?? 'default',
                        dropdownColor: Colors.grey[900],
                        style: const TextStyle(color: Colors.white),
                        items: ['default', 'bell', 'chime', 'none']
                            .map((e) => DropdownMenuItem(
                                  value: e,
                                  child: Text(e),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            context
                                .read<SettingsBloc>()
                                .add(UpdateQrOrderSettings(sound: value));
                          }
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Rush Orders Settings
                _buildExpandableSettingsCard(
                  context,
                  title: 'Rush Orders',
                  icon: Icons.speed_outlined,
                  value: state.rushOrderEnabled,
                  subtitle: 'Enable priority order processing',
                  onChanged: (value) {
                    context
                        .read<SettingsBloc>()
                        .add(ToggleRushOrder(isEnabled: value));
                  },
                  expandedSettings: [
                    _buildSettingRow(
                      label: 'Rush order fee',
                      control: TextButton(
                        child: Text(
                          '\$${state.rushOrderFee?.toStringAsFixed(2) ?? '5.00'}',
                          style: const TextStyle(color: Colors.white),
                        ),
                        onPressed: () => _showFeeDialog(context),
                      ),
                    ),
                    _buildSettingRow(
                      label: 'Priority level',
                      control: DropdownButton<int>(
                        value: state.rushOrderPriority ?? 1,
                        dropdownColor: Colors.grey[900],
                        style: const TextStyle(color: Colors.white),
                        items: [1, 2, 3]
                            .map((e) => DropdownMenuItem(
                                  value: e,
                                  child: Text('Level $e'),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            context
                                .read<SettingsBloc>()
                                .add(UpdateRushOrderSettings(priority: value));
                          }
                        },
                      ),
                    ),
                    _buildSettingRow(
                      label: 'Maximum preparation time',
                      control: TextButton(
                        child: Text(
                          '${state.rushOrderMaxTime ?? 20} min',
                          style: const TextStyle(color: Colors.white),
                        ),
                        onPressed: () => _showTimeDialog(context),
                      ),
                    ),
                  ],
                ),
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white24,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white24,
                      padding: const EdgeInsets.all(16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: () => GoRouter.of(context).pushNamed(
                      RouterConstants.demopos,
                    ),
                    child: Text(
                      'Demo POS',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),

        // Vertical Divider
        Container(
          width: 1,
          margin: const EdgeInsets.symmetric(vertical: 16),
          color: Colors.white24,
        ),

        // Right Column - Service Options & Additional Settings
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Service Options
              _buildSectionHeader('Service Options', Icons.room_service),
              _buildSettingsCard(
                title: 'Takeaway Service',
                icon: Icons.takeout_dining,
                onTap: () {},
                subtitle: 'Manage takeaway orders',
              ),
              _buildSettingsCard(
                title: 'Delivery Service',
                icon: Icons.delivery_dining,
                onTap: () {},
                subtitle: 'Configure delivery options',
              ),
              _buildSettingsCard(
                title: 'Service Management',
                icon: Icons.manage_accounts_outlined,
                onTap: () => GoRouter.of(context)
                    .pushNamed(RouterConstants.serviceManagement),
                subtitle: 'Configure takeaway and delivery options',
              ),

              _buildSectionHeader('Order Processing', Icons.receipt_long),
              _buildSettingsCard(
                title: 'Order ID Generation',
                icon: Icons.format_list_numbered,
                onTap: () {},
                subtitle: 'Configure automatic order ID generation',
              ),
              _buildSettingsCard(
                title: 'Order Settings',
                icon: Icons.settings_applications_outlined,
                onTap: () => GoRouter.of(context)
                    .pushNamed(RouterConstants.orderSettings),
                subtitle: 'Configure rush orders and ID generation',
              ),

              _buildSectionHeader('Additional Settings', Icons.more_horiz),
              _buildSettingsCard(
                title: 'Table Management',
                icon: Icons.table_bar,
                onTap: () {},
                subtitle: 'Configure table layouts and settings',
              ),
              _buildSettingsCard(
                title: 'Payment Options',
                icon: Icons.payment,
                onTap: () {},
                subtitle: 'Manage payment methods and settings',
              ),
              _buildSettingsCard(
                title: 'Printer Settings',
                icon: Icons.print,
                onTap: () => GoRouter.of(context)
                    .pushNamed(RouterConstants.printerSettings),
                subtitle: 'Configure printers for different food categories',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPortraitLayout(BuildContext context, SettingsState state) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Feature Settings
        _buildFeatureSettings(context, state),

        // Service Options
        _buildSectionHeader('Service Options', Icons.room_service),
        _buildSettingsCard(
          title: 'Service Management',
          icon: Icons.manage_accounts_outlined,
          onTap: () =>
              GoRouter.of(context).pushNamed(RouterConstants.serviceManagement),
          subtitle: 'Configure takeaway and delivery options',
        ),

        _buildSectionHeader('Order Processing', Icons.receipt_long),
        _buildSettingsCard(
          title: 'Order Settings',
          icon: Icons.settings_applications_outlined,
          onTap: () =>
              GoRouter.of(context).pushNamed(RouterConstants.orderSettings),
          subtitle: 'Configure rush orders and ID generation',
        ),
        _buildSettingsCard(
          title: 'Printer Settings',
          icon: Icons.print,
          onTap: () =>
              GoRouter.of(context).pushNamed(RouterConstants.printerSettings),
          subtitle: 'Configure printers for different food categories',
        ),
      ],
    );
  }
}

class ExpansionCard extends StatelessWidget {
  final Widget child;
  final Color color;

  const ExpansionCard({
    Key? key,
    required this.child,
    required this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: color,
      child: child,
    );
  }
}
