import 'dart:math' as math;
import 'package:easydine_main/widgets/cleaning_status_indicator.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../utils/get_table_color.dart';
import '../../utils/seat_painter.dart';
import '../../utils/table_border_painter.dart';

class TableLayoutManager extends StatefulWidget {
  final List<Map<String, dynamic>> tables;
  final int floor;

  const TableLayoutManager({
    Key? key,
    required this.tables,
    required this.floor,
  }) : super(key: key);

  @override
  TableLayoutManagerState createState() => TableLayoutManagerState();
}

class TableLayoutManagerState extends State<TableLayoutManager> {
  // Layout configuration
  late Size layoutSize;

  // State management
  late List<Map<String, dynamic>> floorTables;
  bool isEditMode = false;
  bool _initialized = false;

  // Track which table is being dragged
  String? _currentlyDraggedTableId;

  @override
  void initState() {
    super.initState();
    // Create a deep copy of the tables to avoid modifying the original list
    floorTables = widget.tables
        .where((table) => table['floor'] == widget.floor)
        .map((table) => Map<String, dynamic>.from(table))
        .toList();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      _initializeLayout();
      _initialized = true;
    }
  }

  void _initializeLayout() {
    final screenSize = MediaQuery.of(context).size;
    layoutSize = Size(
      screenSize.width * 0.9, // 90% of screen width
      screenSize.height * 0.8, // 80% of screen height
    );

    // Use 4x6 grid (4 columns, 6 rows)
    final gridCrossAxisCount = 4;
    final gridMainAxisCount = 6;

    // Calculate cell sizes
    final cellWidth = layoutSize.width / gridCrossAxisCount;
    final cellHeight = layoutSize.height / gridMainAxisCount;

    // Add padding between cells (5% of cell size)
    final padding = math.min(cellWidth, cellHeight) * 0.05;

    // Create a grid occupancy map (4x6)
    List<List<bool>> occupiedCells = List.generate(gridMainAxisCount,
        (_) => List.generate(gridCrossAxisCount, (_) => false));

    for (var table in floorTables) {
      table['id'] ??= UniqueKey().toString();

      // Ensure crossAxisCount and mainAxisCount don't exceed grid size
      final crossAxisCount =
          math.min(table['crossAxisCellCount'] as int, gridCrossAxisCount);
      final mainAxisCount =
          math.min(table['mainAxisCellCount'] as int, gridMainAxisCount);

      // Update the table's cell counts to respect grid boundaries
      table['crossAxisCellCount'] = crossAxisCount;
      table['mainAxisCellCount'] = mainAxisCount;

      bool foundSpace = false;
      int startX = 0;
      int startY = 0;

      // Search for available space
      for (int y = 0; y <= gridMainAxisCount - mainAxisCount; y++) {
        for (int x = 0; x <= gridCrossAxisCount - crossAxisCount; x++) {
          bool spaceAvailable = true;

          // Check if all required cells are available
          for (int dy = 0; dy < mainAxisCount; dy++) {
            for (int dx = 0; dx < crossAxisCount; dx++) {
              if (occupiedCells[y + dy][x + dx]) {
                spaceAvailable = false;
                break;
              }
            }
            if (!spaceAvailable) break;
          }

          if (spaceAvailable) {
            startX = x;
            startY = y;
            foundSpace = true;

            // Mark cells as occupied
            for (int dy = 0; dy < mainAxisCount; dy++) {
              for (int dx = 0; dx < crossAxisCount; dx++) {
                occupiedCells[y + dy][x + dx] = true;
              }
            }
            break;
          }
        }
        if (foundSpace) break;
      }

      if (!foundSpace) {
        // If no space found, try to place in a single cell
        for (int y = 0; y < gridMainAxisCount; y++) {
          for (int x = 0; x < gridCrossAxisCount; x++) {
            if (!occupiedCells[y][x]) {
              startX = x;
              startY = y;
              occupiedCells[y][x] = true;
              // Update table size to fit single cell
              table['crossAxisCellCount'] = 1;
              table['mainAxisCellCount'] = 1;
              foundSpace = true;
              break;
            }
          }
          if (foundSpace) break;
        }
      }

      // Calculate position with padding
      final posX = startX * cellWidth;
      final posY = startY * cellHeight;

      // Calculate size with padding
      final availableWidth = cellWidth * crossAxisCount;
      final availableHeight = cellHeight * mainAxisCount;

      // Apply padding to all sides
      final tableWidth = availableWidth - (padding * 2);
      final tableHeight = availableHeight - (padding * 2);

      // Set position (including padding) and size
      table['position'] = Offset(posX + padding, posY + padding);
      table['size'] = Size(tableWidth, tableHeight);
    }
  }

  Size _calculateTableSize(double cellWidth, double cellHeight,
      int crossAxisCount, int mainAxisCount) {
    final width = cellWidth * crossAxisCount;
    final height = cellHeight * mainAxisCount;

    return Size(width, height);
  }

  // Save the updated layout back to the parent widget
  void _saveLayout() {
    // Update the original tables list with the new positions
    for (var floorTable in floorTables) {
      final index = widget.tables.indexWhere((t) =>
          t['tableNumber'] == floorTable['tableNumber'] &&
          t['floor'] == widget.floor);

      if (index >= 0) {
        widget.tables[index]['position'] = floorTable['position'];
        widget.tables[index]['size'] = floorTable['size'];
      }
    }

    GoRouter.of(context).pop(widget.tables);
  }

  // Build a draggable table widget
  Widget _buildDraggableTable(Map<String, dynamic> table) {
    final position = table['position'] as Offset;
    final size = table['size'] as Size;
    final tableId = table['id'].toString();
    final isDragging = _currentlyDraggedTableId == tableId;

    return Positioned(
      left: position.dx,
      top: position.dy,
      child: GestureDetector(
        behavior:
            HitTestBehavior.opaque, // Added to ensure gestures are captured
        onTapDown: isEditMode
            ? (details) {
                // Handle initial tap
                setState(() {
                  _currentlyDraggedTableId = tableId;
                });
              }
            : null,
        onPanStart: isEditMode
            ? (details) {
                // Start dragging
                setState(() {
                  _currentlyDraggedTableId = tableId;
                });
              }
            : null,
        onPanUpdate: isEditMode
            ? (details) {
                if (_currentlyDraggedTableId == tableId) {
                  // Verify we're dragging this table
                  setState(() {
                    // Get layout constraints
                    final cellWidth = layoutSize.width / 4;
                    final cellHeight = layoutSize.height / 6;
                    final padding = math.min(cellWidth, cellHeight) * 0.05;

                    // Calculate new position with delta
                    double newX = position.dx + details.delta.dx;
                    double newY = position.dy + details.delta.dy;

                    // Calculate bounds
                    final maxX = layoutSize.width - size.width - padding;
                    final maxY = layoutSize.height - size.height - padding;

                    // Clamp position within bounds
                    newX = newX.clamp(padding, maxX);
                    newY = newY.clamp(padding, maxY);

                    // Update position
                    table['position'] = Offset(newX, newY);
                  });
                }
              }
            : null,
        onPanEnd: isEditMode
            ? (details) {
                if (_currentlyDraggedTableId == tableId) {
                  // Verify we're ending drag for this table
                  setState(() {
                    // Calculate grid dimensions
                    final cellWidth = layoutSize.width / 4;
                    final cellHeight = layoutSize.height / 6;
                    final padding = math.min(cellWidth, cellHeight) * 0.05;

                    final currentPos = table['position'] as Offset;

                    // Snap to nearest grid position
                    final gridX =
                        ((currentPos.dx - padding) / cellWidth).round() *
                                cellWidth +
                            padding;
                    final gridY =
                        ((currentPos.dy - padding) / cellHeight).round() *
                                cellHeight +
                            padding;

                    // Update to snapped position
                    table['position'] = Offset(gridX, gridY);
                    _currentlyDraggedTableId = null; // Clear drag state
                  });
                }
              }
            : null,
        onTapUp: isEditMode
            ? (details) {
                // Clear drag state on tap up
                setState(() {
                  _currentlyDraggedTableId = null;
                });
              }
            : null,
        child: MouseRegion(
          cursor:
              isEditMode ? SystemMouseCursors.grab : SystemMouseCursors.basic,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            opacity: isDragging ? 0.7 : 1.0,
            child: Container(
              width: size.width,
              height: size.height,
              decoration: BoxDecoration(
                border: isDragging
                    ? Border.all(color: Colors.white, width: 2)
                    : null,
                boxShadow: isDragging
                    ? [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        )
                      ]
                    : null,
              ),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: isEditMode ? null : () => _showTableDetails(table),
                child: _buildTableCard(table),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Show table details dialog
  void _showTableDetails(Map<String, dynamic> table) {
    if (!isEditMode) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.grey[850],
          title: Text(
            'Table ${table['tableNumber']} Details',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _infoRow('Status', table['status']),
              _infoRow('Seats', table['seats'].toString()),
              _infoRow('Floor', table['floor'].toString()),
              if (table['reservation'] != null)
                _infoRow('Reservation', table['reservation']),
            ],
          ),
          actions: [
            TextButton(
              child: Text(
                'Close',
                style: TextStyle(color: Colors.white),
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      );
    }
  }

  // Helper for info rows in details dialog
  Widget _infoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: GoogleFonts.poppins(
              color: Colors.white70,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // Build a table visualization
  Widget _buildTableCard(Map<String, dynamic> table) {
    return Container(
      decoration: BoxDecoration(
        color: getTableColor(table['status']).withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border:
            Border.all(color: getTableColor(table['status']).withOpacity(0.3)),
      ),
      child: AspectRatio(
        aspectRatio: 1,
        child: Stack(
          children: [
            CustomPaint(
              painter: SeatPainter(
                seatCount: table['seats'],
                color: Color.fromRGBO(207, 207, 207, 1.0),
              ),
              size: Size.infinite,
            ),
            CustomPaint(
              painter: TableBorderPainter(
                seats: table['seats'],
                color: getTableColor(table['status']),
                strokeWidth: 3.0,
              ),
              size: Size.infinite,
            ),
            Positioned(
              top: 8,
              right: 8,
              child: buildCleaningStatusIndicator(table['cleaningStatus']),
            ),
            Center(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Table ${table['tableNumber']}',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '${table['seats']} Seats',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 2),
                      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: getTableColor(table['status']).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        table['status'],
                        style: GoogleFonts.poppins(
                          color: getTableColor(table['status']),
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (isEditMode)
              Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Center(
                  child: Icon(
                    Icons.drag_indicator,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Floor ${widget.floor} Layout',
          style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold, color: Colors.white),
        ),
        leading: IconButton(
            color: Colors.white,
            onPressed: () => GoRouter.of(context).pop(),
            icon: Icon(Icons.arrow_back)),
        actions: [
          // Edit mode toggle
          TextButton.icon(
            icon: Icon(
              isEditMode ? Icons.edit : Icons.lock,
              color: Colors.white,
            ),
            label: Text(
              isEditMode ? 'Editing' : 'View Mode',
              style: TextStyle(color: Colors.white),
            ),
            onPressed: () {
              setState(() {
                isEditMode = !isEditMode;
                // Clear dragged state when switching modes
                _currentlyDraggedTableId = null;
              });
            },
          ),

          // Save button (only visible in edit mode)
          if (isEditMode)
            IconButton(
              icon: Icon(Icons.save),
              tooltip: 'Save Layout',
              onPressed: _saveLayout,
            ),

          // Cancel button
          IconButton(
            icon: Icon(Icons.close),
            tooltip: 'Cancel',
            onPressed: () {
              GoRouter.of(context).pop();
            },
          ),
        ],
      ),
      body: Container(
        color: Colors.grey[900],
        child: Center(
          child: Container(
            width: layoutSize.width,
            height: layoutSize.height,
            margin: EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.grey[850],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.white24, width: 2),
            ),
            child: Stack(
              children: [
                // Grid lines
                CustomPaint(
                  painter: GridPainter(
                    crossAxisCount: 4,
                    mainAxisCount: 6,
                    color: Colors.white24,
                  ),
                  size: Size.infinite,
                ),

                // Tables
                ...floorTables.map(_buildDraggableTable).toList(),

                // Edit mode overlay
                if (isEditMode)
                  Positioned(
                    bottom: 16,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          'Drag tables to reposition',
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class GridPainter extends CustomPainter {
  final int crossAxisCount;
  final int mainAxisCount;
  final Color color;

  GridPainter({
    required this.crossAxisCount,
    required this.mainAxisCount,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw vertical lines
    for (int i = 1; i < crossAxisCount; i++) {
      final x = size.width * i / crossAxisCount;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal lines
    for (int i = 1; i < mainAxisCount; i++) {
      final y = size.height * i / mainAxisCount;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
