import 'dart:ui';

import 'package:easydine_main/widgets/app_bar.dart';
import 'package:easydine_main/widgets/cart_drawer.dart';
import 'package:easydine_main/widgets/tiled_background.dart';
import 'package:flutter/material.dart';

import '../../blocs/pos/pos_screen.dart';
import '../../widgets/cart_header.dart';
import '../../widgets/cart_items_list.dart';
import '../../widgets/cart_total.dart';
import '../../widgets/extra_options_row.dart';

class POSPage extends StatefulWidget {
  final String tableNumber;
  final String orderId;
  final String orderType;  // Add orderType parameter

  const POSPage({
    super.key, 
    required this.tableNumber, 
    required this.orderId,
    required this.orderType,  // Add to constructor
  });

  @override
  State<POSPage> createState() => _POSPageState();
}

class _POSPageState extends State<POSPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;

    return Scaffold(
      key: _scaffoldKey,
      endDrawer: isPortrait ? CartDrawer(
        tableNumber: widget.tableNumber,
        orderType: widget.orderType,
      ) : null,
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: true,
      appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
      body: Stack(
        children: [
          TiledBackground(),
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
            child: Column(
              children: [
                SizedBox(height: AppBar().preferredSize.height + 20),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: POSScreen(),
                      ),
                      if (!isPortrait) 
                        Container(
                          width: MediaQuery.of(context).size.width * 0.3,
                          decoration: BoxDecoration(
                            color: Colors.white24,
                            border: Border(
                              left: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                          child: Column(
                            children:  [
                              CartHeader(
                                orderId: widget.orderId,
                                tableNumber: widget.tableNumber,
                                orderType: widget.orderType,
                              ),
                              Expanded(child: CartItemsList()),
                              const Divider(color: Colors.grey),
                              extraOptionsRow(context),
                              CartTotal(orderId: widget.orderId, orderType: widget.orderType, tableNumber: widget.tableNumber),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
