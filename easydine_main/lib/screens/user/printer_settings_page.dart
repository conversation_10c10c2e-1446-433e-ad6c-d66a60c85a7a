// lib/screens/user/printer_settings_page.dart

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import '../../blocs/printer_settings/printer_settings_bloc.dart';
import '../../models/printer_settings.dart';

class PrinterSettingsPage extends StatelessWidget {
  const PrinterSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PrinterSettingsBloc()..add(LoadPrinterSettings()),
      child: const PrinterSettingsView(),
    );
  }
}

class PrinterSettingsView extends StatelessWidget {
  const PrinterSettingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[900],
      appBar: AppBar(
        title: Text(
          'Printer Settings',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.grey[850],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          BlocBuilder<PrinterSettingsBloc, PrinterSettingsState>(
            builder: (context, state) {
              return IconButton(
                icon: state.isDiscovering
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.search),
                onPressed: state.isDiscovering
                    ? null
                    : () {
                        context.read<PrinterSettingsBloc>().add(DiscoverPrinters());
                      },
                tooltip: 'Discover Printers',
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<PrinterSettingsBloc>().add(LoadPrinterSettings());
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: BlocConsumer<PrinterSettingsBloc, PrinterSettingsState>(
        listener: (context, state) {
          if (state.error != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.error!),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            );
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Customized Printing'),
                SizedBox(height: 3.h),
                _buildAssignPrintersSection(context, state),
                SizedBox(height: 4.h),
                _buildAssignCategoriesSection(context, state),
                SizedBox(height: 4.h),
                _buildActionButtons(context),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 6.sp,
        fontWeight: FontWeight.w700,
        color: Colors.white,
      ),
    );
  }

  Widget _buildAssignPrintersSection(BuildContext context, PrinterSettingsState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Assign Printers',
          style: GoogleFonts.poppins(
            fontSize: 4.5.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 2.h),
        _buildPrinterGrid(context, state),
      ],
    );
  }

  Widget _buildPrinterGrid(BuildContext context, PrinterSettingsState state) {
    final printers = state.settings.availablePrinters;
    final locations = PrinterLocation.getAllLocations();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 4.w,
        mainAxisSpacing: 2.h,
        childAspectRatio: 3,
      ),
      itemCount: printers.length,
      itemBuilder: (context, index) {
        final printer = printers[index];
        final currentLocation = state.settings.printerAssignments[printer.id] ?? 'Not assigned';

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Printer ${index + 1}',
              style: GoogleFonts.poppins(
                fontSize: 3.5.sp,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 1.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[600]!),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: currentLocation,
                  dropdownColor: Colors.grey[800],
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 3.sp,
                  ),
                  icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                  items: ['Not assigned', ...locations]
                      .map((location) => DropdownMenuItem(
                            value: location,
                            child: Text(location),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      context.read<PrinterSettingsBloc>().add(
                            UpdatePrinterAssignment(
                              printerId: printer.id,
                              location: value,
                            ),
                          );
                    }
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAssignCategoriesSection(BuildContext context, PrinterSettingsState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Assign Categories',
          style: GoogleFonts.poppins(
            fontSize: 4.5.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 2.h),
        _buildCategoriesGrid(context, state),
      ],
    );
  }

  Widget _buildCategoriesGrid(BuildContext context, PrinterSettingsState state) {
    final assignedLocations = state.settings.getAssignedLocations();
    if (assignedLocations.isEmpty) {
      return Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[600]!),
        ),
        child: Text(
          'No printers assigned to locations yet. Please assign printers above first.',
          style: GoogleFonts.poppins(
            color: Colors.white70,
            fontSize: 3.5.sp,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[600]!),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: assignedLocations.map((location) {
          return Expanded(
            child: _buildLocationColumn(context, state, location),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildLocationColumn(BuildContext context, PrinterSettingsState state, String location) {
    final categoriesForLocation = state.settings.getCategoriesForLocation(location);
    final availableCategories = state.availableCategories.isNotEmpty 
        ? state.availableCategories 
        : FoodCategory.getAllCategories();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          location,
          style: GoogleFonts.poppins(
            fontSize: 4.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 2.h),
        ...availableCategories.map((category) {
          final isAssigned = categoriesForLocation.contains(category);
          return Padding(
            padding: EdgeInsets.only(bottom: 1.h),
            child: Row(
              children: [
                Checkbox(
                  value: isAssigned,
                  onChanged: (value) {
                    context.read<PrinterSettingsBloc>().add(
                          UpdateCategoryAssignment(
                            category: category,
                            location: value == true ? location : '',
                          ),
                        );
                  },
                  activeColor: Colors.blue,
                  checkColor: Colors.white,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    category,
                    style: GoogleFonts.poppins(
                      fontSize: 3.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              _showResetConfirmationDialog(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Reset to Default',
              style: GoogleFonts.poppins(
                fontSize: 3.5.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Save & Close',
              style: GoogleFonts.poppins(
                fontSize: 3.5.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showResetConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: Colors.grey[800],
          title: Text(
            'Reset Printer Settings',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'Are you sure you want to reset all printer settings to default? This action cannot be undone.',
            style: GoogleFonts.poppins(
              color: Colors.white70,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                context.read<PrinterSettingsBloc>().add(ResetPrinterSettings());
              },
              child: Text(
                'Reset',
                style: GoogleFonts.poppins(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
