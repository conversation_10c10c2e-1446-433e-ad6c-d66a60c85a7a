// class CartView extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<POSBloc, POSState>(
//       builder: (context, state) {
//         // Group items by their base item ID
//         final Map<String, List<CartItem>> groupedItems = {};
//
//         for (final item in state.cartItems) {
//           final baseId = item.baseItemId ?? item.id;
//           if (!groupedItems.containsKey(baseId)) {
//             groupedItems[baseId] = [];
//           }
//           groupedItems[baseId]!.add(item);
//         }
//
//         return ListView.builder(
//           itemCount: groupedItems.length,
//           itemBuilder: (context, index) {
//             final items = groupedItems.values.elementAt(index);
//             final mainItem = items.first;
//             final variants = items.length > 1 ? items.sublist(1) : [];
//
//             return CartItemTile(
//               item: mainItem,
//               variants: variants,
//             );
//           },
//         );
//       },
//     );
//   }
// }