import 'package:equatable/equatable.dart';

class TableApiModel extends Equatable {
  final String tableId;
  final String name;
  final int minSeats;
  final int maxSeats;
  final int bookedSeats;
  final bool enabled;
  final String status;
  final String cleaning;
  final String location;
  final bool availableOnline;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final int availableSeats;
  final int reservedSeats;
  final bool hasReservations;
  final int reservationCount;
  final List<dynamic> todaysReservations;
  final String displayStatus;
  final bool isFullyOccupied;
  final bool isPartiallyOccupied;
  final bool isEmpty;
  final bool needsCleaning;
  final DateTime lastUpdated;
  final AvailabilitySummary availabilitySummary;

  const TableApiModel({
    required this.tableId,
    required this.name,
    required this.minSeats,
    required this.maxSeats,
    required this.bookedSeats,
    required this.enabled,
    required this.status,
    required this.cleaning,
    required this.location,
    required this.availableOnline,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.availableSeats,
    required this.reservedSeats,
    required this.hasReservations,
    required this.reservationCount,
    required this.todaysReservations,
    required this.displayStatus,
    required this.isFullyOccupied,
    required this.isPartiallyOccupied,
    required this.isEmpty,
    required this.needsCleaning,
    required this.lastUpdated,
    required this.availabilitySummary,
  });

  factory TableApiModel.fromJson(Map<String, dynamic> json) {
    return TableApiModel(
      tableId: json['tableId'] as String? ?? '',
      name: json['name'] as String? ?? '',
      minSeats: (json['minSeats'] as num?)?.toInt() ?? 1,
      maxSeats: (json['maxSeats'] as num?)?.toInt() ?? 2,
      bookedSeats: (json['bookedSeats'] as num?)?.toInt() ?? 0,
      enabled: json['enabled'] as bool? ?? true,
      status: json['status'] as String? ?? 'available',
      cleaning: json['cleaning'] as String? ?? 'clean',
      location: json['location'] as String? ?? '',
      availableOnline: json['availableOnline'] as bool? ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.parse(json['deletedAt'] as String)
          : null,
      availableSeats: (json['availableSeats'] as num?)?.toInt() ?? 0,
      reservedSeats: (json['reservedSeats'] as num?)?.toInt() ?? 0,
      hasReservations: json['hasReservations'] as bool? ?? false,
      reservationCount: (json['reservationCount'] as num?)?.toInt() ?? 0,
      todaysReservations: json['todaysReservations'] as List<dynamic>? ?? [],
      displayStatus: json['displayStatus'] as String? ?? 'AVAILABLE',
      isFullyOccupied: json['isFullyOccupied'] as bool? ?? false,
      isPartiallyOccupied: json['isPartiallyOccupied'] as bool? ?? false,
      isEmpty: json['isEmpty'] as bool? ?? true,
      needsCleaning: json['needsCleaning'] as bool? ?? false,
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.parse(json['lastUpdated'] as String)
          : DateTime.now(),
      availabilitySummary: json['availabilitySummary'] != null
          ? AvailabilitySummary.fromJson(
              json['availabilitySummary'] as Map<String, dynamic>)
          : AvailabilitySummary(
              status: 'available',
              bookedSeats: 0,
              availableSeats: 2,
              maxSeats: 2,
              cleaningStatus: 'clean',
              isEnabled: true,
            ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tableId': tableId,
      'name': name,
      'minSeats': minSeats,
      'maxSeats': maxSeats,
      'bookedSeats': bookedSeats,
      'enabled': enabled,
      'status': status,
      'cleaning': cleaning,
      'location': location,
      'availableOnline': availableOnline,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
      'availableSeats': availableSeats,
      'reservedSeats': reservedSeats,
      'hasReservations': hasReservations,
      'reservationCount': reservationCount,
      'todaysReservations': todaysReservations,
      'displayStatus': displayStatus,
      'isFullyOccupied': isFullyOccupied,
      'isPartiallyOccupied': isPartiallyOccupied,
      'isEmpty': isEmpty,
      'needsCleaning': needsCleaning,
      'lastUpdated': lastUpdated.toIso8601String(),
      'availabilitySummary': availabilitySummary.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        tableId,
        name,
        minSeats,
        maxSeats,
        bookedSeats,
        enabled,
        status,
        cleaning,
        location,
        availableOnline,
        createdAt,
        updatedAt,
        deletedAt,
        availableSeats,
        reservedSeats,
        hasReservations,
        reservationCount,
        todaysReservations,
        displayStatus,
        isFullyOccupied,
        isPartiallyOccupied,
        isEmpty,
        needsCleaning,
        lastUpdated,
        availabilitySummary,
      ];
}

class AvailabilitySummary extends Equatable {
  final String status;
  final int bookedSeats;
  final int availableSeats;
  final int maxSeats;
  final String cleaningStatus;
  final bool isEnabled;

  const AvailabilitySummary({
    required this.status,
    required this.bookedSeats,
    required this.availableSeats,
    required this.maxSeats,
    required this.cleaningStatus,
    required this.isEnabled,
  });

  factory AvailabilitySummary.fromJson(Map<String, dynamic> json) {
    return AvailabilitySummary(
      status: json['status'] as String? ?? 'available',
      bookedSeats: (json['bookedSeats'] as num?)?.toInt() ?? 0,
      availableSeats: (json['availableSeats'] as num?)?.toInt() ?? 0,
      maxSeats: (json['maxSeats'] as num?)?.toInt() ?? 2,
      cleaningStatus: json['cleaningStatus'] as String? ?? 'clean',
      isEnabled: json['isEnabled'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'bookedSeats': bookedSeats,
      'availableSeats': availableSeats,
      'maxSeats': maxSeats,
      'cleaningStatus': cleaningStatus,
      'isEnabled': isEnabled,
    };
  }

  @override
  List<Object> get props => [
        status,
        bookedSeats,
        availableSeats,
        maxSeats,
        cleaningStatus,
        isEnabled,
      ];
}
