import 'package:equatable/equatable.dart';

class TableReservationModel extends Equatable {
  final String reservationId;
  final String tableId;
  final String customerName;
  final String phoneNumber;
  final int numberOfGuests;
  final DateTime reservationTime;
  final String? specialNotes;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? checkedInAt;
  final DateTime? cancelledAt;

  const TableReservationModel({
    required this.reservationId,
    required this.tableId,
    required this.customerName,
    required this.phoneNumber,
    required this.numberOfGuests,
    required this.reservationTime,
    this.specialNotes,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.checkedInAt,
    this.cancelledAt,
  });

  factory TableReservationModel.fromJson(Map<String, dynamic> json) {
    return TableReservationModel(
      reservationId: json['reservationId'],
      tableId: json['tableId'],
      customerName: json['customerName'],
      phoneNumber: json['phoneNumber'],
      numberOfGuests: json['numberOfGuests'],
      reservationTime: DateTime.parse(json['reservationTime']),
      specialNotes: json['specialNotes'],
      status: json['status'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      checkedInAt: json['checkedInAt'] != null 
          ? DateTime.parse(json['checkedInAt']) 
          : null,
      cancelledAt: json['cancelledAt'] != null 
          ? DateTime.parse(json['cancelledAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reservationId': reservationId,
      'tableId': tableId,
      'customerName': customerName,
      'phoneNumber': phoneNumber,
      'numberOfGuests': numberOfGuests,
      'reservationTime': reservationTime.toIso8601String(),
      'specialNotes': specialNotes,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'checkedInAt': checkedInAt?.toIso8601String(),
      'cancelledAt': cancelledAt?.toIso8601String(),
    };
  }

  TableReservationModel copyWith({
    String? reservationId,
    String? tableId,
    String? customerName,
    String? phoneNumber,
    int? numberOfGuests,
    DateTime? reservationTime,
    String? specialNotes,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? checkedInAt,
    DateTime? cancelledAt,
  }) {
    return TableReservationModel(
      reservationId: reservationId ?? this.reservationId,
      tableId: tableId ?? this.tableId,
      customerName: customerName ?? this.customerName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      numberOfGuests: numberOfGuests ?? this.numberOfGuests,
      reservationTime: reservationTime ?? this.reservationTime,
      specialNotes: specialNotes ?? this.specialNotes,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      checkedInAt: checkedInAt ?? this.checkedInAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
    );
  }

  @override
  List<Object?> get props => [
        reservationId,
        tableId,
        customerName,
        phoneNumber,
        numberOfGuests,
        reservationTime,
        specialNotes,
        status,
        createdAt,
        updatedAt,
        checkedInAt,
        cancelledAt,
      ];
}

/// Enum for reservation status
enum ReservationStatus {
  pending,
  confirmed,
  checkedIn,
  cancelled,
  noShow,
}

extension ReservationStatusExtension on ReservationStatus {
  String get value {
    switch (this) {
      case ReservationStatus.pending:
        return 'pending';
      case ReservationStatus.confirmed:
        return 'confirmed';
      case ReservationStatus.checkedIn:
        return 'checked_in';
      case ReservationStatus.cancelled:
        return 'cancelled';
      case ReservationStatus.noShow:
        return 'no_show';
    }
  }

  static ReservationStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return ReservationStatus.pending;
      case 'confirmed':
        return ReservationStatus.confirmed;
      case 'checked_in':
        return ReservationStatus.checkedIn;
      case 'cancelled':
        return ReservationStatus.cancelled;
      case 'no_show':
        return ReservationStatus.noShow;
      default:
        return ReservationStatus.pending;
    }
  }
}

/// Request model for creating a reservation
class CreateReservationRequest extends Equatable {
  final String customerName;
  final String phoneNumber;
  final int numberOfGuests;
  final DateTime reservationTime;
  final String? specialNotes;

  const CreateReservationRequest({
    required this.customerName,
    required this.phoneNumber,
    required this.numberOfGuests,
    required this.reservationTime,
    this.specialNotes,
  });

  Map<String, dynamic> toJson() {
    return {
      'customerName': customerName,
      'phoneNumber': phoneNumber,
      'numberOfGuests': numberOfGuests,
      'reservationTime': reservationTime.toIso8601String(),
      if (specialNotes != null && specialNotes!.isNotEmpty)
        'specialNotes': specialNotes,
    };
  }

  @override
  List<Object?> get props => [
        customerName,
        phoneNumber,
        numberOfGuests,
        reservationTime,
        specialNotes,
      ];
}

/// Request model for updating a reservation
class UpdateReservationRequest extends Equatable {
  final String? customerName;
  final String? phoneNumber;
  final int? numberOfGuests;
  final DateTime? reservationTime;
  final String? specialNotes;

  const UpdateReservationRequest({
    this.customerName,
    this.phoneNumber,
    this.numberOfGuests,
    this.reservationTime,
    this.specialNotes,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};
    
    if (customerName != null) json['customerName'] = customerName;
    if (phoneNumber != null) json['phoneNumber'] = phoneNumber;
    if (numberOfGuests != null) json['numberOfGuests'] = numberOfGuests;
    if (reservationTime != null) json['reservationTime'] = reservationTime!.toIso8601String();
    if (specialNotes != null) json['specialNotes'] = specialNotes;
    
    return json;
  }

  @override
  List<Object?> get props => [
        customerName,
        phoneNumber,
        numberOfGuests,
        reservationTime,
        specialNotes,
      ];
}
