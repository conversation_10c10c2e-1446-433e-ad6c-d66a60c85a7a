import 'package:equatable/equatable.dart';

/// Model for table information in reservation response
class ReservationTable extends Equatable {
  final String tableId;
  final String name;
  final int minSeats;
  final int maxSeats;
  final int bookedSeats;
  final bool enabled;
  final String status;
  final String cleaning;
  final String location;
  final bool availableOnline;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const ReservationTable({
    required this.tableId,
    required this.name,
    required this.minSeats,
    required this.maxSeats,
    required this.bookedSeats,
    required this.enabled,
    required this.status,
    required this.cleaning,
    required this.location,
    required this.availableOnline,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory ReservationTable.fromJson(Map<String, dynamic> json) {
    return ReservationTable(
      tableId: json['tableId'] ?? '',
      name: json['name'] ?? '',
      minSeats: json['minSeats'] ?? 1,
      maxSeats: json['maxSeats'] ?? 2,
      bookedSeats: json['bookedSeats'] ?? 0,
      enabled: json['enabled'] ?? true,
      status: json['status'] ?? 'available',
      cleaning: json['cleaning'] ?? 'clean',
      location: json['location'] ?? '',
      availableOnline: json['availableOnline'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      deletedAt: json['deletedAt'] != null ? DateTime.parse(json['deletedAt']) : null,
    );
  }

  @override
  List<Object?> get props => [
        tableId,
        name,
        minSeats,
        maxSeats,
        bookedSeats,
        enabled,
        status,
        cleaning,
        location,
        availableOnline,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

/// Model for individual reservation
class ReservationItem extends Equatable {
  final String reservationId;
  final String confirmationCode;
  final String customerName;
  final String phoneNumber;
  final int numberOfGuests;
  final DateTime reservationTime;
  final String? specialNotes;
  final String? emailAddress;
  final String status;
  final DateTime? arrivedAt;
  final int durationMinutes;
  final bool isOnline;
  final String? tableCombinationId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final ReservationTable table;

  const ReservationItem({
    required this.reservationId,
    required this.confirmationCode,
    required this.customerName,
    required this.phoneNumber,
    required this.numberOfGuests,
    required this.reservationTime,
    this.specialNotes,
    this.emailAddress,
    required this.status,
    this.arrivedAt,
    required this.durationMinutes,
    required this.isOnline,
    this.tableCombinationId,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.table,
  });

  factory ReservationItem.fromJson(Map<String, dynamic> json) {
    return ReservationItem(
      reservationId: json['reservationId'] ?? '',
      confirmationCode: json['confirmationCode'] ?? '',
      customerName: json['customerName'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      numberOfGuests: json['numberOfGuests'] ?? 1,
      reservationTime: DateTime.parse(json['reservationTime']),
      specialNotes: json['specialNotes'],
      emailAddress: json['emailAddress'],
      status: json['status'] ?? 'confirmed',
      arrivedAt: json['arrivedAt'] != null ? DateTime.parse(json['arrivedAt']) : null,
      durationMinutes: json['durationMinutes'] ?? 60,
      isOnline: json['isOnline'] ?? false,
      tableCombinationId: json['tableCombinationId'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      deletedAt: json['deletedAt'] != null ? DateTime.parse(json['deletedAt']) : null,
      table: ReservationTable.fromJson(json['table']),
    );
  }

  @override
  List<Object?> get props => [
        reservationId,
        confirmationCode,
        customerName,
        phoneNumber,
        numberOfGuests,
        reservationTime,
        specialNotes,
        emailAddress,
        status,
        arrivedAt,
        durationMinutes,
        isOnline,
        tableCombinationId,
        createdAt,
        updatedAt,
        deletedAt,
        table,
      ];
}

/// Model for date range
class DateRange extends Equatable {
  final DateTime start;
  final DateTime end;

  const DateRange({
    required this.start,
    required this.end,
  });

  factory DateRange.fromJson(Map<String, dynamic> json) {
    return DateRange(
      start: DateTime.parse(json['start']),
      end: DateTime.parse(json['end']),
    );
  }

  @override
  List<Object> get props => [start, end];
}

/// Model for the complete reservations response
class ReservationsResponse extends Equatable {
  final List<ReservationItem> reservationsInCurrentWeek;
  final DateRange weekRange;
  final List<ReservationItem> reservationsInMonth;
  final DateRange monthRange;

  const ReservationsResponse({
    required this.reservationsInCurrentWeek,
    required this.weekRange,
    required this.reservationsInMonth,
    required this.monthRange,
  });

  factory ReservationsResponse.fromJson(Map<String, dynamic> json) {
    return ReservationsResponse(
      reservationsInCurrentWeek: (json['reservationsInCurrentWeek'] as List<dynamic>)
          .map((item) => ReservationItem.fromJson(item))
          .toList(),
      weekRange: DateRange.fromJson(json['weekRange']),
      reservationsInMonth: (json['reservationsInMonth'] as List<dynamic>)
          .map((item) => ReservationItem.fromJson(item))
          .toList(),
      monthRange: DateRange.fromJson(json['monthRange']),
    );
  }

  @override
  List<Object> get props => [
        reservationsInCurrentWeek,
        weekRange,
        reservationsInMonth,
        monthRange,
      ];
}

/// Model for the API response wrapper
class ReservationsApiResponse extends Equatable {
  final int statusCode;
  final bool success;
  final String message;
  final ReservationsResponse data;

  const ReservationsApiResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    required this.data,
  });

  factory ReservationsApiResponse.fromJson(Map<String, dynamic> json) {
    return ReservationsApiResponse(
      statusCode: json['statusCode'] ?? 200,
      success: json['success'] ?? true,
      message: json['message'] ?? '',
      data: ReservationsResponse.fromJson(json['data']),
    );
  }

  @override
  List<Object> get props => [statusCode, success, message, data];
}
