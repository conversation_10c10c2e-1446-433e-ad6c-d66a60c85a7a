import 'package:equatable/equatable.dart';

class BranchLocation extends Equatable {
  final double lat;
  final double lng;

  const BranchLocation({
    required this.lat,
    required this.lng,
  });

  factory BranchLocation.fromJson(Map<String, dynamic> json) {
    return BranchLocation(
      lat: (json['lat'] ?? 0.0).toDouble(),
      lng: (json['lng'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'lat': lat,
      'lng': lng,
    };
  }

  @override
  List<Object> get props => [lat, lng];
}

class Branch extends Equatable {
  final String branchId;
  final String name;
  final bool isPrimary;
  final bool isActive;
  final BranchLocation location;
  final String country;
  final String city;
  final String state;
  final String postalCode;
  final String streetName;
  final String houseNumber;
  final String apartment;
  final String locationName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const Branch({
    required this.branchId,
    required this.name,
    required this.isPrimary,
    required this.isActive,
    required this.location,
    required this.country,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.streetName,
    required this.houseNumber,
    required this.apartment,
    required this.locationName,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory Branch.fromJson(Map<String, dynamic> json) {
    return Branch(
      branchId: json['branchId'] ?? '',
      name: json['name'] ?? '',
      isPrimary: json['isPrimary'] ?? false,
      isActive: json['isActive'] ?? true,
      location: BranchLocation.fromJson(json['location'] ?? {}),
      country: json['country'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      postalCode: json['postalCode'] ?? '',
      streetName: json['streetName'] ?? '',
      houseNumber: json['houseNumber'] ?? '',
      apartment: json['apartment'] ?? '',
      locationName: json['locationName'] ?? '',
      createdAt:
          DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt:
          DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      deletedAt:
          json['deletedAt'] != null ? DateTime.parse(json['deletedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'branchId': branchId,
      'name': name,
      'isPrimary': isPrimary,
      'isActive': isActive,
      'location': location.toJson(),
      'country': country,
      'city': city,
      'state': state,
      'postalCode': postalCode,
      'streetName': streetName,
      'houseNumber': houseNumber,
      'apartment': apartment,
      'locationName': locationName,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  // Helper method to get full address
  String get fullAddress {
    final parts = <String>[];
    if (houseNumber.isNotEmpty) parts.add(houseNumber);
    if (apartment.isNotEmpty) parts.add(apartment);
    if (streetName.isNotEmpty) parts.add(streetName);
    if (city.isNotEmpty) parts.add(city);
    if (state.isNotEmpty) parts.add(state);
    if (postalCode.isNotEmpty) parts.add(postalCode);
    if (country.isNotEmpty) parts.add(country);
    return parts.join(', ');
  }

  @override
  List<Object?> get props => [
        branchId,
        name,
        isPrimary,
        isActive,
        location,
        country,
        city,
        state,
        postalCode,
        streetName,
        houseNumber,
        apartment,
        locationName,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}
