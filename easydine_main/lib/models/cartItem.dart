import 'package:equatable/equatable.dart';

// Legacy CartItem for local POS operations
class CartItem extends Equatable {
  final String id;
  final String name;
  final double price;
  final int quantity;
  final Map<String, dynamic>? customization;
  final String? baseItemId; // Original item ID before customization

  const CartItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    this.customization,
    this.baseItemId,
  });

  double get total => price * quantity;

  CartItem copyWith({
    String? id,
    String? name,
    double? price,
    int? quantity,
    Map<String, dynamic>? customization,
    String? baseItemId,
  }) {
    return CartItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      customization: customization ?? this.customization,
      baseItemId: baseItemId ?? this.baseItemId,
    );
  }

  // Helper method to check if this item is a variant of another item
  bool isVariantOf(String itemId) => baseItemId == itemId;

  // Helper method to get a display name including customization summary
  String get displayName {
    if (customization == null || customization!.isEmpty) return name;

    final customizationSummary = _getCustomizationSummary();
    return '$name ($customizationSummary)';
  }

  String _getCustomizationSummary() {
    final List<String> summary = [];

    if (customization!['cookingOption'] != null) {
      summary.add(customization!['cookingOption']);
    }

    if (customization!['side'] != null) {
      summary.add(customization!['side']['name']);
    }

    if (customization!['allergies'] != null &&
        (customization!['allergies'] as List).isNotEmpty) {
      summary.add('${customization!['allergies'].length} allergies');
    }

    return summary.join(', ');
  }

  @override
  List<Object?> get props =>
      [id, name, price, quantity, customization, baseItemId];
}

// Server-side cart models for API integration
class DishAddon extends Equatable {
  final String id;
  final String? name;
  final double? price;
  final int quantity;

  const DishAddon({
    required this.id,
    this.name,
    this.price,
    required this.quantity,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        if (name != null) 'name': name,
        if (price != null) 'price': price,
        'quantity': quantity,
      };

  factory DishAddon.fromJson(Map<String, dynamic> json) => DishAddon(
        id: json['id'],
        name: json['name'],
        price: json['price']?.toDouble(),
        quantity: json['quantity'],
      );

  @override
  List<Object?> get props => [id, name, price, quantity];
}

class DishExtra extends Equatable {
  final String id;
  final String? name;
  final double? price;
  final int quantity;

  const DishExtra({
    required this.id,
    this.name,
    this.price,
    required this.quantity,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        if (name != null) 'name': name,
        if (price != null) 'price': price,
        'quantity': quantity,
      };

  factory DishExtra.fromJson(Map<String, dynamic> json) => DishExtra(
        id: json['id'],
        name: json['name'],
        price: json['price']?.toDouble(),
        quantity: json['quantity'],
      );

  @override
  List<Object?> get props => [id, name, price, quantity];
}

class DishSide extends Equatable {
  final String id;
  final String? name;
  final double? price;
  final int quantity;

  const DishSide({
    required this.id,
    this.name,
    this.price,
    required this.quantity,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        if (name != null) 'name': name,
        if (price != null) 'price': price,
        'quantity': quantity,
      };

  factory DishSide.fromJson(Map<String, dynamic> json) => DishSide(
        id: json['id'],
        name: json['name'],
        price: json['price']?.toDouble(),
        quantity: json['quantity'],
      );

  @override
  List<Object?> get props => [id, name, price, quantity];
}

class DishBeverage extends Equatable {
  final String id;
  final String? name;
  final double? price;
  final int quantity;

  const DishBeverage({
    required this.id,
    this.name,
    this.price,
    required this.quantity,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        if (name != null) 'name': name,
        if (price != null) 'price': price,
        'quantity': quantity,
      };

  factory DishBeverage.fromJson(Map<String, dynamic> json) => DishBeverage(
        id: json['id'],
        name: json['name'],
        price: json['price']?.toDouble(),
        quantity: json['quantity'],
      );

  @override
  List<Object?> get props => [id, name, price, quantity];
}

class DishDessert extends Equatable {
  final String id;
  final String? name;
  final double? price;
  final int quantity;

  const DishDessert({
    required this.id,
    this.name,
    this.price,
    required this.quantity,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        if (name != null) 'name': name,
        if (price != null) 'price': price,
        'quantity': quantity,
      };

  factory DishDessert.fromJson(Map<String, dynamic> json) => DishDessert(
        id: json['id'],
        name: json['name'],
        price: json['price']?.toDouble(),
        quantity: json['quantity'],
      );

  @override
  List<Object?> get props => [id, name, price, quantity];
}
