import 'package:equatable/equatable.dart';
import 'table_api_model.dart';

class TableCombinationModel extends Equatable {
  final String tableCombinationId;
  final int minSeats;
  final int maxSeats;
  final bool enabled;
  final bool availableOnline;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final List<TableApiModel> tables;
  final String status;
  final int totalMaxSeats;
  final int totalBookedSeats;
  final int totalAvailableSeats;
  final int reservedSeats;
  final bool hasReservations;
  final int reservationCount;
  final List<dynamic> todaysReservations;
  final bool isFullyOccupied;
  final bool isPartiallyOccupied;
  final bool isEmpty;
  final bool hasDisabledTables;
  final DateTime lastUpdated;
  final CombinationAvailabilitySummary availabilitySummary;

  const TableCombinationModel({
    required this.tableCombinationId,
    required this.minSeats,
    required this.maxSeats,
    required this.enabled,
    required this.availableOnline,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.tables,
    required this.status,
    required this.totalMaxSeats,
    required this.totalBookedSeats,
    required this.totalAvailableSeats,
    required this.reservedSeats,
    required this.hasReservations,
    required this.reservationCount,
    required this.todaysReservations,
    required this.isFullyOccupied,
    required this.isPartiallyOccupied,
    required this.isEmpty,
    required this.hasDisabledTables,
    required this.lastUpdated,
    required this.availabilitySummary,
  });

  factory TableCombinationModel.fromJson(Map<String, dynamic> json) {
    return TableCombinationModel(
      tableCombinationId: json['tableCombinationId'] as String? ?? '',
      minSeats: (json['minSeats'] as num?)?.toInt() ?? 2,
      maxSeats: (json['maxSeats'] as num?)?.toInt() ?? 4,
      enabled: json['enabled'] as bool? ?? true,
      availableOnline: json['availableOnline'] as bool? ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.parse(json['deletedAt'] as String)
          : null,
      tables: (json['tables'] as List<dynamic>?)
              ?.map((table) =>
                  TableApiModel.fromJson(table as Map<String, dynamic>))
              .toList() ??
          [],
      status: json['status'] as String? ?? 'AVAILABLE',
      totalMaxSeats: (json['totalMaxSeats'] as num?)?.toInt() ?? 4,
      totalBookedSeats: (json['totalBookedSeats'] as num?)?.toInt() ?? 0,
      totalAvailableSeats: (json['totalAvailableSeats'] as num?)?.toInt() ?? 4,
      reservedSeats: (json['reservedSeats'] as num?)?.toInt() ?? 0,
      hasReservations: json['hasReservations'] as bool? ?? false,
      reservationCount: (json['reservationCount'] as num?)?.toInt() ?? 0,
      todaysReservations: json['todaysReservations'] as List<dynamic>? ?? [],
      isFullyOccupied: json['isFullyOccupied'] as bool? ?? false,
      isPartiallyOccupied: json['isPartiallyOccupied'] as bool? ?? false,
      isEmpty: json['isEmpty'] as bool? ?? true,
      hasDisabledTables: json['hasDisabledTables'] as bool? ?? false,
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.parse(json['lastUpdated'] as String)
          : DateTime.now(),
      availabilitySummary: json['availabilitySummary'] != null
          ? CombinationAvailabilitySummary.fromJson(
              json['availabilitySummary'] as Map<String, dynamic>)
          : CombinationAvailabilitySummary(
              status: 'AVAILABLE',
              totalBookedSeats: 0,
              totalAvailableSeats: 4,
              totalMaxSeats: 4,
              isEnabled: true,
              hasDisabledTables: false,
              minSeats: 2,
              maxSeats: 4,
            ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tableCombinationId': tableCombinationId,
      'minSeats': minSeats,
      'maxSeats': maxSeats,
      'enabled': enabled,
      'availableOnline': availableOnline,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
      'tables': tables.map((table) => table.toJson()).toList(),
      'status': status,
      'totalMaxSeats': totalMaxSeats,
      'totalBookedSeats': totalBookedSeats,
      'totalAvailableSeats': totalAvailableSeats,
      'reservedSeats': reservedSeats,
      'hasReservations': hasReservations,
      'reservationCount': reservationCount,
      'todaysReservations': todaysReservations,
      'isFullyOccupied': isFullyOccupied,
      'isPartiallyOccupied': isPartiallyOccupied,
      'isEmpty': isEmpty,
      'hasDisabledTables': hasDisabledTables,
      'lastUpdated': lastUpdated.toIso8601String(),
      'availabilitySummary': availabilitySummary.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        tableCombinationId,
        minSeats,
        maxSeats,
        enabled,
        availableOnline,
        createdAt,
        updatedAt,
        deletedAt,
        tables,
        status,
        totalMaxSeats,
        totalBookedSeats,
        totalAvailableSeats,
        reservedSeats,
        hasReservations,
        reservationCount,
        todaysReservations,
        isFullyOccupied,
        isPartiallyOccupied,
        isEmpty,
        hasDisabledTables,
        lastUpdated,
        availabilitySummary,
      ];
}

class CombinationAvailabilitySummary extends Equatable {
  final String status;
  final int totalBookedSeats;
  final int totalAvailableSeats;
  final int totalMaxSeats;
  final bool isEnabled;
  final bool hasDisabledTables;
  final int minSeats;
  final int maxSeats;

  const CombinationAvailabilitySummary({
    required this.status,
    required this.totalBookedSeats,
    required this.totalAvailableSeats,
    required this.totalMaxSeats,
    required this.isEnabled,
    required this.hasDisabledTables,
    required this.minSeats,
    required this.maxSeats,
  });

  factory CombinationAvailabilitySummary.fromJson(Map<String, dynamic> json) {
    return CombinationAvailabilitySummary(
      status: json['status'] as String? ?? 'AVAILABLE',
      totalBookedSeats: (json['totalBookedSeats'] as num?)?.toInt() ?? 0,
      totalAvailableSeats: (json['totalAvailableSeats'] as num?)?.toInt() ?? 4,
      totalMaxSeats: (json['totalMaxSeats'] as num?)?.toInt() ?? 4,
      isEnabled: json['isEnabled'] as bool? ?? true,
      hasDisabledTables: json['hasDisabledTables'] as bool? ?? false,
      minSeats: (json['minSeats'] as num?)?.toInt() ?? 2,
      maxSeats: (json['maxSeats'] as num?)?.toInt() ?? 4,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'totalBookedSeats': totalBookedSeats,
      'totalAvailableSeats': totalAvailableSeats,
      'totalMaxSeats': totalMaxSeats,
      'isEnabled': isEnabled,
      'hasDisabledTables': hasDisabledTables,
      'minSeats': minSeats,
      'maxSeats': maxSeats,
    };
  }

  @override
  List<Object> get props => [
        status,
        totalBookedSeats,
        totalAvailableSeats,
        totalMaxSeats,
        isEnabled,
        hasDisabledTables,
        minSeats,
        maxSeats,
      ];
}
