/// Custom models for handling the complex menu API response
/// These models match the structure returned by the backend API endpoint:
/// {{LAMBDA_HOST}}/{{food-menu-path}}/active-menu-withAvailability/?branchId=X&pos=true
library;

class MenuApiResponse {
  final int statusCode;
  final bool success;
  final String message;
  final MenuWithAvailability data;

  MenuApiResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    required this.data,
  });

  factory MenuApiResponse.fromJson(Map<String, dynamic> json) {
    return MenuApiResponse(
      statusCode: json['statusCode'] ?? 200,
      success: json['success'] ?? true,
      message: json['message'] ?? '',
      data: MenuWithAvailability.fromJson(json['data'] ?? {}),
    );
  }
}

class MenuWithAvailability {
  final String foodMenuId;
  final String? pictureUrl;
  final bool isDefault;
  final String name;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final MenuAvailability availability;
  final bool visibility;
  final bool customTimes;
  final List<MenuSection> sections;
  final List<dynamic> customSlots;
  final List<dynamic> globalCustomSlots;
  final List<dynamic> specialDays;
  final AvailabilityInfo availabilityInfo;

  MenuWithAvailability({
    required this.foodMenuId,
    this.pictureUrl,
    required this.isDefault,
    required this.name,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.availability,
    required this.visibility,
    required this.customTimes,
    required this.sections,
    required this.customSlots,
    required this.globalCustomSlots,
    required this.specialDays,
    required this.availabilityInfo,
  });

  factory MenuWithAvailability.fromJson(Map<String, dynamic> json) {
    return MenuWithAvailability(
      foodMenuId: json['foodMenuId'] ?? '',
      pictureUrl: json['pictureUrl'],
      isDefault: json['isDefault'] ?? false,
      name: json['name'] ?? '',
      description: json['description'],
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.tryParse(json['deletedAt'])
          : null,
      availability: MenuAvailability.fromJson(json['availability'] ?? {}),
      visibility: json['visibility'] ?? true,
      customTimes: json['customTimes'] ?? false,
      sections: (json['sections'] as List<dynamic>? ?? [])
          .map((section) => MenuSection.fromJson(section))
          .toList(),
      customSlots: json['customSlots'] ?? [],
      globalCustomSlots: json['globalCustomSlots'] ?? [],
      specialDays: json['specialDays'] ?? [],
      availabilityInfo:
          AvailabilityInfo.fromJson(json['availabilityInfo'] ?? {}),
    );
  }
}

class MenuAvailability {
  final bool site;
  final bool dineIn;
  final bool mobile;
  final bool pickup;
  final bool delivery;
  final bool takeAway;
  final bool phoneOrder;
  final bool contactlessDineIn;

  MenuAvailability({
    required this.site,
    required this.dineIn,
    required this.mobile,
    required this.pickup,
    required this.delivery,
    required this.takeAway,
    required this.phoneOrder,
    required this.contactlessDineIn,
  });

  factory MenuAvailability.fromJson(Map<String, dynamic> json) {
    return MenuAvailability(
      site: json['site'] ?? true,
      dineIn: json['dineIn'] ?? true,
      mobile: json['mobile'] ?? true,
      pickup: json['pickup'] ?? true,
      delivery: json['delivery'] ?? true,
      takeAway: json['takeAway'] ?? true,
      phoneOrder: json['phoneOrder'] ?? true,
      contactlessDineIn: json['contactlessDineIn'] ?? true,
    );
  }
}

class AvailabilityInfo {
  final bool isAvailable;
  final String? reason;
  final DateTime? nextAvailableAt;
  final DateTime? availableUntil;

  AvailabilityInfo({
    required this.isAvailable,
    this.reason,
    this.nextAvailableAt,
    this.availableUntil,
  });

  factory AvailabilityInfo.fromJson(Map<String, dynamic> json) {
    return AvailabilityInfo(
      isAvailable: json['isAvailable'] ?? true,
      reason: json['reason'],
      nextAvailableAt: json['nextAvailableAt'] != null
          ? DateTime.tryParse(json['nextAvailableAt'])
          : null,
      availableUntil: json['availableUntil'] != null
          ? DateTime.tryParse(json['availableUntil'])
          : null,
    );
  }
}

class MenuSection {
  final String menuSectionId;
  final List<String> pictureUrls;
  final String name;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final MenuAvailability availability;
  final bool visibility;
  final bool customTimes;
  final SectionIcon? sectionIcon;
  final List<MenuDish> dishes;
  final List<dynamic> customSlots;
  final List<dynamic> specialDays;
  final List<dynamic> globalCustomSlots;
  final AvailabilityInfo availabilityInfo;

  MenuSection({
    required this.menuSectionId,
    required this.pictureUrls,
    required this.name,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.availability,
    required this.visibility,
    required this.customTimes,
    this.sectionIcon,
    required this.dishes,
    required this.customSlots,
    required this.specialDays,
    required this.globalCustomSlots,
    required this.availabilityInfo,
  });

  factory MenuSection.fromJson(Map<String, dynamic> json) {
    return MenuSection(
      menuSectionId: json['menuSectionId'] ?? '',
      pictureUrls: List<String>.from(json['pictureUrls'] ?? []),
      name: json['name'] ?? '',
      description: json['description'],
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.tryParse(json['deletedAt'])
          : null,
      availability: MenuAvailability.fromJson(json['availability'] ?? {}),
      visibility: json['visibility'] ?? true,
      customTimes: json['customTimes'] ?? false,
      sectionIcon: json['sectionIcon'] != null
          ? SectionIcon.fromJson(json['sectionIcon'])
          : null,
      dishes: (json['dishes'] as List<dynamic>? ?? [])
          .map((dish) => MenuDish.fromJson(dish))
          .toList(),
      customSlots: json['customSlots'] ?? [],
      specialDays: json['specialDays'] ?? [],
      globalCustomSlots: json['globalCustomSlots'] ?? [],
      availabilityInfo:
          AvailabilityInfo.fromJson(json['availabilityInfo'] ?? {}),
    );
  }
}

class SectionIcon {
  final String sectionIconId;
  final String iconName;
  final String iconImg;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  SectionIcon({
    required this.sectionIconId,
    required this.iconName,
    required this.iconImg,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory SectionIcon.fromJson(Map<String, dynamic> json) {
    return SectionIcon(
      sectionIconId: json['sectionIconId'] ?? '',
      iconName: json['iconName'] ?? '',
      iconImg: json['iconImg'] ?? '',
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.tryParse(json['deletedAt'])
          : null,
    );
  }
}

class MenuDish {
  final String dishId;
  final String? pictureUrl;
  final String name;
  final String? description;
  final String price;
  final List<String> dietaryInfo;
  final List<String> tags;
  final bool isCustomizable;
  final int? preparationTime;
  final MenuAvailability availability;
  final bool visibility;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final bool customTimes;
  final bool specialRequests;
  final List<dynamic> customSlots;
  final List<dynamic> globalCustomSlots;
  final List<dynamic> specialDays;
  final AvailabilityInfo availabilityInfo;
  final List<DishIngredient> dishIngredients;
  final List<Allergy> allergies;
  final Map<String, dynamic>? customization;

  MenuDish({
    required this.dishId,
    this.pictureUrl,
    required this.name,
    this.description,
    required this.price,
    required this.dietaryInfo,
    required this.tags,
    required this.isCustomizable,
    this.preparationTime,
    required this.availability,
    required this.visibility,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.customTimes,
    required this.specialRequests,
    required this.customSlots,
    required this.globalCustomSlots,
    required this.specialDays,
    required this.availabilityInfo,
    required this.dishIngredients,
    required this.allergies,
    this.customization,
  });

  factory MenuDish.fromJson(Map<String, dynamic> json) {
    return MenuDish(
      dishId: json['dishId'] ?? '',
      pictureUrl: json['pictureUrl'],
      name: json['name'] ?? '',
      description: json['description'],
      price: json['price']?.toString() ?? '0',
      dietaryInfo: List<String>.from(json['dietaryInfo'] ?? []),
      tags: List<String>.from(json['tags'] ?? []),
      isCustomizable: json['isCustomizable'] ?? false,
      preparationTime: json['preparationTime'],
      availability: MenuAvailability.fromJson(json['availability'] ?? {}),
      visibility: json['visibility'] ?? true,
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.tryParse(json['deletedAt'])
          : null,
      customTimes: json['customTimes'] ?? false,
      specialRequests: json['specialRequests'] ?? false,
      customSlots: json['customSlots'] ?? [],
      globalCustomSlots: json['globalCustomSlots'] ?? [],
      specialDays: json['specialDays'] ?? [],
      availabilityInfo:
          AvailabilityInfo.fromJson(json['availabilityInfo'] ?? {}),
      dishIngredients: (json['dishIngredients'] as List<dynamic>? ?? [])
          .map((ingredient) => DishIngredient.fromJson(ingredient))
          .toList(),
      allergies: (json['allergies'] as List<dynamic>? ?? [])
          .map((allergy) => Allergy.fromJson(allergy))
          .toList(),
      customization: json['customization'],
    );
  }

  // Helper method to get price as double
  double get priceAsDouble {
    return double.tryParse(price) ?? 0.0;
  }

  // Helper method to check if dish is available
  bool get isAvailable {
    return availabilityInfo.isAvailable && visibility;
  }
}

class DishIngredient {
  final String dishIngredientId;
  final String amount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final Ingredient ingredient;

  DishIngredient({
    required this.dishIngredientId,
    required this.amount,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.ingredient,
  });

  factory DishIngredient.fromJson(Map<String, dynamic> json) {
    return DishIngredient(
      dishIngredientId: json['dishIngredientId'] ?? '',
      amount: json['amount']?.toString() ?? '0',
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.tryParse(json['deletedAt'])
          : null,
      ingredient: Ingredient.fromJson(json['ingredient'] ?? {}),
    );
  }
}

class Ingredient {
  final String ingredientId;
  final String name;
  final String? description;
  final String? unit;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  Ingredient({
    required this.ingredientId,
    required this.name,
    this.description,
    this.unit,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory Ingredient.fromJson(Map<String, dynamic> json) {
    return Ingredient(
      ingredientId: json['ingredientId'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      unit: json['unit'],
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.tryParse(json['deletedAt'])
          : null,
    );
  }
}

class Allergy {
  final String allergyId;
  final String name;
  final String? description;
  final String? colorPreference;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  Allergy({
    required this.allergyId,
    required this.name,
    this.description,
    this.colorPreference,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory Allergy.fromJson(Map<String, dynamic> json) {
    return Allergy(
      allergyId: json['allergyId'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      colorPreference: json['colorPreference'],
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.tryParse(json['deletedAt'])
          : null,
    );
  }
}
