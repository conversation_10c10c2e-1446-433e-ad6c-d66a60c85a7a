import 'dart:ui';

import 'package:flutter/foundation.dart';

class ChecklistReport {
  final String date;
  final Map<String, Map<String, bool>> checklistItems;
  final Map<String, List<Offset?>> signatures;
  final String completedBy;
  final DateTime completionTime;

  ChecklistReport({
    required this.date,
    required this.checklistItems,
    required this.signatures,
    required this.completedBy,
    required this.completionTime,
  });

  Map<String, dynamic> toJson() => {
    'date': date,
    'checklistItems': checklistItems.map(
            (key, value) => MapEntry(key, value.map((k, v) => MapEntry(k, v)))
    ),
    'signatures': signatures.map(
            (key, value) => MapEntry(
            key,
            value.map((offset) => offset != null
                ? {'dx': offset.dx, 'dy': offset.dy}
                : null
            ).toList()
        )
    ),
    'completedBy': completedBy,
    'completionTime': completionTime.toIso8601String(),
  };

  factory ChecklistReport.fromJson(Map<String, dynamic> json) {
    return ChecklistReport(
      date: json['date'],
      checklistItems: Map<String, Map<String, bool>>.from(
          json['checklistItems'].map((key, value) => MapEntry(
              key,
              Map<String, bool>.from(value)
          ))
      ),
      signatures: Map<String, List<Offset?>>.from(
          json['signatures'].map((key, value) => MapEntry(
              key,
              (value as List).map((point) => point != null
                  ? Offset(point['dx'], point['dy'])
                  : null
              ).toList()
          ))
      ),
      completedBy: json['completedBy'],
      completionTime: DateTime.parse(json['completionTime']),
    );
  }
}