import 'package:equatable/equatable.dart';
import 'table_api_model.dart';
import 'table_combination_model.dart';

class FloorModel extends Equatable {
  final String floorId;
  final String name;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final List<TableApiModel> tables;
  final List<TableCombinationModel> tableCombinations;

  const FloorModel({
    required this.floorId,
    required this.name,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.tables,
    required this.tableCombinations,
  });

  factory FloorModel.fromJson(Map<String, dynamic> json) {
    return FloorModel(
      floorId: json['floorId'] as String? ?? '',
      name: json['name'] as String? ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : DateTime.now(),
      deletedAt: json['deletedAt'] != null
          ? DateTime.parse(json['deletedAt'] as String)
          : null,
      tables: (json['tables'] as List<dynamic>?)
              ?.map((table) =>
                  TableApiModel.fromJson(table as Map<String, dynamic>))
              .toList() ??
          [],
      tableCombinations: (json['tableCombinations'] as List<dynamic>?)
              ?.map((combo) =>
                  TableCombinationModel.fromJson(combo as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'floorId': floorId,
      'name': name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
      'tables': tables.map((table) => table.toJson()).toList(),
      'tableCombinations':
          tableCombinations.map((combo) => combo.toJson()).toList(),
    };
  }

  FloorModel copyWith({
    String? floorId,
    String? name,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    List<TableApiModel>? tables,
    List<TableCombinationModel>? tableCombinations,
  }) {
    return FloorModel(
      floorId: floorId ?? this.floorId,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      tables: tables ?? this.tables,
      tableCombinations: tableCombinations ?? this.tableCombinations,
    );
  }

  @override
  List<Object?> get props => [
        floorId,
        name,
        createdAt,
        updatedAt,
        deletedAt,
        tables,
        tableCombinations,
      ];
}
