import 'package:equatable/equatable.dart';

/// Model for dish addons
class DishAddon extends Equatable {
  final String addonId;
  final String name;
  final String imgUrl;
  final String price;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const DishAddon({
    required this.addonId,
    required this.name,
    required this.imgUrl,
    required this.price,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory DishAddon.fromJson(Map<String, dynamic> json) {
    return DishAddon(
      addonId: json['addonId'],
      name: json['name'],
      imgUrl: json['imgUrl'],
      price: json['price'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      deletedAt: json['deletedAt'] != null ? DateTime.parse(json['deletedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'addonId': addonId,
      'name': name,
      'imgUrl': imgUrl,
      'price': price,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  /// Get price as double
  double get priceAsDouble {
    return double.tryParse(price) ?? 0.0;
  }

  @override
  List<Object?> get props => [
        addonId,
        name,
        imgUrl,
        price,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

/// Model for dish extras
class DishExtra extends Equatable {
  final String extraId;
  final String name;
  final String imgUrl;
  final String price;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const DishExtra({
    required this.extraId,
    required this.name,
    required this.imgUrl,
    required this.price,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory DishExtra.fromJson(Map<String, dynamic> json) {
    return DishExtra(
      extraId: json['extraId'],
      name: json['name'],
      imgUrl: json['imgUrl'],
      price: json['price'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      deletedAt: json['deletedAt'] != null ? DateTime.parse(json['deletedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'extraId': extraId,
      'name': name,
      'imgUrl': imgUrl,
      'price': price,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  /// Get price as double
  double get priceAsDouble {
    return double.tryParse(price) ?? 0.0;
  }

  @override
  List<Object?> get props => [
        extraId,
        name,
        imgUrl,
        price,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

/// Model for allergies
class Allergy extends Equatable {
  final String allergyId;
  final String name;
  final String description;
  final String colorPreference;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const Allergy({
    required this.allergyId,
    required this.name,
    required this.description,
    required this.colorPreference,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory Allergy.fromJson(Map<String, dynamic> json) {
    return Allergy(
      allergyId: json['allergyId'],
      name: json['name'],
      description: json['description'],
      colorPreference: json['colorPreference'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      deletedAt: json['deletedAt'] != null ? DateTime.parse(json['deletedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'allergyId': allergyId,
      'name': name,
      'description': description,
      'colorPreference': colorPreference,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        allergyId,
        name,
        description,
        colorPreference,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

/// Model for order types
class OrderType extends Equatable {
  final String orderTypeId;
  final String name;
  final String reservedName;
  final bool enabled;
  final DateTime createdAt;
  final DateTime updatedAt;

  const OrderType({
    required this.orderTypeId,
    required this.name,
    required this.reservedName,
    required this.enabled,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OrderType.fromJson(Map<String, dynamic> json) {
    return OrderType(
      orderTypeId: json['orderTypeId'],
      name: json['name'],
      reservedName: json['reservedName'],
      enabled: json['enabled'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orderTypeId': orderTypeId,
      'name': name,
      'reservedName': reservedName,
      'enabled': enabled,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        orderTypeId,
        name,
        reservedName,
        enabled,
        createdAt,
        updatedAt,
      ];
}

/// Response wrapper for API responses
class CustomizationApiResponse<T> extends Equatable {
  final int statusCode;
  final bool success;
  final String message;
  final T data;

  const CustomizationApiResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    required this.data,
  });

  factory CustomizationApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    return CustomizationApiResponse(
      statusCode: json['statusCode'],
      success: json['success'],
      message: json['message'],
      data: fromJsonT(json['data']),
    );
  }

  @override
  List<Object?> get props => [statusCode, success, message, data];
}
