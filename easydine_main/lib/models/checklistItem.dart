import 'package:flutter/material.dart';

class ChecklistItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final List<String>? subitems;
  bool isCompleted;

  ChecklistItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    this.subitems,
    this.isCompleted = false,
  });

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'subtitle': subtitle,
      'iconCodePoint': icon.codePoint,
      'iconFontFamily': icon.fontFamily,
      'iconFontPackage': icon.fontPackage,
      'subitems': subitems,
      'isCompleted': isCompleted,
    };
  }

  // Create from JSON
  factory ChecklistItem.fromJson(Map<String, dynamic> json) {
    // Use a predefined icon or fallback to a constant icon
    IconData iconData = Icons.check_circle; // Default fallback icon

    // Map common icon code points to their constant equivalents
    final int? codePoint = json['iconCodePoint'];
    if (codePoint != null) {
      switch (codePoint) {
        case 0xe5ca: // Icons.check_circle
          iconData = Icons.check_circle;
          break;
        case 0xe5d5: // Icons.error
          iconData = Icons.error;
          break;
        case 0xe88f: // Icons.warning
          iconData = Icons.warning;
          break;
        case 0xe88e: // Icons.info
          iconData = Icons.info;
          break;
        case 0xe5c9: // Icons.check_box
          iconData = Icons.check_box;
          break;
        case 0xe5c8: // Icons.check
          iconData = Icons.check;
          break;
        default:
          // For unknown icons, use the default
          iconData = Icons.check_circle;
      }
    }

    return ChecklistItem(
      title: json['title'],
      subtitle: json['subtitle'],
      icon: iconData,
      subitems:
          json['subitems'] != null ? List<String>.from(json['subitems']) : null,
      isCompleted: json['isCompleted'] ?? false,
    );
  }

  // Create a copy with updated properties
  ChecklistItem copyWith({
    String? title,
    String? subtitle,
    IconData? icon,
    List<String>? subitems,
    bool? isCompleted,
  }) {
    return ChecklistItem(
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      icon: icon ?? this.icon,
      subitems: subitems ?? this.subitems,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}
