/// API Configuration for EasyDine application
/// Contains all the API endpoints and configuration settings
library;

class ApiConfig {
  // Base configuration - these should be loaded from environment variables or config files
  static const String lambdaHost =
      'YOUR_LAMBDA_HOST_HERE'; // Replace with actual host
  static const String foodMenuPath =
      'YOUR_FOOD_MENU_PATH_HERE'; // Replace with actual path

  // Example branch ID - replace with actual branch ID
  static const String defaultBranchId = '69a591fb-a51b-417f-9a38-d7d71239b418';

  // API endpoints
  static String get activeMenuWithAvailabilityEndpoint =>
      '$lambdaHost/$foodMenuPath/active-menu-withAvailability/';

  /// Builds the complete URL for fetching active menu with availability
  static String buildActiveMenuUrl({
    required String branchId,
    bool forPOS = false,
  }) {
    return '$activeMenuWithAvailabilityEndpoint?branchId=$branchId&pos=$forPOS';
  }

  // HTTP configuration
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
}
