import 'package:easydine_main/blocs/pos/pos_bloc.dart';
import 'package:easydine_main/blocs/pos/pos_state.dart';
import 'package:easydine_main/blocs/cart/cart_bloc.dart';
import 'package:easydine_main/blocs/cart/cart_state.dart';
import 'package:easydine_main/blocs/session/session_bloc.dart';
import 'package:easydine_main/blocs/session/session_event.dart';
import 'package:easydine_main/blocs/session/session_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import '../router/router_constants.dart';

class WaiterAppBar extends StatelessWidget implements PreferredSizeWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;
  const WaiterAppBar({super.key, required this.scaffoldKey});

  void _showQuickLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.power_settings_new, color: Colors.red[400]),
              SizedBox(width: 8),
              Text(
                'Quick Logout',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
          content: Text(
            'End your session?',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[400],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: Icon(Icons.power_settings_new, size: 5.sp),
              label: Text('Logout'),
              onPressed: () {
                context.read<SessionBloc>().add(EndSession());
                GoRouter.of(context).goNamed(
                  RouterConstants.pinEntry,
                );
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    bool? hasEnd = scaffoldKey.currentState?.hasEndDrawer;
    bool isHomePage =
        GoRouterState.of(context).fullPath == RouterConstants.runningOrders;

    return AppBar(
      automaticallyImplyLeading: false,
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Row(
        children: [
          Text(
            'EasyDine',
            style: GoogleFonts.lobster(
              fontWeight: FontWeight.bold,
              fontSize: 32,
              color: const Color.fromRGBO(254, 250, 224, 1.0),
            ),
          ),
          const Spacer(),
          if (!isHomePage) ...[
            _buildAnimatedButton(
              icon: Icons.receipt_long_outlined,
              label: 'Running Orders',
              onTap: () =>
                  GoRouter.of(context).goNamed(RouterConstants.runningOrders),
            ),
          ],
          const SizedBox(width: 8),
          _buildAnimatedButton(
            icon: Icons.home_filled,
            label: 'Home',
            onTap: () => GoRouter.of(context).goNamed(RouterConstants.home),
          ),
        ],
      ),
      actions: [
        // User Info Section
        BlocBuilder<SessionBloc, SessionState>(
          builder: (context, state) {
            final waiterName = state.waiterName ?? '';
            final role = state.currentStaff?.role ?? 'Staff';
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () {
                    // Add profile view action here
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      children: [
                        Stack(
                          children: [
                            Container(
                              width: 4.h,
                              height: 4.w,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.orange.shade400,
                                    Colors.deepOrange.shade600,
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.deepOrange.withOpacity(0.5),
                                    blurRadius: 10,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Text(
                                  waiterName.isNotEmpty ? waiterName[0] : '?',
                                  style: GoogleFonts.poppins(
                                    color: Colors.white,
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              right: 0,
                              bottom: 0,
                              child: Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(width: 12),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  waiterName.isNotEmpty ? waiterName : 'Guest',
                                  style: GoogleFonts.poppins(
                                    color: Colors.white,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.verified,
                                  color: Colors.blue,
                                  size: 14,
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.purple.withOpacity(0.3),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                role,
                                style: GoogleFonts.poppins(
                                  color: Colors.white70,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        // Cart Icon with modern styling
        if (hasEnd == true)
          BlocBuilder<CartBloc, CartState>(
            builder: (context, cartState) {
              final cartItems = cartState.currentCart?.items ?? [];

              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 8),
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.shopping_cart,
                            color: Colors.white, size: 24),
                        onPressed: () {
                          scaffoldKey.currentState?.openEndDrawer();
                        },
                      ),
                    ),
                    if (cartItems.isNotEmpty)
                      Positioned(
                        top: -5,
                        right: -5,
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.red.shade400,
                                Colors.red.shade700
                              ],
                            ),
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.red.withOpacity(0.5),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: Text(
                            cartItems.length.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        // Modern Logout Button
        Container(
          margin: const EdgeInsets.only(right: 16, left: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.red.shade500.withAlpha(200),
                Colors.red.shade400.withAlpha(200),
              ],
            ),
            borderRadius: BorderRadius.circular(15),
          ),
          child: IconButton(
            icon: const Icon(Icons.lock_clock, color: Colors.white),
            tooltip: 'Quick Logout',
            onPressed: () => _showQuickLogoutDialog(context),
          ),
        ),
      ],
    );
  }

  Widget _buildAnimatedButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Container(
      height: 3.h,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withOpacity(0.3),
            Colors.purple.withOpacity(0.3)
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(icon, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(6.h);
}
