import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PaymentOptionsModal extends StatefulWidget {
  final double totalAmount;
  final Function(Map<String, dynamic>) onPaymentComplete;

  const PaymentOptionsModal({
    Key? key,
    required this.totalAmount,
    required this.onPaymentComplete,
  }) : super(key: key);

  @override
  State<PaymentOptionsModal> createState() => _PaymentOptionsModalState();
}

class _PaymentOptionsModalState extends State<PaymentOptionsModal> {
  String _selectedPaymentMethod = '';
  double _cashAmount = 0.0;
  double _cardAmount = 0.0;
  final TextEditingController _cashController = TextEditingController();
  final TextEditingController _cardController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Select Payment Method',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 20),
          _buildPaymentOptions(),
          const SizedBox(height: 20),
          if (_selectedPaymentMethod == 'split') _buildSplitPaymentFields(),
          const SizedBox(height: 20),
          _buildTotalAmount(),
          const SizedBox(height: 20),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildPaymentOptions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildPaymentOption('cash', Icons.money, 'Cash'),
        _buildPaymentOption('card', Icons.credit_card, 'Card'),
        _buildPaymentOption('split', Icons.call_split, 'Split'),
      ],
    );
  }

  Widget _buildPaymentOption(String method, IconData icon, String label) {
    final isSelected = _selectedPaymentMethod == method;
    return InkWell(
      onTap: () {
        setState(() {
          _selectedPaymentMethod = method;
          if (method != 'split') {
            _cashAmount = 0.0;
            _cardAmount = 0.0;
            _cashController.clear();
            _cardController.clear();
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? const Color.fromRGBO(44, 191, 90, 1) : Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.black,
              size: 24,
            ),
            const SizedBox(height: 5),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSplitPaymentFields() {
    return Column(
      children: [
        TextField(
          controller: _cashController,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'Cash Amount',
            prefixIcon: const Icon(Icons.money),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          onChanged: (value) {
            setState(() {
              _cashAmount = double.tryParse(value) ?? 0.0;
              // Automatically calculate card amount
              _cardAmount = widget.totalAmount - _cashAmount;
              _cardController.text = _cardAmount.toStringAsFixed(2);
            });
          },
        ),
        const SizedBox(height: 10),
        TextField(
          controller: _cardController,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'Card Amount',
            prefixIcon: const Icon(Icons.credit_card),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          onChanged: (value) {
            setState(() {
              _cardAmount = double.tryParse(value) ?? 0.0;
              // Automatically calculate cash amount
              _cashAmount = widget.totalAmount - _cardAmount;
              _cashController.text = _cashAmount.toStringAsFixed(2);
            });
          },
        ),
      ],
    );
  }

  Widget _buildTotalAmount() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Total Amount:',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            '\$${widget.totalAmount.toStringAsFixed(2)}',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color.fromRGBO(44, 191, 90, 1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: GoogleFonts.poppins(
              color: Colors.grey[600],
            ),
          ),
        ),
        const SizedBox(width: 10),
        ElevatedButton(
          onPressed: _processPayment,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color.fromRGBO(44, 191, 90, 1),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            'Confirm Payment',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  void _processPayment() {
    if (_selectedPaymentMethod.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a payment method')),
      );
      return;
    }

    if (_selectedPaymentMethod == 'split') {
      final totalSplit = _cashAmount + _cardAmount;
      if ((totalSplit - widget.totalAmount).abs() > 0.01) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Split amounts must equal total amount')),
        );
        return;
      }
    }

    final paymentDetails = {
      'method': _selectedPaymentMethod,
      'total': widget.totalAmount,
      'cashAmount': _selectedPaymentMethod == 'cash' ? widget.totalAmount : _cashAmount,
      'cardAmount': _selectedPaymentMethod == 'card' ? widget.totalAmount : _cardAmount,
      'timestamp': DateTime.now().toIso8601String(),
    };

    widget.onPaymentComplete(paymentDetails);
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _cashController.dispose();
    _cardController.dispose();
    super.dispose();
  }
}