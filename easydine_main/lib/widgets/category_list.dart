import 'package:easydine_main/services/menu_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';
import '../blocs/pos/pos_state.dart';
import 'category_card.dart';

class CategoryList extends StatefulWidget {
  const CategoryList({super.key});

  @override
  State<CategoryList> createState() => _CategoryListState();
}

class _CategoryListState extends State<CategoryList> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollLeft() {
    if (!_scrollController.hasClients) return;
    final newOffset = _scrollController.offset - 200;
    _scrollController.animateTo(
      newOffset < 0 ? 0 : newOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _scrollRight() {
    if (!_scrollController.hasClients) return;
    _scrollController.animateTo(
      _scrollController.offset + 200,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<POSBloc, POSState>(
      builder: (context, state) {
        // Get all categories and add "All" at the beginning
        final List<String> categories = [
          'All',
          ...MenuService.getAllCategories()
        ];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 3.w),
              child: Row(
                children: [
                  Text(
                    "Categories",
                    style: GoogleFonts.poppins(
                      fontSize: 8.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Container(
                      height: 4.h,
                      margin: EdgeInsets.symmetric(horizontal: 4.w),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.15),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: Focus(
                          onFocusChange: (hasFocus) {
                            setState(() {
                              // Add visual feedback when focused
                            });
                          },
                          child: TextField(
                            controller: _searchController,
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                            keyboardAppearance: Brightness.dark,
                            cursorColor: Colors.white,
                            decoration: InputDecoration(
                              fillColor: Colors.transparent,
                              filled: true,
                              isDense: true,
                              hintText: 'Search menu items...',
                              hintStyle: GoogleFonts.poppins(
                                color: Colors.white.withOpacity(0.4),
                                fontSize: 4.sp,
                                fontWeight: FontWeight.w400,
                              ),
                              prefixIcon: AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                padding: EdgeInsets.all(2.w),
                                child: Icon(
                                  Icons.search_rounded,
                                  color: Colors.white.withOpacity(0.6),
                                  size: 5.sp,
                                ),
                              ),
                              suffixIcon: state.searchQuery.isNotEmpty
                                  ? Container(
                                      margin: EdgeInsets.all(2.w),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(50),
                                      ),
                                      child: IconButton(
                                        padding: EdgeInsets.zero,
                                        icon: Icon(
                                          Icons.close_rounded,
                                          color: Colors.white.withOpacity(0.6),
                                          size: 4.5.sp,
                                        ),
                                        onPressed: () {
                                          _searchController.clear();
                                          context
                                              .read<POSBloc>()
                                              .add(const SearchMenuItems(''));
                                        },
                                      ),
                                    )
                                  : null,
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 4.w,
                                vertical: 2.5.w,
                              ),
                              enabledBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                            ),
                            onChanged: (value) {
                              context
                                  .read<POSBloc>()
                                  .add(SearchMenuItems(value));
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _scrollLeft,
                    icon: Icon(
                      Icons.arrow_circle_left,
                      color: Colors.white,
                      size: 10.sp,
                    ),
                  ),
                  IconButton(
                    onPressed: _scrollRight,
                    icon: Icon(
                      Icons.arrow_circle_right,
                      color: Colors.white,
                      size: 10.sp,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 0.5.h),
            SizedBox(
              height: MediaQuery.of(context).orientation == Orientation.portrait
                  ? 12.h
                  : 10.h,
              width: double.infinity,
              child: ListView.builder(
                padding: EdgeInsets.only(left: 3.w),
                controller: _scrollController,
                scrollDirection: Axis.horizontal,
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final categoryName = categories[index];
                  final categoryItems = categoryName == 'All'
                      ? MenuService.getAllMenuItems()
                      : MenuService.getMenuItems(categoryName);
                  return CategoryCard(
                    category: {
                      'name': categoryName,
                      'icon': _getCategoryIcon(categoryName),
                      'itemCount': categoryItems.length.toString(),
                    },
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  String _getCategoryIcon(String category) {
    final lowerCategory = category.toLowerCase();
    if (lowerCategory.contains('pizza')) {
      return '🍕';
    } else if (lowerCategory.contains('burger')) {
      return '🍔';
    } else if (lowerCategory.contains('drink')) {
      return '🥤';
    } else if (lowerCategory.contains('dessert')) {
      return '🍰';
    } else if (lowerCategory.contains('side')) {
      return '🍟';
    } else if (lowerCategory.contains('beverage')) {
      return '🍹';
    } else if (lowerCategory.contains('appetizer')) {
      return '🫕';
    } else if (lowerCategory.contains('entree')) {
      return '🍲';
    } else if (lowerCategory.contains('salad')) {
      return '🥗';
    } else if (lowerCategory.contains('all')) {
      return '🍽️';
    } else if (lowerCategory.contains('soup')) {
      return '🍜';
    } else if (lowerCategory.contains('sandwich')) {
      return '🥪';
    } else if (lowerCategory.contains('wrap')) {
      return '🌯';
    } else if (lowerCategory.contains('taco')) {
      return '🌮';
    } else if (lowerCategory.contains('sushi')) {
      return '🍣';
    } else if (lowerCategory.contains('pasta')) {
      return '🍝';
    } else if (lowerCategory.contains('biriyani')) {
      return '🍛';
    } else {
      return '🍽️';
    }
  }
}
