import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';
import '../blocs/pos/pos_state.dart';
import '../blocs/cart/cart_bloc.dart';
import '../blocs/cart/cart_event.dart';
import '../blocs/cart/cart_state.dart';
import '../models/menuItem.dart';
import '../models/cart_models.dart';

class EnhancedMenuItemCard extends StatefulWidget {
  final MenuItem item;
  final bool useServerCart;
  final VoidCallback? onCustomize;

  const EnhancedMenuItemCard({
    super.key,
    required this.item,
    this.useServerCart = true,
    this.onCustomize,
  });

  @override
  State<EnhancedMenuItemCard> createState() => _EnhancedMenuItemCardState();
}

class _EnhancedMenuItemCardState extends State<EnhancedMenuItemCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  void _addToCart() {
    if (widget.useServerCart) {
      // Add directly to server cart
      context.read<POSBloc>().add(AddToServerCart(
            dishId: widget.item.id,
            quantity: 1,
            type: "customized",
            notes: null,
            allergyIds: const [],
          ));
    } else {
      // Add to local cart with server sync
      context.read<POSBloc>().add(AddToCart(
            widget.item,
            id: widget.item.id,
            name: widget.item.name,
            price: widget.item.price,
            syncWithServer: true,
          ));
    }
  }

  void _showCustomizationDialog() {
    showDialog(
      context: context,
      builder: (context) => _CustomizationDialog(
        item: widget.item,
        onConfirm: (customization) {
          // Add customized item to cart
          if (widget.useServerCart) {
            context.read<POSBloc>().add(AddToServerCart(
                  dishId: widget.item.id,
                  quantity: customization['quantity'] ?? 1,
                  type: "customized",
                  notes: customization['notes'],
                  allergyIds:
                      List<String>.from(customization['allergyIds'] ?? []),
                ));
          } else {
            context.read<POSBloc>().add(AddToCart(
                  widget.item,
                  id: widget.item.id,
                  name: widget.item.name,
                  price: widget.item.price,
                  customization: customization,
                  syncWithServer: true,
                ));
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, cartState) {
        // Check if item is in cart
        final cartItems = cartState.currentCart?.items ?? [];
        final isInCart =
            cartItems.any((cartItem) => cartItem.dishId == widget.item.id);

        return GestureDetector(
          onTapDown: (_) => _scaleController.forward(),
          onTapUp: (_) {
            _scaleController.reverse();
            _addToCart();
          },
          onTapCancel: () => _scaleController.reverse(),
          onLongPress: () {
            _scaleController.reverse();
            _showCustomizationDialog();
          },
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) => Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            ),
            child: Card(
              color: isInCart ? Colors.green.withOpacity(0.3) : Colors.white24,
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: isInCart
                    ? const BorderSide(color: Colors.green, width: 2)
                    : BorderSide.none,
              ),
              child: Container(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Item image placeholder
                    Container(
                      height: 80,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.restaurant,
                        color: Colors.white54,
                        size: 32,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Item name
                    Text(
                      widget.item.name,
                      style: GoogleFonts.dmSans(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // Item description
                    Text(
                      widget.item.description,
                      style: GoogleFonts.dmSans(
                        fontSize: 12,
                        color: Colors.white70,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),

                    // Price and actions
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '\$${widget.item.price.toStringAsFixed(2)}',
                          style: GoogleFonts.dmSans(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                        Row(
                          children: [
                            if (widget.onCustomize != null)
                              IconButton(
                                onPressed: _showCustomizationDialog,
                                icon: const Icon(
                                  Icons.tune,
                                  color: Colors.white70,
                                  size: 20,
                                ),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            const SizedBox(width: 4),
                            Icon(
                              isInCart ? Icons.check_circle : Icons.add_circle,
                              color: isInCart ? Colors.green : Colors.white70,
                              size: 24,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _CustomizationDialog extends StatefulWidget {
  final MenuItem item;
  final Function(Map<String, dynamic>) onConfirm;

  const _CustomizationDialog({
    required this.item,
    required this.onConfirm,
  });

  @override
  State<_CustomizationDialog> createState() => _CustomizationDialogState();
}

class _CustomizationDialogState extends State<_CustomizationDialog> {
  int quantity = 1;
  String notes = '';
  final List<String> selectedAllergies = [];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Customize ${widget.item.name}'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Quantity selector
          Row(
            children: [
              const Text('Quantity: '),
              IconButton(
                onPressed:
                    quantity > 1 ? () => setState(() => quantity--) : null,
                icon: const Icon(Icons.remove),
              ),
              Text('$quantity'),
              IconButton(
                onPressed: () => setState(() => quantity++),
                icon: const Icon(Icons.add),
              ),
            ],
          ),

          // Notes
          TextField(
            decoration: const InputDecoration(
              labelText: 'Special Notes',
              hintText: 'Any special requests...',
            ),
            onChanged: (value) => notes = value,
            maxLines: 2,
          ),

          // Add more customization options here as needed
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onConfirm({
              'quantity': quantity,
              'notes': notes.isNotEmpty ? notes : null,
              'allergyIds': selectedAllergies,
            });
            Navigator.of(context).pop();
          },
          child: const Text('Add to Cart'),
        ),
      ],
    );
  }
}
