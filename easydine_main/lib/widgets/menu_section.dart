import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import 'category_list.dart';
import 'menu_grid.dart';

class MenuSection extends StatelessWidget {
  const MenuSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 1.h),
        CategoryList(),
        Expanded(child: MenuGrid()),
      ],
    );
  }
}
