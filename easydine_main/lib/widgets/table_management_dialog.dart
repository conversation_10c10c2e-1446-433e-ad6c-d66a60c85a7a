import'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../blocs/table/table_bloc.dart';
import '../blocs/table/table_event.dart';
import '../services/table_order_service.dart';

class TableManagementDialog extends StatefulWidget {
  final Map<String, dynamic> table;

  const TableManagementDialog({
    Key? key,
    required this.table,
  }) : super(key: key);

  @override
  State<TableManagementDialog> createState() => _TableManagementDialogState();
}

class _TableManagementDialogState extends State<TableManagementDialog>
    with TickerProviderStateMixin {
  final _seatsController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _seatsController.text = widget.table['bookedSeats']?.toString() ?? '0';

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _seatsController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final table = widget.table;

    // Modern color palette
    final primaryColor = const Color(0xFF6366F1); // Indigo
    final secondaryColor = const Color(0xFF10B981); // Emerald
    final backgroundColor = const Color(0xFF0F172A); // Slate 900
    final surfaceColor = const Color(0xFF1E293B); // Slate 800
    final cardColor = const Color(0xFF334155); // Slate 700
    final textPrimary = Colors.white;
    final textSecondary = const Color(0xFF94A3B8); // Slate 400

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                width: 450,
                constraints: const BoxConstraints(maxHeight: 700),
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: surfaceColor,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.5),
                      blurRadius: 32,
                      offset: const Offset(0, 16),
                      spreadRadius: -4,
                    ),
                    BoxShadow(
                      color: primaryColor.withValues(alpha: 0.1),
                      blurRadius: 64,
                      offset: const Offset(0, 0),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Modern Header with gradient
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            primaryColor.withValues(alpha: 0.1),
                            primaryColor.withValues(alpha: 0.05),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(24),
                        ),
                        border: Border(
                          bottom: BorderSide(
                            color: surfaceColor,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: primaryColor.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: primaryColor.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.table_restaurant_rounded,
                              color: primaryColor,
                              size: 28,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Table ${table['tableNumber']}',
                                  style: GoogleFonts.inter(
                                    color: textPrimary,
                                    fontSize: 22,
                                    fontWeight: FontWeight.w700,
                                    letterSpacing: -0.5,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    _buildStatusChip(table['status']),
                                    const SizedBox(width: 8),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: cardColor.withValues(alpha: 0.6),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Text(
                                        '${table['seats']} seats',
                                        style: GoogleFonts.inter(
                                          color: textSecondary,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.close_rounded,
                              color: textSecondary,
                            ),
                            style: IconButton.styleFrom(
                              backgroundColor: surfaceColor.withValues(alpha: 0.5),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Content with better spacing
                    Flexible(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Table Status Section
                            _buildSectionHeader('Table Status', Icons.assignment_turned_in_rounded),
                            const SizedBox(height: 16),
                            _buildStatusButtons(),

                            const SizedBox(height: 32),

                            // Cleaning Status Section
                            _buildSectionHeader('Cleaning Status', Icons.cleaning_services_rounded),
                            const SizedBox(height: 16),
                            _buildCleaningButtons(),

                            const SizedBox(height: 32),

                            // Booked Seats Section
                            _buildSectionHeader('Booked Seats', Icons.people_rounded),
                            const SizedBox(height: 16),
                            _buildBookedSeatsInput(),

                            const SizedBox(height: 32),

                            // Quick Actions
                            _buildSectionHeader('Quick Actions', Icons.flash_on_rounded),
                            const SizedBox(height: 16),
                            _buildQuickActions(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: const Color(0xFF6366F1),
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.inter(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: -0.2,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(String status) {
    Color chipColor;
    IconData chipIcon;

    switch (status.toLowerCase()) {
      case 'available':
        chipColor = const Color(0xFF10B981);
        chipIcon = Icons.check_circle_rounded;
        break;
      case 'occupied':
        chipColor = const Color(0xFFF59E0B);
        chipIcon = Icons.people_rounded;
        break;
      case 'reserved':
        chipColor = const Color(0xFF3B82F6);
        chipIcon = Icons.bookmark_rounded;
        break;
      default:
        chipColor = const Color(0xFF6B7280);
        chipIcon = Icons.help_rounded;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: chipColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            chipIcon,
            color: chipColor,
            size: 14,
          ),
          const SizedBox(width: 4),
          Text(
            status,
            style: GoogleFonts.inter(
              color: chipColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusButtons() {
    return Row(
      children: [
        _buildModernButton('Available', const Color(0xFF10B981), Icons.check_circle_rounded,
                () => _updateTableStatus('Available'),
            widget.table['status'].toString().toLowerCase() == 'available'),
        const SizedBox(width: 12),
        _buildModernButton('Occupied', const Color(0xFFF59E0B), Icons.people_rounded,
                () => _updateTableStatus('Occupied'),
            widget.table['status'].toString().toLowerCase() == 'occupied'),
        const SizedBox(width: 12),
        _buildModernButton('Reserved', const Color(0xFF3B82F6), Icons.bookmark_rounded,
                () => _updateTableStatus('Reserved'),
            widget.table['status'].toString().toLowerCase() == 'reserved'),
      ],
    );
  }

  Widget _buildCleaningButtons() {
    return Row(
      children: [
        _buildModernButton('Clean', const Color(0xFF10B981), Icons.verified_rounded,
                () => _updateCleaningStatus('Clean'),
            widget.table['cleaningStatus'].toString().toLowerCase() == 'clean'),
        const SizedBox(width: 12),
        _buildModernButton('Needs Cleaning', const Color(0xFFF59E0B), Icons.warning_rounded,
                () => _updateCleaningStatus('Needs Cleaning'),
            widget.table['cleaningStatus'].toString().toLowerCase() == 'needs cleaning'),
        const SizedBox(width: 12),
        _buildModernButton('Dirty', const Color(0xFFEF4444), Icons.cancel_rounded,
                () => _updateCleaningStatus('Dirty'),
            widget.table['cleaningStatus'].toString().toLowerCase() == 'dirty'),
      ],
    );
  }

  Widget _buildModernButton(String text, Color color, IconData icon, VoidCallback onPressed, bool isSelected) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: isSelected ? color.withValues(alpha: 0.15) : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? color : color.withValues(alpha: 0.3),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: isSelected ? color : color.withValues(alpha: 0.7),
                  size: 16,
                ),
                const SizedBox(width: 6),
                Flexible(
                  child: Text(
                    text,
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? color : color.withValues(alpha: 0.7),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBookedSeatsInput() {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF334155),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _seatsController,
              keyboardType: TextInputType.number,
              style: GoogleFonts.inter(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              decoration: InputDecoration(
                hintText: 'Number of seats',
                hintStyle: GoogleFonts.inter(
                  color: const Color(0xFF64748B),
                  fontSize: 14,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                border: InputBorder.none,
                prefixIcon: Icon(
                  Icons.person_outline_rounded,
                  color: const Color(0xFF64748B),
                  size: 20,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton(
          onPressed: _updateBookedSeats,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF6366F1),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 0,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.update_rounded, size: 18),
              const SizedBox(width: 6),
              Text(
                'Update',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      children: [
        // Cancel Order button (only show if table is occupied)
        if (widget.table['status'] == 'Occupied') ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _cancelOrder,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFEF4444),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.cancel_rounded, size: 18),
                  const SizedBox(width: 8),
                  Text(
                    'Cancel Order',
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: _resetTable,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF374151),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.refresh_rounded, size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'Reset Table',
                      style: GoogleFonts.inter(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF10B981),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.check_rounded, size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'Done',
                      style: GoogleFonts.inter(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _updateTableStatus(String status) {
    final tableBloc = context.read<TableBloc>();
    tableBloc.add(UpdateTableStatus(
      tableId: widget.table['id'] as String,
      newStatus: status,
    ));
    Navigator.pop(context);
    _showSuccessMessage('Table status updated to $status');
  }

  void _updateCleaningStatus(String status) {
    final tableBloc = context.read<TableBloc>();
    tableBloc.add(UpdateTableCleaningStatus(
      tableId: widget.table['id'] as String,
      newStatus: status,
    ));
    Navigator.pop(context);
    _showSuccessMessage('Cleaning status updated to $status');
  }

  void _updateBookedSeats() {
    final seats = int.tryParse(_seatsController.text) ?? 0;
    final tableBloc = context.read<TableBloc>();
    tableBloc.add(UpdateTableBookedSeats(
      tableId: widget.table['id'] as String,
      bookedSeats: seats,
    ));
    Navigator.pop(context);
    _showSuccessMessage('Booked seats updated to $seats');
  }

  void _resetTable() {
    final tableBloc = context.read<TableBloc>();
    tableBloc.add(ResetTable(
      tableId: widget.table['id'] as String,
    ));
    Navigator.pop(context);
    _showSuccessMessage('Table reset successfully');
  }

  Future<void> _cancelOrder() async {
    // Show modern confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E293B),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFFEF4444).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.warning_rounded,
                color: Color(0xFFEF4444),
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'Cancel Order',
              style: GoogleFonts.inter(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Text(
          'Are you sure you want to cancel the order for this table? This action cannot be undone.',
          style: GoogleFonts.inter(
            color: const Color(0xFF94A3B8),
            fontSize: 14,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            child: Text(
              'Cancel',
              style: GoogleFonts.inter(
                color: const Color(0xFF6B7280),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEF4444),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Confirm',
              style: GoogleFonts.inter(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final numOfGuests = widget.table['bookedSeats'] as int? ?? 1;

      final success = await TableOrderService.cancelOrder(
        tableId: widget.table['id'] as String,
        numOfGuests: numOfGuests,
      );

      if (success) {
        final tableBloc = context.read<TableBloc>();
        tableBloc.add(MarkTableAsAvailable(
          tableId: widget.table['id'] as String,
        ));

        Navigator.pop(context);
        _showSuccessMessage('Order cancelled successfully');
      } else {
        _showErrorMessage('Failed to cancel order. Please try again.');
      }
    } catch (e) {
      _showErrorMessage('Error cancelling order: $e');
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(
                Icons.check_rounded,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(
                Icons.error_rounded,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFFEF4444),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }
}