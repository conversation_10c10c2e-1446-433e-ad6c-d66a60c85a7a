import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';
import '../models/cartItem.dart';

class QuantityControls extends StatelessWidget {
  final CartItem item;

  const QuantityControls({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    debugPrint(
        '🛒 QuantityControls: Displaying quantity ${item.quantity} for item ${item.id}');
    return Container(
      alignment: Alignment.center,
      constraints: BoxConstraints(
        minHeight: 2.h,
        maxHeight: 3.h,
        minWidth: 13.w,
        maxWidth: 15.w,
      ),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: IntrinsicWidth(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _QuantityButton(
              icon: Icons.remove,
              onPressed: () {
                debugPrint(
                    '🛒 QuantityControls: Decrement button pressed for item ${item.id}, current quantity: ${item.quantity}');
                if (item.quantity <= 1) {
                  // Remove item when quantity would go below 1
                  context.read<POSBloc>().add(RemoveFromCart(item.id));
                } else {
                  context.read<POSBloc>().add(
                        UpdateCartItemQuantity(
                          id: item.id,
                          quantity: item.quantity - 1,
                        ),
                      );
                }
              },
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 0.02.w),
              constraints: BoxConstraints(minWidth: 1.w),
              child: Center(
                child: Text(
                  item.quantity.toString(),
                  style: GoogleFonts.dmSans(
                    fontSize: 4.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            _QuantityButton(
              icon: Icons.add,
              onPressed: () {
                debugPrint(
                    '🛒 QuantityControls: Increment button pressed for item ${item.id}, current quantity: ${item.quantity}');
                context.read<POSBloc>().add(
                      UpdateCartItemQuantity(
                        id: item.id,
                        quantity: item.quantity + 1,
                      ),
                    );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _QuantityButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;

  const _QuantityButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(
        icon,
        size: 5.sp,
        color: Colors.white,
      ),
      onPressed: onPressed,
      constraints: BoxConstraints(
        minWidth: 4.w,
        minHeight: 3.h,
        maxWidth: 6.w,
        maxHeight: 4.h,
      ),
      padding: EdgeInsets.all(0.5.w),
    );
  }
}
