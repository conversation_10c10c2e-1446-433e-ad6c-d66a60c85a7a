import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../blocs/cart/cart_bloc.dart';
import '../blocs/cart/cart_event.dart';
import '../blocs/cart/cart_state.dart';
import '../models/cartItem.dart';
import '../models/cart_models.dart';
import '../services/cart_service.dart';
import 'cart_item_tile.dart';

class CartItemsList extends StatelessWidget {
  const CartItemsList({super.key});

  // Convert ServerCartItem to CartItem for display
  CartItem _convertToCartItem(ServerCartItem serverItem) {
    debugPrint(
        '🛒 CartItemsList: Converting ServerCartItem ${serverItem.id} with quantity ${serverItem.quantity}');

    // Build customization details for display
    final List<String> customizationSummary = [];

    // Check if item has any customizations
    final hasCustomizations = serverItem.allergyNames.isNotEmpty ||
        serverItem.dishAddons.isNotEmpty ||
        serverItem.dishExtras.isNotEmpty ||
        serverItem.dishSides.isNotEmpty ||
        serverItem.dishBeverages.isNotEmpty ||
        serverItem.dishDesserts.isNotEmpty ||
        (serverItem.notes?.isNotEmpty ?? false);

    // Add allergies if present
    if (serverItem.allergyNames.isNotEmpty) {
      customizationSummary
          .add('Allergies: ${serverItem.allergyNames.join(", ")}');
    }

    // Add addons if present
    if (serverItem.dishAddons.isNotEmpty) {
      final addonDetails = serverItem.dishAddons
          .map((addon) => '${addon.name ?? 'Addon'} (${addon.quantity}x)')
          .toList();
      customizationSummary.add('Addons: ${addonDetails.join(", ")}');
    }

    // Add extras if present
    if (serverItem.dishExtras.isNotEmpty) {
      final extraDetails = serverItem.dishExtras
          .map((extra) => '${extra.name ?? 'Extra'} (${extra.quantity}x)')
          .toList();
      customizationSummary.add('Extras: ${extraDetails.join(", ")}');
    }

    // Add sides if present
    if (serverItem.dishSides.isNotEmpty) {
      final sideDetails = serverItem.dishSides
          .map((side) => '${side.name ?? 'Side'} (${side.quantity}x)')
          .toList();
      customizationSummary.add('Sides: ${sideDetails.join(", ")}');
    }

    // Add beverages if present
    if (serverItem.dishBeverages.isNotEmpty) {
      final beverageDetails = serverItem.dishBeverages
          .map((beverage) =>
              '${beverage.name ?? 'Beverage'} (${beverage.quantity}x)')
          .toList();
      customizationSummary.add('Beverages: ${beverageDetails.join(", ")}');
    }

    // Add desserts if present
    if (serverItem.dishDesserts.isNotEmpty) {
      final dessertDetails = serverItem.dishDesserts
          .map((dessert) =>
              '${dessert.name ?? 'Dessert'} (${dessert.quantity}x)')
          .toList();
      customizationSummary.add('Desserts: ${dessertDetails.join(", ")}');
    }

    // Add notes if present
    if (serverItem.notes?.isNotEmpty ?? false) {
      customizationSummary.add('Special notes');
    }

    final cartItem = CartItem(
      id: serverItem.id, // Use cartItemId for unique identification
      name: serverItem.name, // Use actual name from API
      price: serverItem.price, // Use actual price from API
      quantity: serverItem.quantity,
      customization: hasCustomizations
          ? {
              'notes': serverItem.notes,
              'type':
                  'customized', // Mark as customized if it has any customizations
              'baseItemId': serverItem.dishId,
              'allergyIds': serverItem.allergyIds,
              'allergies':
                  serverItem.allergyNames, // Use allergy names for display
              'dishAddons':
                  serverItem.dishAddons.map((e) => e.toJson()).toList(),
              'dishExtras':
                  serverItem.dishExtras.map((e) => e.toJson()).toList(),
              'dishSides': serverItem.dishSides.map((e) => e.toJson()).toList(),
              'dishBeverages':
                  serverItem.dishBeverages.map((e) => e.toJson()).toList(),
              'dishDesserts':
                  serverItem.dishDesserts.map((e) => e.toJson()).toList(),
              'customizationSummary': customizationSummary,
            }
          : null, // Set to null if no customizations
    );
    debugPrint(
        '🛒 CartItemsList: Created CartItem ${cartItem.id} with quantity ${cartItem.quantity} and ${customizationSummary.length} customizations');
    return cartItem;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, cartState) {
        debugPrint(
            '🛒 CartItemsList: BlocBuilder rebuilding with state: ${cartState.runtimeType}');
        final cartItems = cartState.currentCart?.items ?? [];
        final miscItems = cartState.currentCart?.miscItems ?? [];
        final alertItems = cartState.currentCart?.alerts ?? [];
        debugPrint(
            '🛒 CartItemsList: Found ${cartItems.length} cart items, ${miscItems.length} misc items, ${alertItems.length} alert items');

        final totalItems =
            cartItems.length + miscItems.length + alertItems.length;
        if (totalItems == 0) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.transparent,
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 8.sp,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Your cart is empty',
                    style: GoogleFonts.dmSans(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: totalItems,
          itemBuilder: (context, index) {
            // First show regular cart items
            if (index < cartItems.length) {
              return CartItemTile(item: _convertToCartItem(cartItems[index]));
            }

            // Then show misc items
            final miscStartIndex = cartItems.length;
            if (index < miscStartIndex + miscItems.length) {
              final miscIndex = index - miscStartIndex;
              return _buildMiscItemTile(miscItems[miscIndex], context);
            }

            // Finally show alert items
            final alertStartIndex = miscStartIndex + miscItems.length;
            if (index < alertStartIndex + alertItems.length) {
              final alertIndex = index - alertStartIndex;
              return _buildAlertItemTile(alertItems[alertIndex], context);
            }

            return const SizedBox.shrink();
          },
        );
      },
    );
  }

  Widget _buildMiscItemTile(CartMiscItem miscItem, BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.purple.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.purple,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.add_shopping_cart,
              size: 16,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  miscItem.name,
                  style: GoogleFonts.dmSans(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Miscellaneous Item',
                  style: GoogleFonts.dmSans(
                    fontSize: 12,
                    color: Colors.purple.shade300,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '\$${miscItem.price.toStringAsFixed(2)}',
            style: GoogleFonts.dmSans(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () {
              // Delete misc item
              final cartBloc = context.read<CartBloc>();
              final currentCart = cartBloc.state.currentCart;
              if (currentCart != null) {
                CartService.deleteMiscItem(
                        currentCart.cartId, miscItem.miscItemId)
                    .then((success) {
                  if (success) {
                    cartBloc.add(RefreshCart());
                  }
                });
              }
            },
            icon: Icon(
              Icons.delete_outline,
              size: 18,
              color: Colors.red.shade400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertItemTile(CartAlertItem alertItem, BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.amber,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.warning,
              size: 16,
              color: Colors.black,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  alertItem.note,
                  style: GoogleFonts.dmSans(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Alert/Warning',
                  style: GoogleFonts.dmSans(
                    fontSize: 12,
                    color: Colors.amber.shade300,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // Delete alert item
              final cartBloc = context.read<CartBloc>();
              final currentCart = cartBloc.state.currentCart;
              if (currentCart != null) {
                CartService.deleteAlertItem(
                        currentCart.cartId, alertItem.alertId)
                    .then((success) {
                  if (success) {
                    cartBloc.add(RefreshCart());
                  }
                });
              }
            },
            icon: Icon(
              Icons.delete_outline,
              size: 18,
              color: Colors.red.shade400,
            ),
          ),
        ],
      ),
    );
  }
}
