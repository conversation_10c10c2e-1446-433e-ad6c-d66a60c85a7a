import 'package:flutter/material.dart';
import 'package:rive/rive.dart';

class TiledBackground extends StatefulWidget {
  const TiledBackground({super.key});

  @override
  State<TiledBackground> createState() => _TiledBackgroundState();
}

class _TiledBackgroundState extends State<TiledBackground> {

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: RiveAnimation.asset(
        'assets/circuits_parallax_background.riv',
        fit: BoxFit.cover,
        speedMultiplier: 1,
      ),
    );
  }
}