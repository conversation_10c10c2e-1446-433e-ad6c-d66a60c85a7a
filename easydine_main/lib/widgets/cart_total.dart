import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';
import '../blocs/pos/pos_state.dart';
import '../blocs/cart/cart_bloc.dart';
import '../blocs/cart/cart_state.dart';

class CartTotal extends StatelessWidget {
  final String orderId;
  final String orderType;
  final String tableNumber;

  const CartTotal({
    super.key,
    required this.orderId,
    required this.orderType,
    required this.tableNumber,
  });

  Widget _buildPriorityDropdown(BuildContext context, POSState state) {
    return DropdownButtonFormField<int>(
      value: state.currentPriority ?? 1,
      decoration: InputDecoration(
        labelText: 'Order Priority',
        labelStyle: GoogleFonts.dmSans(
          color: Colors.white70,
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white30),
        ),
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white),
        ),
      ),
      dropdownColor: Colors.grey[850],
      style: GoogleFonts.dmSans(color: Colors.white),
      items: [
        DropdownMenuItem(
          value: 1,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.green, size: 8.sp),
              const SizedBox(width: 8),
              Text('Normal Priority',
                  style: GoogleFonts.dmSans(color: Colors.green)),
            ],
          ),
        ),
        DropdownMenuItem(
          value: 2,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.orange, size: 8.sp),
              const SizedBox(width: 8),
              Text('High Priority',
                  style: GoogleFonts.dmSans(color: Colors.orange)),
            ],
          ),
        ),
        DropdownMenuItem(
          value: 3,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.red, size: 8.sp),
              const SizedBox(width: 8),
              Text('Urgent Priority',
                  style: GoogleFonts.dmSans(color: Colors.red)),
            ],
          ),
        ),
      ],
      onChanged: (value) {
        if (value != null) {
          context.read<POSBloc>().add(UpdateOrderPriority(priority: value));
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(builder: (context, cartState) {
      return BlocBuilder<POSBloc, POSState>(
        builder: (context, posState) {
          final cartTotal = cartState
              .total; // Use the CartState getter that safely parses the string
          final cartItems = cartState.currentCart?.items ?? [];

          return Container(
            padding: EdgeInsets.all(1.h),
            child: Column(
              children: [
                // Priority Dropdown
                _buildPriorityDropdown(context, posState),
                SizedBox(height: 1.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Subtotal',
                      style: GoogleFonts.dmSans(color: Colors.white54),
                    ),
                    Text(
                      '\$${cartTotal.toStringAsFixed(2)}',
                      style: GoogleFonts.dmSans(
                          fontWeight: FontWeight.bold, color: Colors.orange),
                    ),
                  ],
                ),
                SizedBox(height: 1.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Tax (10%)',
                      style: GoogleFonts.dmSans(color: Colors.white54),
                    ),
                    Text(
                      '\$${(cartTotal * 0.1).toStringAsFixed(2)}',
                      style: GoogleFonts.dmSans(
                          fontWeight: FontWeight.bold, color: Colors.orange),
                    ),
                  ],
                ),
                Divider(height: 1.h),
                Row(
                  children: [
                    Text(
                      'Total',
                      style: GoogleFonts.dmSans(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Spacer(),
                    Text(
                      '\$${(cartTotal * 1.1).toStringAsFixed(2)}',
                      style: GoogleFonts.dmSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    Spacer(),
                    SizedBox(
                      height: 4.h,
                      child: ElevatedButton(
                        onPressed: cartItems.isEmpty || posState.isProcessing
                            ? null
                            : () {
                                context.read<POSBloc>().add(PlaceOrder(
                                      orderId: orderId,
                                      priority: posState.currentPriority ?? 1,
                                      orderType: orderType,
                                      tableNumber: tableNumber,
                                    ));
                              },
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          backgroundColor: const Color.fromRGBO(44, 191, 90, 1),
                          foregroundColor:
                              const Color.fromRGBO(255, 255, 255, 1),
                        ),
                        child: posState.isProcessing
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Text('Place Order'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      );
    });
  }
}
