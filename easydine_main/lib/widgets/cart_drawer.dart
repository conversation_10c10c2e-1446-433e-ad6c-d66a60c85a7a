import 'package:easydine_main/services/order_id_generator.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_state.dart';
import 'cart_header.dart';
import 'cart_items_list.dart';
import 'cart_total.dart';
import 'extra_options_row.dart';

class CartDrawer extends StatelessWidget {
  final String tableNumber;
  final String orderType;

  const CartDrawer({
    super.key,
    required this.tableNumber,
    required this.orderType,
  });

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        double drawerWidth = orientation == Orientation.portrait
            ? MediaQuery.of(context).size.width * 0.5
            : MediaQuery.of(context).size.width * 0.3;
        
        return BackdropFilter(
          filter: ui.ImageFilter.blur(sigmaX: 12, sigmaY: 12),
          child: Drawer(
            backgroundColor: Colors.transparent,
            width: drawerWidth,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white24,
                border: Border(
                  left: BorderSide(color: Colors.grey.shade800),
                ),
              ),
              child: BlocBuilder<POSBloc, POSState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      CartHeader(orderType: orderType ,tableNumber: tableNumber, orderId: OrderIdGenerator.generateOrderId(orderType: orderType, tableNumber: tableNumber, priority: 1)),
                      const Divider(color: Colors.grey),
                      const Expanded(child: CartItemsList()),
                      const Divider(color: Colors.grey),
                      extraOptionsRow(context),
                      const Divider(color: Colors.grey),
                      CartTotal(
                        orderId: OrderIdGenerator.generateOrderId(orderType: orderType, tableNumber: tableNumber, priority: 1),
                        orderType: orderType,
                        tableNumber: tableNumber,
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
