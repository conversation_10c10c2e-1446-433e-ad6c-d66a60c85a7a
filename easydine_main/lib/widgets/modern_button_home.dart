// Modern button design
import 'package:flutter/material.dart';
import 'package:easydine_main/utils/colors.dart';

Widget buildModernButton({
  required String text,
  required IconData icon,
  required VoidCallback onClick,
  bool enabled = true,
  bool isHighlighted = false,
}) {
  // Base colors
  final Color baseColor = isHighlighted ? errorColor : primaryColor;
  final Color cardColor = enabled
      ? (isHighlighted
      ? Color.fromRGBO(255, 152, 0, 0.10196078431372549)
      : Colors.white24)
      : Colors.grey.shade200;
  final Color iconColor = enabled
      ? (isHighlighted ? Colors.orange : Colors.white)
      : Colors.grey;
  final Color textColor = enabled
      ? (isHighlighted ? Colors.orange : Colors.white)
      : Colors.grey;

  return Opacity(
    opacity: enabled ? 1.0 : 0.6,
    child: Card(
      elevation: 10,
      color: cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: enabled ? Colors.black.withOpacity(0.3) : Colors.grey.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: enabled ? onClick : null,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                icon,
                size: 32,
                color: iconColor,
                semanticLabel: text,
              ),
              const SizedBox(height: 12),
              Text(
                text,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    ),
  );
}