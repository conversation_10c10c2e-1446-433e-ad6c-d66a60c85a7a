import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

Widget buildStatCard({
  required String title,
  required String value,
  required IconData icon,
  required Color color,
  required BuildContext context,
}) {
  final bool isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
  final double baseSize = isPortrait
      ? MediaQuery.of(context).size.width * 0.18
      : MediaQuery.of(context).size.width * 0.1;
  final double width = isPortrait
      ? MediaQuery.of(context).size.width * 0.18
      : MediaQuery.of(context).size.width * 0.15;

  return Container(
    height: MediaQuery.of(context).orientation == Orientation.portrait ? MediaQuery.of(context).size.height * 0.11 : MediaQuery.of(context).size.height * 0.08,
    width: width,
    margin: const EdgeInsets.only(right: 16),
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.08),
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: color.withOpacity(0.2),
        width: 1.5,
      ),
      boxShadow: [
        BoxShadow(
          color: color.withOpacity(0.1),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Stack(
        children: [
          // Gradient overlay
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color.withOpacity(0.15),
                    Colors.transparent,
                    color.withOpacity(0.05),
                  ],
                ),
              ),
            ),
          ),
          
          // Content
          Padding(
            padding: EdgeInsets.all(baseSize * 0.1),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(baseSize * 0.05),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: baseSize * 0.2,
                  ),
                ),
                Spacer(),
                Center(
                  child: Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: baseSize * 0.12,
                      fontWeight: FontWeight.w500,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ),
                Spacer(),
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontSize: baseSize * 0.15,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
