import 'package:flutter/material.dart';

Widget buildCleaningStatusIndicator(String cleaningStatus) {
  IconData icon;
  Color color;
  String tooltip;

  switch (cleaningStatus) {
    case 'Clean':
      icon = Icons.cleaning_services;
      color = Colors.green;
      tooltip = 'Table is clean';
      break;
    case 'Needs Cleaning':
      icon = Icons.warning_amber;
      color = Colors.orange;
      tooltip = 'Table needs cleaning';
      break;
    case 'Dirty':
      icon = Icons.cleaning_services_outlined;
      color = Colors.red;
      tooltip = 'Table is dirty';
      break;
    default:
      icon = Icons.question_mark;
      color = Colors.grey;
      tooltip = 'Unknown status';
  }

  return Tooltip(
    message: tooltip,
    child: Container(
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        icon,
        color: color,
        size: 16,
      ),
    ),
  );
}
