import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

enum OrderStatus {
  PENDING('PENDING', 'Pending', Colors.orange),
  IN_PREPARATION('IN_PREPARATION', 'In Preparation', Colors.blue),
  READY('READY', 'Ready', Colors.green),
  SERVED('SERVED', 'Served', Colors.purple),
  CANCELLED('CANCELLED', 'Cancelled', Colors.red),
  CHECKOUT('CHECKOUT', 'Checkout', Colors.teal),
  COMPLETED('COMPLETED', 'Completed', Colors.grey);

  const OrderStatus(this.value, this.displayName, this.color);
  final String value;
  final String displayName;
  final Color color;

  static OrderStatus fromString(String status) {
    return OrderStatus.values.firstWhere(
      (e) => e.value == status.toUpperCase(),
      orElse: () => OrderStatus.PENDING,
    );
  }
}

class OrderStatusDropdown extends StatelessWidget {
  final OrderStatus currentStatus;
  final Function(OrderStatus) onStatusChanged;
  final bool isCompact;

  const OrderStatusDropdown({
    super.key,
    required this.currentStatus,
    required this.onStatusChanged,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[800]?.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[600]!),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<OrderStatus>(
          value: currentStatus,
          isDense: isCompact,
          dropdownColor: Colors.grey[850],
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: Colors.white70,
            size: isCompact ? 16 : 20,
          ),
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: isCompact ? 12 : 14,
            fontWeight: FontWeight.w500,
          ),
          items: OrderStatus.values.map((status) {
            return DropdownMenuItem<OrderStatus>(
              value: status,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: status.color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: 8),
                  Text(
                    status.displayName,
                    style: GoogleFonts.dmSans(
                      color: status == currentStatus ? status.color : Colors.white,
                      fontSize: isCompact ? 12 : 14,
                      fontWeight: status == currentStatus 
                          ? FontWeight.bold 
                          : FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (OrderStatus? newStatus) {
            if (newStatus != null && newStatus != currentStatus) {
              _showConfirmationDialog(context, newStatus);
            }
          },
        ),
      ),
    );
  }

  void _showConfirmationDialog(BuildContext context, OrderStatus newStatus) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.swap_horiz, color: newStatus.color),
              SizedBox(width: 8),
              Text(
                'Change Status',
                style: GoogleFonts.dmSans(color: Colors.white),
              ),
            ],
          ),
          content: RichText(
            text: TextSpan(
              style: GoogleFonts.dmSans(color: Colors.white70),
              children: [
                TextSpan(text: 'Change order status from '),
                TextSpan(
                  text: currentStatus.displayName,
                  style: TextStyle(
                    color: currentStatus.color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(text: ' to '),
                TextSpan(
                  text: newStatus.displayName,
                  style: TextStyle(
                    color: newStatus.color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(text: '?'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.dmSans(color: Colors.white70),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: newStatus.color,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                Navigator.pop(context);
                onStatusChanged(newStatus);
              },
              child: Text(
                'Confirm',
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
