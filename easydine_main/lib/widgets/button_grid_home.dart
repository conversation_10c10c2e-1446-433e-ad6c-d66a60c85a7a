import 'package:flutter/material.dart';
import '../models/buttonGridItem.dart';
import 'modern_button_home.dart';
import 'modern_grid_button.dart';


Widget buildButtonGrid({required List<ButtonGridItem> items, required int count, required BuildContext context}) {
  return GridView.builder(
    shrinkWrap: true,
    physics: const NeverScrollableScrollPhysics(),
    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: count,
      childAspectRatio:MediaQuery.of(context).orientation == Orientation.portrait ? 1.2 : 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 8,
    ),
    itemCount: items.length,
    itemBuilder: (context, index) {
      final item = items[index];
      return ModernGridButton(
        text: item.text,
        icon: item.icon,
        onPressed: item.onClick,
        enabled: item.enabled,
        isHighlighted: item.isHighlighted,
        customColor: item.customColor,
      );
    },
  );
}

// buildModernButton(
// text: item.text,
// icon: item.icon,
// onClick: item.onClick,
// enabled: item.enabled,
// isHighlighted: item.isHighlighted,
// );