import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../blocs/checklist/checklist_bloc.dart';
import '../blocs/checklist/checklist_event.dart';
import 'dart:ui';

class SignaturePadDialog extends StatefulWidget {
  final String itemTitle;
  final Function(List<Offset?>) onSignatureSaved;

  const SignaturePadDialog({
    Key? key,
    required this.itemTitle,
    required this.onSignatureSaved,
  }) : super(key: key);

  @override
  State<SignaturePadDialog> createState() => _SignaturePadDialogState();
}

class _SignaturePadDialogState extends State<SignaturePadDialog> {
  List<Offset?> _points = [];
  bool _isDrawing = false;

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color(0xFF1E88E5),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.draw,
                    color: Colors.white,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Signature Required',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            // Signature area
            Container(
              height: isLandscape ? 200 : 300,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: GestureDetector(
                onPanStart: (details) {
                  setState(() {
                    _isDrawing = true;
                    final renderBox = context.findRenderObject() as RenderBox;
                    final localPosition =
                        renderBox.globalToLocal(details.globalPosition);

                    // Normalize the position
                    final normalizedPosition = Offset(
                      localPosition.dx / renderBox.size.width,
                      localPosition.dy / renderBox.size.height,
                    );

                    _points.add(normalizedPosition);
                  });
                },
                onPanUpdate: (details) {
                  if (!_isDrawing) return;

                  setState(() {
                    final renderBox = context.findRenderObject() as RenderBox;
                    final localPosition =
                        renderBox.globalToLocal(details.globalPosition);

                    // Normalize the position
                    final normalizedPosition = Offset(
                      localPosition.dx / renderBox.size.width,
                      localPosition.dy / renderBox.size.height,
                    );

                    _points.add(normalizedPosition);
                  });
                },
                onPanEnd: (details) {
                  setState(() {
                    _isDrawing = false;
                    // Add a separator point
                    _points.add(null);
                  });
                },
                child: CustomPaint(
                  painter: SignaturePainter(
                    points: _points,
                  ),
                  size: Size.infinite,
                ),
              ),
            ),
            // Buttons
            Padding(
              padding: EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _points = [];
                      });
                    },
                    child: Text(
                      'Clear',
                      style: GoogleFonts.poppins(
                        color: Colors.red[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          'Cancel',
                          style: GoogleFonts.poppins(
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: _points.isEmpty
                            ? null
                            : () {
                                // Save the signature
                                context.read<ChecklistBloc>().add(
                                      AddSignature(
                                        widget.itemTitle,
                                        _points,
                                      ),
                                    );
                                widget.onSignatureSaved(_points);
                                Navigator.of(context).pop();
                              },
                        child: Text(
                          'Save',
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFF43A047),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SignaturePainter extends CustomPainter {
  final List<Offset?> points;

  SignaturePainter({required this.points});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Color(0xFF1E88E5)
      ..strokeCap = StrokeCap.round
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < points.length - 1; i++) {
      if (points[i] != null && points[i + 1] != null) {
        final startPoint = Offset(
          points[i]!.dx * size.width,
          points[i]!.dy * size.height,
        );
        final endPoint = Offset(
          points[i + 1]!.dx * size.width,
          points[i + 1]!.dy * size.height,
        );
        canvas.drawLine(startPoint, endPoint, paint);
      }
    }
  }

  @override
  bool shouldRepaint(SignaturePainter oldDelegate) => true;
}
