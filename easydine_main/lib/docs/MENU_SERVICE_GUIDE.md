# Menu Service Integration Guide

## Overview

This guide explains how to use the new MenuService to integrate with the EasyDine backend API endpoint for fetching menu data with availability information.

## API Endpoint

The service integrates with the following endpoint:
```
{{LAMBDA_HOST}}/{{food-menu-path}}/active-menu-withAvailability/?branchId=X&pos=true
```

## Setup

### 1. Configure API Settings

Update `lib/config/api_config.dart` with your actual API configuration:

```dart
class ApiConfig {
  static const String lambdaHost = 'https://your-api-host.com';
  static const String foodMenuPath = 'food-menu';
  static const String defaultBranchId = 'your-branch-id';
}
```

### 2. Install Dependencies

The service requires the `http` package. It's already added to `pubspec.yaml`:

```yaml
dependencies:
  http: ^1.1.0
```

Run `flutter pub get` to install the dependency.

## Usage

### Basic Menu Fetching

```dart
import 'package:your_app/services/menu_service.dart';
import 'package:your_app/config/api_config.dart';

// Fetch menu for customer app (filters unavailable items)
final menu = await MenuService.getActiveMenuWithAvailability(
  branchId: ApiConfig.defaultBranchId,
  forPOS: false, // false for customer app, true for POS
  lambdaHost: ApiConfig.lambdaHost,
  foodMenuPath: ApiConfig.foodMenuPath,
);

if (menu != null) {
  print('Menu loaded: ${menu.name}');
  print('Sections: ${menu.sections.length}');
}
```

### Working with Menu Data

#### Get Available Dishes
```dart
final availableDishes = MenuService.getAvailableDishes(menu);
```

#### Search Dishes
```dart
final searchResults = MenuService.searchDishes(menu, 'pasta');
```

#### Filter by Dietary Requirements
```dart
final vegetarianDishes = MenuService.getDishesByDietaryInfo(menu, 'vegetarian');
```

#### Get Dishes by Section
```dart
final starterDishes = MenuService.getDishesBySection(menu, sectionId);
```

#### Get Specific Dish
```dart
final dish = MenuService.getDishById(menu, dishId);
if (dish != null) {
  print('Dish: ${dish.name} - \$${dish.price}');
  print('Available: ${dish.isAvailable}');
}
```

## Data Models

### MenuWithAvailability
Main menu object containing:
- `foodMenuId`: Unique menu identifier
- `name`: Menu name
- `sections`: List of menu sections
- `availability`: Availability settings
- `availabilityInfo`: Current availability status

### MenuSection
Menu section containing:
- `menuSectionId`: Unique section identifier
- `name`: Section name
- `dishes`: List of dishes in the section
- `sectionIcon`: Optional section icon
- `availabilityInfo`: Section availability status

### MenuDish
Individual dish containing:
- `dishId`: Unique dish identifier
- `name`: Dish name
- `price`: Price as string
- `priceAsDouble`: Price as double (helper property)
- `description`: Dish description
- `dietaryInfo`: List of dietary information
- `dishIngredients`: List of ingredients with amounts
- `allergies`: List of allergens
- `availabilityInfo`: Dish availability status
- `isAvailable`: Boolean helper property

### DishIngredient
Ingredient information:
- `ingredient`: Ingredient details (name, description, unit)
- `amount`: Amount used in the dish

### Allergy
Allergy information:
- `name`: Allergy name
- `description`: Allergy description
- `colorPreference`: UI color preference

## Error Handling

The service includes basic error handling:

```dart
try {
  final menu = await MenuService.getActiveMenuWithAvailability(
    branchId: branchId,
    forPOS: false,
    lambdaHost: ApiConfig.lambdaHost,
    foodMenuPath: ApiConfig.foodMenuPath,
  );
  
  if (menu != null) {
    // Process menu data
  } else {
    // Handle null response (network error, API error, etc.)
  }
} catch (e) {
  // Handle exceptions
  print('Error fetching menu: $e');
}
```

## Backward Compatibility

For existing code using the old MenuItem model, use the conversion method:

```dart
final availableDishes = MenuService.getAvailableDishes(menu);
final legacyMenuItems = MenuService.convertDishesToMenuItems(availableDishes);

// Now you can use legacyMenuItems with existing code
```

## POS vs Customer App

The service supports two modes:

### Customer App Mode (`forPOS: false`)
- Filters out unavailable items
- Shows only items customers can order
- Optimized for customer experience

### POS Mode (`forPOS: true`)
- Shows all items (available and unavailable)
- Includes availability status for each item
- Allows staff to see full menu state

## Performance Considerations

1. **Caching**: Consider implementing caching for menu data to reduce API calls
2. **Error Retry**: Implement retry logic for failed requests
3. **Loading States**: Show loading indicators while fetching data
4. **Offline Support**: Consider storing last successful response for offline access

## Example Integration in a Widget

```dart
class MenuWidget extends StatefulWidget {
  @override
  _MenuWidgetState createState() => _MenuWidgetState();
}

class _MenuWidgetState extends State<MenuWidget> {
  MenuWithAvailability? menu;
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    loadMenu();
  }

  Future<void> loadMenu() async {
    try {
      final fetchedMenu = await MenuService.getActiveMenuWithAvailability(
        branchId: ApiConfig.defaultBranchId,
        forPOS: false,
        lambdaHost: ApiConfig.lambdaHost,
        foodMenuPath: ApiConfig.foodMenuPath,
      );
      
      setState(() {
        menu = fetchedMenu;
        isLoading = false;
        error = fetchedMenu == null ? 'Failed to load menu' : null;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return CircularProgressIndicator();
    }
    
    if (error != null) {
      return Text('Error: $error');
    }
    
    if (menu == null) {
      return Text('No menu available');
    }
    
    return ListView.builder(
      itemCount: menu!.sections.length,
      itemBuilder: (context, index) {
        final section = menu!.sections[index];
        return ExpansionTile(
          title: Text(section.name),
          children: section.dishes
              .where((dish) => dish.isAvailable)
              .map((dish) => ListTile(
                title: Text(dish.name),
                subtitle: Text('\$${dish.price}'),
                trailing: dish.isAvailable 
                    ? Icon(Icons.check, color: Colors.green)
                    : Icon(Icons.close, color: Colors.red),
              ))
              .toList(),
        );
      },
    );
  }
}
```

## Next Steps

1. Update `ApiConfig` with your actual API endpoints
2. Test the integration with your backend
3. Implement proper error handling and loading states
4. Add caching if needed
5. Update existing UI components to use the new data models
