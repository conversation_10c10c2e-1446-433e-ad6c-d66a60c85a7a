import 'package:flutter/material.dart';

Color getTableColor(String status) {
  switch (status.toLowerCase()) {
    case 'available':
      return Colors.green;
    case 'occupied':
      return Colors.orange;
    case 'reserved':
      return Colors.red;
    case 'dirty':
      return Colors.brown;
    default:
      return Colors.grey;
  }
}

Color getCleaningStatusColor(String status) {
  switch (status) {
    case 'Clean':
      return Color(0xFF2CBF5A); // primary green
    case 'Needs Cleaning':
      return Color(0xFFFFA000); // warning amber
    case 'Cleaning In Progress':
      return Color(0xFF2196F3); // accent blue
    default:
      return Colors.grey;
  }
}