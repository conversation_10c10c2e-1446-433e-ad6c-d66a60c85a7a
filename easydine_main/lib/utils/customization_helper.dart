import 'package:flutter/material.dart';
import '../blocs/customization/customization_modal.dart';
import '../models/menuItem.dart';

/// Helper class for opening customization modals
class CustomizationHelper {
  /// Open customization modal for a new item (adds to cart)
  static void openForNewItem(BuildContext context, MenuItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ItemCustomizationBottomSheet(
        item: item,
      ),
    );
  }

  /// Open customization modal for an existing cart item (updates existing item)
  static void openForExistingCartItem(
    BuildContext context, 
    MenuItem item, 
    String cartItemId
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ItemCustomizationBottomSheet(
        item: item,
        cartItemId: cartItemId, // This tells the modal to update existing item
      ),
    );
  }
}
