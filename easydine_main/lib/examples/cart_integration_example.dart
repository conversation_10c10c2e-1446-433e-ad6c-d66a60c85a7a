// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
//
// import '../blocs/pos/pos_bloc.dart';
// import '../blocs/pos/pos_event.dart';
// import '../blocs/pos/pos_state.dart';
// import '../blocs/cart/cart_bloc.dart';
// import '../blocs/cart/cart_event.dart';
// import '../blocs/cart/cart_state.dart';
// import '../widgets/enhanced_menu_item_card.dart';
// import '../widgets/menu_item_card.dart';
// import '../models/menuItem.dart';
//
// /// Example screen showing how to integrate cart service with POS bloc
// class CartIntegrationExample extends StatefulWidget {
//   const CartIntegrationExample({super.key});
//
//   @override
//   State<CartIntegrationExample> createState() => _CartIntegrationExampleState();
// }
//
// class _CartIntegrationExampleState extends State<CartIntegrationExample> {
//   @override
//   void initState() {
//     super.initState();
//
//     // Initialize cart synchronization when screen loads
//     _initializeCartSync();
//   }
//
//   void _initializeCartSync() {
//     // Load server cart and sync with local POS cart
//     context.read<CartBloc>().add(const LoadCart());
//     context.read<POSBloc>().add(const SyncCartWithServer());
//   }
//
//   void _syncWithServer() {
//     // Manual sync with server cart
//     context.read<POSBloc>().add(const SyncCartWithServer(forceRefresh: true));
//   }
//
//   void _clearCart() {
//     // Clear both local and server cart
//     context.read<POSBloc>().add(ClearServerCart());
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Cart Integration Example'),
//         actions: [
//           IconButton(
//             onPressed: _syncWithServer,
//             icon: const Icon(Icons.sync),
//             tooltip: 'Sync with Server',
//           ),
//           IconButton(
//             onPressed: _clearCart,
//             icon: const Icon(Icons.clear_all),
//             tooltip: 'Clear Cart',
//           ),
//         ],
//       ),
//       body: Column(
//         children: [
//           // Cart status indicator
//           _buildCartStatusIndicator(),
//
//           // Menu items grid
//           Expanded(
//             child: _buildMenuGrid(),
//           ),
//
//           // Cart summary
//           _buildCartSummary(),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildCartStatusIndicator() {
//     return BlocBuilder<CartBloc, CartState>(
//       builder: (context, cartState) {
//         return BlocBuilder<POSBloc, POSState>(
//           builder: (context, posState) {
//             return Container(
//               padding: const EdgeInsets.all(16),
//               color: Colors.blue.withOpacity(0.1),
//               child: Row(
//                 children: [
//                   Icon(
//                     cartState.isLoading ? Icons.sync : Icons.shopping_cart,
//                     color: cartState.hasError ? Colors.red : Colors.blue,
//                   ),
//                   const SizedBox(width: 8),
//                   Expanded(
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           'Local Cart: ${posState.cartItems.length} items',
//                           style: const TextStyle(fontWeight: FontWeight.bold),
//                         ),
//                         if (cartState.currentCart != null)
//                           Text(
//                             'Server Cart: ${cartState.currentCart!.items.length} items',
//                             style: const TextStyle(fontSize: 12),
//                           ),
//                         if (cartState.hasError)
//                           Text(
//                             'Error: ${cartState.error}',
//                             style: const TextStyle(color: Colors.red, fontSize: 12),
//                           ),
//                       ],
//                     ),
//                   ),
//                   if (cartState.isLoading)
//                     const SizedBox(
//                       width: 16,
//                       height: 16,
//                       child: CircularProgressIndicator(strokeWidth: 2),
//                     ),
//                 ],
//               ),
//             );
//           },
//         );
//       },
//     );
//   }
//
//   Widget _buildMenuGrid() {
//     // Sample menu items for demonstration
//     final sampleItems = [
//       MenuItem(
//         id: '1',
//         name: 'Burger',
//         description: 'Delicious beef burger',
//         price: 12.99,
//         category: 'Main',
//         isAvailable: true,
//       ),
//       MenuItem(
//         id: '2',
//         name: 'Pizza',
//         description: 'Margherita pizza',
//         price: 15.99,
//         category: 'Main',
//         isAvailable: true,
//       ),
//       MenuItem(
//         id: '3',
//         name: 'Salad',
//         description: 'Fresh garden salad',
//         price: 8.99,
//         category: 'Appetizer',
//         isAvailable: true,
//       ),
//     ];
//
//     return GridView.builder(
//       padding: const EdgeInsets.all(16),
//       gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
//         crossAxisCount: 2,
//         crossAxisSpacing: 16,
//         mainAxisSpacing: 16,
//         childAspectRatio: 0.8,
//       ),
//       itemCount: sampleItems.length,
//       itemBuilder: (context, index) {
//         final item = sampleItems[index];
//
//         // Choose between enhanced or regular menu item card
//         return _buildMenuItemCard(item, index);
//       },
//     );
//   }
//
//   Widget _buildMenuItemCard(MenuItem item, int index) {
//     // Alternate between different integration approaches for demonstration
//     switch (index % 3) {
//       case 0:
//         // Enhanced menu item card with server cart
//         return EnhancedMenuItemCard(
//           item: item,
//           useServerCart: true,
//           onCustomize: () {
//             // Handle customization
//           },
//         );
//
//       case 1:
//         // Regular menu item card with server sync
//         return MenuItemCard(item: item);
//
//       default:
//         // Custom implementation example
//         return _buildCustomMenuItemCard(item);
//     }
//   }
//
//   Widget _buildCustomMenuItemCard(MenuItem item) {
//     return BlocBuilder<POSBloc, POSState>(
//       builder: (context, state) {
//         final isInCart = state.cartItems.any((cartItem) => cartItem.id == item.id);
//
//         return Card(
//           child: InkWell(
//             onTap: () {
//               // Example: Add to server cart directly
//               context.read<POSBloc>().add(AddToServerCart(
//                 dishId: item.id,
//                 quantity: 1,
//                 type: "customized",
//                 notes: "Added from custom card",
//               ));
//             },
//             child: Padding(
//               padding: const EdgeInsets.all(12),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     item.name,
//                     style: const TextStyle(
//                       fontWeight: FontWeight.bold,
//                       fontSize: 16,
//                     ),
//                   ),
//                   const SizedBox(height: 4),
//                   Text(
//                     item.description,
//                     style: const TextStyle(fontSize: 12),
//                     maxLines: 2,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                   const Spacer(),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Text(
//                         '\$${item.price.toStringAsFixed(2)}',
//                         style: const TextStyle(
//                           fontWeight: FontWeight.bold,
//                           color: Colors.green,
//                         ),
//                       ),
//                       Icon(
//                         isInCart ? Icons.check_circle : Icons.add_circle_outline,
//                         color: isInCart ? Colors.green : Colors.grey,
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
//
//   Widget _buildCartSummary() {
//     return BlocBuilder<POSBloc, POSState>(
//       builder: (context, state) {
//         if (state.cartItems.isEmpty) {
//           return const SizedBox.shrink();
//         }
//
//         return Container(
//           padding: const EdgeInsets.all(16),
//           decoration: BoxDecoration(
//             color: Colors.white,
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.1),
//                 blurRadius: 4,
//                 offset: const Offset(0, -2),
//               ),
//             ],
//           ),
//           child: Row(
//             children: [
//               Expanded(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       '${state.cartItems.length} items in cart',
//                       style: const TextStyle(fontWeight: FontWeight.bold),
//                     ),
//                     Text(
//                       'Total: \$${state.total.toStringAsFixed(2)}',
//                       style: const TextStyle(
//                         fontSize: 18,
//                         fontWeight: FontWeight.bold,
//                         color: Colors.green,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               ElevatedButton(
//                 onPressed: () {
//                   // Handle checkout - this would typically confirm the cart
//                   _showCheckoutDialog();
//                 },
//                 child: const Text('Checkout'),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }
//
//   void _showCheckoutDialog() {
//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('Checkout'),
//         content: const Text('This would confirm the cart and place the order.'),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const Text('Cancel'),
//           ),
//           ElevatedButton(
//             onPressed: () {
//               // Example: Confirm cart with server
//               // You would typically collect table/waiter info here
//               Navigator.of(context).pop();
//
//               // For demonstration, just clear the cart
//               _clearCart();
//             },
//             child: const Text('Confirm Order'),
//           ),
//         ],
//       ),
//     );
//   }
// }
