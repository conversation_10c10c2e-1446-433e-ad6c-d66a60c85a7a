import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/env_config.dart';
import '../models/floor_model.dart';
import '../models/table_reservation_model.dart';
import '../utils/http_client.dart';
import 'table_reservation_service.dart';

class TableService {
  static final String _baseUrl = EnvConfig.apiBaseUrl;

  /// Fetch all floors and tables with availability information
  /// GET {{LAMBDA_HOST}}/tenant-settings/table-reservation/floors/view_all
  static Future<List<FloorModel>?> getAllFloorsAndTables() async {
    try {
      final url = '$_baseUrl/tenant-settings/table-reservation/floors/view_all';
      debugPrint('🏢 TableService: Fetching floors and tables from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🏢 TableService: Response status: ${response.statusCode}');
      debugPrint('🏢 TableService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> floorsData =
              responseData['data'] as List<dynamic>;

          final List<FloorModel> floors = floorsData
              .map((floorJson) =>
                  FloorModel.fromJson(floorJson as Map<String, dynamic>))
              .toList();

          debugPrint(
              '🏢 TableService: Successfully parsed ${floors.length} floors');
          return floors;
        } else {
          debugPrint('🏢 TableService: API returned success=false or no data');
          return null;
        }
      } else {
        debugPrint(
            '🏢 TableService: HTTP error ${response.statusCode}: ${response.body}');
        return null;
      }
    } catch (e, stackTrace) {
      debugPrint('🏢 TableService: Error fetching floors and tables: $e');
      debugPrint('🏢 TableService: Stack trace: $stackTrace');
      return null;
    }
  }

  /// Update table attributes (status, cleaning, bookedSeats)
  /// PATCH {{LAMBDA_HOST}}/dineIn/table-attrs/:tableId
  static Future<bool> updateTableAttributes({
    required String tableId,
    String? status,
    String? cleaning,
    int? bookedSeats,
  }) async {
    try {
      final url = '$_baseUrl/dineIn/table-attrs/$tableId';
      debugPrint('🔧 TableService: Updating table $tableId at $url');

      final Map<String, dynamic> requestBody = {};

      if (status != null) {
        requestBody['status'] = status;
      }
      if (cleaning != null) {
        requestBody['cleaning'] = cleaning;
      }
      if (bookedSeats != null) {
        requestBody['bookedSeats'] = bookedSeats;
      }

      debugPrint('🔧 TableService: Request body: ${json.encode(requestBody)}');

      final response = await HttpClientService.patch(
        url,
        body: json.encode(requestBody),
      );

      debugPrint('🔧 TableService: Response status: ${response.statusCode}');
      debugPrint('🔧 TableService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true) {
          debugPrint('🔧 TableService: Successfully updated table $tableId');
          return true;
        } else {
          debugPrint(
              '🔧 TableService: API returned success=false for table $tableId');
          return false;
        }
      } else {
        debugPrint(
            '🔧 TableService: HTTP error ${response.statusCode}: ${response.body}');
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('🔧 TableService: Error updating table $tableId: $e');
      debugPrint('🔧 TableService: Stack trace: $stackTrace');
      return false;
    }
  }

  /// Update table status specifically
  static Future<bool> updateTableStatus({
    required String tableId,
    required String status,
  }) async {
    return updateTableAttributes(
      tableId: tableId,
      status: status,
    );
  }

  /// Update table cleaning status specifically
  static Future<bool> updateTableCleaningStatus({
    required String tableId,
    required String cleaning,
  }) async {
    return updateTableAttributes(
      tableId: tableId,
      cleaning: cleaning,
    );
  }

  /// Update table booked seats specifically
  static Future<bool> updateTableBookedSeats({
    required String tableId,
    required int bookedSeats,
  }) async {
    return updateTableAttributes(
      tableId: tableId,
      bookedSeats: bookedSeats,
    );
  }

  /// Reset table (set to available, clean, and 0 booked seats)
  static Future<bool> resetTable({
    required String tableId,
  }) async {
    return updateTableAttributes(
      tableId: tableId,
      status: 'available',
      cleaning: 'clean',
      bookedSeats: 0,
    );
  }

  /// Mark table as occupied with specified seats
  static Future<bool> occupyTable({
    required String tableId,
    required int bookedSeats,
  }) async {
    return updateTableAttributes(
      tableId: tableId,
      status: 'occupied',
      bookedSeats: bookedSeats,
    );
  }

  /// Mark table as reserved with specified seats
  static Future<bool> reserveTable({
    required String tableId,
    required int bookedSeats,
  }) async {
    return updateTableAttributes(
      tableId: tableId,
      status: 'reserved',
      bookedSeats: bookedSeats,
    );
  }

  /// Mark table as needing cleaning
  static Future<bool> markTableForCleaning({
    required String tableId,
  }) async {
    return updateTableAttributes(
      tableId: tableId,
      cleaning: 'needscleaning',
    );
  }

  /// Mark table as dirty
  static Future<bool> markTableAsDirty({
    required String tableId,
  }) async {
    return updateTableAttributes(
      tableId: tableId,
      cleaning: 'dirty',
    );
  }

  /// Mark table as clean
  static Future<bool> markTableAsClean({
    required String tableId,
  }) async {
    return updateTableAttributes(
      tableId: tableId,
      cleaning: 'clean',
    );
  }

  /// Create a table reservation using the reservation service
  static Future<bool> createTableReservation({
    required String tableId,
    required String customerName,
    required String phoneNumber,
    required int numberOfGuests,
    required DateTime reservationTime,
    String? specialNotes,
  }) async {
    return await TableReservationService.createReservation(
      tableId: tableId,
      customerName: customerName,
      phoneNumber: phoneNumber,
      numberOfGuests: numberOfGuests,
      reservationTime: reservationTime,
      specialNotes: specialNotes,
    );
  }

  /// Get reservations for a specific table
  static Future<List<TableReservationModel>?> getTableReservations({
    required String tableId,
  }) async {
    final reservationsData = await TableReservationService.getTableReservations(
      tableId: tableId,
    );

    if (reservationsData == null) return null;

    return reservationsData
        .map((data) => TableReservationModel.fromJson(data))
        .toList();
  }

  /// Get today's reservations
  static Future<List<TableReservationModel>?> getTodaysReservations() async {
    final reservationsData =
        await TableReservationService.getTodaysReservations();

    if (reservationsData == null) return null;

    return reservationsData
        .map((data) => TableReservationModel.fromJson(data))
        .toList();
  }

  /// Cancel a reservation
  static Future<bool> cancelReservation({
    required String reservationId,
  }) async {
    return await TableReservationService.cancelReservation(
      reservationId: reservationId,
    );
  }

  /// Check-in a reservation
  static Future<bool> checkInReservation({
    required String reservationId,
  }) async {
    return await TableReservationService.checkInReservation(
      reservationId: reservationId,
    );
  }
}
