import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../router/router_constants.dart';

class NavigationService {
  static const MethodChannel _channel =
      MethodChannel('com.zynktech.easydine_main/navigation');

  /// Handle back button press - navigate to home instead of closing app
  static Future<bool> handleBackButton(BuildContext context) async {
    final router = GoRouter.of(context);
    final currentLocation = router.routerDelegate.currentConfiguration.fullPath;

    // If we're already on home page, minimize the app instead of closing
    if (currentLocation == RouterConstants.home) {
      await minimizeApp();
      return false; // Don't close the app
    }

    // For other pages, navigate to home
    router.go(RouterConstants.home);
    return false; // Don't close the app
  }

  /// Minimize the app instead of closing it
  static Future<void> minimizeApp() async {
    try {
      await _channel.invokeMethod('minimizeApp');
    } on PlatformException catch (e) {
      debugPrint("Failed to minimize app: '${e.message}'.");
    }
  }
}
