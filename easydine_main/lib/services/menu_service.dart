import 'dart:convert';
import 'package:easydine_main/config/env_config.dart';
import 'package:easydine_main/utils/http_client.dart';
import 'package:http/http.dart' as http;
import '../models/menuItem.dart';
import '../models/menu_response_models.dart';

class MenuService {
  static final List<MenuItem> _allMenuItems = [];

  // Cache for API data
  static MenuWithAvailability? _cachedMenuData;
  static Map<String, List<MenuItem>> _cachedGroupedItems = {};

  // Legacy methods for backward compatibility
  static List<MenuItem> getMenuItems(String category) {
    // Try to get from cached API data first
    if (_cachedGroupedItems.isNotEmpty) {
      return _cachedGroupedItems[category] ?? [];
    }
    return _allMenuItems.where((item) => item.category == category).toList();
  }

  static List<MenuItem> getAllMenuItems() {
    // Try to get from cached API data first
    if (_cachedGroupedItems.isNotEmpty) {
      return _cachedGroupedItems.values.expand((items) => items).toList();
    }
    return List.from(_allMenuItems);
  }

  static List<String> getAllCategories() {
    // Try to get from cached API data first
    if (_cachedGroupedItems.isNotEmpty) {
      return _cachedGroupedItems.keys.toList();
    }
    return _allMenuItems.map((item) => item.category).toSet().toList();
  }

  // Method to update cache with API data
  static void updateCache(
      MenuWithAvailability menuData, Map<String, List<MenuItem>> groupedItems) {
    _cachedMenuData = menuData;
    _cachedGroupedItems = groupedItems;
  }

  // Method to clear cache
  static void clearCache() {
    _cachedMenuData = null;
    _cachedGroupedItems = {};
  }

  static MenuItem? getItemById(String id) {
    try {
      return _allMenuItems.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }

  // New methods for handling the complex API response

  /// Fetches the active menu with availability information from the API
  ///
  /// [branchId] - The UUID of the branch to fetch menu for
  /// [forPOS] - Set to true for POS system (shows all items), false for customer app (filters unavailable)
  /// [lambdaHost] - The base URL for the API (e.g., "https://api.example.com")
  /// [foodMenuPath] - The path segment for food menu endpoints (e.g., "food-menu")
  static Future<MenuWithAvailability?> getActiveMenuWithAvailability({
    required String? branchId,
    bool forPOS = false,
  }) async {
    try {
      final url =
          '${EnvConfig.apiBaseUrl}/food-menu/active-menu-withAvailability/?branchId=$branchId&pos=$forPOS';

      final response = await HttpClientService.get(
        url
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final apiResponse = MenuApiResponse.fromJson(jsonData);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw Exception('API returned error: ${apiResponse.message}');
        }
      } else {
        throw Exception('Failed to load menu: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching menu: $e');
      return null;
    }
  }

  /// Converts MenuDish objects to legacy MenuItem objects for backward compatibility
  static List<MenuItem> convertDishesToMenuItems(List<MenuDish> dishes) {
    return dishes
        .map((dish) => MenuItem(
              id: dish.dishId,
              name: dish.name,
              price: dish.priceAsDouble,
              image: dish.pictureUrl ?? '',
              category:
                  'Unknown', // You might want to derive this from section name
              description: dish.description ?? '',
              ingredients:
                  dish.dishIngredients.map((di) => di.ingredient.name).toList(),
              prepTime: dish.preparationTime ?? 0,
              rating: 4.5, // Default rating since it's not in the API response
              isSpicy: dish.tags.contains('spicy'),
              dietaryInfo: dish.dietaryInfo,
            ))
        .toList();
  }

  /// Gets all available dishes from all sections
  static List<MenuDish> getAllDishesFromMenu(MenuWithAvailability menu) {
    List<MenuDish> allDishes = [];
    for (var section in menu.sections) {
      allDishes.addAll(section.dishes);
    }
    return allDishes;
  }

  /// Gets only available dishes (filters out unavailable ones)
  static List<MenuDish> getAvailableDishes(MenuWithAvailability menu) {
    return getAllDishesFromMenu(menu)
        .where((dish) => dish.isAvailable)
        .toList();
  }

  /// Gets dishes by section
  static List<MenuDish> getDishesBySection(
      MenuWithAvailability menu, String sectionId) {
    final section = menu.sections.firstWhere(
      (s) => s.menuSectionId == sectionId,
      orElse: () => throw Exception('Section not found'),
    );
    return section.dishes;
  }

  /// Gets dishes by dietary info (e.g., 'vegetarian', 'gluten-free')
  static List<MenuDish> getDishesByDietaryInfo(
      MenuWithAvailability menu, String dietaryInfo) {
    return getAllDishesFromMenu(menu)
        .where((dish) => dish.dietaryInfo.contains(dietaryInfo))
        .toList();
  }

  /// Searches dishes by name or description
  static List<MenuDish> searchDishes(MenuWithAvailability menu, String query) {
    final lowercaseQuery = query.toLowerCase();
    return getAllDishesFromMenu(menu)
        .where((dish) =>
            dish.name.toLowerCase().contains(lowercaseQuery) ||
            (dish.description?.toLowerCase().contains(lowercaseQuery) ?? false))
        .toList();
  }

  /// Gets a specific dish by ID
  static MenuDish? getDishById(MenuWithAvailability menu, String dishId) {
    try {
      return getAllDishesFromMenu(menu)
          .firstWhere((dish) => dish.dishId == dishId);
    } catch (e) {
      return null;
    }
  }
}
