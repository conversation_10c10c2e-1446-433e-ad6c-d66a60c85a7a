import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/env_config.dart';
import '../models/customization_models.dart';
import '../utils/http_client.dart';

/// Service class for handling all customization-related API calls
/// Manages dish addons, extras, allergies, and order types
class CustomizationService {
  static final String _baseUrl = EnvConfig.apiBaseUrl;

  /// Fetch all dish addons
  /// GET {{LAMBDA_HOST}}/dish-addon/view_all
  static Future<List<DishAddon>?> getDishAddons() async {
    try {
      final url = '$_baseUrl/dish-addon/view_all';
      debugPrint('🔧 CustomizationService: Fetching dish addons from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🔧 CustomizationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        debugPrint('🔧 CustomizationService: Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> addonsJson = responseData['data'];
          final addons = addonsJson.map((json) => DishAddon.fromJson(json)).toList();
          debugPrint('🔧 CustomizationService: Successfully parsed ${addons.length} dish addons');
          return addons;
        } else {
          debugPrint('🔧 CustomizationService: API returned success=false or null data');
          throw Exception('Failed to fetch dish addons: ${responseData['message']}');
        }
      } else {
        debugPrint('🔧 CustomizationService: HTTP error ${response.statusCode}');
        throw Exception('Failed to fetch dish addons: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🔧 CustomizationService: Error fetching dish addons: $e');
      return null;
    }
  }

  /// Fetch all dish extras
  /// GET {{LAMBDA_HOST}}/dish-extra/view_all
  static Future<List<DishExtra>?> getDishExtras() async {
    try {
      final url = '$_baseUrl/dish-extra/view_all';
      debugPrint('🔧 CustomizationService: Fetching dish extras from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🔧 CustomizationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        debugPrint('🔧 CustomizationService: Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> extrasJson = responseData['data'];
          final extras = extrasJson.map((json) => DishExtra.fromJson(json)).toList();
          debugPrint('🔧 CustomizationService: Successfully parsed ${extras.length} dish extras');
          return extras;
        } else {
          debugPrint('🔧 CustomizationService: API returned success=false or null data');
          throw Exception('Failed to fetch dish extras: ${responseData['message']}');
        }
      } else {
        debugPrint('🔧 CustomizationService: HTTP error ${response.statusCode}');
        throw Exception('Failed to fetch dish extras: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🔧 CustomizationService: Error fetching dish extras: $e');
      return null;
    }
  }

  /// Fetch all allergies
  /// GET {{LAMBDA_HOST}}/allergy/view_all
  static Future<List<Allergy>?> getAllergies() async {
    try {
      final url = '$_baseUrl/allergy/view_all';
      debugPrint('🔧 CustomizationService: Fetching allergies from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🔧 CustomizationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        debugPrint('🔧 CustomizationService: Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> allergiesJson = responseData['data'];
          final allergies = allergiesJson.map((json) => Allergy.fromJson(json)).toList();
          debugPrint('🔧 CustomizationService: Successfully parsed ${allergies.length} allergies');
          return allergies;
        } else {
          debugPrint('🔧 CustomizationService: API returned success=false or null data');
          throw Exception('Failed to fetch allergies: ${responseData['message']}');
        }
      } else {
        debugPrint('🔧 CustomizationService: HTTP error ${response.statusCode}');
        throw Exception('Failed to fetch allergies: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🔧 CustomizationService: Error fetching allergies: $e');
      return null;
    }
  }

  /// Fetch all order types
  /// GET {{LAMBDA_HOST}}/tenant-settings/general/order-types/view_all
  static Future<List<OrderType>?> getOrderTypes() async {
    try {
      final url = '$_baseUrl/tenant-settings/general/order-types/view_all';
      debugPrint('🔧 CustomizationService: Fetching order types from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🔧 CustomizationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        debugPrint('🔧 CustomizationService: Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> orderTypesJson = responseData['data'];
          final orderTypes = orderTypesJson.map((json) => OrderType.fromJson(json)).toList();
          debugPrint('🔧 CustomizationService: Successfully parsed ${orderTypes.length} order types');
          return orderTypes;
        } else {
          debugPrint('🔧 CustomizationService: API returned success=false or null data');
          throw Exception('Failed to fetch order types: ${responseData['message']}');
        }
      } else {
        debugPrint('🔧 CustomizationService: HTTP error ${response.statusCode}');
        throw Exception('Failed to fetch order types: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🔧 CustomizationService: Error fetching order types: $e');
      return null;
    }
  }

  /// Fetch all customization data at once
  /// Returns a map with all customization options
  static Future<Map<String, dynamic>?> getAllCustomizationData() async {
    try {
      debugPrint('🔧 CustomizationService: Fetching all customization data');

      final results = await Future.wait([
        getDishAddons(),
        getDishExtras(),
        getAllergies(),
        getOrderTypes(),
      ]);

      final dishAddons = results[0] as List<DishAddon>?;
      final dishExtras = results[1] as List<DishExtra>?;
      final allergies = results[2] as List<Allergy>?;
      final orderTypes = results[3] as List<OrderType>?;

      if (dishAddons != null && dishExtras != null && allergies != null && orderTypes != null) {
        debugPrint('🔧 CustomizationService: Successfully fetched all customization data');
        return {
          'dishAddons': dishAddons,
          'dishExtras': dishExtras,
          'allergies': allergies,
          'orderTypes': orderTypes,
        };
      } else {
        debugPrint('🔧 CustomizationService: Some customization data failed to load');
        return null;
      }
    } catch (e) {
      debugPrint('🔧 CustomizationService: Error fetching all customization data: $e');
      return null;
    }
  }

  /// Helper method to get enabled order types only
  static List<OrderType> getEnabledOrderTypes(List<OrderType> orderTypes) {
    return orderTypes.where((orderType) => orderType.enabled).toList();
  }

  /// Helper method to search dish addons by name
  static List<DishAddon> searchDishAddons(List<DishAddon> addons, String query) {
    if (query.isEmpty) return addons;
    final lowerQuery = query.toLowerCase();
    return addons.where((addon) => addon.name.toLowerCase().contains(lowerQuery)).toList();
  }

  /// Helper method to search dish extras by name
  static List<DishExtra> searchDishExtras(List<DishExtra> extras, String query) {
    if (query.isEmpty) return extras;
    final lowerQuery = query.toLowerCase();
    return extras.where((extra) => extra.name.toLowerCase().contains(lowerQuery)).toList();
  }

  /// Helper method to search allergies by name
  static List<Allergy> searchAllergies(List<Allergy> allergies, String query) {
    if (query.isEmpty) return allergies;
    final lowerQuery = query.toLowerCase();
    return allergies.where((allergy) => allergy.name.toLowerCase().contains(lowerQuery)).toList();
  }

  /// Helper method to get allergies by color preference
  static List<Allergy> getAllergiesByColor(List<Allergy> allergies, String colorPreference) {
    return allergies.where((allergy) => allergy.colorPreference == colorPreference).toList();
  }

  /// Helper method to calculate total price for selected addons
  static double calculateAddonsTotal(List<DishAddon> selectedAddons) {
    return selectedAddons.fold(0.0, (total, addon) => total + addon.priceAsDouble);
  }

  /// Helper method to calculate total price for selected extras
  static double calculateExtrasTotal(List<DishExtra> selectedExtras) {
    return selectedExtras.fold(0.0, (total, extra) => total + extra.priceAsDouble);
  }
}
