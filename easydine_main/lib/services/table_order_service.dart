import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/env_config.dart';
import '../utils/http_client.dart';

class TableOrderService {
  static final String _baseUrl = EnvConfig.apiBaseUrl;

  /// Take order for a table
  /// POST {{LAMBDA_HOST}}/dineIn/take-order/:tableId
  static Future<Map<String, dynamic>?> takeOrder({
    required String tableId,
    required int numOfGuests,
    String? confirmationCode,
  }) async {
    try {
      final url = '$_baseUrl/dineIn/take-order/$tableId';
      debugPrint('🍽️ TableOrderService: Taking order for table $tableId at $url');

      final requestBody = <String, dynamic>{
        'numOfGuests': numOfGuests,
      };

      if (confirmationCode != null && confirmationCode.isNotEmpty) {
        requestBody['confirmationCode'] = confirmationCode;
      }

      debugPrint('🍽️ TableOrderService: Request body: ${jsonEncode(requestBody)}');

      final response = await HttpClientService.post(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint(
          '🍽️ TableOrderService: Take order response status: ${response.statusCode}');
      debugPrint('🍽️ TableOrderService: Take order response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return data['data'];
        } else {
          debugPrint('❌ TableOrderService: API returned success=false');
          return null;
        }
      } else {
        debugPrint(
            '❌ TableOrderService: Failed to take order: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ TableOrderService: Error taking order: $e');
      return null;
    }
  }

  /// Cancel order for a table
  /// DELETE {{LAMBDA_HOST}}/dineIn/cancel-order/:tableId
  static Future<bool> cancelOrder({
    required String tableId,
    required int numOfGuests,
    String? confirmationCode,
  }) async {
    try {
      final url = '$_baseUrl/dineIn/cancel-order/$tableId';
      debugPrint('🍽️ TableOrderService: Canceling order for table $tableId at $url');

      final requestBody = <String, dynamic>{
        'numOfGuests': numOfGuests,
      };

      if (confirmationCode != null && confirmationCode.isNotEmpty) {
        requestBody['confirmationCode'] = confirmationCode;
      }

      debugPrint('🍽️ TableOrderService: Request body: ${jsonEncode(requestBody)}');

      final response = await HttpClientService.delete(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint(
          '🍽️ TableOrderService: Cancel order response status: ${response.statusCode}');
      debugPrint('🍽️ TableOrderService: Cancel order response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        return true;
      } else {
        debugPrint(
            '❌ TableOrderService: Failed to cancel order: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ TableOrderService: Error canceling order: $e');
      return false;
    }
  }
}
