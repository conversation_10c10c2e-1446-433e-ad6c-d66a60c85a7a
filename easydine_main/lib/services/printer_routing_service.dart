// lib/services/printer_routing_service.dart

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/printer_settings.dart';
import '../models/cartItem.dart';
import '../models/order_model.dart';

/// Service to route print jobs to appropriate printers based on food categories
class PrinterRoutingService {
  static const String _printerSettingsKey = 'printer_settings';
  static PrinterSettings? _cachedSettings;

  /// Load printer settings from storage
  static Future<PrinterSettings> loadPrinterSettings() async {
    if (_cachedSettings != null) {
      return _cachedSettings!;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_printerSettingsKey);

      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        _cachedSettings = PrinterSettings.fromJson(settingsMap);
      } else {
        _cachedSettings = _createDefaultSettings();
      }

      return _cachedSettings!;
    } catch (e) {
      print('Error loading printer settings: $e');
      _cachedSettings = _createDefaultSettings();
      return _cachedSettings!;
    }
  }

  /// Clear cached settings (call when settings are updated)
  static void clearCache() {
    _cachedSettings = null;
  }

  /// Get printer assignments for order items grouped by printer location
  static Future<Map<String, List<OrderItem>>> getOrderItemsByPrinter(
    List<OrderItem> orderItems,
  ) async {
    final settings = await loadPrinterSettings();
    final Map<String, List<OrderItem>> printerGroups = {};

    for (final item in orderItems) {
      final category = _extractCategoryFromItem(item);
      final printerLocation = settings.getPrinterLocationForCategory(category);

      if (printerLocation != null) {
        if (!printerGroups.containsKey(printerLocation)) {
          printerGroups[printerLocation] = [];
        }
        printerGroups[printerLocation]!.add(item);
      } else {
        // Default to counter if no specific printer assigned
        const defaultLocation = 'Counter';
        if (!printerGroups.containsKey(defaultLocation)) {
          printerGroups[defaultLocation] = [];
        }
        printerGroups[defaultLocation]!.add(item);
      }
    }

    return printerGroups;
  }

  /// Get printer assignments for cart items grouped by printer location
  static Future<Map<String, List<CartItem>>> getCartItemsByPrinter(
    List<CartItem> cartItems,
  ) async {
    final settings = await loadPrinterSettings();
    final Map<String, List<CartItem>> printerGroups = {};

    for (final item in cartItems) {
      final category = _extractCategoryFromCartItem(item);
      final printerLocation = settings.getPrinterLocationForCategory(category);

      if (printerLocation != null) {
        if (!printerGroups.containsKey(printerLocation)) {
          printerGroups[printerLocation] = [];
        }
        printerGroups[printerLocation]!.add(item);
      } else {
        // Default to counter if no specific printer assigned
        const defaultLocation = 'Counter';
        if (!printerGroups.containsKey(defaultLocation)) {
          printerGroups[defaultLocation] = [];
        }
        printerGroups[defaultLocation]!.add(item);
      }
    }

    return printerGroups;
  }

  /// Get the printer device for a specific category
  static Future<PrinterDevice?> getPrinterForCategory(String category) async {
    final settings = await loadPrinterSettings();
    return settings.getPrinterForCategory(category);
  }

  /// Get all printer locations that have items to print
  static Future<List<String>> getActivePrinterLocations(
      List<OrderItem> orderItems) async {
    final printerGroups = await getOrderItemsByPrinter(orderItems);
    return printerGroups.keys.toList();
  }

  /// Check if a printer is available for a specific location
  static Future<bool> isPrinterAvailableForLocation(String location) async {
    final settings = await loadPrinterSettings();

    // Find printer assigned to this location
    final printerId = settings.printerAssignments.entries
        .firstWhere((entry) => entry.value == location,
            orElse: () => const MapEntry('', ''))
        .key;

    if (printerId.isEmpty) return false;

    final printer = settings.availablePrinters.firstWhere(
      (p) => p.id == printerId,
      orElse: () => PrinterDevice.notAssigned(),
    );

    return printer.isConnected;
  }

  /// Get printer device for a specific location
  static Future<PrinterDevice?> getPrinterForLocation(String location) async {
    final settings = await loadPrinterSettings();

    // Find printer assigned to this location
    final printerId = settings.printerAssignments.entries
        .firstWhere((entry) => entry.value == location,
            orElse: () => const MapEntry('', ''))
        .key;

    if (printerId.isEmpty) return null;

    final printer = settings.availablePrinters.firstWhere(
      (p) => p.id == printerId,
      orElse: () => PrinterDevice.notAssigned(),
    );

    return printer.id != 'not_assigned' ? printer : null;
  }

  /// Create a kitchen ticket for a specific printer location
  static Future<KitchenTicket> createKitchenTicket({
    required String location,
    required List<OrderItem> items,
    required String orderNumber,
    required String tableNumber,
    required DateTime orderTime,
    String? specialInstructions,
  }) async {
    final printer = await getPrinterForLocation(location);

    return KitchenTicket(
      location: location,
      printer: printer,
      items: items,
      orderNumber: orderNumber,
      tableNumber: tableNumber,
      orderTime: orderTime,
      specialInstructions: specialInstructions,
    );
  }

  /// Extract category from order item
  static String _extractCategoryFromItem(OrderItem item) {
    // Try to extract category from item name or use a default mapping
    final itemName = item.name.toLowerCase();

    if (itemName.contains('drink') ||
        itemName.contains('beverage') ||
        itemName.contains('juice') ||
        itemName.contains('soda') ||
        itemName.contains('coffee') ||
        itemName.contains('tea')) {
      return 'Beverages';
    } else if (itemName.contains('dessert') ||
        itemName.contains('cake') ||
        itemName.contains('ice cream')) {
      return 'Desserts';
    } else if (itemName.contains('salad')) {
      return 'Salads';
    } else if (itemName.contains('soup')) {
      return 'Soups';
    } else {
      return 'Food'; // Default category
    }
  }

  /// Extract category from cart item
  static String _extractCategoryFromCartItem(CartItem item) {
    // Similar logic for cart items
    final itemName = item.name.toLowerCase();

    if (itemName.contains('drink') ||
        itemName.contains('beverage') ||
        itemName.contains('juice') ||
        itemName.contains('soda') ||
        itemName.contains('coffee') ||
        itemName.contains('tea')) {
      return 'Beverages';
    } else if (itemName.contains('dessert') ||
        itemName.contains('cake') ||
        itemName.contains('ice cream')) {
      return 'Desserts';
    } else if (itemName.contains('salad')) {
      return 'Salads';
    } else if (itemName.contains('soup')) {
      return 'Soups';
    } else {
      return 'Food'; // Default category
    }
  }

  /// Create default printer settings
  static PrinterSettings _createDefaultSettings() {
    final defaultPrinters = [
      PrinterDevice(
        id: 'printer_1',
        name: 'Kitchen 1',
        type: 'thermal',
        isConnected: false,
      ),
      PrinterDevice(
        id: 'printer_2',
        name: 'Kitchen 2',
        type: 'thermal',
        isConnected: false,
      ),
      PrinterDevice(
        id: 'printer_3',
        name: 'Counter',
        type: 'receipt',
        isConnected: false,
      ),
    ];

    final defaultPrinterAssignments = {
      'printer_1': PrinterLocation.kitchen1,
      'printer_2': PrinterLocation.kitchen2,
      'printer_3': PrinterLocation.counter,
    };

    final defaultCategoryAssignments = {
      PrinterLocation.kitchen1: ['Food', 'Mains'],
      PrinterLocation.kitchen2: ['Desserts'],
      PrinterLocation.counter: ['Beverages', 'Drinks'],
    };

    return PrinterSettings(
      printerAssignments: defaultPrinterAssignments,
      categoryAssignments: defaultCategoryAssignments,
      availablePrinters: defaultPrinters,
    );
  }
}

/// Kitchen ticket model for printer routing
class KitchenTicket {
  final String location;
  final PrinterDevice? printer;
  final List<OrderItem> items;
  final String orderNumber;
  final String tableNumber;
  final DateTime orderTime;
  final String? specialInstructions;

  KitchenTicket({
    required this.location,
    this.printer,
    required this.items,
    required this.orderNumber,
    required this.tableNumber,
    required this.orderTime,
    this.specialInstructions,
  });

  /// Check if this ticket can be printed (has a connected printer)
  bool get canPrint => printer != null && printer!.isConnected;

  /// Get formatted ticket content for printing
  String get formattedContent {
    final buffer = StringBuffer();

    buffer.writeln('=== $location ===');
    buffer.writeln('Order: $orderNumber');
    buffer.writeln('Table: $tableNumber');
    buffer.writeln('Time: ${orderTime.toString().substring(11, 16)}');
    buffer.writeln('');

    for (final item in items) {
      buffer.writeln('${item.quantity}x ${item.name}');
      if (item.notes != null && item.notes!.isNotEmpty) {
        buffer.writeln('  Notes: ${item.notes}');
      }
    }

    if (specialInstructions != null && specialInstructions!.isNotEmpty) {
      buffer.writeln('');
      buffer.writeln('Special: $specialInstructions');
    }

    buffer.writeln('');
    buffer.writeln('========================');

    return buffer.toString();
  }
}
