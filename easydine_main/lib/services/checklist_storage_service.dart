import 'dart:convert';
import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/checklistItem.dart';

class ChecklistStorageService {
  static const String _checklistItemsKey = 'checklist_items';
  static const String _signaturesKey = 'checklist_signatures';
  static const String _lastCheckDateKey = 'last_check_date';
  static const String _checklistCompletedKey = 'checklist_completed';
  static const String _checklistSkippedKey = 'checklist_skipped';

  // Save checklist items
  static Future<void> saveChecklistItems(List<ChecklistItem> items) async {
    final prefs = await SharedPreferences.getInstance();
    final itemsJson = items.map((item) => item.toJson()).toList();
    await prefs.setString(_checklistItemsKey, jsonEncode(itemsJson));
  }

  // Load checklist items
  static Future<List<ChecklistItem>> loadChecklistItems() async {
    final prefs = await SharedPreferences.getInstance();
    final itemsString = prefs.getString(_checklistItemsKey);
    
    if (itemsString == null) return [];
    
    try {
      final itemsJson = jsonDecode(itemsString) as List;
      return itemsJson.map((item) => ChecklistItem.fromJson(item)).toList();
    } catch (e) {
      print('Error loading checklist items: $e');
      return [];
    }
  }

  // Save signatures
  static Future<void> saveSignatures(Map<String, List<Offset?>> signatures) async {
    final prefs = await SharedPreferences.getInstance();
    
    // Convert signatures to a format that can be stored in SharedPreferences
    final signaturesMap = signatures.map((key, value) {
      final pointsString = value.map((p) => 
        p != null ? '${p.dx},${p.dy}' : 'null'
      ).join('|');
      return MapEntry(key, pointsString);
    });
    
    await prefs.setString(_signaturesKey, jsonEncode(signaturesMap));
  }

  // Load signatures
  static Future<Map<String, List<Offset?>>> loadSignatures() async {
    final prefs = await SharedPreferences.getInstance();
    final signaturesString = prefs.getString(_signaturesKey);
    
    if (signaturesString == null) return {};
    
    try {
      final signaturesJson = jsonDecode(signaturesString) as Map<String, dynamic>;
      
      return signaturesJson.map((key, value) {
        final pointsString = value as String;
        if (pointsString.isEmpty) return MapEntry(key, <Offset?>[]);
        
        final points = pointsString.split('|').map((point) {
          if (point == 'null') return null;
          
          final coords = point.split(',');
          return Offset(double.parse(coords[0]), double.parse(coords[1]));
        }).toList();
        
        return MapEntry(key, points);
      });
    } catch (e) {
      print('Error loading signatures: $e');
      return {};
    }
  }

  // Save subitem status
  static Future<void> saveSubitemStatus(String itemTitle, String subitem, bool isCompleted) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('subitem_${itemTitle}_$subitem', isCompleted ? 'true' : 'false');
  }

  // Load subitem status
  static Future<bool> loadSubitemStatus(String itemTitle, String subitem) async {
    final prefs = await SharedPreferences.getInstance();
    final status = prefs.getString('subitem_${itemTitle}_$subitem');
    
    if (status == null) {
      // Try to load legacy boolean value
      try {
        return prefs.getBool('subitem_${itemTitle}_$subitem') ?? false;
      } catch (e) {
        return false;
      }
    }
    
    return status == 'true';
  }

  // Save last check date
  static Future<void> saveLastCheckDate(String date) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastCheckDateKey, date);
  }

  // Load last check date
  static Future<String?> loadLastCheckDate() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_lastCheckDateKey);
  }

  // Save checklist completion status
  static Future<void> saveChecklistStatus({required bool completed, required bool skipped}) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_checklistCompletedKey, completed);
    await prefs.setBool(_checklistSkippedKey, skipped);
  }

  // Load checklist completion status
  static Future<Map<String, bool>> loadChecklistStatus() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'completed': prefs.getBool(_checklistCompletedKey) ?? false,
      'skipped': prefs.getBool(_checklistSkippedKey) ?? false,
    };
  }

  // Clear old data (for a new day)
  static Future<void> clearOldData() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Get all keys
    final keys = prefs.getKeys();
    
    // Filter keys related to subitems and signatures
    final subitemKeys = keys.where((key) => key.startsWith('subitem_')).toList();
    
    // Remove subitem statuses
    for (var key in subitemKeys) {
      await prefs.remove(key);
    }
    
    // Clear signatures
    await prefs.remove(_signaturesKey);
    
    // Reset completion status
    await prefs.setBool(_checklistCompletedKey, false);
    await prefs.setBool(_checklistSkippedKey, false);
  }
}