import 'dart:async';
import 'dart:math';
import 'dart:ui';
import '../models/checklistItem.dart';

class ChecklistCloudService {
  // Mock cloud storage
  static final Map<String, dynamic> _cloudData = {};
  
  // Simulate network delay
  static Future<void> _simulateNetworkDelay() async {
    final random = Random();
    final delay = 500 + random.nextInt(1500); // 0.5-2 seconds delay
    await Future.delayed(Duration(milliseconds: delay));
    
    // Simulate network failure occasionally (10% chance)
    if (random.nextDouble() < 0.1) {
      throw Exception('Network error: Failed to connect to server');
    }
  }
  
  // Upload checklist data to cloud
  static Future<void> uploadChecklistData({
    required String date,
    required List<ChecklistItem> items,
    required Map<String, List<Offset?>> signatures,
  }) async {
    await _simulateNetworkDelay();
    
    // Convert items to a serializable format
    final itemsData = items.map((item) => item.toJson()).toList();
    
    // Convert signatures to a serializable format
    final signaturesData = signatures.map((key, value) {
      final pointsList = value.map((p) => 
        p != null ? {'dx': p.dx, 'dy': p.dy} : null
      ).toList();
      return MapEntry(key, pointsList);
    });
    
    // Store in our mock cloud
    _cloudData[date] = {
      'items': itemsData,
      'signatures': signaturesData,
      'syncedAt': DateTime.now().toIso8601String(),
    };
    
    print('Data synced to cloud for date: $date');
  }
  
  // Download checklist data from cloud
  static Future<Map<String, dynamic>> downloadChecklistData(String date) async {
    await _simulateNetworkDelay();
    
    // Check if data exists for the given date
    if (!_cloudData.containsKey(date)) {
      return {
        'items': [],
        'signatures': {},
      };
    }
    
    final cloudData = _cloudData[date];
    
    // Convert items from JSON
    final itemsJson = cloudData['items'] as List;
    final items = itemsJson.map((item) => ChecklistItem.fromJson(item)).toList();
    
    // Convert signatures from JSON
    final signaturesJson = cloudData['signatures'] as Map<String, dynamic>;
    final signatures = signaturesJson.map((key, value) {
      final pointsList = (value as List).map((p) => 
        p != null ? Offset(p['dx'], p['dy']) : null
      ).toList();
      return MapEntry(key, pointsList);
    });
    
    return {
      'items': items,
      'signatures': signatures,
    };
  }
  
  // Check if data is synced for a date
  static Future<bool> isDataSynced(String date) async {
    await _simulateNetworkDelay();
    return _cloudData.containsKey(date);
  }
}