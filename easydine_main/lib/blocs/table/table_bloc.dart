import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import '../../models/table_model.dart';
import '../../models/floor_model.dart';
import '../../models/table_api_model.dart';
import '../../models/order_model.dart';
import '../../services/table_service.dart';
import '../running_orders/running_orders_bloc.dart';
import 'table_event.dart';
import 'table_state.dart';

class TableBloc extends Bloc<TableEvent, TableState> {
  final RunningOrdersBloc runningOrdersBloc;

  TableBloc({required this.runningOrdersBloc}) : super(const TableState()) {
    on<LoadTables>(_onLoadTables);
    on<LoadFloorsAndTables>(_onLoadFloorsAndTables);
    on<UpdateTableStatus>(_onUpdateTableStatus);
    on<UpdateTableCleaningStatus>(_onUpdateTableCleaningStatus);
    on<UpdateTableBookedSeats>(_onUpdateTableBookedSeats);
    on<UpdateTableFilters>(_onUpdateTableFilters);
    on<UpdateCurrentFloor>(_onUpdateCurrentFloor);
    on<ViewTableBill>(_onViewTableBill);
    on<ReserveTable>(_onReserveTable);
    on<CancelReservation>(_onCancelReservation);
    on<MarkTableAsOccupied>(_onMarkTableAsOccupied);
    on<MarkTableAsAvailable>(_onMarkTableAsAvailable);
    on<ResetTable>(_onResetTable);
    on<UpdateTableLayout>(_onUpdateTableLayout);
  }

  Future<void> _onLoadTables(LoadTables event, Emitter<TableState> emit) async {
    // Delegate to the new LoadFloorsAndTables event for consistency
    add(LoadFloorsAndTables());
  }

  Future<void> _onLoadFloorsAndTables(
      LoadFloorsAndTables event, Emitter<TableState> emit) async {
    emit(state.copyWith(isLoading: true));
    try {
      debugPrint('🏢 TableBloc: Loading floors and tables from API');

      final floors = await TableService.getAllFloorsAndTables();

      if (floors != null && floors.isNotEmpty) {
        // Convert API tables to legacy TableModel for backward compatibility
        final List<TableModel> legacyTables = [];
        final List<String> floorIds = [];

        for (final floor in floors) {
          floorIds.add(floor.floorId);

          for (final apiTable in floor.tables) {
            final legacyTable = _convertApiTableToLegacy(apiTable, floor);
            legacyTables.add(legacyTable);
          }
        }

        emit(state.copyWith(
          floors: floors,
          tables: legacyTables,
          availableFloorIds: floorIds,
          currentFloorId: floorIds.isNotEmpty ? floorIds.first : null,
          isLoading: false,
          error: null,
        ));

        debugPrint(
            '🏢 TableBloc: Successfully loaded ${floors.length} floors and ${legacyTables.length} tables');
      } else {
        // API returned no data
        debugPrint('🏢 TableBloc: API returned no data');
        emit(state.copyWith(
          floors: [],
          tables: [],
          availableFloorIds: [],
          currentFloorId: null,
          isLoading: false,
          error: 'No floors and tables data available from API',
        ));
      }
    } catch (e, stackTrace) {
      debugPrint('🏢 TableBloc: Error loading floors and tables: $e');
      debugPrint('🏢 TableBloc: Stack trace: $stackTrace');

      // Show error without fallback
      emit(state.copyWith(
        floors: [],
        tables: [],
        availableFloorIds: [],
        currentFloorId: null,
        error: 'Failed to load tables: ${e.toString()}',
        isLoading: false,
      ));
    }
  }



  OrderModel? _getOrderForTable(String tableId) {
    try {
      // This is a mock implementation. Replace with actual implementation
      // that integrates with your RunningOrdersBloc
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> _onUpdateTableBookedSeats(
      UpdateTableBookedSeats event, Emitter<TableState> emit) async {
    try {
      debugPrint(
          '🔧 TableBloc: Updating booked seats for table ${event.tableId} to ${event.bookedSeats}');

      final success = await TableService.updateTableBookedSeats(
        tableId: event.tableId,
        bookedSeats: event.bookedSeats,
      );

      if (success) {
        // Reload tables to get updated data
        add(LoadFloorsAndTables());
        debugPrint(
            '🔧 TableBloc: Successfully updated booked seats for table ${event.tableId}');
      } else {
        emit(state.copyWith(error: 'Failed to update table booked seats'));
      }
    } catch (e) {
      debugPrint('🔧 TableBloc: Error updating table booked seats: $e');
      emit(state.copyWith(
          error: 'Failed to update table booked seats: ${e.toString()}'));
    }
  }

  Future<void> _onResetTable(ResetTable event, Emitter<TableState> emit) async {
    try {
      debugPrint('🔧 TableBloc: Resetting table ${event.tableId}');

      final success = await TableService.resetTable(tableId: event.tableId);

      if (success) {
        // Reload tables to get updated data
        add(LoadFloorsAndTables());
        debugPrint('🔧 TableBloc: Successfully reset table ${event.tableId}');
      } else {
        emit(state.copyWith(error: 'Failed to reset table'));
      }
    } catch (e) {
      debugPrint('🔧 TableBloc: Error resetting table: $e');
      emit(state.copyWith(error: 'Failed to reset table: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateTableStatus(
      UpdateTableStatus event, Emitter<TableState> emit) async {
    try {
      debugPrint(
          '🔧 TableBloc: Updating status for table ${event.tableId} to ${event.newStatus}');

      final success = await TableService.updateTableStatus(
        tableId: event.tableId,
        status: event.newStatus.toLowerCase(),
      );

      if (success) {
        // Reload tables to get updated data
        add(LoadFloorsAndTables());
        debugPrint(
            '🔧 TableBloc: Successfully updated status for table ${event.tableId}');
      } else {
        emit(state.copyWith(error: 'Failed to update table status'));
      }
    } catch (e) {
      debugPrint('🔧 TableBloc: Error updating table status: $e');
      emit(state.copyWith(
          error: 'Failed to update table status: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateTableCleaningStatus(
    UpdateTableCleaningStatus event,
    Emitter<TableState> emit,
  ) async {
    try {
      debugPrint(
          '🔧 TableBloc: Updating cleaning status for table ${event.tableId} to ${event.newStatus}');

      // Map display text to backend enum values
      String backendCleaningStatus;
      switch (event.newStatus) {
        case 'Clean':
          backendCleaningStatus = 'clean';
          break;
        case 'Needs Cleaning':
          backendCleaningStatus = 'needscleaning';
          break;
        case 'Dirty':
          backendCleaningStatus = 'dirty';
          break;
        default:
          backendCleaningStatus = event.newStatus.toLowerCase();
      }

      final success = await TableService.updateTableCleaningStatus(
        tableId: event.tableId,
        cleaning: backendCleaningStatus,
      );

      if (success) {
        // Reload tables to get updated data
        add(LoadFloorsAndTables());
        debugPrint(
            '🔧 TableBloc: Successfully updated cleaning status for table ${event.tableId}');
      } else {
        emit(state.copyWith(error: 'Failed to update table cleaning status'));
      }
    } catch (e) {
      debugPrint('🔧 TableBloc: Error updating table cleaning status: $e');
      emit(state.copyWith(
          error: 'Failed to update table cleaning status: ${e.toString()}'));
    }
  }

  Future<void> _onReserveTable(
      ReserveTable event, Emitter<TableState> emit) async {
    try {
      debugPrint(
          '🔧 TableBloc: Reserving table ${event.tableId} with ${event.bookedSeats} seats');

      final success = await TableService.reserveTable(
        tableId: event.tableId,
        bookedSeats: event.bookedSeats,
      );

      if (success) {
        // Reload tables to get updated data
        add(LoadFloorsAndTables());
        debugPrint(
            '🔧 TableBloc: Successfully reserved table ${event.tableId}');
      } else {
        emit(state.copyWith(error: 'Failed to reserve table'));
      }
    } catch (e) {
      debugPrint('🔧 TableBloc: Error reserving table: $e');
      emit(state.copyWith(error: 'Failed to reserve table: ${e.toString()}'));
    }
  }

  Future<void> _onCancelReservation(
      CancelReservation event, Emitter<TableState> emit) async {
    try {
      debugPrint(
          '🔧 TableBloc: Cancelling reservation for table ${event.tableId}');

      final success = await TableService.updateTableStatus(
        tableId: event.tableId,
        status: 'available',
      );

      if (success) {
        // Reload tables to get updated data
        add(LoadFloorsAndTables());
        debugPrint(
            '🔧 TableBloc: Successfully cancelled reservation for table ${event.tableId}');
      } else {
        emit(state.copyWith(error: 'Failed to cancel reservation'));
      }
    } catch (e) {
      debugPrint('🔧 TableBloc: Error cancelling reservation: $e');
      emit(state.copyWith(
          error: 'Failed to cancel reservation: ${e.toString()}'));
    }
  }

  Future<void> _onMarkTableAsOccupied(
      MarkTableAsOccupied event, Emitter<TableState> emit) async {
    try {
      debugPrint(
          '🔧 TableBloc: Marking table ${event.tableId} as occupied with ${event.bookedSeats} seats');

      final success = await TableService.occupyTable(
        tableId: event.tableId,
        bookedSeats: event.bookedSeats,
      );

      if (success) {
        // Reload tables to get updated data
        add(LoadFloorsAndTables());
        debugPrint(
            '🔧 TableBloc: Successfully marked table ${event.tableId} as occupied');
      } else {
        emit(state.copyWith(error: 'Failed to mark table as occupied'));
      }
    } catch (e) {
      debugPrint('🔧 TableBloc: Error marking table as occupied: $e');
      emit(state.copyWith(
          error: 'Failed to mark table as occupied: ${e.toString()}'));
    }
  }

  Future<void> _onMarkTableAsAvailable(
      MarkTableAsAvailable event, Emitter<TableState> emit) async {
    try {
      debugPrint('🔧 TableBloc: Marking table ${event.tableId} as available');

      final success = await TableService.resetTable(tableId: event.tableId);

      if (success) {
        // Reload tables to get updated data
        add(LoadFloorsAndTables());
        debugPrint(
            '🔧 TableBloc: Successfully marked table ${event.tableId} as available');
      } else {
        emit(state.copyWith(error: 'Failed to mark table as available'));
      }
    } catch (e) {
      debugPrint('🔧 TableBloc: Error marking table as available: $e');
      emit(state.copyWith(
          error: 'Failed to mark table as available: ${e.toString()}'));
    }
  }

  void _onUpdateTableLayout(UpdateTableLayout event, Emitter<TableState> emit) {
    try {
      final updatedTables = state.tables.map((table) {
        if (table.tableIdString == event.tableId) {
          return table.copyWith(
            position: event.position,
            size: event.size,
          );
        }
        return table;
      }).toList();

      emit(state.copyWith(
        tables: updatedTables,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(
          error: 'Failed to update table layout: ${e.toString()}'));
    }
  }

  void _onUpdateTableFilters(
      UpdateTableFilters event, Emitter<TableState> emit) {
    emit(state.copyWith(
      filterStatus: event.filterStatus,
      filterSeats: event.filterSeats,
    ));
  }

  void _onUpdateCurrentFloor(
      UpdateCurrentFloor event, Emitter<TableState> emit) {
    final floorIdString = event.floor.toString();
    if (state.availableFloorIds.contains(floorIdString)) {
      emit(state.copyWith(currentFloorId: floorIdString));
    }
  }

  void _onViewTableBill(ViewTableBill event, Emitter<TableState> emit) {
    try {
      final table = state.tables.firstWhere(
        (t) => t.tableNumber == event.tableId,
        orElse: () => throw Exception('Table not found'),
      );

      if (table.status != 'Occupied') {
        emit(state.copyWith(error: 'No active bill for this table'));
        return;
      }

      final order = _getOrderForTable(event.tableId);
      if (order == null) {
        emit(state.copyWith(error: 'No order found for this table'));
        return;
      }

      emit(state.copyWith(
        currentOrder: order,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to view table bill: ${e.toString()}'));
    }
  }

  List<String> _getAvailableFloorIds(List<TableModel> tables) {
    final floorIds =
        tables.map((table) => table.floor.toString()).toSet().toList();
    floorIds.sort();
    return floorIds.isEmpty ? ['1'] : floorIds;
  }

  TableModel _convertApiTableToLegacy(
      TableApiModel apiTable, FloorModel floor) {
    // Calculate cell counts based on seat count
    final cellCounts = _calculateCellCounts(apiTable.maxSeats);

    return TableModel(
      id: apiTable.tableId
          .hashCode,
      // Convert string ID to int for legacy compatibility
      apiTableId: apiTable.tableId, // Store the original API table ID
      name: apiTable.name,
      seats: apiTable.maxSeats,
      status: _mapApiStatusToLegacy(apiTable.status),
      cleaningStatus: _mapApiCleaningStatusToLegacy(apiTable.cleaning),
      crossAxisCellCount: cellCounts['crossAxis']!,
      mainAxisCellCount: cellCounts['mainAxis']!,
      location: apiTable.location,
      price: 0.0,
      minimumSpend: 0.0,
      reservationFee: 0.0,
      features: [],
      reservationTime: null,
      reservedBy: null,
      reservationDetails: null,
      lastCleaned: null,
      lastOccupied: null,
      averageOccupancyTime: 90,
      popularityScore: 4.0,
      section: floor.name,
      tableNumber: apiTable.name,
      floor: int.tryParse(floor.name.replaceAll(RegExp(r'[^0-9]'), '')) ?? 1,
      position: null,
      size: null,
      occupiedBy: apiTable.bookedSeats > 0
          ? {
              'seats': apiTable.bookedSeats,
              'status': apiTable.status,
            }
          : null,
    );
  }

  /// Calculate appropriate cell counts based on seat count
  /// Returns a map with 'crossAxis' and 'mainAxis' keys
  Map<String, int> _calculateCellCounts(int seatCount) {
    if (seatCount <= 2) {
      // Small tables (1-2 seats): 1x1 cell
      return {'crossAxis': 1, 'mainAxis': 1};
    } else if (seatCount <= 4) {
      // Medium tables (3-4 seats): 1x2 cells (rectangular)
      return {'crossAxis': 2, 'mainAxis': 1};
    } else if (seatCount <= 6) {
      // Large tables (5-6 seats): 2x2 cells (square)
      return {'crossAxis': 2, 'mainAxis': 1};
    } else if (seatCount <= 8) {
      // Extra large tables (7-8 seats): 2x3 cells (wide rectangle)
      return {'crossAxis': 3, 'mainAxis': 1};
    } else if (seatCount <= 12) {
      // Very large tables (9-12 seats): 3x3 cells (large square)
      return {'crossAxis': 3, 'mainAxis': 1};
    } else {
      // Massive tables (13+ seats): 3x4 cells (very wide)
      return {'crossAxis': 3, 'mainAxis': 1};
    }
  }

  String _mapApiStatusToLegacy(String apiStatus) {
    switch (apiStatus.toLowerCase()) {
      case 'available':
        return 'Available';
      case 'occupied':
        return 'Occupied';
      case 'reserved':
        return 'Reserved';
      default:
        return 'Available';
    }
  }

  String _mapApiCleaningStatusToLegacy(String apiCleaning) {
    switch (apiCleaning.toLowerCase()) {
      case 'clean':
        return 'Clean';
      case 'needscleaning':
        return 'Needs Cleaning';
      case 'dirty':
        return 'Dirty';
      default:
        return 'Clean';
    }
  }
}
