import 'dart:ui';
import 'package:equatable/equatable.dart';
import '../../models/checklistItem.dart';

abstract class ChecklistEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class LoadChecklist extends ChecklistEvent {
  @override
  List<Object?> get props => [];
}

class UpdateChecklistItem extends ChecklistEvent {
  final ChecklistItem item;
  final bool isCompleted;

  UpdateChecklistItem(this.item, this.isCompleted);

  @override
  List<Object?> get props => [item, isCompleted];
}

class UpdateSubitem extends ChecklistEvent {
  final String itemTitle;
  final String subitem;
  final bool isCompleted;

  UpdateSubitem(this.itemTitle, this.subitem, this.isCompleted);

  @override
  List<Object?> get props => [itemTitle, subitem, isCompleted];
}

class AddSignature extends ChecklistEvent {
  final String itemId;
  final List<Offset?> points;

  AddSignature(this.itemId, this.points);

  @override
  List<Object?> get props => [itemId, points];
}

class ClearSignature extends ChecklistEvent {
  final String itemId;

  ClearSignature(this.itemId);

  @override
  List<Object?> get props => [itemId];
}

class SyncToCloud extends ChecklistEvent {
  @override
  List<Object?> get props => [];
}

class ResetDailyChecklist extends ChecklistEvent {
  @override
  List<Object?> get props => [];
}

class SkipChecklist extends ChecklistEvent {
  @override
  List<Object?> get props => [];
}

class CompleteChecklist extends ChecklistEvent {
  @override
  List<Object?> get props => [];
}