import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/checklistItem.dart';
import '../../services/checklist_storage_service.dart';
import '../../services/checklist_cloud_service.dart';
import 'checklist_event.dart';
import 'checklist_state.dart';

class ChecklistBloc extends Bloc<ChecklistEvent, ChecklistState> {
  ChecklistBloc() : super(const ChecklistState()) {
    on<LoadChecklist>(_onLoadChecklist);
    on<UpdateChecklistItem>(_onUpdateChecklistItem);
    on<UpdateSubitem>(_onUpdateSubitem);
    on<AddSignature>(_onAddSignature);
    on<ClearSignature>(_onClearSignature);
    on<SyncToCloud>(_onSyncToCloud);
    on<ResetDailyChecklist>(_onResetDailyChecklist);
    on<SkipChecklist>(_onSkipChecklist);
    on<CompleteChecklist>(_onCompleteChecklist);
  }

  Future<void> _onLoadChecklist(
    LoadChecklist event,
    Emitter<ChecklistState> emit,
  ) async {
    emit(state.copyWith(status: ChecklistStatus.loading));

    try {
      // Get today's date
      final today = DateTime.now().toIso8601String().split('T')[0];

      // Check if we need to reset for a new day
      final lastCheckDate = await ChecklistStorageService.loadLastCheckDate();

      if (lastCheckDate != today) {
        // It's a new day, reset the checklist
        add(ResetDailyChecklist());
        return;
      }

      // Load items from local storage
      final items = await ChecklistStorageService.loadChecklistItems();

      // If no items are stored yet, use default items
      final checklistItems = items.isEmpty ? _getDefaultItems() : items;

      // Load signatures
      final signatures = await ChecklistStorageService.loadSignatures();

      // Calculate progress
      final progress = _calculateProgress(checklistItems);

      // Check if data is synced to cloud
      final isSynced = await ChecklistCloudService.isDataSynced(today);

      emit(state.copyWith(
        items: checklistItems,
        signatures: signatures,
        status: ChecklistStatus.loaded,
        date: today,
        progress: progress,
        isSyncedToCloud: isSynced,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ChecklistStatus.error,
        errorMessage: 'Failed to load checklist: $e',
      ));
    }
  }

  Future<void> _onUpdateChecklistItem(
    UpdateChecklistItem event,
    Emitter<ChecklistState> emit,
  ) async {
    try {
      // Update the item in the list
      final updatedItems = state.items.map((item) {
        if (item.title == event.item.title) {
          return item.copyWith(isCompleted: event.isCompleted);
        }
        return item;
      }).toList();

      // Save to local storage
      await ChecklistStorageService.saveChecklistItems(updatedItems);

      // Calculate new progress
      final progress = _calculateProgress(updatedItems);

      emit(state.copyWith(
        items: updatedItems,
        progress: progress,
        isSyncedToCloud: false, // Mark as not synced
      ));

      // Trigger cloud sync
      add(SyncToCloud());
    } catch (e) {
      emit(state.copyWith(
        status: ChecklistStatus.error,
        errorMessage: 'Failed to update checklist item: $e',
      ));
    }
  }

  Future<void> _onUpdateSubitem(
    UpdateSubitem event,
    Emitter<ChecklistState> emit,
  ) async {
    try {
      // Save subitem status
      await ChecklistStorageService.saveSubitemStatus(
          event.itemTitle, event.subitem, event.isCompleted);

      // Check if all subitems are completed for this item
      final item =
          state.items.firstWhere((item) => item.title == event.itemTitle);

      bool allSubitemsCompleted = true;
      if (item.subitems != null) {
        for (var subitem in item.subitems!) {
          final isCompleted = await ChecklistStorageService.loadSubitemStatus(
              event.itemTitle, subitem);
          if (!isCompleted) {
            allSubitemsCompleted = false;
            break;
          }
        }
      }

      // Update the item completion status if all subitems are completed
      // and the item has a signature
      final hasSignature = state.hasSignature(event.itemTitle);
      final shouldMarkCompleted = allSubitemsCompleted && hasSignature;

      add(UpdateChecklistItem(item, shouldMarkCompleted));
    } catch (e) {
      emit(state.copyWith(
        status: ChecklistStatus.error,
        errorMessage: 'Failed to update subitem: $e',
      ));
    }
  }

  Future<void> _onAddSignature(
    AddSignature event,
    Emitter<ChecklistState> emit,
  ) async {
    try {
      // Update signatures
      final updatedSignatures =
          Map<String, List<Offset?>>.from(state.signatures);
      updatedSignatures[event.itemId] = event.points;

      // Save to local storage
      await ChecklistStorageService.saveSignatures(updatedSignatures);

      emit(state.copyWith(
        signatures: updatedSignatures,
        isSyncedToCloud: false, // Mark as not synced
      ));

      // Check if all subitems are completed for this item
      final item = state.items.firstWhere((item) => item.title == event.itemId);

      bool allSubitemsCompleted = true;
      if (item.subitems != null) {
        for (var subitem in item.subitems!) {
          final isCompleted = await ChecklistStorageService.loadSubitemStatus(
              event.itemId, subitem);
          if (!isCompleted) {
            allSubitemsCompleted = false;
            break;
          }
        }
      }

      // Update the item completion status if all subitems are completed
      if (allSubitemsCompleted) {
        add(UpdateChecklistItem(item, true));
      }

      // Trigger cloud sync
      add(SyncToCloud());
    } catch (e) {
      emit(state.copyWith(
        status: ChecklistStatus.error,
        errorMessage: 'Failed to add signature: $e',
      ));
    }
  }

  Future<void> _onClearSignature(
    ClearSignature event,
    Emitter<ChecklistState> emit,
  ) async {
    try {
      // Update signatures
      final updatedSignatures =
          Map<String, List<Offset?>>.from(state.signatures);
      updatedSignatures[event.itemId] = [];

      // Save to local storage
      await ChecklistStorageService.saveSignatures(updatedSignatures);

      // Mark the item as incomplete
      final item = state.items.firstWhere((item) => item.title == event.itemId);
      add(UpdateChecklistItem(item, false));

      emit(state.copyWith(
        signatures: updatedSignatures,
        isSyncedToCloud: false, // Mark as not synced
      ));

      // Trigger cloud sync
      add(SyncToCloud());
    } catch (e) {
      emit(state.copyWith(
        status: ChecklistStatus.error,
        errorMessage: 'Failed to clear signature: $e',
      ));
    }
  }

  Future<void> _onSyncToCloud(
    SyncToCloud event,
    Emitter<ChecklistState> emit,
  ) async {
    // Only sync if there's data to sync and we're not already syncing
    if (state.status == ChecklistStatus.syncing || state.isSyncedToCloud) {
      return;
    }

    emit(state.copyWith(status: ChecklistStatus.syncing));

    try {
      final today = DateTime.now().toIso8601String().split('T')[0];

      // Upload data to cloud
      await ChecklistCloudService.uploadChecklistData(
        date: today,
        items: state.items,
        signatures: state.signatures,
      );

      emit(state.copyWith(
        status: ChecklistStatus.synced,
        isSyncedToCloud: true,
      ));

      // After a delay, return to loaded state
      await Future.delayed(Duration(seconds: 2));
      emit(state.copyWith(status: ChecklistStatus.loaded));
    } catch (e) {
      emit(state.copyWith(
        status: ChecklistStatus.error,
        errorMessage: 'Failed to sync to cloud: $e',
        isSyncedToCloud: false,
      ));

      // After a delay, return to loaded state
      await Future.delayed(Duration(seconds: 2));
      emit(state.copyWith(
        status: ChecklistStatus.loaded,
        errorMessage: null,
      ));
    }
  }

  Future<void> _onResetDailyChecklist(
    ResetDailyChecklist event,
    Emitter<ChecklistState> emit,
  ) async {
    emit(state.copyWith(status: ChecklistStatus.loading));

    try {
      // Clear old data
      await ChecklistStorageService.clearOldData();

      // Get today's date
      final today = DateTime.now().toIso8601String().split('T')[0];

      // Save the new date
      await ChecklistStorageService.saveLastCheckDate(today);

      // Reset checklist status
      await ChecklistStorageService.saveChecklistStatus(
        completed: false,
        skipped: false,
      );

      // Get default items
      final defaultItems = _getDefaultItems();

      // Save default items
      await ChecklistStorageService.saveChecklistItems(defaultItems);

      emit(state.copyWith(
        items: defaultItems,
        signatures: {},
        status: ChecklistStatus.loaded,
        date: today,
        progress: 0.0,
        isSyncedToCloud: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ChecklistStatus.error,
        errorMessage: 'Failed to reset checklist: $e',
      ));
    }
  }

  Future<void> _onSkipChecklist(
    SkipChecklist event,
    Emitter<ChecklistState> emit,
  ) async {
    try {
      // Save checklist status as skipped
      await ChecklistStorageService.saveChecklistStatus(
        completed: false,
        skipped: true,
      );

      emit(state.copyWith(isSyncedToCloud: false));

      // Sync to cloud
      add(SyncToCloud());
    } catch (e) {
      emit(state.copyWith(
        status: ChecklistStatus.error,
        errorMessage: 'Failed to skip checklist: $e',
      ));
    }
  }

  Future<void> _onCompleteChecklist(
    CompleteChecklist event,
    Emitter<ChecklistState> emit,
  ) async {
    try {
      // Save checklist status as completed
      await ChecklistStorageService.saveChecklistStatus(
        completed: true,
        skipped: false,
      );

      emit(state.copyWith(isSyncedToCloud: false));

      // Sync to cloud
      add(SyncToCloud());
    } catch (e) {
      emit(state.copyWith(
        status: ChecklistStatus.error,
        errorMessage: 'Failed to complete checklist: $e',
      ));
    }
  }

  // Helper method to calculate progress
  double _calculateProgress(List<ChecklistItem> items) {
    if (items.isEmpty) return 0.0;
    final completedItems = items.where((item) => item.isCompleted).length;
    return completedItems / items.length;
  }

  // Helper method to get default checklist items
  List<ChecklistItem> _getDefaultItems() {
    return [
      ChecklistItem(
        title: 'Opening Checks',
        subtitle: 'Verify all opening tasks are completed',
        icon: Icons.store,
        subitems: [
          'Check front door is unlocked',
          'Turn on all lights',
          'Check POS system is online',
          'Count float in register',
          'Ensure staff areas are clean'
        ],
      ),
      ChecklistItem(
        title: 'Chilled Storage Checks',
        subtitle: 'Check all chilled storage units',
        icon: Icons.inventory_2,
        subitems: [
          'Fridge 1: Temp below 5°C',
          'Fridge 2: Temp below 5°C',
          'Walk-in cooler: Temp below 5°C',
          'Check for expired items',
          'Ensure proper food storage hierarchy'
        ],
      ),
      ChecklistItem(
        title: 'Cooking Temperature Checks',
        subtitle: 'Check all cooking temperatures',
        icon: Icons.local_fire_department,
        subitems: [
          'Grill: 200°C minimum',
          'Fryer oil: 180°C',
          'Oven: 180°C minimum',
          'Check probe thermometer calibration',
          'Record temperatures in log'
        ],
      ),
      ChecklistItem(
        title: 'Cooling Checks',
        subtitle: 'Complete all cooling checks',
        icon: Icons.ac_unit,
        subitems: [
          'Hot food cooled within 2 hours',
          'Food stored in shallow containers',
          'Proper air circulation around food',
          'Food covered properly',
          'Cooling log completed'
        ],
      ),
      ChecklistItem(
        title: 'Reheating Temperature Checks',
        subtitle: 'Complete all reheating checks',
        icon: Icons.whatshot,
        subitems: [
          'Food reheated to 75°C minimum',
          'Record temperatures in log',
          'Use probe thermometer correctly',
          'Reheat food only once',
          'Discard food if not hot enough'
        ],
      ),
    ];
  }
}
