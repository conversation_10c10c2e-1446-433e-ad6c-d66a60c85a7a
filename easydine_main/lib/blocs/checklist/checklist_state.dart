import 'dart:ui';
import 'package:equatable/equatable.dart';
import '../../models/checklistItem.dart';

enum ChecklistStatus { initial, loading, loaded, error, syncing, synced }

class ChecklistState extends Equatable {
  final List<ChecklistItem> items;
  final Map<String, List<Offset?>> signatures;
  final ChecklistStatus status;
  final String? date;
  final String? errorMessage;
  final bool isSyncedToCloud;
  final double progress;

  const ChecklistState({
    this.items = const [],
    this.signatures = const {},
    this.status = ChecklistStatus.initial,
    this.date,
    this.errorMessage,
    this.isSyncedToCloud = false,
    this.progress = 0.0,
  });

  ChecklistState copyWith({
    List<ChecklistItem>? items,
    Map<String, List<Offset?>>? signatures,
    ChecklistStatus? status,
    String? date,
    String? errorMessage,
    bool? isSyncedToCloud,
    double? progress,
  }) {
    return ChecklistState(
      items: items ?? this.items,
      signatures: signatures ?? this.signatures,
      status: status ?? this.status,
      date: date ?? this.date,
      errorMessage: errorMessage ?? this.errorMessage,
      isSyncedToCloud: isSyncedToCloud ?? this.isSyncedToCloud,
      progress: progress ?? this.progress,
    );
  }

  bool get isCompleted {
    if (items.isEmpty) return false;
    
    // Check if all items are completed and signed
    for (var item in items) {
      if (!item.isCompleted) return false;
      if (!hasSignature(item.title)) return false;
    }
    
    return true;
  }

  bool hasSignature(String itemId) {
    return signatures.containsKey(itemId) && 
           signatures[itemId] != null && 
           signatures[itemId]!.isNotEmpty;
  }

  @override
  List<Object?> get props => [items, signatures, status, date, errorMessage, isSyncedToCloud, progress];
}