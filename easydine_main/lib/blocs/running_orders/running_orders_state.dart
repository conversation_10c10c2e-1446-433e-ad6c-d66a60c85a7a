import 'package:equatable/equatable.dart';

enum RunningOrdersStatus { initial, loading, success, failure }

class RunningOrdersState extends Equatable {
  final List<Map<String, dynamic>> orders;
  final List<Map<String, dynamic>>
      allOrders; // Keep original orders for filtering
  final RunningOrdersStatus status;
  final String? error;
  final String selectedCategory;
  final List<String> selectedStatuses;
  final String sortBy;

  const RunningOrdersState({
    this.orders = const [],
    this.allOrders = const [],
    this.status = RunningOrdersStatus.initial,
    this.error,
    this.selectedCategory = "All",
    this.selectedStatuses = const [],
    this.sortBy = "newest",
  });

  RunningOrdersState copyWith({
    List<Map<String, dynamic>>? orders,
    List<Map<String, dynamic>>? allOrders,
    RunningOrdersStatus? status,
    String? error,
    String? selectedCategory,
    List<String>? selectedStatuses,
    String? sortBy,
  }) {
    return RunningOrdersState(
      orders: orders ?? this.orders,
      allOrders: allOrders ?? this.allOrders,
      status: status ?? this.status,
      error: error,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      selectedStatuses: selectedStatuses ?? this.selectedStatuses,
      sortBy: sortBy ?? this.sortBy,
    );
  }

  @override
  List<Object?> get props => [
        orders,
        allOrders,
        status,
        error,
        selectedCategory,
        selectedStatuses,
        sortBy,
      ];
}
