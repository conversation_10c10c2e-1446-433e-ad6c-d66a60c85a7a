import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/staff_service.dart';
import 'session_event.dart';
import 'session_state.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SessionBloc extends Bloc<SessionEvent, SessionState> {
  static const sessionTimeout = Duration(minutes: 30);
  Timer? _sessionTimer;
  Timer? _activityTimer;

  final StaffService staffService = StaffService();

  SessionBloc() : super(const SessionState()) {
    on<InitializeSession>(_onInitializeSession);
    on<VerifyPin>(_onVerifyPin);
    on<CheckSessionStatus>(_onCheckSessionStatus);
    on<EndSession>(_onEndSession);
    on<UpdateLastActive>(_onUpdateLastActive);
    on<SetSessionAuthenticated>(_onSetSessionAuthenticated);

    _activityTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => add(CheckSessionStatus()),
    );
  }

  Future<void> _onInitializeSession(
    InitializeSession event,
    Emitter<SessionState> emit,
  ) async {
    debugPrint('🚀 SessionBloc: Initializing staff session for PIN entry');

    // SessionBloc only handles staff session management
    // Staff data fetching is handled by StaffBloc
    emit(state.copyWith(
      status: SessionStatus.pinRequired,
      waiterId: event.waiterId,
      waiterName: event.waiterName,
    ));

    debugPrint('✅ SessionBloc: Ready for staff PIN entry');
  }

  Future<void> _onVerifyPin(
    VerifyPin event,
    Emitter<SessionState> emit,
  ) async {
    emit(state.copyWith(status: SessionStatus.loadingStaff));
    try {
      debugPrint(
          '🔄 SessionBloc: Verifying PIN for staff ${event.staffId} (dashboard access)');

      // Verify the PIN for dashboard access only
      // This will call the check-in endpoint to verify PIN and create attendance record
      final staff = await staffService.verifyStaffPin(event.staffId, event.pin);

      if (staff != null) {
        debugPrint(
            '✅ SessionBloc: PIN verification successful, creating staff session');
        debugPrint('✅ SessionBloc: Staff data received: ${staff.name}');

        // Create local session for the staff member
        await _saveSessionData(staff.id, staff.name);

        emit(state.copyWith(
          status: SessionStatus.authenticated,
          waiterId: staff.id,
          waiterName: staff.name,
          lastActiveTime: DateTime.now(),
        ));

        _startSessionTimer();

        debugPrint('✅ SessionBloc: Staff session created for ${staff.name}');
      } else {
        debugPrint('❌ SessionBloc: PIN verification failed');
        emit(state.copyWith(
          status: SessionStatus.error,
          error: 'Invalid PIN. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ SessionBloc: PIN verification error: $e');
      emit(state.copyWith(
        status: SessionStatus.error,
        error: 'Failed to verify PIN: ${e.toString()}',
      ));
    }
  }

  Future<void> _onCheckSessionStatus(
    CheckSessionStatus event,
    Emitter<SessionState> emit,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final lastActiveStr = prefs.getString('lastActiveTime');

    if (lastActiveStr != null) {
      final lastActive = DateTime.parse(lastActiveStr);
      final now = DateTime.now();
      final difference = now.difference(lastActive);

      if (difference >= sessionTimeout) {
        await _clearSessionData();
        emit(state.copyWith(status: SessionStatus.pinRequired));
      }
    }
  }

  Future<void> _onEndSession(
    EndSession event,
    Emitter<SessionState> emit,
  ) async {
    await _clearSessionData();
    emit(state.copyWith(status: SessionStatus.pinRequired));
  }

  Future<void> _onUpdateLastActive(
    UpdateLastActive event,
    Emitter<SessionState> emit,
  ) async {
    if (state.status == SessionStatus.authenticated) {
      final now = DateTime.now();
      emit(state.copyWith(lastActiveTime: now));
      _startSessionTimer();
    }
  }

  Future<void> _onSetSessionAuthenticated(
    SetSessionAuthenticated event,
    Emitter<SessionState> emit,
  ) async {
    debugPrint(
        '🔐 SessionBloc: Setting session as authenticated for ${event.waiterName}');

    // Save session data
    await _saveSessionData(event.waiterId, event.waiterName);

    emit(state.copyWith(
      status: SessionStatus.authenticated,
      waiterId: event.waiterId,
      waiterName: event.waiterName,
      lastActiveTime: DateTime.now(),
    ));

    _startSessionTimer();

    debugPrint('✅ SessionBloc: Session authenticated for ${event.waiterName}');
  }

  Future<void> _saveSessionData(String waiterId, String waiterName) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('waiterId', waiterId);
    await prefs.setString('waiterName', waiterName);
    await prefs.setString('lastActiveTime', DateTime.now().toIso8601String());
  }

  Future<void> _clearSessionData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('lastActiveTime');
    await prefs.remove('waiterId');
    await prefs.remove('waiterName');
  }

  void _startSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = Timer(sessionTimeout, () {
      add(EndSession());
    });
  }

  @override
  Future<void> close() {
    _sessionTimer?.cancel();
    _activityTimer?.cancel();
    return super.close();
  }
}
