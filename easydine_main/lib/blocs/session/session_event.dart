import 'package:equatable/equatable.dart';

abstract class SessionEvent extends Equatable {
  const SessionEvent();

  @override
  List<Object?> get props => [];
}

class InitializeSession extends SessionEvent {
  final String waiterId;
  final String waiterName;

  const InitializeSession({
    required this.waiterId,
    required this.waiterName,
  });

  @override
  List<Object> get props => [waiterId, waiterName];
}

class VerifyPin extends SessionEvent {
  final String pin;
  final String staffId;

  const VerifyPin({
    required this.pin,
    required this.staffId,
  });

  @override
  List<Object> get props => [pin, staffId];
}

class CheckSessionStatus extends SessionEvent {}

class EndSession extends SessionEvent {}

class UpdateLastActive extends SessionEvent {}

class SetSessionAuthenticated extends SessionEvent {
  final String waiterId;
  final String waiterName;

  const SetSessionAuthenticated({
    required this.waiterId,
    required this.waiterName,
  });

  @override
  List<Object> get props => [waiterId, waiterName];
}
