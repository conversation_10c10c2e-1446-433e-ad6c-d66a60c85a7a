import 'package:equatable/equatable.dart';
import '../../models/staff_model.dart';

enum SessionStatus {
  initial,
  loadingStaff,
  pinRequired,
  authenticated,
  error,
}

class SessionState extends Equatable {
  final SessionStatus status;
  final DateTime? lastActiveTime;
  final String? waiterId;
  final String? waiterName;
  final String? error;
  final StaffModel? currentStaff;

  const SessionState({
    this.status = SessionStatus.initial,
    this.lastActiveTime,
    this.waiterId,
    this.waiterName,
    this.error,
    this.currentStaff,
  });

  SessionState copyWith({
    SessionStatus? status,
    DateTime? lastActiveTime,
    String? waiterId,
    String? waiterName,
    String? error,
    StaffModel? currentStaff,
  }) {
    return SessionState(
      status: status ?? this.status,
      lastActiveTime: lastActiveTime ?? this.lastActiveTime,
      waiterId: waiterId ?? this.waiterId,
      waiterName: waiterName ?? this.waiterName,
      error: error ?? this.error,
      currentStaff: currentStaff ?? this.currentStaff,
    );
  }

  @override
  List<Object?> get props => [
        status,
        lastActiveTime,
        waiterId,
        waiterName,
        error,
        currentStaff,
      ];
}
