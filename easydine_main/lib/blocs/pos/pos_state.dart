import 'package:equatable/equatable.dart';

import '../../models/menuItem.dart';

class POSState extends Equatable {
  final bool isProcessing;
  final String? error;
  final String? currentOrderId;
  final int? currentPriority;
  final String selectedCategory;
  final String searchQuery;
  final List<MenuItem> filteredItems;
  final bool isMenuLoaded;

  const POSState({
    this.isProcessing = false,
    this.error,
    this.currentOrderId,
    this.currentPriority,
    this.selectedCategory = "All",
    this.searchQuery = '',
    this.filteredItems = const [],
    this.isMenuLoaded = false,
  });

  POSState copyWith({
    bool? isProcessing,
    String? error,
    String? currentOrderId,
    int? currentPriority,
    String? selectedCategory,
    String? searchQuery,
    List<MenuItem>? filteredItems,
    bool? isMenuLoaded,
  }) {
    return POSState(
      isProcessing: isProcessing ?? this.isProcessing,
      error: error,
      currentOrderId: currentOrderId,
      currentPriority: currentPriority,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      searchQuery: searchQuery ?? this.searchQuery,
      filteredItems: filteredItems ?? this.filteredItems,
      isMenuLoaded: isMenuLoaded ?? this.isMenuLoaded,
    );
  }

  @override
  List<Object?> get props => [
        isProcessing,
        error,
        currentOrderId,
        currentPriority,
        selectedCategory,
        searchQuery,
        filteredItems,
        isMenuLoaded,
      ];
}
