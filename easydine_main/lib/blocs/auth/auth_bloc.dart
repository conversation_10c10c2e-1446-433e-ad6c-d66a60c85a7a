import 'dart:async';
import 'dart:convert';
import 'package:easydine_main/config/env_config.dart';
import 'package:easydine_main/utils/http_client.dart';
import 'package:easydine_main/utils/http_interceptor.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final String _baseUrl = EnvConfig.apiBaseUrl;
  static const Duration tokenExpirationDuration = Duration(hours: 24);
  Timer? _tokenValidationTimer;

  AuthBloc() : super(AuthInitial()) {
    on<SignInRequested>(_onSignInRequested);
    on<SignOutRequested>(_onSignOutRequested);
    on<CheckAuthStatus>(_onCheckAuthStatus);
    on<TokenExpired>(_onTokenExpired);

    // Check authentication status on startup
    add(CheckAuthStatus());

    // Start periodic token validation (check every 5 minutes)
    _startTokenValidationTimer();

    // Register callback for immediate token expiration from HTTP interceptor
    setTokenExpiredCallback(() {
      add(TokenExpired());
    });
  }

  // Add this method to check manager token
  Future<String?> getStoredToken() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('manager-token');
  }

  // 1. Sign In
  void _onSignInRequested(
      SignInRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      final Map<String, String> requestBody = {
        'emailAddress': event.emailAddress.trim(),
        'password': event.password.trim()
      };

      final response = await HttpClientService.post(
        '$_baseUrl/tenant/login',
        body: jsonEncode(requestBody),
      );

      debugPrint('SignIn response: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data["success"] == true) {
          // Use token from cookie if available, else use a dummy token
          String? token =
              _extractTokenFromCookies(response.headers['set-cookie']);
          token ??= 'dummy_token';

          debugPrint("Reached here with token: $token");

          // Get userType from data['data']['userType'] if available
          String userType = 'user';
          if (data['data'] != null && data['data']['userType'] != null) {
            userType = data['data']['userType'];
          }
          // Store the token in SharedPreferences with timestamp
          await _setToken(token);

          emit(AuthAuthenticated(token, userType));
        } else {
          emit(AuthError('Login failed: ${data['message']}'));
        }
      } else {
        final data = jsonDecode(response.body);
        emit(AuthError(data['message'] ?? 'Login failed'));
      }
    } catch (e) {
      emit(AuthError('An error occurred: ${e.toString()}'));
    }
  }

  String? _extractTokenFromCookies(dynamic cookieHeader) {
    if (cookieHeader == null) return null;

    debugPrint('Raw cookie header type: ${cookieHeader.runtimeType}');
    debugPrint('Raw cookie header: $cookieHeader');

    List<String> cookieParts = [];

    // Handle different types of cookie headers
    if (cookieHeader is List<String>) {
      cookieParts = cookieHeader;
      debugPrint(
          'Cookie header is List<String> with ${cookieParts.length} items');
    } else if (cookieHeader is String) {
      // First try splitting by actual Set-Cookie boundaries
      // Multiple Set-Cookie headers are sometimes joined by newlines or specific patterns
      if (cookieHeader.contains('set-cookie access-token=')) {
        // Handle case where multiple set-cookie headers are concatenated
        final setCookiePattern = RegExp(r'set-cookie\s+access-token=([^;]+)');
        final matches = setCookiePattern.allMatches(cookieHeader);
        cookieParts =
            matches.map((match) => 'access-token=${match.group(1)}').toList();
      } else {
        // Handle normal case - split by newlines first
        final lines = cookieHeader.split('\n');
        for (final line in lines) {
          if (line.trim().contains('access-token=')) {
            cookieParts.add(line.trim());
          }
        }

        // If no newlines, check if it's a single line with multiple cookies
        if (cookieParts.isEmpty && cookieHeader.contains('access-token=')) {
          // Try to find multiple access-token patterns
          final tokenPattern =
              RegExp(r'access-token=([^,]*(?:,[^=]*)*?)(?=\s+\w+:|$)');
          final matches = tokenPattern.allMatches(cookieHeader);
          cookieParts =
              matches.map((match) => 'access-token=${match.group(1)}').toList();

          if (cookieParts.isEmpty) {
            // Fallback - just use the whole string
            cookieParts = [cookieHeader];
          }
        }
      }
      debugPrint('Cookie header is String, found ${cookieParts.length} parts');
    } else {
      debugPrint('Unknown cookie header type: ${cookieHeader.runtimeType}');
      return null;
    }

    // Extract all access-token values
    final accessTokens = <String>[];

    for (int i = 0; i < cookieParts.length; i++) {
      final part = cookieParts[i].trim();
      debugPrint('Processing cookie part $i: $part');

      // Find ALL occurrences of 'access-token=' in this part
      final accessTokenPattern = RegExp(r'access-token=([^;,]+)');
      final matches = accessTokenPattern.allMatches(part);

      for (final match in matches) {
        final tokenValue = match.group(1)?.trim();
        if (tokenValue != null && tokenValue.isNotEmpty) {
          accessTokens.add(tokenValue);
          debugPrint(
              'Extracted token ${accessTokens.length - 1}: ${tokenValue.substring(0, tokenValue.length.clamp(0, 30))}...');
        }
      }
    }

    debugPrint('Found ${accessTokens.length} access-token cookies total');

    // Find the valid JWT token (should contain "Bearer" when decoded)
    for (int i = accessTokens.length - 1; i >= 0; i--) {
      try {
        final decodedToken = Uri.decodeComponent(accessTokens[i]);
        debugPrint(
            'Decoded token $i: ${decodedToken.substring(0, decodedToken.length.clamp(0, 50))}...');

        // Skip the clearing token (usually just contains a dot or is very short)
        if (decodedToken.length > 10 && decodedToken.contains('Bearer')) {
          debugPrint('✅ Using token $i as it contains Bearer');
          return decodedToken;
        } else {
          debugPrint('❌ Skipping token $i: too short or no Bearer');
        }
      } catch (e) {
        debugPrint('❌ Failed to decode token $i: $e');
        continue;
      }
    }

    // Fallback: use the last token if no Bearer token found
    if (accessTokens.isNotEmpty) {
      try {
        final lastToken = Uri.decodeComponent(accessTokens.last);
        if (lastToken.isNotEmpty && lastToken != '.' && lastToken.length > 5) {
          debugPrint('🔄 Fallback: using last token');
          return lastToken;
        }
      } catch (e) {
        debugPrint('❌ Fallback failed: $e');
      }
    }

    debugPrint('❌ No valid token found');
    return null;
  }

  // 3. Sign Out
  void _onSignOutRequested(
      SignOutRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      await _clearToken();
      emit(AuthInitial());
    } catch (e) {
      emit(AuthError('An error occurred during sign out: ${e.toString()}'));
    }
  }

  // 4. Check Auth Status
  void _onCheckAuthStatus(
      CheckAuthStatus event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('manager-token');
      final tokenTimestamp = prefs.getString('manager-token-timestamp');

      if (token != null && token.isNotEmpty) {
        // Check if token is expired
        if (tokenTimestamp != null) {
          final tokenTime = DateTime.parse(tokenTimestamp);
          final now = DateTime.now();
          final difference = now.difference(tokenTime);

          if (difference >= tokenExpirationDuration) {
            debugPrint(
                '🕐 AuthBloc: Manager token expired, clearing and redirecting to login');
            await _clearToken();
            emit(AuthInitial());
            return;
          }
        }

        emit(AuthAuthenticated(token));
      } else {
        emit(AuthInitial());
      }
    } catch (e) {
      emit(AuthError(
          'An error occurred while checking auth status: ${e.toString()}'));
    }
  }

  // 5. Handle Token Expiration
  void _onTokenExpired(TokenExpired event, Emitter<AuthState> emit) async {
    debugPrint('🕐 AuthBloc: Token expired event received');
    await _clearToken();
    emit(AuthInitial());
  }

  // Utility: Store manager token in SharedPreferences with timestamp
  Future<void> _setToken(String token) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('manager-token', token);
    await prefs.setString(
        'manager-token-timestamp', DateTime.now().toIso8601String());

    // Also store in legacy location for backward compatibility
    await prefs.setString('access-token', token);
    await prefs.setString('token-timestamp', DateTime.now().toIso8601String());

    debugPrint(
        '🔐 AuthBloc: Manager token stored with timestamp: ${DateTime.now()}');
  }

  // Utility: Clear manager token from SharedPreferences
  Future<void> _clearToken() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('manager-token');
    await prefs.remove('manager-token-timestamp');

    // Also clear legacy tokens
    await prefs.remove('access-token');
    await prefs.remove('token-timestamp');

    debugPrint('🗑️ AuthBloc: Manager token and timestamp cleared');
  }

  // Utility: Check if manager token is expired
  Future<bool> isTokenExpired() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final tokenTimestamp = prefs.getString('manager-token-timestamp');

      if (tokenTimestamp == null) {
        return true;
      }

      final tokenTime = DateTime.parse(tokenTimestamp);
      final now = DateTime.now();
      final difference = now.difference(tokenTime);

      return difference >= tokenExpirationDuration;
    } catch (e) {
      debugPrint('❌ AuthBloc: Error checking manager token expiration: $e');
      return true; // Assume expired on error
    }
  }

  // Start periodic token validation timer
  void _startTokenValidationTimer() {
    _tokenValidationTimer?.cancel();
    _tokenValidationTimer = Timer.periodic(
      const Duration(minutes: 5), // Check every 5 minutes
      (_) async {
        if (state is AuthAuthenticated) {
          final expired = await isTokenExpired();
          if (expired) {
            debugPrint('🕐 AuthBloc: Periodic check found expired token');
            add(TokenExpired());
          }
        }
      },
    );
  }

  @override
  Future<void> close() {
    _tokenValidationTimer?.cancel();
    return super.close();
  }
}
