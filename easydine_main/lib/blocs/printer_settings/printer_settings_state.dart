// lib/blocs/printer_settings/printer_settings_state.dart

part of 'printer_settings_bloc.dart';

class PrinterSettingsState {
  final PrinterSettings settings;
  final List<String> availableCategories;
  final bool isLoading;
  final bool isDiscovering;
  final String? error;

  PrinterSettingsState({
    required this.settings,
    required this.availableCategories,
    required this.isLoading,
    required this.isDiscovering,
    this.error,
  });

  factory PrinterSettingsState.initial() {
    return PrinterSettingsState(
      settings: PrinterSettings.empty(),
      availableCategories: [],
      isLoading: false,
      isDiscovering: false,
    );
  }

  PrinterSettingsState copyWith({
    PrinterSettings? settings,
    List<String>? availableCategories,
    bool? isLoading,
    bool? isDiscovering,
    String? error,
  }) {
    return PrinterSettingsState(
      settings: settings ?? this.settings,
      availableCategories: availableCategories ?? this.availableCategories,
      isLoading: isLoading ?? this.isLoading,
      isDiscovering: isDiscovering ?? this.isDiscovering,
      error: error,
    );
  }
}
