// lib/blocs/printer_settings/printer_settings_bloc.dart

import 'dart:convert';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/printer_settings.dart';
import '../../services/menu_service.dart';

part 'printer_settings_event.dart';
part 'printer_settings_state.dart';

class PrinterSettingsBloc
    extends Bloc<PrinterSettingsEvent, PrinterSettingsState> {
  static const String _printerSettingsKey = 'printer_settings';

  PrinterSettingsBloc() : super(PrinterSettingsState.initial()) {
    on<LoadPrinterSettings>(_onLoadPrinterSettings);
    on<UpdatePrinterAssignment>(_onUpdatePrinterAssignment);
    on<UpdateCategoryAssignment>(_onUpdateCategoryAssignment);
    on<AddPrinterDevice>(_onAddPrinterDevice);
    on<RemovePrinterDevice>(_onRemovePrinterDevice);
    on<UpdatePrinterDevice>(_onUpdatePrinterDevice);
    on<ResetPrinterSettings>(_onResetPrinterSettings);
    on<DiscoverPrinters>(_onDiscoverPrinters);
  }

  Future<void> _onLoadPrinterSettings(
    LoadPrinterSettings event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_printerSettingsKey);

      PrinterSettings settings;
      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        settings = PrinterSettings.fromJson(settingsMap);
      } else {
        settings = _createDefaultSettings();
        await _savePrinterSettings(settings);
      }

      // Get available categories from menu service
      final availableCategories = MenuService.getAllCategories();

      emit(state.copyWith(
        isLoading: false,
        settings: settings,
        availableCategories: availableCategories,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to load printer settings: $e',
      ));
    }
  }

  Future<void> _onUpdatePrinterAssignment(
    UpdatePrinterAssignment event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedAssignments =
          Map<String, String>.from(state.settings.printerAssignments);

      if (event.location.isEmpty || event.location == 'Not assigned') {
        updatedAssignments.remove(event.printerId);
      } else {
        updatedAssignments[event.printerId] = event.location;
      }

      final updatedSettings = state.settings.copyWith(
        printerAssignments: updatedAssignments,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update printer assignment: $e'));
    }
  }

  Future<void> _onUpdateCategoryAssignment(
    UpdateCategoryAssignment event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedCategoryAssignments =
          Map<String, List<String>>.from(state.settings.categoryAssignments);

      // Remove category from all locations first
      for (final location in updatedCategoryAssignments.keys) {
        updatedCategoryAssignments[location]?.remove(event.category);
      }

      // Add category to new location if specified
      if (event.location.isNotEmpty && event.location != 'Not assigned') {
        if (!updatedCategoryAssignments.containsKey(event.location)) {
          updatedCategoryAssignments[event.location] = [];
        }
        if (!updatedCategoryAssignments[event.location]!
            .contains(event.category)) {
          updatedCategoryAssignments[event.location]!.add(event.category);
        }
      }

      // Clean up empty location entries
      updatedCategoryAssignments.removeWhere((key, value) => value.isEmpty);

      final updatedSettings = state.settings.copyWith(
        categoryAssignments: updatedCategoryAssignments,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update category assignment: $e'));
    }
  }

  Future<void> _onAddPrinterDevice(
    AddPrinterDevice event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedPrinters =
          List<PrinterDevice>.from(state.settings.availablePrinters);
      updatedPrinters.add(event.printer);

      final updatedSettings = state.settings.copyWith(
        availablePrinters: updatedPrinters,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to add printer device: $e'));
    }
  }

  Future<void> _onRemovePrinterDevice(
    RemovePrinterDevice event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedPrinters =
          List<PrinterDevice>.from(state.settings.availablePrinters);
      updatedPrinters.removeWhere((printer) => printer.id == event.printerId);

      // Also remove from assignments
      final updatedAssignments =
          Map<String, String>.from(state.settings.printerAssignments);
      updatedAssignments.remove(event.printerId);

      final updatedSettings = state.settings.copyWith(
        availablePrinters: updatedPrinters,
        printerAssignments: updatedAssignments,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to remove printer device: $e'));
    }
  }

  Future<void> _onUpdatePrinterDevice(
    UpdatePrinterDevice event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedPrinters =
          List<PrinterDevice>.from(state.settings.availablePrinters);
      final index = updatedPrinters
          .indexWhere((printer) => printer.id == event.printer.id);

      if (index != -1) {
        updatedPrinters[index] = event.printer;
      }

      final updatedSettings = state.settings.copyWith(
        availablePrinters: updatedPrinters,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update printer device: $e'));
    }
  }

  Future<void> _onResetPrinterSettings(
    ResetPrinterSettings event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final defaultSettings = _createDefaultSettings();
      await _savePrinterSettings(defaultSettings);
      emit(state.copyWith(settings: defaultSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to reset printer settings: $e'));
    }
  }

  Future<void> _onDiscoverPrinters(
    DiscoverPrinters event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    emit(state.copyWith(isDiscovering: true));

    try {
      // Simulate printer discovery - in real implementation, this would scan network
      await Future.delayed(const Duration(seconds: 2));

      final discoveredPrinters = [
        PrinterDevice(
          id: 'printer_${DateTime.now().millisecondsSinceEpoch}',
          name: 'Kitchen Thermal Printer',
          type: 'thermal',
          isConnected: true,
          ipAddress: '*************',
          port: 9100,
        ),
        PrinterDevice(
          id: 'printer_${DateTime.now().millisecondsSinceEpoch + 1}',
          name: 'Bar Receipt Printer',
          type: 'receipt',
          isConnected: true,
          ipAddress: '*************',
          port: 9100,
        ),
      ];

      final updatedPrinters =
          List<PrinterDevice>.from(state.settings.availablePrinters);
      for (final printer in discoveredPrinters) {
        if (!updatedPrinters.any((p) => p.id == printer.id)) {
          updatedPrinters.add(printer);
        }
      }

      final updatedSettings = state.settings.copyWith(
        availablePrinters: updatedPrinters,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(
        settings: updatedSettings,
        isDiscovering: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isDiscovering: false,
        error: 'Failed to discover printers: $e',
      ));
    }
  }

  PrinterSettings _createDefaultSettings() {
    // Create some default printers
    final defaultPrinters = [
      PrinterDevice(
        id: 'printer_1',
        name: 'Kitchen 1',
        type: 'thermal',
        isConnected: false,
      ),
      PrinterDevice(
        id: 'printer_2',
        name: 'Kitchen 2',
        type: 'thermal',
        isConnected: false,
      ),
      PrinterDevice(
        id: 'printer_3',
        name: 'Counter',
        type: 'receipt',
        isConnected: false,
      ),
      PrinterDevice(
        id: 'printer_4',
        name: 'Bar Station',
        type: 'thermal',
        isConnected: false,
      ),
    ];

    // Default assignments
    final defaultPrinterAssignments = {
      'printer_1': PrinterLocation.kitchen1,
      'printer_2': PrinterLocation.kitchen2,
      'printer_3': PrinterLocation.counter,
    };

    final defaultCategoryAssignments = {
      PrinterLocation.kitchen1: ['Food', 'Mains', 'Appetizers'],
      PrinterLocation.kitchen2: ['Food', 'Desserts'],
      PrinterLocation.counter: ['Drinks', 'Beverages'],
    };

    return PrinterSettings(
      printerAssignments: defaultPrinterAssignments,
      categoryAssignments: defaultCategoryAssignments,
      availablePrinters: defaultPrinters,
    );
  }

  Future<void> _savePrinterSettings(PrinterSettings settings) async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = jsonEncode(settings.toJson());
    await prefs.setString(_printerSettingsKey, settingsJson);
  }
}
