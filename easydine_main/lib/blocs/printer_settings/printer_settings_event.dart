// lib/blocs/printer_settings/printer_settings_event.dart

part of 'printer_settings_bloc.dart';

abstract class PrinterSettingsEvent {}

class LoadPrinterSettings extends PrinterSettingsEvent {}

class UpdatePrinterAssignment extends PrinterSettingsEvent {
  final String printerId;
  final String location;

  UpdatePrinterAssignment({
    required this.printerId,
    required this.location,
  });
}

class UpdateCategoryAssignment extends PrinterSettingsEvent {
  final String category;
  final String location;

  UpdateCategoryAssignment({
    required this.category,
    required this.location,
  });
}

class AddPrinterDevice extends PrinterSettingsEvent {
  final PrinterDevice printer;

  AddPrinterDevice({required this.printer});
}

class RemovePrinterDevice extends PrinterSettingsEvent {
  final String printerId;

  RemovePrinterDevice({required this.printerId});
}

class UpdatePrinterDevice extends PrinterSettingsEvent {
  final PrinterDevice printer;

  UpdatePrinterDevice({required this.printer});
}

class ResetPrinterSettings extends PrinterSettingsEvent {}

class DiscoverPrinters extends PrinterSettingsEvent {}
