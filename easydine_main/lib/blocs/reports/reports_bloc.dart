import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Events
abstract class ReportsEvent {}

class FetchReports extends ReportsEvent {}

class DeleteReport extends ReportsEvent {
  final String filePath;
  DeleteReport(this.filePath);
}

// States
abstract class ReportsState {}

class ReportsInitial extends ReportsState {}

class ReportsLoading extends ReportsState {}

class ReportsLoaded extends ReportsState {
  final List<File> reports;
  final DateTime lastUpdated;

  ReportsLoaded(this.reports, this.lastUpdated);
}

class ReportsError extends ReportsState {
  final String message;
  ReportsError(this.message);
}

// Bloc
class ReportsBloc extends Bloc<ReportsEvent, ReportsState> {
  ReportsBloc() : super(ReportsInitial()) {
    on<FetchReports>(_onFetchReports);
    on<DeleteReport>(_onDeleteReport);
  }

  Future<void> _onFetchReports(
    FetchReports event,
    Emitter<ReportsState> emit,
  ) async {
    emit(ReportsLoading());

    try {
      final prefs = await SharedPreferences.getInstance();
      final reportPaths = prefs.getStringList('checklist_reports') ?? [];
      
      final reports = reportPaths
          .map((path) => File(path))
          .where((file) => file.existsSync())
          .toList();

      // Sort by creation date, newest first
      reports.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      emit(ReportsLoaded(reports, DateTime.now()));
    } catch (e) {
      emit(ReportsError('Failed to load reports: $e'));
    }
  }

  Future<void> _onDeleteReport(
    DeleteReport event,
    Emitter<ReportsState> emit,
  ) async {
    try {
      final file = File(event.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      final prefs = await SharedPreferences.getInstance();
      final reportPaths = prefs.getStringList('checklist_reports') ?? [];
      reportPaths.remove(event.filePath);
      await prefs.setStringList('checklist_reports', reportPaths);

      add(FetchReports());
    } catch (e) {
      emit(ReportsError('Failed to delete report: $e'));
    }
  }
}