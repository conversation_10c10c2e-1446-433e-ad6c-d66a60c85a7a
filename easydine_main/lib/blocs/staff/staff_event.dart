import 'package:equatable/equatable.dart';

abstract class StaffEvent extends Equatable {
  const StaffEvent();

  @override
  List<Object?> get props => [];
}

class FetchBranchStaff extends StaffEvent {
  final String branchId;

  const FetchBranchStaff({required this.branchId});

  @override
  List<Object> get props => [branchId];
}

class FetchClockedInStaff extends StaffEvent {
  final String branchId;

  const FetchClockedInStaff({required this.branchId});

  @override
  List<Object> get props => [branchId];
}

class RefreshStaffData extends StaffEvent {
  final String branchId;

  const RefreshStaffData({required this.branchId});

  @override
  List<Object> get props => [branchId];
}
