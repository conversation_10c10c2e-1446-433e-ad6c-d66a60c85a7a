import 'package:equatable/equatable.dart';
import '../../models/staff_model.dart';

enum StaffStatus {
  initial,
  loading,
  loaded,
  error,
}

class StaffState extends Equatable {
  final StaffStatus status;
  final List<StaffModel> allStaff;
  final List<StaffModel> clockedInStaff;
  final String? error;

  const StaffState({
    this.status = StaffStatus.initial,
    this.allStaff = const [],
    this.clockedInStaff = const [],
    this.error,
  });

  StaffState copyWith({
    StaffStatus? status,
    List<StaffModel>? allStaff,
    List<StaffModel>? clockedInStaff,
    String? error,
  }) {
    return StaffState(
      status: status ?? this.status,
      allStaff: allStaff ?? this.allStaff,
      clockedInStaff: clockedInStaff ?? this.clockedInStaff,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
        status,
        allStaff,
        clockedInStaff,
        error,
      ];
}
