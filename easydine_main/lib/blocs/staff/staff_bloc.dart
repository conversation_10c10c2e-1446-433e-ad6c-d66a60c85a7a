import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/staff_service.dart';
import 'staff_event.dart';
import 'staff_state.dart';

class StaffBloc extends Bloc<StaffEvent, StaffState> {
  final StaffService _staffService = StaffService();

  StaffBloc() : super(const StaffState()) {
    on<FetchBranchStaff>(_onFetchBranchStaff);
    on<FetchClockedInStaff>(_onFetchClockedInStaff);
    on<RefreshStaffData>(_onRefreshStaffData);
  }

  Future<void> _onFetchBranchStaff(
    FetchBranchStaff event,
    Emitter<StaffState> emit,
  ) async {
    emit(state.copyWith(status: StaffStatus.loading));

    try {
      debugPrint(
          '📋 StaffBloc: Fetching all staff for branch: ${event.branchId}');

      // Use fetchBranchStaffsWithCache to populate StaffService cache for PIN verification
      final allStaff =
          await _staffService.fetchBranchStaffsWithCache(event.branchId);

      debugPrint(
          '✅ StaffBloc: Successfully loaded ${allStaff.length} staff members and cached for PIN verification');

      emit(state.copyWith(
        status: StaffStatus.loaded,
        allStaff: allStaff,
      ));
    } catch (e) {
      debugPrint('❌ StaffBloc: Error fetching staff: $e');
      emit(state.copyWith(
        status: StaffStatus.error,
        error: 'Failed to load staff: ${e.toString()}',
      ));
    }
  }

  Future<void> _onFetchClockedInStaff(
    FetchClockedInStaff event,
    Emitter<StaffState> emit,
  ) async {
    emit(state.copyWith(status: StaffStatus.loading));

    try {
      debugPrint(
          '🕐 StaffBloc: Fetching clocked-in staff for branch: ${event.branchId}');

      // First populate the cache with all staff for PIN verification
      await _staffService.fetchBranchStaffsWithCache(event.branchId);

      // Then fetch clocked-in staff for display
      final clockedInStaff =
          await _staffService.fetchBranchClockedInStaffs(event.branchId);

      debugPrint(
          '✅ StaffBloc: Found ${clockedInStaff.length} clocked-in staff and populated cache for PIN verification');

      emit(state.copyWith(
        status: StaffStatus.loaded,
        clockedInStaff: clockedInStaff,
      ));
    } catch (e) {
      debugPrint('❌ StaffBloc: Error fetching clocked-in staff: $e');
      emit(state.copyWith(
        status: StaffStatus.error,
        error: 'Failed to load clocked-in staff: ${e.toString()}',
      ));
    }
  }

  Future<void> _onRefreshStaffData(
    RefreshStaffData event,
    Emitter<StaffState> emit,
  ) async {
    emit(state.copyWith(status: StaffStatus.loading));

    try {
      debugPrint(
          '🔄 StaffBloc: Refreshing all staff data for branch: ${event.branchId}');

      // Fetch both all staff and clocked-in staff using manager's token via HTTP interceptor
      // Use fetchBranchStaffsWithCache to populate StaffService cache for PIN verification
      final allStaff =
          await _staffService.fetchBranchStaffsWithCache(event.branchId);
      final clockedInStaff =
          await _staffService.fetchBranchClockedInStaffs(event.branchId);

      debugPrint(
          '✅ StaffBloc: Refreshed - Total: ${allStaff.length}, Clocked-in: ${clockedInStaff.length}, Cache populated for PIN verification');

      emit(state.copyWith(
        status: StaffStatus.loaded,
        allStaff: allStaff,
        clockedInStaff: clockedInStaff,
      ));
    } catch (e) {
      debugPrint('❌ StaffBloc: Error refreshing staff data: $e');
      emit(state.copyWith(
        status: StaffStatus.error,
        error: 'Failed to refresh staff data: ${e.toString()}',
      ));
    }
  }
}
