// settings_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'settings_event.dart';
part 'settings_state.dart';

class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  static const String _takeawayKey = 'settings_takeaway_enabled';
  static const String _deliveryKey = 'settings_delivery_enabled';
  static const String _rushOrderKey = 'settings_rush_order_enabled';
  static const String _autoGenerateOrderIdKey = 'settings_auto_generate_order_id';
  static const String _contactlessDineInKey = 'settings_contactless_dine_in';
  static const String _autoAcceptQrOrdersKey = 'settings_auto_accept_qr_orders';
  static const String _requireTableVerificationKey = 'settings_require_table_verification';
  static const String _qrOrderSoundKey = 'settings_qr_order_sound';
  static const String _rushOrderFeeKey = 'settings_rush_order_fee';
  static const String _rushOrderPriorityKey = 'settings_rush_order_priority';
  static const String _rushOrderMaxTimeKey = 'settings_rush_order_max_time';

  SettingsBloc() : super(SettingsState.initial()) {
    // Load saved settings when bloc is created
    _loadSettings();

    on<ToggleTakeaway>((event, emit) async {
      emit(state.copyWith(takeawayEnabled: event.isEnabled));
      await _saveSettings();
    });

    on<ToggleDelivery>((event, emit) async {
      emit(state.copyWith(deliveryEnabled: event.isEnabled));
      await _saveSettings();
    });

    on<ToggleRushOrder>((event, emit) async {
      emit(state.copyWith(rushOrderEnabled: event.isEnabled));
      await _saveSettings();
    });

    on<ToggleAutoGenerateOrderId>((event, emit) async {
      emit(state.copyWith(autoGenerateOrderId: event.isEnabled));
      await _saveSettings();
    });

    on<ToggleContactlessDineIn>((event, emit) async {
      emit(state.copyWith(contactlessDineInEnabled: event.isEnabled));
      await _saveSettings();
    });

    on<UpdateQrOrderSettings>((event, emit) async {
      emit(state.copyWith(
        autoAcceptQrOrders: event.autoAccept,
        requireTableVerification: event.tableVerification,
        qrOrderSound: event.sound,
      ));
      await _saveSettings();
    });

    on<UpdateRushOrderSettings>((event, emit) async {
      emit(state.copyWith(
        rushOrderFee: event.fee,
        rushOrderPriority: event.priority,
        rushOrderMaxTime: event.maxTime,
      ));
      await _saveSettings();
    });
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    emit(SettingsState(
      takeawayEnabled: prefs.getBool(_takeawayKey) ?? true,
      deliveryEnabled: prefs.getBool(_deliveryKey) ?? true,
      rushOrderEnabled: prefs.getBool(_rushOrderKey) ?? false,
      autoGenerateOrderId: prefs.getBool(_autoGenerateOrderIdKey) ?? false,
      contactlessDineInEnabled: prefs.getBool(_contactlessDineInKey) ?? false,
      autoAcceptQrOrders: prefs.getBool(_autoAcceptQrOrdersKey) ?? false,
      requireTableVerification: prefs.getBool(_requireTableVerificationKey) ?? true,
      qrOrderSound: prefs.getString(_qrOrderSoundKey) ?? 'default',
      rushOrderFee: prefs.getDouble(_rushOrderFeeKey) ?? 5.00,
      rushOrderPriority: prefs.getInt(_rushOrderPriorityKey) ?? 1,
      rushOrderMaxTime: prefs.getInt(_rushOrderMaxTimeKey) ?? 20,
    ));
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setBool(_takeawayKey, state.takeawayEnabled);
    await prefs.setBool(_deliveryKey, state.deliveryEnabled);
    await prefs.setBool(_rushOrderKey, state.rushOrderEnabled);
    await prefs.setBool(_autoGenerateOrderIdKey, state.autoGenerateOrderId);
    await prefs.setBool(_contactlessDineInKey, state.contactlessDineInEnabled);
    await prefs.setBool(_autoAcceptQrOrdersKey, state.autoAcceptQrOrders ?? false);
    await prefs.setBool(_requireTableVerificationKey, state.requireTableVerification ?? true);
    await prefs.setString(_qrOrderSoundKey, state.qrOrderSound ?? 'default');
    await prefs.setDouble(_rushOrderFeeKey, state.rushOrderFee ?? 5.00);
    await prefs.setInt(_rushOrderPriorityKey, state.rushOrderPriority ?? 1);
    await prefs.setInt(_rushOrderMaxTimeKey, state.rushOrderMaxTime ?? 20);
  }
}
