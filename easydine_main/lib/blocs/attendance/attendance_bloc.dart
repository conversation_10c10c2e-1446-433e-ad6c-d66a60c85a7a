import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/staff_service.dart';
import 'attendance_event.dart';
import 'attendance_state.dart';

class AttendanceBloc extends Bloc<AttendanceEvent, AttendanceState> {
  final StaffService _staffService = StaffService();

  AttendanceBloc() : super(const AttendanceState()) {
    on<ClockInStaff>(_onClockInStaff);
    on<ClockOutStaff>(_onClockOutStaff);
    on<CheckInStaff>(_onCheckInStaff);
    on<CheckOutStaff>(_onCheckOutStaff);
    on<ResetAttendanceState>(_onResetAttendanceState);
  }

  Future<void> _onClockInStaff(
    ClockInStaff event,
    Emitter<AttendanceState> emit,
  ) async {
    emit(state.copyWith(
      status: AttendanceStatus.loading,
      lastOperation: AttendanceOperation.clockIn,
    ));

    try {
      debugPrint('🔄 AttendanceBloc: Clock-in staff ${event.staffId}');

      final success =
          await _staffService.clockInStaff(event.staffId, event.pin);

      if (success) {
        debugPrint('✅ AttendanceBloc: Clock-in successful');
        emit(state.copyWith(
          status: AttendanceStatus.success,
          successMessage: 'Staff clocked in successfully',
        ));
      } else {
        debugPrint('❌ AttendanceBloc: Clock-in failed');
        emit(state.copyWith(
          status: AttendanceStatus.error,
          error: 'Clock-in failed. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ AttendanceBloc: Clock-in error: $e');
      String errorMessage = 'Failed to clock in. Please try again.';

      // Extract meaningful error message
      if (e.toString().contains('Exception:')) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      } else if (e.toString().contains('Invalid PIN')) {
        errorMessage = 'Invalid PIN. Please check your PIN and try again.';
      } else if (e.toString().contains('Already clocked in')) {
        errorMessage = 'Staff is already clocked in. Please clock out first.';
      } else if (e.toString().contains('not found')) {
        errorMessage = 'Staff not found. Please contact your manager.';
      }

      emit(state.copyWith(
        status: AttendanceStatus.error,
        error: errorMessage,
      ));
    }
  }

  Future<void> _onClockOutStaff(
    ClockOutStaff event,
    Emitter<AttendanceState> emit,
  ) async {
    emit(state.copyWith(
      status: AttendanceStatus.loading,
      lastOperation: AttendanceOperation.clockOut,
    ));

    try {
      debugPrint('🔄 AttendanceBloc: Clock-out staff ${event.staffId}');

      final success =
          await _staffService.clockOutStaff(event.staffId, event.pin);

      if (success) {
        debugPrint('✅ AttendanceBloc: Clock-out successful');
        emit(state.copyWith(
          status: AttendanceStatus.success,
          successMessage: 'Staff clocked out successfully',
        ));
      } else {
        debugPrint('❌ AttendanceBloc: Clock-out failed');
        emit(state.copyWith(
          status: AttendanceStatus.error,
          error: 'Clock-out failed. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ AttendanceBloc: Clock-out error: $e');
      String errorMessage = 'Failed to clock out. Please try again.';

      // Extract meaningful error message
      if (e.toString().contains('Exception:')) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      } else if (e.toString().contains('Invalid PIN')) {
        errorMessage = 'Invalid PIN. Please check your PIN and try again.';
      } else if (e.toString().contains('not found')) {
        errorMessage =
            'No active clock-in record found. Please clock in first.';
      } else if (e.toString().contains('Staff not found')) {
        errorMessage = 'Staff not found. Please contact your manager.';
      }

      emit(state.copyWith(
        status: AttendanceStatus.error,
        error: errorMessage,
      ));
    }
  }

  Future<void> _onCheckInStaff(
    CheckInStaff event,
    Emitter<AttendanceState> emit,
  ) async {
    emit(state.copyWith(
      status: AttendanceStatus.loading,
      lastOperation: AttendanceOperation.checkIn,
    ));

    try {
      debugPrint('🔄 AttendanceBloc: Check-in staff ${event.staffId}');

      final staff = await _staffService.checkInStaff(event.staffId, event.pin);

      if (staff != null) {
        debugPrint('✅ AttendanceBloc: Check-in successful for ${staff.name}');

        // Create staff session after successful check-in
        await _createStaffSession(staff.id, staff.name);

        emit(state.copyWith(
          status: AttendanceStatus.success,
          successMessage: 'Staff checked in successfully',
        ));
      } else {
        debugPrint(
            '❌ AttendanceBloc: Check-in failed - no staff data returned');
        emit(state.copyWith(
          status: AttendanceStatus.error,
          error: 'Check-in failed. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ AttendanceBloc: Check-in error: $e');
      String errorMessage = 'Failed to check in. Please try again.';

      // Extract meaningful error message
      if (e.toString().contains('Exception:')) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      } else if (e.toString().contains('Invalid PIN')) {
        errorMessage = 'Invalid PIN. Please check your PIN and try again.';
      } else if (e.toString().contains('Already checked in')) {
        errorMessage = 'Staff is already checked in. Please check out first.';
      } else if (e.toString().contains('not found')) {
        errorMessage =
            'Staff must be clocked in before checking in for attendance.';
      }

      emit(state.copyWith(
        status: AttendanceStatus.error,
        error: errorMessage,
      ));
    }
  }

  // Create staff session after successful check-in
  Future<void> _createStaffSession(String staffId, String staffName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('staff-session-id', staffId);
      await prefs.setString('staff-session-name', staffName);
      await prefs.setString(
          'staff-session-timestamp', DateTime.now().toIso8601String());

      debugPrint('✅ AttendanceBloc: Staff session created for $staffName');
    } catch (e) {
      debugPrint('❌ AttendanceBloc: Failed to create staff session: $e');
    }
  }

  Future<void> _onCheckOutStaff(
    CheckOutStaff event,
    Emitter<AttendanceState> emit,
  ) async {
    emit(state.copyWith(
      status: AttendanceStatus.loading,
      lastOperation: AttendanceOperation.checkOut,
    ));

    try {
      debugPrint('🔄 AttendanceBloc: Check-out staff ${event.staffId}');

      final success =
          await _staffService.checkOutStaff(event.staffId, event.pin);

      if (success) {
        debugPrint('✅ AttendanceBloc: Check-out successful');
        emit(state.copyWith(
          status: AttendanceStatus.success,
          successMessage: 'Staff checked out successfully',
        ));
      } else {
        debugPrint('❌ AttendanceBloc: Check-out failed');
        emit(state.copyWith(
          status: AttendanceStatus.error,
          error: 'Check-out failed. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ AttendanceBloc: Check-out error: $e');
      String errorMessage = 'Failed to check out. Please try again.';

      // Extract meaningful error message
      if (e.toString().contains('Exception:')) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      } else if (e.toString().contains('Invalid PIN')) {
        errorMessage = 'Invalid PIN. Please check your PIN and try again.';
      } else if (e.toString().contains('not found')) {
        errorMessage =
            'No active check-in record found. Please check in first.';
      } else if (e.toString().contains('Staff not found')) {
        errorMessage = 'Staff not found. Please contact your manager.';
      }

      emit(state.copyWith(
        status: AttendanceStatus.error,
        error: errorMessage,
      ));
    }
  }

  void _onResetAttendanceState(
    ResetAttendanceState event,
    Emitter<AttendanceState> emit,
  ) {
    debugPrint('🔄 AttendanceBloc: Resetting attendance state');
    emit(const AttendanceState());
  }
}
