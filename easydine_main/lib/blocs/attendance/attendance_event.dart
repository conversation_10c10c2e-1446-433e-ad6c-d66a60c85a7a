import 'package:equatable/equatable.dart';

abstract class AttendanceEvent extends Equatable {
  const AttendanceEvent();

  @override
  List<Object?> get props => [];
}

class ClockInStaff extends AttendanceEvent {
  final String staffId;
  final String pin;

  const ClockInStaff({
    required this.staffId,
    required this.pin,
  });

  @override
  List<Object> get props => [staffId, pin];
}

class ClockOutStaff extends AttendanceEvent {
  final String staffId;
  final String pin;

  const ClockOutStaff({
    required this.staffId,
    required this.pin,
  });

  @override
  List<Object> get props => [staffId, pin];
}

class CheckInStaff extends AttendanceEvent {
  final String staffId;
  final String pin;

  const CheckInStaff({
    required this.staffId,
    required this.pin,
  });

  @override
  List<Object> get props => [staffId, pin];
}

class CheckOutStaff extends AttendanceEvent {
  final String staffId;
  final String pin;

  const CheckOutStaff({
    required this.staffId,
    required this.pin,
  });

  @override
  List<Object> get props => [staffId, pin];
}

class ResetAttendanceState extends AttendanceEvent {
  const ResetAttendanceState();
}
