import 'package:easydine_main/blocs/session/session_event.dart';
import 'package:easydine_main/router/router_constants.dart';
import 'package:easydine_main/screens/auth/branch_selection_page.dart';
import 'package:easydine_main/screens/auth/clock_action_page.dart';
import 'package:easydine_main/screens/auth/login_page.dart';
import 'package:easydine_main/screens/splash/splash_page.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:easydine_main/screens/user/contactless_dining_page.dart';
import 'package:easydine_main/screens/user/delivery_page.dart';
import 'package:easydine_main/screens/user/dine_in/dinein_page.dart';
import 'package:easydine_main/screens/user/home_page.dart';
import 'package:easydine_main/screens/user/pos_page.dart';
import 'package:easydine_main/screens/user/reports_page.dart';
import 'package:easydine_main/screens/user/running_orders.dart';
import 'package:easydine_main/screens/user/order_history.dart';
import 'package:easydine_main/screens/user/rush_order_management_page.dart';
import 'package:easydine_main/screens/user/settings_page.dart';
import 'package:easydine_main/screens/user/support_page.dart';
import 'package:easydine_main/screens/user/table_layout_manager.dart';
import 'package:easydine_main/screens/user/bulk_orders_page.dart';
import 'package:easydine_main/screens/user/reservations_management_page.dart';
import 'package:easydine_main/screens/user/printer_settings_page.dart';
import 'package:go_router/go_router.dart';
import 'package:easydine_main/screens/user/daily_checklist_page.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_state.dart';
import '../blocs/auth/auth_event.dart';
import '../blocs/session/session_bloc.dart';
import '../blocs/session/session_state.dart';
import '../screens/auth/pin_entry_page.dart';
import '../screens/demo/demo_pos_screen.dart';

final GoRouter router = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      name: RouterConstants.splash,
      builder: (context, state) => SplashScreen(),
    ),
    GoRoute(
      path: '/login',
      name: RouterConstants.login,
      builder: (context, state) => const SignInPage(),
    ),
    GoRoute(
      path: '/branch-selection',
      name: RouterConstants.branchSelection,
      builder: (context, state) => const BranchSelectionPage(),
    ),
    GoRoute(
      path: '/pin-entry',
      name: RouterConstants.pinEntry,
      builder: (context, state) => const PinEntryPage(),
    ),
    GoRoute(
      path: '/daily-checklist',
      name: RouterConstants.dailyChecklist,
      builder: (context, state) => DailyChecklistPage(),
    ),
    GoRoute(
      path: '/home',
      name: RouterConstants.home,
      builder: (context, state) => HomePage(),
    ),
    GoRoute(
        path: '/dinein',
        name: RouterConstants.dineIn,
        builder: (context, state) => DineInPage()),
    GoRoute(
      name: RouterConstants.pos,
      path: '/posmenu',
      builder: (context, state) => POSPage(
        tableNumber: state.uri.queryParameters['tableNumber'] ?? '',
        orderId: state.uri.queryParameters['orderId'] ?? '',
        orderType: state.uri.queryParameters['orderType'] ?? 'dine_in',
      ),
    ),
    GoRoute(
      name: RouterConstants.demopos,
      path: '/demoposmenu',
      builder: (context, state) => DemoPOSPage(
        tableNumber: 'Demo POS',
        orderId: 'Demo Order ID:01',
        orderType: 'Demo Order',
      ),
    ),
    GoRoute(
      path: '/delivery',
      name: RouterConstants.delivery,
      builder: (context, state) => const DeliveryPage(),
    ),
    GoRoute(
      path: '/rushorders',
      name: RouterConstants.rushOrders,
      builder: (context, state) => const RushOrderManagementPage(),
    ),
    GoRoute(
        path: '/runningorders',
        name: RouterConstants.runningOrders,
        builder: (context, state) => RunningOrdersPage()),
    GoRoute(
        path: '/order-history',
        name: RouterConstants.orderHistory,
        builder: (context, state) => OrderHistoryPage()),
    GoRoute(
        path: '/contactlessdining',
        name: RouterConstants.contactlessDining,
        builder: (context, state) => ContactlessDiningPage()),
    GoRoute(
        path: '/reports',
        name: RouterConstants.reports,
        builder: (context, state) => ReportsPage()),
    GoRoute(
        path: '/support',
        name: RouterConstants.support,
        builder: (context, state) => SupportPage()),
    GoRoute(
      path: '/settings',
      name: RouterConstants.settings,
      builder: (context, state) => SettingsPage(),
    ),
    GoRoute(
      path: '/table-layout-manager',
      name: RouterConstants.tableLayoutManager,
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>;
        return TableLayoutManager(
          tables: extra['tables'] as List<Map<String, dynamic>>,
          floor: extra['floor'] as int,
        );
      },
    ),
    GoRoute(
      name: RouterConstants.clockAction,
      path: '/clock-action',
      builder: (context, state) => ClockActionPage(
        action: state.extra as ClockAction,
      ),
    ),
    GoRoute(
      path: '/bulk-orders',
      name: RouterConstants.bulkOrders,
      builder: (context, state) => const BulkOrdersPage(),
    ),
    GoRoute(
      path: '/reservations-management',
      name: RouterConstants.reservationsManagement,
      builder: (context, state) => const ReservationsManagementPage(),
    ),
    GoRoute(
      path: '/printer-settings',
      name: RouterConstants.printerSettings,
      builder: (context, state) => const PrinterSettingsPage(),
    ),
  ],
  redirect: (context, state) async {
    final authState = context.read<AuthBloc>().state;
    final sessionState = context.read<SessionBloc>().state;
    final currentPath = state.matchedLocation;

    print('🔄 Router: Checking redirect for $currentPath');
    print('🔄 Router: AuthState: ${authState.runtimeType}');
    print('🔄 Router: SessionStatus: ${sessionState.status}');

    // Allow these paths without authentication checks
    final allowedPaths = ['/', '/login'];
    if (allowedPaths.contains(currentPath)) {
      print('✅ Router: Allowing access to $currentPath');
      return null;
    }

    // Check authentication first
    if (authState is! AuthAuthenticated) {
      print('❌ Router: Not authenticated, redirecting to login');
      return '/login';
    }

    // Additional check for token expiration using AuthBloc method
    // But allow pin-entry page to continue working even with expired token
    // since staff operations should not be disrupted by manager token expiration
    final authBloc = context.read<AuthBloc>();
    final isExpired = await authBloc.isTokenExpired();
    if (isExpired && currentPath != '/pin-entry') {
      print('🕐 Router: Token expired, triggering token expiration event');
      authBloc.add(TokenExpired());
      return '/login';
    }

    // Check if branch is selected for authenticated users
    final prefs = await SharedPreferences.getInstance();
    final selectedBranchId = prefs.getString('selected_branch_id');

    if (selectedBranchId == null || selectedBranchId.isEmpty) {
      if (currentPath != '/branch-selection') {
        print('🏢 Router: No branch selected, redirecting to branch selection');
        return '/branch-selection';
      }
      return null; // Allow branch selection page
    }

    // If branch is selected but session not initialized, allow pin entry
    if (sessionState.status == SessionStatus.initial ||
        sessionState.status == SessionStatus.loadingStaff ||
        sessionState.status == SessionStatus.pinRequired) {
      if (currentPath != '/pin-entry') {
        print('📌 Router: Session requires PIN, redirecting to pin entry');
        return '/pin-entry';
      }
      return null; // Allow pin entry page
    }

    // If session is authenticated, check for checklist requirements
    if (sessionState.status == SessionStatus.authenticated) {
      final isChecklistPath = currentPath == '/daily-checklist';

      if (!isChecklistPath) {
        final prefs = await SharedPreferences.getInstance();
        final lastCheckDate = prefs.getString('last_check_date');
        final today = DateTime.now().toIso8601String().split('T')[0];
        final isSkipped = prefs.getBool('checklist_skipped') ?? false;

        // Check if all checklist items are completed
        bool allCompleted = true;
        for (String item in [
          'Opening Checks',
          'Chilled Storage Checks',
          'Cooking Temperature Checks',
          'Cooling Checks',
          'Reheating Temperature Checks'
        ]) {
          final isCompleted = prefs.getBool('checklist_$item') ?? false;
          if (!isCompleted) {
            allCompleted = false;
            break;
          }
        }

        // Only redirect to checklist if:
        // 1. It's a new day (lastCheckDate != today) AND
        // 2. Checklist wasn't skipped AND
        // 3. Not all items are completed
        if (lastCheckDate != today && !isSkipped && !allCompleted) {
          print('📋 Router: Redirecting to daily checklist');
          return '/daily-checklist';
        }
      }
    }

    print('✅ Router: No redirect needed, allowing access to $currentPath');
    return null;
  },
);
