import 'dart:convert';
import 'lib/models/cart_models.dart';

void main() {
  // Test JSON response from your API
  const testJson = '''
{
  "statusCode": 200,
  "success": true,
  "message": "Cart retrieved successfully",
  "data": {
    "cartId": "c9ac375a-82a2-4f73-ab0e-c7bfaeb8cc8c",
    "staffId": "cd7f2c6f-9fe2-40cd-99ff-974a2a402193",
    "status": "ACTIVE",
    "table": {
      "id": "9c190807-206b-43f2-b95c-0431bcc2c5a3",
      "name": "T4"
    },
    "orderType": {
      "id": "051b160e-4368-4d6d-947f-3e96e7e1dbde",
      "name": "Dine In"
    },
    "customerInfo": null,
    "branch": {
      "id": "5a3aefe8-402e-4a74-ba0b-68a6180b7d04",
      "name": "Portsmouth Branch"
    },
    "notes": null,
    "total": "114.61",
    "miscItems": [],
    "alerts": [],
    "createdAt": "2025-07-26T09:44:19.679Z",
    "updatedAt": "2025-07-26T09:45:19.243Z",
    "cartItems": [
      {
        "cartItemId": "4f37e2d4-cece-4a8f-ac10-ee49a3c550b4",
        "name": "Spicy Chicken Burger",
        "price": "6.79",
        "totalPrice": "6.79",
        "quantity": 1,
        "type": "standard",
        "baseItem": {
          "id": "1708c627-1593-4338-bb43-5450c2676e25",
          "name": "Spicy Chicken Burger",
          "price": "6.79"
        },
        "allergies": null,
        "dishSizes": null,
        "dishExclusions": null,
        "cookingStyles": null,
        "spiciness": null,
        "dishAddons": null,
        "dishExtras": null,
        "dishSides": null,
        "dishBeverages": null,
        "dishDesserts": null,
        "notes": null,
        "createdAt": "2025-07-26T09:44:24.344Z",
        "updatedAt": "2025-07-26T09:44:24.344Z"
      }
    ]
  }
}
''';

  try {
    print('🧪 Testing Cart JSON parsing...');
    
    final Map<String, dynamic> responseData = jsonDecode(testJson);
    final cartData = responseData['data'];
    
    print('📋 Raw cart data:');
    print('  - cartId: ${cartData['cartId']}');
    print('  - table: ${cartData['table']}');
    print('  - orderType: ${cartData['orderType']}');
    
    final cart = Cart.fromJson(cartData);
    
    print('✅ Cart parsed successfully!');
    print('  - Cart ID: ${cart.cartId}');
    print('  - Staff ID: ${cart.staffId}');
    print('  - Status: ${cart.status}');
    print('  - Table: ${cart.table?.name} (ID: ${cart.table?.id})');
    print('  - Order Type: ${cart.orderType?.name} (ID: ${cart.orderType?.id})');
    print('  - Branch: ${cart.branch?.name}');
    print('  - Total: ${cart.total}');
    print('  - Items count: ${cart.items.length}');
    
    if (cart.items.isNotEmpty) {
      print('  - First item: ${cart.items.first.name} (${cart.items.first.quantity}x)');
    }
    
    print('🎉 Test completed successfully!');
    
  } catch (e, stackTrace) {
    print('❌ Test failed with error: $e');
    print('Stack trace: $stackTrace');
  }
}
