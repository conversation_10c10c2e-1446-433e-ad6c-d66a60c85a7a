# EasyDine (Zynk Restaurant Chain Backend)

## Major dependencies

- Server: [Express](https://www.npmjs.com/package/express) **>=** 5
- ORM: [TypeORM](https://www.npmjs.com/package/typeorm)
- Documentation: [Swagger](https://www.npmjs.com/package/swagger-ui-express)/Postman
- Validation: [Zod](https://www.npmjs.com/package/zod)
- Domain Parser: [tldts](https://www.npmjs.com/package/tldts)
- Compression: Express [compression middleware](https://www.npmjs.com/package/compression) with Brotli
- Postgres Driver: [pg](https://www.npmjs.com/package/pg)
- Decorator support (experimental decorators): [reflect-metadata](https://www.npmjs.com/package/reflect-metadata)
- JWT: [jsonwebtoken](https://www.npmjs.com/package/jsonwebtoken)
- Hashing: [Argon2](https://www.npmjs.com/package/argon2) as recommended by OWASP<sup>[1](#owasp-password-storage)</sup>. Bcrypt is no longer recommended for newer projects.

## Installation
- Clone the repo

```bash
git clone https://github.com/Sigainfotech-Group/zynk-admin-backend.git .
```

- Install all the dependencies
```bash
npm i
```

- Install nodemon and ts-node globally if you already don't have them
```bash
npm i -g nodemon ts-node
```

- Get the .env file from [me](mailto:<EMAIL>)

- Run a postgres database locally in your system for testing, either by docker or other ways,
you are not required to create the db. The database will be created automatically by the server if it does not exist.


#### Docker variables required
`POSTGRES_USER`: `your username`

`POSTGRES_PASSWORD`: `your password`

The values for these are present in the .env file

## Get Started

To get started, first read the following documents:

1. [Guide](./docs/guide.md)

2. [About Migrations](./docs/aboutMigration.md)

## CLI Tools

- [Migration_CLI](./src/scripts/migration-cli.ts): Used for creating, generating and running migrations.
For automatic generation, how this works is it compares the models we have in file and the models currently in the
database. It acts more like a diffing tool. It can generate and revert almost all migrations successfully. Do
remember, when you add new models, don't forget to add it to [`entity-config.ts`](./src/config/entity-config.ts).

### Warning:

Also there are some migrations where you need to be careful, for example "Imagine you change a model's attribute called `name` to `sectionName` in file and you save it. Now you try to run the migration cli, it compares the model 
in db and it finds outs in its perspective that the `name` attribute is removed and that you have added a new `sectionName`
attribute, but that's not the case is it? We know we renamed the attribute.

**NOTE:** Sometimes this issue does not occur, and the tool correctly generates the rename query. However, it is always advisable to review your migrations to ensure accuracy.

How to run?

```bash
npm run migration:cli
```

The tool will ask you for the specific action you want to do:
```bash
=== TypeORM Migration CLI ===
1. Create empty migration
2. Generate migration from schema changes
3. Run pending migrations
4. Revert last migration
5. Exit
```

Type the appropriate number to do the changes.

- [Swagger Bundler](./src/scripts/bundle-swagger.ts): This tool is used to bundle all the API docs that we have
written as `.yaml` files into a single `swagger.json` file for presenting documentation at `/reference` route
so that we can easily run and test APIs.

How to run?

```bash
npm run bundle-swagger
```

### References

1. <a name="owasp-password-storage"></a>[OWASP Password Storage Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Password_Storage_Cheat_Sheet.html)