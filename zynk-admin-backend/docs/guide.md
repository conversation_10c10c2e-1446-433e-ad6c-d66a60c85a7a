# Guide

## 1. The Basics

### 1.1 Models

#### Rules to Follow

- All table names for models must be lowercase. Underscores are allowed when combining two words. While using
  underscores is optional, ensuring that the table name is lowercase is mandatory.

To define a table name, use the `@Entity` decorator:

```ts
@Entity("superadmins")
```

All table names must be plural, but there are cases where this is not required, such as for `CompanyInformation`.

- All model names for tables should be uppercase. If two words occur, for example, `superadmin` should be written as
  `SuperAdmin`.

Example:

```ts
@Entity("superadmins")
export class SuperAdmin
```

- Any model you create must extend the top-level base model `EasyDine`, located at `src/models/base/easydine.base.ts`.
  Failure to do so may result in unforeseen issues.

```ts
@Entity("superadmins")
export class SuperAdmin extends EasyDine
```

- Any model you define related to `SuperAdmin` or `Tenant` should be added to their respective entity configuration
  methods located at `src/config/entity-config.ts`. For superadmins, use `getAllSuperAdminModels`; for tenants, use
  `getAllTenantModels`. This ensures that all models are loaded. However, if `synchronize` is ever set to `false` or you
  are making changes to a tenant database, you might need to run migrations, which we will discuss later.

### Some Tips

- If you have a common model or notice similar attributes across multiple models, consider creating a base model or
  class. Extend this base model in your new models to promote reusability and maintainability. However, ensure that the
  base model itself extends the `EasyDine` model. This approach simplifies managing shared attributes and reduces the
  effort required to make changes, as updates to the base model automatically propagate to all derived models.

**_DO NOT DEFINE BASE MODELS/CLASSES WITH THE `@Entity` DECORATOR_**

```ts
export abstract class CompanyInformationBase extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    companyInfoId: string;

    @Column({
        nullable: false,
        unique: true,
    })
    regNumber: string;
...
```

```ts
@Entity("companyinformation")
export class CompanyInformation extends CompanyInformationBase {
  @OneToMany(() => Branch, (branch) => branch.companyInfo)
  branches: Branch[];

  @OneToOne(() => BankDetail, (bankdetail) => bankdetail.companyInfo, {
    cascade: true,
  })
  bankdetail: BankDetail;

  @OneToMany(() => AccountRep, (accountRep) => accountRep.companyInfo)
  accountReps: AccountRep[];
}
```

### 1.2 Ways to Define Models and Their Attributes

- Model attributes can be defined in various ways:

```ts
@Column({
        type: 'text',
        nullable: true,
        default: "https://placehold.co/100x100/png",
})
something: string
```

Type can also be given this way:

```ts
@Column('text', {
        nullable: true,
        default: "https://placehold.co/100x100/png",
})
something: string
```

- Primary keys/generated columns:

Use the `@PrimaryGeneratedColumn()` decorator along with a type. UUID is our preferred choice. Thus,
`@PrimaryGeneratedColumn('uuid')` is the recommended argument for the decorator.

- Timestamps

For timestamps, define all these decorators always:

```ts
@CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

@UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

@DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
```

For timestamps, you must provide the argument `{ type: "timestamptz" }` to store in UTC time with time zone info;
otherwise, it will store in local time without time zone by default.

### 1.3 Models and Relations

How models are defined and how relations work in TypeORM is different compared to other ORMs/solutions.

#### 1.3.1 OneToOne

If it's a one-to-one relation, the owner must define the relation as:

```ts
@OneToOne(() => ChildModel, (childModel) => childModel.parent, {
        cascade: true,
})
childModel: ChildModel;
```

`cascade: true` is recommended in the owner model for cascading operations.

However, in the child model, you must define or provide a field for storing the parent's primary key:

```ts
@Column({ nullable: true })
parentId: string;

@OneToOne(() => Parent, (parent) => parent.childModel, {
        onDelete: "CASCADE",
})
@JoinColumn({ name: "parentId" })
parent: Parent;
```

As you can see, when defining the inverse relation on the child, we use the `@JoinColumn` decorator to store the
`parentId` in the child or belonging model. We must also enable `onDelete: "CASCADE"` for cascading operations such as
delete to occur database-wise. If you define `cascade: true` on one side, like in the parent's case, it won't take
effect on the database as a whole, as it is more specific to TypeORM.

#### 1.3.2 OneToMany and ManyToOne

In the owner, we must define using the `@OneToMany` decorator:

```ts
  @OneToMany(() => ChildModel, (childModel) => childModel.parent, {
    cascade: true,
  })
  childModels: ChildModel[];
```

Here `cascade: true` is not compulsory but can be helpful for cascading operations.

However, in the child or belonging model, `onDelete: "CASCADE"` is recommended.

```ts
@ManyToOne(() => Parent, (parent) => parent.childModels, {
    onDelete: "CASCADE",
})
parent: Parent;
```

This time, we don't need to use the `@JoinColumn` decorator or anything of the sort since this is a
one-to-many/many-to-one relation, and as such, IDs are stored in an array-like structure. `@JoinColumn` could still be
given
if you want to store id directly instead, but its optional.

#### 1.3.3 ManyToMany / Junction tables

To create many to many relations such that a junction table exists to know what belongs to what,
we can use the `@ManyToMany` decorator.

```ts
@Entity()
export class Category {
    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column()
    name: string
}
```

```ts
@Entity()
export class Question {
    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column()
    title: string

    @Column()
    text: string

    @ManyToMany(() => Category)
    @JoinTable()
    categories: Category[]
}
```

In owning side of relation, we must put the `@JoinColumn`, the resulting structure would be:

```bash
+-------------+--------------+----------------------------+
|                        category                         |
+-------------+--------------+----------------------------+
| id          | uuid         | PRIMARY KEY                |
| name        | varchar(255) |                            |
+-------------+--------------+----------------------------+

+-------------+--------------+----------------------------+
|                        question                         |
+-------------+--------------+----------------------------+
| id          | uuid         | PRIMARY KEY                |
| title       | varchar(255) |                            |
| text        | varchar(255) |                            |
+-------------+--------------+----------------------------+

+-------------+--------------+----------------------------+
|              question_categories_category               |
+-------------+--------------+----------------------------+
| questionId  | uuid         | PRIMARY KEY FOREIGN KEY    |
| categoryId  | uuid         | PRIMARY KEY FOREIGN KEY    |
+-------------+--------------+----------------------------+
```

***IMPORTANT: However, the `@ManyToMany` decorator is only useful in cases where we only need to store the IDs of two
models. If we want to store an extra attribute, for example, a dish can have multiple ingredients, and we also need to
know how much of each ingredient is used in that dish, we should explicitly create a junction table.***

An example of this is the [`dish_ingredients`](../src/models/foodmenu/dish_ingredient.model.ts) model:

```ts
export class Dish extends EasyDine {
    @OneToMany(() => DishIngredient, (di) => di.dish, {
        cascade: true,
    })
    dishIngredients: DishIngredient[];
}
```

```ts
@Entity("dish_ingredients")
export class DishIngredient {
    @PrimaryGeneratedColumn("uuid")
    dishIngredientId: string;

    @Column("decimal", { scale: 2, nullable: false })
    amount: number;

    @ManyToOne(() => Dish, (dish) => dish.dishIngredients, {
        onDelete: "CASCADE",
    })
    dish: Dish;

    @ManyToOne(() => Ingredient, {
        eager: true,
        onDelete: "CASCADE",
    })
    ingredient: Ingredient;

    @CreateDateColumn({ type: "timestamptz" })
    createdAt: Date;

    @UpdateDateColumn({ type: "timestamptz" })
    updatedAt: Date;

    @DeleteDateColumn({ type: "timestamptz" })
    deletedAt?: Date;
}
```

Here, as we can see, an extra attribute `amount` was needed. Hence, we needed to define a relation to another model that
holds references to both the dish and the ingredient models.

### 1.4 Repositories

To create, save, or update model structures/instances, we need access to their repositories from their entity managers,
which come from a `DataSource` (a connection in the context of TypeORM).

#### 1.4.1 The `QueryRunner` class

Think of query runners as a query builder to a data source that helps us connect, start, commit, and rollback
transactions. It also helps us manage repositories and other information related to the database connection.

Thankfully, I have already defined the `rootQueryRunner` and `queryRunner` attributes in the request parameter for
Express, allowing us to easily manage and use different data sources in the case of multiple tenants.

#### 1.4.2 The `rootQueryRunner` attribute

This property provides access to the query runner specifically configured for operations on the super admin database. It
is typically used for administrative tasks and operations that require access to the root database. You probably won't
use this often; however, just know that if it exists in any controller or middleware, or if you see the `root` keyword,
it is most likely referring to the super admin data source.

#### 1.4.3 The `queryRunner` attribute

This is the property you will most likely use for tenant-specific operations on the database. Whenever a request comes
in, we automatically extract the tenant information and create an instance of the `queryRunner` from the correct data
source, it is cached by default in memory (check `initializer.middleware.ts` for more information). This way, you only
need to connect, start a transaction, make your changes, and commit them to the database.

#### 1.4.4 Getting the model repo

To access a model repo, you must use the `getRepositories(qRunner)` where qRunner is the query runner from which you
want to access a model. This is not a built-in method but defined by me for easy access to repositories from the entity
manager. Be careful when using it, as you might run into trouble if you accidentally pass the wrong query runner or make
a typo when extracting a model.

```ts

const qRunner = req.queryRunner
await qRunner.connect()
await qRunner.startTransaction()

const {Model1, Model2} = getRepositories(qRunner) as {
    Model1: Repository<Model1>,
    Model2: Repository<Model2>
}
```

Since we are using TypeScript, we must also cast and tell the compiler that `Model1` and `Model2` are actually
repositories of models. This way, we get IntelliSense and can use and look up all attributes and methods the model
offers.

#### 1.4.5 Creation, Updation and Deletion

Unlike other ORMs and solutions, creations do not directly commit to the database or happen immediately.  
We must first construct an object attribute by attribute, then save and commit to actually make the records appear.  
This means the create and update methods are not asynchronous; they do not save to the database but only construct the
object and apply changes to it.

```ts
const newModel1 = Model1.create({
    ...somedata
})
```

As you can see we are not using `await` or something like that to save.

To actually save, we use the Repo directly with the newly constructed object like:

```ts
await Model1.save(newModel1)
```

Same goes with updations:

```ts
exModel.update({
    ...somedata
})

await Model1.save(exModel)
```

For deletions, there are two methods, the preferred way is `remove()` which hard deletes the data,
you can also use soft delete, which is `softRemove()`, but to use `softRemove`, you must include
the `deletedAt` column.

```ts
await Model1.remove(newModel1)
```

NOTE: You can also do operations directly with the repo instead of model instance, but you will require the ID of the
instance tho.

### 1.4.6 Controller Management

First get the query runner, connect.

- Start a transaction only if you are going to commit changes

```ts
// get query runner
const qRunner = req.queryRunner

// connect
await qRunner.connect()

// start transaction
await qRunner.startTransaction()
```

Now we can start actually writing the controller

- Start with try, at end of try put the commit transaction

```ts
try{
    await qRunner.commitTransaction()
}
```

- In catch, rollback the transaction and throw the error

```ts
catch(error){
    await qRunner.rollbackTransaction()
    next(error)
}
```

- It's not over, we must also release the query runner after the controller has finished all its logic.

```ts
finally{
    await qRunner.release()
}
```

The final structure will look like:

```ts
export const controller = async (req: Request, res: Response, next: NextFunction) => {
    // get query runner
    const qRunner = req.queryRunner

    // connect
    await qRunner.connect()

    // start transaction
    await qRunner.startTransaction()

    try{
        // some changes
        await qRunner.commitTransaction()

        // return response
        ResponseHandler.success(res, status code, message, data)
    }
    catch(error){
        await qRunner.rollbackTransaction()
        next(error)
    }
    finally{
        await qRunner.release()
    }
}
```

### 1.4.7 Cascading operations

Due to enabling `cascade: true` in parent models, we can construct the parent along with its child or belonging models
in a single swoop instead of writing create method for each and every model.

An example would be:

```ts
const newTenant = Tenant.create({
      name,
      emailAddress,
      subDomain,
      phoneNumber,
      status,
      activeFrom,
      activeTill,
    tenantAuth: TenantAuth.create({
        password: hashedPass,
    }),
    tenantInfo: CompanyInformation.create({
        regNumber: regNo,
        name: companyName,
        businessType,
        addrLine1,
        addrLine2,
        city,
        state,
        postalCode,
        country,
        taxIDNumber: taxIdentificationNumber,
        description,
        businessAlias,
        regDocument,
        logoUrl,
    }),
});

```

Here `tenant` is the only model repo we are accessing at the top level, the attributes like `tenantAuth` and
`tenantInfo` are referring to other models, but since cascading is true, we can create all of the related models in a
single step.

And save works normally like any other model would:

```ts
await Tenant.save(newTenant)
```

# 2. Structure

## 2.1 The constants folder

Messages you normally use within your controllers/middlewares can be quite common. Instead, we can make them constants
and store them elsewhere for reusability and easy access. Therefore, it is recommended to store all your errors in a
file called `err.ts` in the model-specific folder in the `src/constants` folder. If your model is related to the tenant
database, it is wise to put it inside the tenant folder and create a folder for your model as well inside. Just like
errors, store your successful messages in `msg.ts`.

### 2.1.1 Throwing errors

To throw errors, call the `errorHandler` function and return its output. `errorHandler` returns an instance of [
`APIError`](../src/utils/errorHandler.ts)
which is then caught by the `serverErrorHandler` middleware. The `errorHandler` takes two parameters, first is the
status code, all status codes are already stored in [`STATUS_CODES.ts`](../src/constants/STATUS_CODES.ts) file,
you just need to import them. The second parameter takes the actual message, which you would need to define
in an `err.ts` for your specific model.

You need to pass the `errorHandler` to the next function.

```ts
return next(errorHandler(STATUS_CODE, YOUR_MESSAGE))
```

For errors that occur on `catch`, just use next normally:

```ts
next(error)
```

## 2.2 Migrations (***VERY IMPORTANT***)

To start with migrations, check out the [Guide on migrations](./aboutMigration.md).
When you are done with previous migrations, you can store them in archive folder located at `src/migrations/archive`

## 2.3 Master entries/Reference data

When the backend starts for the first time, it creates a database called `template_tenant`, which contains all the
models and master entries for easy and common tenant creation. This helps speed up the tenant creation process and
serves as a template or blueprint for all tenant databases.

However, later on we will add many more master entries and as such we might need to seed more data into tenant template.
For this we need to create seeds at `src/seeds` folder which takes a query runner and creates the appropriate entries in
the template. After creating the seed, we put it like this:

```ts
try {
    await seedSectionIcons(queryRunner);
    // put more seeds below

    await queryRunner.commitTransaction();
} catch (error) {
...
```

Here `seedSectionIcons` is a seed method to create default master entries for section icons. Similarly we can create our
own seed for the master entry and load it in `initializeTemplateStructure` located at `src/config/templateConfig.ts`.

Check out [Section Icon Seed](../src/seeds/templateSeeds/sectionIcon.seed.ts) for an example seed.

### Updating seeds

Suppose you have created new master/reference data routes. To update the template or tenant database you are currently
working on with sample data, you can create routes and add these manually, but that is time-consuming. Instead, there's
a utility called 'update-seeds' that allows you to update seeds on both tenant and template databases.

How to run?

```ts
npm run update-seeds
```

This will prompt you to specify where to run the seed update:

```
Where do you want to run the seeds? (tenant/template): tenant
Enter the tenant name (e.g., 'tenant1'): easydine
...
```

## 2.4 API Documentation

For doing documentations, which is recommended but not a high priority now, check
out [Sample Super Admin Doc](../src/openapi/paths/superAdmin.yaml)

To compile newly written doc, run command:

```bash
npm run bundle-swagger
```