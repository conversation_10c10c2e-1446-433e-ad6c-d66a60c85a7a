# TypeORM Migration Integration Guide

This guide explains how to use the migration system to manage database schema changes for both SuperAdmin and tenant databases.

## Overview

First to enable migrations, enable `MIGRATION_TOGGLE` in .env file.

Instead of using `synchronize: true` (which is dangerous in production), we now use TypeORM migrations to safely manage database schema changes. The migration system is set up to handle:

1. SuperAdmin database migrations
2. Tenant-specific database migrations
3. Migration generation, execution, and rollback

## Setup

The migration system consists of several components:

- `MigrationManager.ts`: Core functions for managing migrations
- `migration-cli.ts`: Interactive CLI tool for creating and running migrations
- Updated DataSource configuration files

## Directory Structure

```
src/
  db/
    migrations/
      MigrationManager.ts
      superadmin/
        # SuperAdmin database migrations
      tenant/
        # Tenant database migrations
    datasource.ts
  scripts/
    migration-cli.ts
```

## Usage

### Using the CLI Tool

Run the CLI tool:

```bash
npm run migration:cli
```

This provides an interactive menu with options:
1. Create an empty migration
2. Generate a migration from schema changes
3. Run pending migrations
4. Revert the last migration

**NOTE: It is always advisable to review your migrations to ensure accuracy.**

### Using NPM Scripts

The following npm scripts are available:

- `npm run migration:create -- src/db/migrations/[superadmin|tenant]/MigrationName` - Create an empty migration
- `npm run migration:generate:superadmin -- src/db/migrations/superadmin/MigrationName` - Generate a SuperAdmin migration
- `npm run migration:run:superadmin` - Run pending SuperAdmin migrations
- `npm run migration:revert:superadmin` - Revert the last SuperAdmin migration

### Manually Creating Migrations

Create migrations using the CLI tool or by directly creating files in the appropriate directory:

- `src/db/migrations/superadmin/` for SuperAdmin database
- `src/db/migrations/tenant/` for tenant databases

## Best Practices

1. **Never use `synchronize: true` in production**
2. **Always test migrations on a copy of production data**
3. **Always include a working `down` method to revert changes**
4. **Commit migrations to version control**
5. **Run migrations as part of your deployment process**

## Common Migration Tasks

### Adding a new column

```typescript
public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "email" character varying NOT NULL`);
}

public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "email"`);
}
```

### Creating a new table

```typescript
public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE "product" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
            "name" character varying NOT NULL,
            "price" numeric(10,2) NOT NULL,
            CONSTRAINT "PK_product_id" PRIMARY KEY ("id")
        )
    `);
}

public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "product"`);
}
```

### Adding a foreign key

```typescript
public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE "order" 
        ADD CONSTRAINT "FK_order_user" 
        FOREIGN KEY ("user_id") 
        REFERENCES "user"("id") 
        ON DELETE CASCADE
    `);
}

public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" DROP CONSTRAINT "FK_order_user"`);
}
```

## Handling Tenant Migrations

When a new tenant is created, you should:

1. Create the tenant database
2. Run all tenant migrations

Example:

```typescript
import { ensureTenantDatabaseExists } from "../db/datasource";
import { runTenantMigrations } from "../db/migrations/MigrationManager";

async function createNewTenant(tenantName) {
  // Create tenant database
  await ensureTenantDatabaseExists(tenantName);
  
  // Run all tenant migrations
  await runTenantMigrations(tenantName);
  
  // Continue with tenant setup...
}
```

## Troubleshooting

- **Migration fails**: Check the error message and fix the migration file
- **Migration runs but database is incorrect**: Use `migration:revert` to roll back and fix the migration
- **Database and entity mismatch**: Generate a new migration to fix the discrepancy

## Additional Resources

- [TypeORM Migration Documentation](https://typeorm.io/#/migrations)
- [Database Migration Best Practices](https://www.prisma.io/dataguide/types/relational/what-are-database-migrations)