import {OperationType, PermActionModelType} from "../../types/pBAC";
import {PermissionAction} from "../../models/pBAC/PermissionAction";
import {Repository} from "typeorm";
import {formatUNKNOWNPermissionsActions} from "./formatUNKNOWNPermsActions.helper";
import {FormatType} from "../../types/pBAC/formatterTypes";

interface OperationResult {
    success: boolean;
    updatedUnknownPermissions: any[];
    errors: string[];
    warnings: string[];
}

interface ProcessPermissionActionsOptions {
    formatType?: FormatType;
    requiredActive?: boolean;
}

interface ProcessPermissionActionsResult {
    success: boolean;
    formattedPermissions: any;
    errors: string[];
    warnings: string[];
}

export const updatePermissionAction = async (
    permissionActionIds: PermActionModelType[],
    rolePermissionsActions: any[],
    userPermissionsActions: any[],
    userId: string,
    userKey: string,
    userIdKey: string,
    UserPermissionAction: Repository<any>,
    permissionAction: Repository<any>
): Promise<OperationResult> => {

    const result: OperationResult = {
        success: true,
        updatedUnknownPermissions: [...userPermissionsActions],
        errors: [],
        warnings: []
    };

    const entitiesToSave: any[] = [];

    try {
        if (!Array.isArray(permissionActionIds) || permissionActionIds.length === 0) {
            result.errors.push("permissionActionIds must be a non-empty array");
            result.success = false;
            return result;
        }

        const validRequests: PermActionModelType[] = [];
        const seenPairs = new Set<string>();

        for (let i = permissionActionIds.length - 1; i >= 0; i--) {
            const request = permissionActionIds[i];

            if (!request.actionId || !request.permissionId) {
                result.warnings.push(`Missing actionId or permissionId at index ${i}`);
                continue;
            }

            if (!Object.values(OperationType).includes(request.operationType)) {
                result.warnings.push(`Invalid operationType at index ${i}: ${request.operationType}`);
                continue;
            }

            const pairKey = `${request.actionId}:${request.permissionId}`;
            if (seenPairs.has(pairKey)) {
                result.warnings.push(`Duplicate actionId/permissionId pair found: ${pairKey}, keeping last occurrence`);
                continue;
            }

            seenPairs.add(pairKey);
            validRequests.unshift(request);
        }

        if (validRequests.length === 0) {
            result.errors.push("No valid permission action requests found");
            result.success = false;
            return result;
        }

        const availablePermissionActionsMap = new Map<string, { id: string, action: any, permission: any }>();

        for (const request of validRequests) {
            try {
                const foundPermission = await permissionAction.findOne({
                    where: {
                        action: {actionId: request.actionId},
                        permission: {permissionId: request.permissionId},
                    },
                    relations: ['action', 'permission']
                });
                if (foundPermission &&
                    foundPermission.action.isActive &&
                    foundPermission.permission.isActive) {
                    const pairKey = `${request.actionId}:${request.permissionId}`;
                    availablePermissionActionsMap.set(pairKey, {
                        id: foundPermission.pAId,
                        action: foundPermission.action,
                        permission: foundPermission.permission
                    });
                }
            } catch (error) {
                result.warnings.push(`Error validating permission action for actionId: ${request.actionId}, permissionId: ${request.permissionId}`);
            }
        }

        const validatedRequests = validRequests.filter(request => {
            const pairKey = `${request.actionId}:${request.permissionId}`;
            if (!availablePermissionActionsMap.has(pairKey)) {
                result.warnings.push(`Permission action not found or inactive for actionId: ${request.actionId}, permissionId: ${request.permissionId}`);
                return false;
            }
            return true;
        });

        if (validatedRequests.length === 0) {
            result.errors.push("No valid and active permission actions found");
            result.success = false;
            return result;
        }

        const rolePermissionActionsMap = new Map();
        rolePermissionsActions?.forEach(rolePerm => {
            const key = rolePerm.permissionActionId || rolePerm.id;
            if (key) {
                rolePermissionActionsMap.set(key, rolePerm);
            }
        });

        const unknownPermissionActionsMap = new Map();
        result.updatedUnknownPermissions.forEach((unknown, index) => {
            const key = unknown.permissionActionId || unknown.id;
            if (key) {
                unknownPermissionActionsMap.set(key, {...unknown, index});
            }
        });

        for (const request of validatedRequests) {
            const pairKey = `${request.actionId}:${request.permissionId}`;
            const permissionActionId = availablePermissionActionsMap.get(pairKey)?.id;
            const operationType = request.operationType;

            const existingEntity = await UserPermissionAction.findOne({
                where: {
                    permissionAction: {pAId: permissionActionId},
                    [userKey]: {[userIdKey]: userId}
                }
            });

            if (operationType === OperationType.PLUS) {
                const existingUnknown = unknownPermissionActionsMap.get(permissionActionId);
                if (existingUnknown && existingUnknown.operationType === OperationType.MINUS) {
                    result.updatedUnknownPermissions[existingUnknown.index].operationType = OperationType.PLUS;
                    unknownPermissionActionsMap.set(permissionActionId, {
                        ...existingUnknown,
                        operationType: OperationType.PLUS
                    });
                    result.warnings.push(`Changed operation from MINUS to PLUS for permission ${permissionActionId}`);
                    if (existingEntity) {
                        existingEntity.operationType = OperationType.PLUS;
                        existingEntity.updatedAt = new Date();
                        entitiesToSave.push(existingEntity);
                    }
                } else if (rolePermissionActionsMap.has(permissionActionId)) {
                    result.warnings.push(`Permission ${permissionActionId} already granted via role`);
                } else if (existingUnknown) {
                    result.warnings.push(`PermissionActionID${permissionActionId} already pending for addition`);
                } else {
                    const newEntry = {
                        permissionActionId: permissionActionId,
                        operationType: OperationType.PLUS,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    };
                    result.updatedUnknownPermissions.push(newEntry);
                    unknownPermissionActionsMap.set(permissionActionId, {
                        ...newEntry,
                        index: result.updatedUnknownPermissions.length - 1
                    });
                    if (existingEntity) {
                        existingEntity.operationType = OperationType.PLUS;
                        existingEntity.updatedAt = new Date();
                        entitiesToSave.push(existingEntity);
                    } else {
                        const newEntity = UserPermissionAction.create({
                            permissionAction: {pAId: permissionActionId},
                            [userKey]: {[userIdKey]: userId},
                            operationType: OperationType.PLUS,
                        });
                        entitiesToSave.push(newEntity);
                    }
                }

            } else if (operationType === OperationType.MINUS) {
                if (!rolePermissionActionsMap.has(permissionActionId)) {
                    const existingUnknown = unknownPermissionActionsMap.get(permissionActionId);
                    if (existingUnknown) {
                        if (existingUnknown.operationType === OperationType.PLUS) {
                            result.updatedUnknownPermissions[existingUnknown.index].operationType = OperationType.MINUS;
                            unknownPermissionActionsMap.set(permissionActionId, {
                                ...existingUnknown,
                                operationType: OperationType.MINUS
                            });
                            result.warnings.push(`Changed operation from PLUS to MINUS for permission ${permissionActionId}`);
                            if (existingEntity) {
                                existingEntity.operationType = OperationType.MINUS;
                                existingEntity.updatedAt = new Date();
                                entitiesToSave.push(existingEntity);
                            }
                        } else {
                            result.warnings.push(`Permission ${permissionActionId} already pending for removal`);
                        }
                    } else {
                        const newEntry = {
                            permissionActionId: permissionActionId,
                            operationType: OperationType.MINUS,
                            createdAt: new Date(),
                            updatedAt: new Date()
                        };
                        result.updatedUnknownPermissions.push(newEntry);
                        unknownPermissionActionsMap.set(permissionActionId, {
                            ...newEntry,
                            index: result.updatedUnknownPermissions.length - 1
                        });
                        if (existingEntity) {
                            existingEntity.operationType = OperationType.MINUS;
                            existingEntity.updatedAt = new Date();
                            entitiesToSave.push(existingEntity);
                        } else {
                            const newEntity = UserPermissionAction.create({
                                permissionAction: {pAId: permissionActionId},
                                [userKey]: {[userIdKey]: userId},
                                operationType: OperationType.MINUS,
                            });
                            entitiesToSave.push(newEntity);
                        }
                    }
                } else {
                    const existingUnknown = unknownPermissionActionsMap.get(permissionActionId);
                    if (existingUnknown) {
                        if (existingUnknown.operationType === OperationType.PLUS) {
                            result.updatedUnknownPermissions[existingUnknown.index].operationType = OperationType.MINUS;
                            unknownPermissionActionsMap.set(permissionActionId, {
                                ...existingUnknown,
                                operationType: OperationType.MINUS
                            });
                            if (existingEntity) {
                                existingEntity.operationType = OperationType.MINUS;
                                existingEntity.updatedAt = new Date();
                                entitiesToSave.push(existingEntity);
                            }
                        } else {
                            result.warnings.push(`Permission ${permissionActionId} already pending for removal`);
                        }
                    } else {
                        const newEntry = {
                            permissionActionId: permissionActionId,
                            operationType: OperationType.MINUS,
                            createdAt: new Date(),
                            updatedAt: new Date()
                        };
                        result.updatedUnknownPermissions.push(newEntry);
                        unknownPermissionActionsMap.set(permissionActionId, {
                            ...newEntry,
                            index: result.updatedUnknownPermissions.length - 1
                        });

                        if (existingEntity) {
                            existingEntity.operationType = OperationType.MINUS;
                            existingEntity.updatedAt = new Date();
                            entitiesToSave.push(existingEntity);
                        } else {
                            const newEntity = UserPermissionAction.create({
                                permissionAction: {pAId: permissionActionId},
                                [userKey]: {[userIdKey]: userId},
                                operationType: OperationType.MINUS,
                            });
                            entitiesToSave.push(newEntity);
                        }
                    }
                }
            }
        }

        if (entitiesToSave.length > 0) {
            await UserPermissionAction.save(entitiesToSave);
        }

    } catch (error) {
        result.success = false;
        result.errors.push(`Unexpected error: ${(error as any).message}`);
        throw error;
    }

    return result;
};


export const processPermissionActions = (
    rolePermissionActions: any[],
    unknownPermissionActions: any[],
    options: ProcessPermissionActionsOptions = {}
): ProcessPermissionActionsResult => {
    const {
        formatType = FormatType.STRING_STRING,
        requiredActive = true
    } = options;

    const result: ProcessPermissionActionsResult = {
        success: true,
        formattedPermissions: null,
        errors: [],
        warnings: []
    };

    try {
        const clonedRolePermissions = rolePermissionActions ?
            JSON.parse(JSON.stringify(rolePermissionActions)) : [];
        const clonedUnknownPermissions = unknownPermissionActions ?
            JSON.parse(JSON.stringify(unknownPermissionActions)) : [];

        interface UserPermissionAction {
            id: string;
            permissionAction: PermissionAction;
            operationType?: OperationType;
        }

        const userPermissions: UserPermissionAction[] = clonedRolePermissions.map((rp: any) => ({
            id: rp.id,
            permissionAction: rp.permissionAction
        }));

        if (Array.isArray(clonedUnknownPermissions) && clonedUnknownPermissions.length > 0) {
            const validUnknownPermissions = clonedUnknownPermissions.filter(unknown => {
                if (!unknown.permissionAction?.pAId) {
                    result.warnings.push(`Invalid permissionActionId in unknown permission`);
                    return false;
                }
                if (!Object.values(OperationType).includes(unknown.operationType)) {
                    result.warnings.push(`Invalid operationType: ${unknown.operationType}`);
                    return false;
                }
                return true;
            });

            for (const unknownPerm of validUnknownPermissions) {
                const permissionActionId = unknownPerm.permissionAction.pAId;
                const operationType = unknownPerm.operationType;

                if (operationType === OperationType.PLUS) {
                    userPermissions.push({
                        id: unknownPerm.id,
                        permissionAction: unknownPerm.permissionAction,
                        operationType: OperationType.PLUS
                    });
                } else if (operationType === OperationType.MINUS) {
                    const existingIndex = userPermissions.findIndex(up =>
                        up.permissionAction.pAId === permissionActionId
                    );
                    if (existingIndex !== -1) {
                        userPermissions.splice(existingIndex, 1);
                    } else {
                        result.warnings.push(`Permission ${permissionActionId} not found in role to remove`);
                    }
                }
            }
        }

        result.formattedPermissions = formatUNKNOWNPermissionsActions(
            userPermissions,
            formatType,
            requiredActive
        );

    } catch (error: any) {
        result.success = false;
        result.errors.push(`Processing error: ${error.message}`);

        try {
            result.formattedPermissions = formatUNKNOWNPermissionsActions(
                rolePermissionActions || [],
                formatType,
                requiredActive
            );
        } catch (formatterError) {
            result.formattedPermissions = null;
        }
    }

    return result;
};