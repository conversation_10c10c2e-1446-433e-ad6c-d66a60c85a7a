import {FormatType} from "../../types/pBAC/formatterTypes";


const filterValidPermissionActions = (permissionsActions: any, requiredActive: boolean) => {
    if (!Array.isArray(permissionsActions)) {
        return [];
    }

    return permissionsActions.filter(item => {
        if (!item || !item.permissionAction) {
            return false;
        }

        const permission = item.permissionAction.permission;
        const action = item.permissionAction.action;

        if (!permission || !action) {
            return false;
        }

        if (requiredActive) {
            return permission.isActive && action.isActive;
        }

        return true;
    });
};

const FORMAT_STRING_STRING = (permissionsActions: any, requiredActive: boolean) => {
    const filteredItems = filterValidPermissionActions(permissionsActions, requiredActive);

    return filteredItems.map(item => {
        const permission = item.permissionAction.permission;
        const action = item.permissionAction.action;
        return `${permission.name}:${action.name}`;
    });
};

const FORMAT_OBJECT_ARR_OBJECT = (permissionsActions: any, requiredActive: boolean) => {
    const filteredItems = filterValidPermissionActions(permissionsActions, requiredActive);

    const groupedByPermission: any = {};

    filteredItems.forEach(item => {
        const permission = item.permissionAction.permission;
        const action = item.permissionAction.action;

        if (!groupedByPermission[permission.name]) {
            groupedByPermission[permission.name] = {
                ...permission,
                actions: []
            };
        }

        groupedByPermission[permission.name].actions.push(action);
    });
    return Object.values(groupedByPermission);
};

const FORMAT_OBJECT_ARR_STRING = (permissionsActions: any, requiredActive: boolean) => {
    const filteredItems = filterValidPermissionActions(permissionsActions, requiredActive);

    const groupedByPermission: any = {};

    filteredItems.forEach(item => {
        const permission = item.permissionAction.permission;
        const action = item.permissionAction.action;

        if (!groupedByPermission[permission.name]) {
            groupedByPermission[permission.name] = {
                ...permission,
                actions: []
            };
        }

        groupedByPermission[permission.name].actions.push(action.name);
    });
    return Object.values(groupedByPermission);
};

const FORMAT_STRING_ARR_STRING = (permissionsActions: any, requiredActive: boolean) => {
    const filteredItems = filterValidPermissionActions(permissionsActions, requiredActive);

    const groupedByPermission: any = {};

    filteredItems.forEach(item => {
        const permission = item.permissionAction.permission;
        const action = item.permissionAction.action;

        if (!groupedByPermission[permission.name]) {
            groupedByPermission[permission.name] = [];
        }

        groupedByPermission[permission.name].push(action.name);
    });
    return Object.entries(groupedByPermission).map(([permissionName, actions]) => ({
        permissionName,
        actions
    }));
};

const FORMAT_ALL = (unknownPermissionsActions: any, requiredActive: boolean) => {
    return {
        STRING_STRING: FORMAT_STRING_STRING(unknownPermissionsActions, requiredActive),
        OBJECT_ARR_OBJECT: FORMAT_OBJECT_ARR_OBJECT(unknownPermissionsActions, requiredActive),
        OBJECT_ARR_STRING: FORMAT_OBJECT_ARR_STRING(unknownPermissionsActions, requiredActive),
        STRING_ARR_STRING: FORMAT_STRING_ARR_STRING(unknownPermissionsActions, requiredActive),
    }
}
const formatters = {
    [FormatType.STRING_STRING]: FORMAT_STRING_STRING,
    [FormatType.OBJECT_ARR_OBJECT]: FORMAT_OBJECT_ARR_OBJECT,
    [FormatType.OBJECT_ARR_STRING]: FORMAT_OBJECT_ARR_STRING,
    [FormatType.STRING_ARR_STRING]: FORMAT_STRING_ARR_STRING,
    [FormatType.ALL]: FORMAT_ALL,
};
export const formatUNKNOWNPermissionsActions = (
    unknownPermissionsActions: any[],
    formatType: FormatType = FormatType.STRING_STRING,
    requiredActive: boolean = true
) => formatters[formatType](unknownPermissionsActions, requiredActive);

export const formatUNKNOWNPermissionsActionsList = (
    hasUnknownPermissionsActions: any[],
    key: string,
    formatType: FormatType = FormatType.STRING_STRING,
    requiredActive: boolean = true
) => {
    for (const unknownPermissionsActions of hasUnknownPermissionsActions) {
        if (unknownPermissionsActions && key in unknownPermissionsActions) {
            const formattedData = formatUNKNOWNPermissionsActions(
                unknownPermissionsActions[key],
                formatType,
                requiredActive
            );

            delete unknownPermissionsActions[key];
            unknownPermissionsActions.permissionsActions = formattedData;
        }
    }
    return hasUnknownPermissionsActions;
};