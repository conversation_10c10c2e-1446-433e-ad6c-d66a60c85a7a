import {NextFunction} from "express";
import {QueryRunner, Repository} from "typeorm";
import {APIError, errorHandler} from "../../utils/errorHandler";
import {INTERNAL_SERVER_ERROR, NOT_FOUND} from "../../constants/STATUS_CODES";
import {getRepositories} from "./RepositoryHelper.helper";
import {CompanyInformation} from "../../models/company/companyinformation.model";
import {Branch} from "../../models/company/branch.model";
import {NO_PRIMARY_BRANCH} from "../../constants/tenant/company/err";
import {PrefBranch, PrefUserType} from "../../models/common/prefbranch.model";

export const createCompanyRecordsInTenant = async (
    tenantQRunnner: QueryRunner,
    next: NextFunction,
    data: any,
    tenantId: string
) => {
    try {
        await tenantQRunnner.connect();
        await tenantQRunnner.startTransaction();

        const {CompanyInformation, Branch, PrefBranch} = getRepositories(tenantQRunnner) as {
            CompanyInformation: Repository<CompanyInformation>;
            Branch: Repository<Branch>;
            PrefBranch: Repository<PrefBranch>
        };

        const allBranches = await Branch.find({
            order: {createdAt: "ASC"},
        });

        let primaryBranch: Branch | undefined;

        if (allBranches.length > 0) {
            primaryBranch = allBranches[0]
        } else {
            throw new APIError(NOT_FOUND, NO_PRIMARY_BRANCH);
        }

        const newCompanyInfo = CompanyInformation.create({
            ...data,
            branches: [primaryBranch]
        });

        await CompanyInformation.save(newCompanyInfo);

        const newPrefBranch = PrefBranch.create({
            prefBranchId: primaryBranch.branchId,
            userId: tenantId,
            userType: PrefUserType.TENANT
        })

        await PrefBranch.save(newPrefBranch)

        await tenantQRunnner.commitTransaction();
    } catch (error) {
        await tenantQRunnner.rollbackTransaction();
        return next(
            errorHandler(
                INTERNAL_SERVER_ERROR,
                error instanceof Error ? error.message : "Unknown error"
            )
        );
    } finally {
        await tenantQRunnner.release();
    }
};
