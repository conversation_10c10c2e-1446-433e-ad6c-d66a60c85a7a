import {JSONSchema4} from "json-schema";
import {jsonSchemaToZod} from "../../utils/jsonSchemaToZod";

export const handlebarsHelpers = {
    toLowerCase: (str: string): string => str.toLowerCase(),
    formatDate: (date: string | Date): string =>
        new Date(date).toLocaleString('en-IN', {timeZone: 'Asia/Kolkata'}),
    JSONstringify: (obj: any, indent: number = 0): string => {
        try {
            return JSON.stringify(obj, null, indent);
        } catch (e) {
            return '';
        }
    },
    getDataType: (data: any): string => {
        if (Array.isArray(data)) return 'List';
        if (typeof data === 'object' && data !== null) return 'Dictionary';
        if (typeof data === 'string') return 'Text';
        return 'Unknown';
    },
    toCamelCase: (str: string): string =>
        str.replace(/([-_][a-z])/g, (group) =>
            group.toUpperCase().replace('-', '').replace('_', '')
        ),
    zod: (schema: JSONSchema4, title: string): string => {
        if (!schema || !schema.properties) return `${title}: z.any()`;
        const zodString = jsonSchemaToZod(schema);
        return `${title}:\n${zodString}`;
    },
};