import { Response } from "express";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./JWTHelper.helper";

export const setCookie = (res: Response, payload: Object) => {
  // clearCookie(res); this way only one header will be sent
  res.cookie(
    (process.env.ACCESS_TOKEN_NAME as string) || "access-token",
    `Bearer ${JWTHelper.generateToken(payload)}`,
    {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: parseInt(process.env.ACCESS_TOKEN_MAX_AGE || "7200000"),
      sameSite: "lax",
      signed: true
    }
  );
};

export const clearCookie = (res: Response) => {
  res.clearCookie((process.env.ACCESS_TOKEN_NAME as string) || "access-token", { signed: true });
};
