import jwt, {SignOptions} from "jsonwebtoken";
import {JWTPayload} from "../../../types/jwt";
import {APIError} from "../../utils/errorHandler";
import {UNAUTHORIZED} from "../../constants/STATUS_CODES";
import {loadEnv} from "../../config/envConfig";

loadEnv();

class JWTHelper {
    private static secret: string =
        (process.env.JWT_SECRET as string) || "default_secret";

    private static refresh_secret: string =
        (process.env.JWT_REFRESH_SECRET as string) || "default_ref_secret";

    private static expiresIn: number =
        parseInt(process.env.JWT_EXPIRES_IN!) || 3600;

    private static issuer: string =
        (process.env.JWT_ISSUER as string) || "default_issuer";

    static generateToken(
        payload: object,
        expiresIn: number = this.expiresIn,
        issuer: string = this.issuer,
        refreshToken = false
    ): string {
        const options: SignOptions = {
            expiresIn,
            issuer,
        };

        if (refreshToken) {
            return jwt.sign(payload, this.refresh_secret, options);
        } else {
            return jwt.sign(payload, this.secret, options);
        }
    }

    static async verifyToken(token: string, refreshToken = false): Promise<JWTPayload> {
        try {
            if (refreshToken) {
                return jwt.verify(token, this.refresh_secret) as JWTPayload;
            } else {
                return jwt.verify(token, this.secret) as JWTPayload;
            }
        } catch (err: any) {
            if (err.name === "TokenExpiredError") {
                throw new APIError(UNAUTHORIZED, "Token expired!");
            }
            throw new APIError(UNAUTHORIZED, "Access denied: Invalid token!");
        }
    }

    static decodeToken(token: string) {
        try {
            return jwt.decode(token);
        } catch (error) {
            return null;
        }
    }
}

export default JWTHelper;
