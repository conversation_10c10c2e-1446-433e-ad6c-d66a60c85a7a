import { QueryRunner, Repository } from "typeorm";
import { LoginRateLimitService } from "../../services/loginAttempt.service";
import { getRepositories } from "./RepositoryHelper.helper";
import { LoginAttempt } from "../../models/security/loginAttempt.model";
import { JWTPayload } from "../../../types/jwt";

export const isKnownIpForUser = async (
  rootRunner: QueryRunner,
  qRunner: QueryRunner,
  userId: string,
  currentIp: string,
  decodedJwtData: JWTPayload,
): Promise<boolean> => {
  try {
    const hashedIp = await LoginRateLimitService.hashIp(currentIp);

    let queryRunnerToUse: QueryRunner | null = null
    let type: "TENANT" | "STAFF" | "SUPERADMIN" | "USER" | null = null

    if(!decodedJwtData.isTenant){
      queryRunnerToUse = rootRunner
    }
    else{
      queryRunnerToUse = qRunner
      if(decodedJwtData.inSuper){
        type = 'TENANT'
      }
      else if(decodedJwtData.isCustomer === undefined){
        type = 'STAFF'
      }
      else{
        type = 'USER'
      }
    }
    
    const { LoginAttempt } = getRepositories(queryRunnerToUse) as {
      LoginAttempt: Repository<LoginAttempt>;
    };

    if(!type)
      return false;

    const knownIpAttempt = await LoginAttempt.findOne({
      where: {
        userId,
        ipAddress: hashedIp,
        success: true,
        loginType: type
      }
    });

    return !!knownIpAttempt;
  } catch (error) {
    return false;
  }
};