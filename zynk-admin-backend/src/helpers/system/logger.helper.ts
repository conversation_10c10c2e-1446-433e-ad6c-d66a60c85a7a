import winston from 'winston';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

const winstonLevels = {
    fatal: 0,
    error: 1,
    warn: 2,
    info: 3,
    http: 4,
    verbose: 5,
    debug: 6,
    silly: 7,
};

// ANSI color codes
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    green: '\x1b[32m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    gray: '\x1b[90m',
};

const getColorForLevel = (level: string): string => {
    switch (level.toLowerCase()) {
        case 'fatal':
            return colors.red;
        case 'error':
            return colors.red;
        case 'warn':
            return colors.yellow;
        case 'info':
            return colors.green;
        case 'http':
            return colors.blue;
        case 'verbose':
            return colors.cyan;
        case 'debug':
            return colors.magenta;
        case 'silly':
            return colors.gray;
        default:
            return colors.white;
    }
};

const safeStringify = (details: any): string => {
    if (!details) return '';
    try {
        if (details instanceof Error) {
            return JSON.stringify(
                {message: details.message, stack: details.stack},
                null,
                2
            );
        }
        return JSON.stringify(details, null, 2);
    } catch (err) {
        return `Unable to stringify details: ${(err as any).message}`;
    }
};

const logger = winston.createLogger({
    level: process.env.NODE_ENV === 'production' ? 'error' : 'silly',
    levels: winstonLevels,
    format: winston.format.combine(
        winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss'}),
        winston.format.printf(({timestamp, level, message, filePath, details, tenantSubDomain}) => {
            const subDomainPart = tenantSubDomain ? `[${tenantSubDomain}] ` : '';
            const color = getColorForLevel(level);

            // For fatal level, make the entire line red
            if (level.toLowerCase() === 'fatal') {
                let output = `${color}[${timestamp}] [${level.toUpperCase()}] ${subDomainPart}[${filePath}]: ${message}`;
                if (details) {
                    const detailsStr = safeStringify(details);
                    if (detailsStr) {
                        output += `\n    Details: ${detailsStr.replace(/\n/g, '\n    ')}`;
                    }
                }
                output += colors.reset;
                return output;
            }

            // For other levels, use the original format
            let output = `[${timestamp}] ${color}[${level.toUpperCase()}]${colors.reset} ${subDomainPart}[${filePath}]: ${message}`;
            if (details) {
                const detailsStr = safeStringify(details);
                if (detailsStr) {
                    output += `${color}\n    Details: ${detailsStr.replace(/\n/g, '\n    ')}${colors.reset}`;
                }
            }
            return output;
        })
    ),
    transports: [
        new winston.transports.Console(),
        ...(process.env.NODE_ENV === 'production' && process.env.IS_SERVERLESS_DEPLOYMENT !== 'true'
            ? [
                new winston.transports.File({
                    filename: 'logs/fatal.log',
                    level: 'fatal',
                    format: winston.format.combine(
                        winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss'}),
                        winston.format.printf(({timestamp, level, message, filePath, details, tenantSubDomain}) => {
                            const subDomainPart = tenantSubDomain ? `[${tenantSubDomain}] ` : '';
                            let output = `[${timestamp}] [${level.toUpperCase()}] ${subDomainPart}[${filePath}]: ${message}`;
                            if (details) {
                                const detailsStr = safeStringify(details);
                                if (detailsStr) {
                                    output += `\n    Details: ${detailsStr.replace(/\n/g, '\n    ')}`;
                                }
                            }
                            return output;
                        })
                    )
                }),
                new winston.transports.File({
                    filename: 'logs/error.log',
                    level: 'error',
                    format: winston.format.combine(
                        winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss'}),
                        winston.format.printf(({timestamp, level, message, filePath, details, tenantSubDomain}) => {
                            const subDomainPart = tenantSubDomain ? `[${tenantSubDomain}] ` : '';
                            let output = `[${timestamp}] [${level.toUpperCase()}] ${subDomainPart}[${filePath}]: ${message}`;
                            if (details) {
                                const detailsStr = safeStringify(details);
                                if (detailsStr) {
                                    output += `\n    Details: ${detailsStr.replace(/\n/g, '\n    ')}`;
                                }
                            }
                            return output;
                        })
                    )
                }),
                new winston.transports.File({
                    filename: 'logs/warn.log',
                    level: 'warn',
                    format: winston.format.combine(
                        winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss'}),
                        winston.format.printf(({timestamp, level, message, filePath, details, tenantSubDomain}) => {
                            const subDomainPart = tenantSubDomain ? `[${tenantSubDomain}] ` : '';
                            let output = `[${timestamp}] [${level.toUpperCase()}] ${subDomainPart}[${filePath}]: ${message}`;
                            if (details) {
                                const detailsStr = safeStringify(details);
                                if (detailsStr) {
                                    output += `\n    Details: ${detailsStr.replace(/\n/g, '\n    ')}`;
                                }
                            }
                            return output;
                        })
                    )
                }),
                new winston.transports.File({filename: 'logs/info.log', level: 'info'}),
                new winston.transports.File({filename: 'logs/http.log', level: 'http'}),
                new winston.transports.File({filename: 'logs/verbose.log', level: 'verbose'}),
                new winston.transports.File({filename: 'logs/debug.log', level: 'debug'}),
                new winston.transports.File({filename: 'logs/silly.log', level: 'silly'}),
                new winston.transports.File({filename: 'logs/combined.log'})
            ]
            : []),
    ],
});

const getCallerFilePath = (): string => {
    const error = new Error();
    const stack = error.stack?.split('\n') || [];
    for (let i = 2; i < stack.length; i++) {
        const line = stack[i];
        if (line && !line.includes('logger.helper.ts') && !line.includes('node:internal')) {
            const match = line.match(/\((.*):\d+:\d+\)/) || line.match(/at.*\s(.*):\d+:\d+/);
            if (match && match[1]) {
                return path.relative(process.cwd(), match[1]).replace(/\\/g, '/');
            }
        }
    }
    return 'unknown';
};

const log = {
    fatal: (message: string, details?: any, tenantSubDomain?: string) => {
        logger.log('fatal', {message, filePath: getCallerFilePath(), details, tenantSubDomain});
    },
    error: (message: string, details?: any, tenantSubDomain?: string) => {
        logger.error({message, filePath: getCallerFilePath(), details, tenantSubDomain});
    },
    warn: (message: string, details?: any, tenantSubDomain?: string) => {
        if (process.env.NODE_ENV !== 'production' || process.env.LOG_LEVEL === 'warn' || process.env.LOG_LEVEL === 'info') {
            logger.warn({message, filePath: getCallerFilePath(), details, tenantSubDomain});
        }
    },
    info: (message: string, details?: any, tenantSubDomain?: string) => {
        if (process.env.NODE_ENV !== 'production' || process.env.LOG_LEVEL === 'info') {
            logger.info({message, filePath: getCallerFilePath(), details, tenantSubDomain});
        }
    },
    http: (message: string, details?: any, tenantSubDomain?: string) => {
        if (process.env.NODE_ENV !== 'production' || process.env.LOG_LEVEL === 'http' || process.env.LOG_LEVEL === 'info') {
            logger.http({message, filePath: getCallerFilePath(), details, tenantSubDomain});
        }
    },
    verbose: (message: string, details?: any, tenantSubDomain?: string) => {
        if (process.env.NODE_ENV !== 'production' || process.env.LOG_LEVEL === 'verbose' || process.env.LOG_LEVEL === 'info') {
            logger.verbose({message, filePath: getCallerFilePath(), details, tenantSubDomain});
        }
    },
    debug: (message: string, details?: any, tenantSubDomain?: string) => {
        if (process.env.NODE_ENV !== 'production' || process.env.LOG_LEVEL === 'debug' || process.env.LOG_LEVEL === 'info') {
            logger.debug({message, filePath: getCallerFilePath(), details, tenantSubDomain});
        }
    },
    silly: (message: string, details?: any, tenantSubDomain?: string) => {
        if (process.env.NODE_ENV !== 'production' || process.env.LOG_LEVEL === 'silly' || process.env.LOG_LEVEL === 'info') {
            logger.silly({message, filePath: getCallerFilePath(), details, tenantSubDomain});
        }
    },
};

export default log;