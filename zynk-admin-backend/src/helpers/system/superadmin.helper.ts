import {JWTPayload} from "../../../types/jwt";
import {AESHelper} from "./AESHelper";
import JWTHelper from "./JWTHelper.helper";

export const genNewRefreshToken = async (
    payload: JWTPayload,
    secret_key: Buffer<ArrayBuffer>
) => {
    const newRefreshToken = JWTHelper.generateToken(
        payload,
        parseInt(process.env.JWT_REFRESH_EXPIRY!) || 2592000,
        (process.env.JWT_ISSUER as string) || "default_issuer",
        true
    );

    return await AESHelper.encrypt(
        newRefreshToken,
        secret_key
    );
};
