import * as crypto from "crypto";

export class AESHelper {
  private static readonly algorithm = 'aes-256-cbc';
  private static readonly keyLength = 32; // 256 bits
  private static readonly ivLength = 16;  // 128 bits (block size)

  /**
   * Encrypts a string using AES-256-CBC.
   * A random IV is generated per message and prepended to the ciphertext.
   * Output is base64 encoded.
   */
  static async encrypt(plaintext: string, key: Buffer | string): Promise<string> {
    const keyBuf = typeof key === 'string' ? Buffer.from(key, 'base64') : key;
    if (keyBuf.length !== AESHelper.keyLength) throw new Error('Key must be 32 bytes');

    const iv = crypto.randomBytes(this.ivLength);
    const cipher = crypto.createCipheriv(this.algorithm, keyBuf, iv);

    let encrypted = cipher.update(plaintext, 'utf8');
    encrypted = Buffer.concat([encrypted, cipher.final()]);

    const combined = Buffer.concat([iv, encrypted]); // IV + ciphertext
    return combined.toString('base64');
  }

  /**
   * Decrypts a base64 string that has the IV prepended.
   */
  static async decrypt(encryptedData: string, key: Buffer | string): Promise<string> {
    const keyBuf = typeof key === 'string' ? Buffer.from(key, 'base64') : key;
    if (keyBuf.length !== AESHelper.keyLength) throw new Error('Key must be 32 bytes');

    const combined = Buffer.from(encryptedData, 'base64');
    const iv = combined.subarray(0, this.ivLength);
    const ciphertext = combined.subarray(this.ivLength);

    const decipher = crypto.createDecipheriv(this.algorithm, keyBuf, iv);
    let decrypted = decipher.update(ciphertext);
    decrypted = Buffer.concat([decrypted, decipher.final()]);

    return decrypted.toString('utf8');
  }

  static generateKey(): Buffer {
    return crypto.randomBytes(this.keyLength);
  }
}
