import {APIError} from "../../utils/errorHandler";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./JWTHelper.helper";

export const isTokenVerified = async (
    token: any,
    refresh = false
): Promise<boolean> => {
    let verification = null;
    try {
        verification = await JWTHelper.verifyToken(token, refresh);

        return true;
    } catch (err) {
        return false;
    }
};

export const isTokenVerifiedWithErrors = async (
    token: any,
    refresh = false
) => {
    let verification = null;
    try {
        verification = await JWTHelper.verifyToken(token, refresh);
        return verification;
    } catch (err) {
        return {
            isErr: true,
            err_message: (err as APIError).message,
        };
    }
};
