import { QueryRunner, Repository } from "typeorm";

export const getRepositories = (qRunner: QueryRunner) => {
  const repositoryMap: { [key: string]: Repository<any>; } = {};

  const entities = qRunner.manager.connection.entityMetadatas;

  entities.forEach((entityMetadata) => {
    const entityName = entityMetadata.name;
    repositoryMap[entityName] = qRunner.manager.getRepository(
      entityMetadata.target
    );
  });

  return repositoryMap;
};
