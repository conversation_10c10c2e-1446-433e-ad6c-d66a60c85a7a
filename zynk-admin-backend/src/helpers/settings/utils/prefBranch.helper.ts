import { Request } from "express";
import { Query<PERSON>unner, Repository } from "typeorm";
import { PrefBranch, PrefUserType } from "../../../models/common/prefbranch.model";
import { UserType } from "../../../types/pBAC";
import { APIError } from "../../../utils/errorHandler";
import { NOT_FOUND } from "../../../constants/STATUS_CODES";
import { PREFBRANCH_NOT_FOUND, PREFBRANCH_NOTASSIGNED } from "../../../constants/tenant/userPref/err";
import { getRepositories } from "../../system/RepositoryHelper.helper";

export const getPrefBranchHelper = async (req: Request, qRunner: QueryRunner) => {
  try {

    const {PrefBranch} = getRepositories(qRunner) as {
        PrefBranch: Repository<PrefBranch>
    }
    const userId = req.userId;
    let userType;

    if (req.USER_TYPE === UserType.TENANT_ADMIN) {
      userType = PrefUserType.TENANT;
    } else {
      userType = PrefUserType.STAFF;
    }

    const exPrefBranch = await PrefBranch.findOne({
      where: {
        userId,
        userType,
      },
    });

    if (!exPrefBranch)
      throw new APIError(NOT_FOUND, PREFBRANCH_NOT_FOUND);

    if (!exPrefBranch.prefBranchId)
      throw new APIError(NOT_FOUND, PREFBRANCH_NOTASSIGNED);

    return exPrefBranch
  } catch (error) {
    throw error;
  }
};
