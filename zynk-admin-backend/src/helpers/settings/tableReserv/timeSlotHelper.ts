import { Repository } from "typeorm";
import { OnlineReservationConfig } from "../../../models/tenantSettings/tableReservation/online/online_reserv.model";
import { TimeSlotPace } from "../../../models/tenantSettings/tableReservation/online/timeSlotPace.model";
import { timeSlotPatchSchema } from "../../../validation/tenantSettings/tableReserv/timeSlotConfig.validation";
import { z } from "zod";

export const patchTimeSlotHelper = async (
  timeSlots: z.infer<typeof timeSlotPatchSchema>,
  onlineReservConfig: OnlineReservationConfig,
  OnlineReservRepo: Repository<OnlineReservationConfig>,
  TimeSlotPaceRepo: Repository<TimeSlotPace>
) => {
  try {
    const existingTimeSlots = onlineReservConfig.timeSlotPacings || [];
    const providedTimeSlotIds = timeSlots
      .filter(slot => slot.timeSlotPaceId)
      .map(slot => slot.timeSlotPaceId);

    const slotsToDelete = existingTimeSlots.filter(
      existingSlot => !providedTimeSlotIds.includes(existingSlot.timeSlotPaceId)
    );

    if (slotsToDelete.length > 0) {
      await TimeSlotPaceRepo.remove(slotsToDelete);
    }

    const processedSlots: TimeSlotPace[] = [];

    for (const slotData of timeSlots) {
      if (slotData.timeSlotPaceId) {
        const existingSlot = existingTimeSlots.find(
          slot => slot.timeSlotPaceId === slotData.timeSlotPaceId
        );

        if (existingSlot) {
          if (slotData.time !== undefined) {
            existingSlot.time = slotData.time;
          }
          if (slotData.maxGuests !== undefined) {
            existingSlot.maxGuests = slotData.maxGuests;
          }

          const updatedSlot = await TimeSlotPaceRepo.save(existingSlot);
          processedSlots.push(updatedSlot);
        }
      } else {
        const newSlot = TimeSlotPaceRepo.create({
          time: slotData.time!,
          maxGuests: slotData.maxGuests!,
          online_reservation: onlineReservConfig,
        });

        const savedSlot = await TimeSlotPaceRepo.save(newSlot);
        processedSlots.push(savedSlot);
      }
    }

    onlineReservConfig.timeSlotPacings = processedSlots;
    await OnlineReservRepo.save(onlineReservConfig);

    return processedSlots;
  } catch (error) {
    throw error;
  }
};