import { Repository } from "typeorm";
import { TableModel } from "../../../models/tenantSettings/tableReservation/management/table.model";
import { APIError } from "../../../utils/errorHandler";
import { NOT_FOUND } from "../../../constants/STATUS_CODES";
import {
  TABLE_COMBINATON_NOT_FOUND,
  TABLE_NOT_FOUND,
} from "../../../constants/tenant/settings/tableReserv/err";
import { TableCombination } from "../../../models/tenantSettings/tableReservation/management/tableCombination.model";

export const patchTablesInBranch = async (
  tables: any[],
  TableModel: Repository<TableModel>,
  branchId: string
) => {
  try {
    const existingTableIds = await TableModel.find({
      where: {
        branch: {
          branchId,
        },
      },
    });

    let tableIdsToUpdate = tables.map((table: any) => table.tableId);
    tableIdsToUpdate = existingTableIds
      .filter((table: any) => tableIdsToUpdate.includes(table.tableId))
      .map((table) => table.tableId);

    const tableIdsToRemove = existingTableIds
      .filter((table: any) => !tableIdsToUpdate.includes(table.tableId))
      .map((table: any) => table.tableId);

    for (const table of tables) {
      if (table.tableId && tableIdsToUpdate.includes(table.tableId)) {
        if (Object.keys(table).length > 1) {
          const existingTable = await TableModel.findOneBy({
            tableId: table.tableId,
          });

          if (!existingTable) throw new APIError(NOT_FOUND, TABLE_NOT_FOUND);

          const { tableId, floor, ...rest } = table;

          existingTable.update({
            ...rest,
          });

          if (floor) {
            existingTable.floor.floorId = floor;
          }

          await TableModel.save(existingTable);
        }
      } else {
        // create new table
        const newTable = TableModel.create({
          name: table.name,

          minSeats: table.minSeats,

          maxSeats: table.maxSeats,

          enabled: table.enabled,

          availableOnline: table.availableOnline,
          branch: {
            branchId,
          },

          status: table.status,
          cleaning: table.cleaning,
          location: table.location,
          floor: {
            floorId: table.floor,
          },
        });

        await TableModel.save(newTable);
      }
    }

    // Deletions
    if (tableIdsToRemove.length > 0) {
      await TableModel.delete(tableIdsToRemove);
    }
  } catch (error) {
    throw error;
  }
};

export const patchTableCombinationsInBranch = async (
  tableCombinations: any[],
  TableModel: Repository<TableModel>,
  TableCombination: Repository<TableCombination>,
  branchId: string
) => {
  try {
    const existingTableCombinationIds = await TableCombination.find({
      where: {
        branch: {
          branchId,
        },
      },
    });

    let tableCombinationIdsToUpdate = tableCombinations.map(
      (tableCombination: any) => tableCombination.tableCombinationId
    );
    tableCombinationIdsToUpdate = existingTableCombinationIds
      .filter((tableCombination: any) =>
        tableCombinationIdsToUpdate.includes(
          tableCombination.tableCombinationId
        )
      )
      .map((tableCombination) => tableCombination.tableCombinationId);

    const tableIdsToRemove = existingTableCombinationIds
      .filter(
        (tableCombination: any) =>
          !tableCombinationIdsToUpdate.includes(
            tableCombination.tableCombinationId
          )
      )
      .map((tableCombination: any) => tableCombination.tableCombinationId);

    for (const tableCombination of tableCombinations) {
      if (
        tableCombination.tableCombinationId &&
        tableCombinationIdsToUpdate.includes(
          tableCombination.tableCombinationId
        )
      ) {
        if (Object.keys(tableCombination).length > 1) {
          const existingTableCombination = await TableCombination.findOne({
            where: {
              tableCombinationId: tableCombination.tableCombinationId,
            },
            relations: ["tables"],
          });

          if (!existingTableCombination)
            throw new APIError(NOT_FOUND, TABLE_COMBINATON_NOT_FOUND);

          const { tableCombinationId, tables, ...rest } = tableCombination;

          existingTableCombination.update({
            ...rest,
          });

          if (tables) {
            let tablesToCombine: TableModel[] = [];

            for (const tableId of tables) {
              const exTable = await TableModel.findOne({
                where: {
                  tableId,
                  branch: {
                    branchId,
                  },
                },
              });

              if (!exTable) throw new APIError(NOT_FOUND, TABLE_NOT_FOUND);

              tablesToCombine.push(exTable);
            }

            existingTableCombination.tables = tablesToCombine;
          }

          await TableCombination.save(existingTableCombination);
        }
      } else {
        // create new table combination

        let tablesToCombine: TableModel[] = [];

        for (const tableId of tableCombination.tables) {
          const exTable = await TableModel.findOne({
            where: {
              tableId,
              branch: {
                branchId,
              },
            },
          });

          if (!exTable) throw new APIError(NOT_FOUND, TABLE_NOT_FOUND);

          tablesToCombine.push(exTable);
        }

        const newTableCombination = TableCombination.create({
          minSeats: tableCombination.minSeats,

          maxSeats: tableCombination.maxSeats,

          enabled: tableCombination.enabled,

          availableOnline: tableCombination.availableOnline,
          tables: tablesToCombine,
          branch: {
            branchId,
          },
        });

        await TableCombination.save(newTableCombination);
      }
    }

    // Deletions
    if (tableIdsToRemove.length > 0) {
      await TableCombination.delete(tableIdsToRemove);
    }
  } catch (error) {
    throw error;
  }
};
