import { Repository } from "typeorm";
import { TurnoverTime } from "../../../models/tenantSettings/tableReservation/turnoverTime/turnover_time.model";
import { TurnoverRule } from "../../../models/tenantSettings/tableReservation/turnoverTime/turnover_rules.model";
import { APIError } from "../../../utils/errorHandler";
import { NOT_FOUND } from "../../../constants/STATUS_CODES";
import { TURNOVERTIME_NOTFOUND } from "../../../constants/tenant/settings/tableReserv/err";
import { DefaultTurnoverDurations } from "../../../models/tenantSettings/tableReservation/turnoverTime/turnover.def";

export const getDurationMinutes = async (
  numberOfGuests: number,
  TurnoverTime: Repository<TurnoverTime>,
  branchId: string
) => {
  try {
    const exTurnoverTime = await TurnoverTime.findOne({
      where: {
        turnoverTimeId: branchId,
      },
      relations: ["turnoverRules"],
    });

    if (!exTurnoverTime) throw new APIError(NOT_FOUND, TURNOVERTIME_NOTFOUND);

    let defaultDuration: DefaultTurnoverDurations | number =
      exTurnoverTime.defaultDuration;

    switch (defaultDuration) {
      case DefaultTurnoverDurations.THIRTYMIN:
        defaultDuration = 30;
        break;
      case DefaultTurnoverDurations.ONEHOUR:
        defaultDuration = 60;
        break;
      case DefaultTurnoverDurations.ONEANDHALFHOUR:
        defaultDuration = 90;
        break;
      case DefaultTurnoverDurations.TWOHOURS:
        defaultDuration = 120;
        break;
      case DefaultTurnoverDurations.TWOANDHALFHOURS:
        defaultDuration = 150;
        break;
      case DefaultTurnoverDurations.THREEHOUR:
        defaultDuration = 180;
        break;
      case DefaultTurnoverDurations.THREEANDHALFHOURS:
        defaultDuration = 210;
        break;
      case DefaultTurnoverDurations.FOURHOURS:
        defaultDuration = 240;
        break;
      case DefaultTurnoverDurations.FOURANDHALFHOURS:
        defaultDuration = 270;
        break;
      case DefaultTurnoverDurations.FIVEHOURS:
        defaultDuration = 300;
        break;
      case DefaultTurnoverDurations.FIVEANDHALFHOURS:
        defaultDuration = 330;
        break;
      case DefaultTurnoverDurations.SIXHOURS:
        defaultDuration = 360;
        break;
      case DefaultTurnoverDurations.SIXANDHALFHOURS:
        defaultDuration = 390;
        break;
      case DefaultTurnoverDurations.SEVENHOURS:
        defaultDuration = 420;
        break;
      case DefaultTurnoverDurations.SEVENANDHALFHOURS:
        defaultDuration = 450;
        break;
      case DefaultTurnoverDurations.EIGHTHOURS:
        defaultDuration = 480;
        break;
      case DefaultTurnoverDurations.EIGHTANDHALFHOURS:
        defaultDuration = 510;
        break;
      default:
        defaultDuration = 120;
    }

    let turnoverRuleExists = false;
    let turnoverRuleDuration = null;

    for (const turnoverRule of exTurnoverTime.turnoverRules) {
      if (
        numberOfGuests >= turnoverRule.minGuests &&
        numberOfGuests <= turnoverRule.maxGuests
      ) {
        turnoverRuleExists = true;
        turnoverRuleDuration = turnoverRule.duration;
        break;
      }
    }

    if (turnoverRuleExists) {
      switch (turnoverRuleDuration) {
        case DefaultTurnoverDurations.THIRTYMIN:
          turnoverRuleDuration = 30;
          break;
        case DefaultTurnoverDurations.ONEHOUR:
          turnoverRuleDuration = 60;
          break;
        case DefaultTurnoverDurations.ONEANDHALFHOUR:
          turnoverRuleDuration = 90;
          break;
        case DefaultTurnoverDurations.TWOHOURS:
          turnoverRuleDuration = 120;
          break;
        case DefaultTurnoverDurations.TWOANDHALFHOURS:
          turnoverRuleDuration = 150;
          break;
        case DefaultTurnoverDurations.THREEHOUR:
          turnoverRuleDuration = 180;
          break;
        case DefaultTurnoverDurations.THREEANDHALFHOURS:
          turnoverRuleDuration = 210;
          break;
        case DefaultTurnoverDurations.FOURHOURS:
          turnoverRuleDuration = 240;
          break;
        case DefaultTurnoverDurations.FOURANDHALFHOURS:
          turnoverRuleDuration = 270;
          break;
        case DefaultTurnoverDurations.FIVEHOURS:
          turnoverRuleDuration = 300;
          break;
        case DefaultTurnoverDurations.FIVEANDHALFHOURS:
          turnoverRuleDuration = 330;
          break;
        case DefaultTurnoverDurations.SIXHOURS:
          turnoverRuleDuration = 360;
          break;
        case DefaultTurnoverDurations.SIXANDHALFHOURS:
          turnoverRuleDuration = 390;
          break;
        case DefaultTurnoverDurations.SEVENHOURS:
          turnoverRuleDuration = 420;
          break;
        case DefaultTurnoverDurations.SEVENANDHALFHOURS:
          turnoverRuleDuration = 450;
          break;
        case DefaultTurnoverDurations.EIGHTHOURS:
          turnoverRuleDuration = 480;
          break;
        case DefaultTurnoverDurations.EIGHTANDHALFHOURS:
          turnoverRuleDuration = 510;
          break;
        default:
          turnoverRuleDuration = 120;
      }
    }

    return turnoverRuleExists ? turnoverRuleDuration : defaultDuration;
  } catch (error) {
    throw error;
  }
};
