import { Repository } from "typeorm";
import { Floor } from "../../../models/tenantSettings/tableReservation/management/floor.model";
import { APIError } from "../../../utils/errorHandler";
import { NOT_FOUND } from "../../../constants/STATUS_CODES";
import { FLOOR_NOT_FOUND } from "../../../constants/tenant/settings/tableReserv/err";

export const patchFloorHelper = async (
  floors: any[],
  Floor: Repository<Floor>,
  branchId: string
) => {
  try {
    const existingFloorIds = await Floor.find({
      where: {
        branch: {
          branchId,
        },
      },
    });

    let floorIdsToUpdate = floors.map((floor: any) => floor.floorId);
    floorIdsToUpdate = existingFloorIds
      .filter((floor: any) => floorIdsToUpdate.includes(floor.floorId))
      .map((floor) => floor.floorId);

    const floorsIdsToRemove = existingFloorIds
      .filter((floor: any) => !existingFloorIds.includes(floor.floorId))
      .map((floor: any) => floor.floorId);

    for (const floor of floors) {
      if (floor.floorId && floorIdsToUpdate.includes(floor.floorId)) {
        if (Object.keys(floor).length > 1) {
          const existingFloor = await Floor.findOneBy({
            floorId: floor.floorId,
          });

          if (!existingFloor) throw new APIError(NOT_FOUND, FLOOR_NOT_FOUND);

          const { floorId, ...rest } = floor;

          existingFloor.update({
            ...rest,
          });

          await Floor.save(existingFloor);
        }
      } else {
        // create new floor
        const newFloor = Floor.create({
          name: floor.name,
          branch: {
            branchId,
          },
        });

        await Floor.save(newFloor);
      }
    }

    // Deletions
    if (floorsIdsToRemove.length > 0) {
      await Floor.delete(floorsIdsToRemove);
    }
  } catch (error) {
    throw error;
  }
};
