import {In, Not, Repository} from "typeorm";
import {getRepositories} from "../../../system/RepositoryHelper.helper";
import {TenantBlockList} from "../../../../models/admin/settings/tenantBlockList/TenantBlockList";
import {CheckBlockListParams, CheckBlockListResult} from "../../../../types/settings/admin/BlockList";
import {LockedType} from "../../../../types/system";
import {BlockType} from "../../../../models/admin/settings/tenantBlockList/BlockType";
import {Tenant} from "../../../../models/admin/Tenant/tenant.model";
import {BlockTypeAlreadyExistEx} from "../../../../exceptions/controller/admin/settings/impl/BlockTypeAlreadyExistEx";
import {ResponseTypeConfig} from "../../../../utils/Common.util";

const Modifier = LockedType.TENANT_BLOCKER_ONLY;

//BlockList Utils
export const blockListResTypeConfig: ResponseTypeConfig = {
    FULL: {
        relations: [],
        select: ["tenantBlockListId", "lockType", "createdAt", "updatedAt"]
    },
    FULL_WITH_ASSOCIATION: {
        relations: ["tenant", "blockType"],
        select: ["tenantBlockListId", "lockType", "createdAt", "updatedAt"]
    },
    MINIMAL: {
        relations: [],
        select: ["tenantBlockListId", "lockType"]
    },
    MINIMAL_WITH_ASSOCIATION: {
        relations: ["tenant", "blockType"],
        select: ["tenantBlockListId", "lockType"]
    }
}
export const checkBlockList = async (
    {
        qRunner,
        currentModifier,
        tenantId,
        lockedTypes,
        isNotOperation = false,
        unBlock = false,
        throwError = false,
    }: CheckBlockListParams): Promise<CheckBlockListResult> => {
    const result: CheckBlockListResult = {
        success: false,
        isTenantBlocked: false,
        warnings: [],
        error: [],
        unBlocked: false,
    };

    try {
        if (!lockedTypes?.length) {
            result.error.push('lockedTypes array cannot be empty');
            if (throwError) throw new Error('lockedTypes array cannot be empty');
            return result;
        }
        const {BlockList} = getRepositories(qRunner) as {
            BlockList: Repository<TenantBlockList>;
        };
        const whereClause = {
            tenant: {tenantId},
            lockedType: isNotOperation ? Not(In(lockedTypes)) : In(lockedTypes),
        };

        const foundBlockList = await BlockList.findOne({
            where: whereClause,
            relations: ['blockType'],
        });

        if (foundBlockList) {
            result.isTenantBlocked = true;
            const blockMessage = foundBlockList.blockType?.blockMessage || 'Tenant is blocked';
            result.warnings.push(blockMessage);

            if (unBlock && foundBlockList.lockedType === currentModifier) {
                await BlockList.delete({tenantBlockListId: foundBlockList.tenantBlockListId});
                result.unBlocked = true;
                result.warnings.push('Tenant has been unblocked');
            } else if (unBlock) {
                result.success = false;
                result.warnings.push('Unblock failed: currentModifier does not match lockedType');
            }
            if (throwError && !unBlock) {
                throw new Error(blockMessage);
            }
            result.success = true;
        } else {
            result.success = true;
            result.isTenantBlocked = false;
            result.warnings.push('No block found for the specified tenant and criteria');
        }
        return result;
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error during checking BlockList';
        result.error.push(errorMessage);
        result.success = false;
        result.isTenantBlocked = false;
        result.unBlocked = false;
        if (throwError) throw error;
        return result;
    }
};

export const createTenantBlockList = async (blockListRepo: Repository<TenantBlockList>, blockType: BlockType, tenant: Tenant, modifier: LockedType = Modifier) => {
    try {
        const blockList = new TenantBlockList();
        blockList.blockType = blockType;
        blockList.tenant = tenant;
        blockList.lockedType = modifier;
        return await blockListRepo.save(blockList);
    } catch (error) {
        throw error;
    }
}

//BlockType Utils
export const blockTypeResTypeConfig: ResponseTypeConfig = {
    FULL: {
        relations: [],
        select: ["blockTypeId", "name", "blockMessage", "isActive", "createdAt", "updatedAt"]
    },
    FULL_WITH_ASSOCIATION: {
        relations: ["tenantBlockLists"],
        select: ["blockTypeId", "name", "blockMessage", "isActive", "createdAt", "updatedAt"]
    },
    MINIMAL: {
        relations: [],
        select: ["blockTypeId", "name", "blockMessage"]
    },
    MINIMAL_WITH_ASSOCIATION: {
        relations: ["tenantBlockLists"],
        select: ["blockTypeId", "name", "blockMessage"]
    }
}

export const createBlockType = async (blockTypeRepo: Repository<BlockType>, name: string, blockMessage: string, isActive: boolean = true) => {
    try {
        const exBlockType = await blockTypeRepo.findOne({where: {name}});
        if (exBlockType) throw new BlockTypeAlreadyExistEx(name);
        const blockType = new BlockType();
        blockType.name = name;
        blockType.blockMessage = blockMessage;
        blockType.lockedType = LockedType.NOT_SYSTEM_MANAGER;
        blockType.isActive = isActive;
        return await blockTypeRepo.save(blockType);
    } catch (error) {
        throw error;
    }
}
