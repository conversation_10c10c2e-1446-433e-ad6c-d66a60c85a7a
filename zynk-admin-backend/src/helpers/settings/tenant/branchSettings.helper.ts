import {QueryR<PERSON>ner, Repository} from "typeorm";
import {getRepositories} from "../../system/RepositoryHelper.helper";
import {specialInstructionsHelper} from "./specialInstructions.helper";
import {Branch} from "../../../models/company/branch.model";
import {wasteManagementHelper} from "./wasteManagement.helper";
import {preparationDurationHelper} from "./preparationDuration.helper";
import {SpecialInstructions} from "../../../models/tenantSettings/food/SpecialInstructions.model";
import {WasteManagement} from "../../../models/tenantSettings/wasteManagement/waste_management.model";
import {PreparationDuration} from "../../../models/tenantSettings/food/preparation_duration.model";
import {OrderType} from "../../../models/tenantSettings/general/orderType.model";
import {orderTypesHelper} from "./general/orderType.helper";
import {CurrencyUnit} from "../../../models/tenantSettings/general/currencyUnit.model";
import {currencyUnitHelper} from "./general/currencyUnit.helper";
import { ParkingService } from "../../../models/tenantSettings/general/parkingService.model";
import { parkingServiceHelperForBranch } from "./general/parkingService.helper";
import { TurnoverTime } from "../../../models/tenantSettings/tableReservation/turnoverTime/turnover_time.model";
import { TurnoverRule } from "../../../models/tenantSettings/tableReservation/turnoverTime/turnover_rules.model";
import { turnoverTimeInitialHelper } from "./tableReserv/turnoverTimeInitial.helper";
import { BufferTime } from "../../../models/tenantSettings/tableReservation/management/bufferTime.model";
import { bufferTimeCreateHelper } from "./tableReserv/bufferTimeInitial.helper";
import { OnlineReservationConfig } from "../../../models/tenantSettings/tableReservation/online/online_reserv.model";
import { onlineReservConfigHelper } from "./tableReserv/onlineReservConfig.helper";

export const branchSettingsHelper = async (branch: Branch, qRunner: QueryRunner) => {
    const {
        SpecialInstructions, WasteManagement, PreparationDuration, OrderType,
        CurrencyUnit, ParkingService, TurnoverTime, TurnoverRule,
        BufferTime, OnlineReservationConfig
    } = getRepositories(qRunner) as {
        SpecialInstructions: Repository<SpecialInstructions>;
        WasteManagement: Repository<WasteManagement>;
        PreparationDuration: Repository<PreparationDuration>;
        OrderType: Repository<OrderType>,
        CurrencyUnit: Repository<CurrencyUnit>,
        ParkingService: Repository<ParkingService>,
        TurnoverTime: Repository<TurnoverTime>,
        TurnoverRule: Repository<TurnoverRule>
        BufferTime: Repository<BufferTime>,
        OnlineReservationConfig: Repository<OnlineReservationConfig>
    }
    await specialInstructionsHelper(branch, SpecialInstructions);
    await wasteManagementHelper(branch, WasteManagement);
    await preparationDurationHelper(branch, PreparationDuration)

    await orderTypesHelper(branch, OrderType)
    await currencyUnitHelper(branch, CurrencyUnit)
    await parkingServiceHelperForBranch(branch, ParkingService)
    await turnoverTimeInitialHelper(branch, TurnoverTime, TurnoverRule)
    await bufferTimeCreateHelper(branch, BufferTime)
    await onlineReservConfigHelper(branch, OnlineReservationConfig)
};