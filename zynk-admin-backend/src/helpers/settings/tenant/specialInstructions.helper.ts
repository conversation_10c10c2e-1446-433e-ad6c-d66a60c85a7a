import {SpecialInstructions} from "../../../models/tenantSettings/food/SpecialInstructions.model";
import {Repository} from "typeorm";
import {Branch} from "../../../models/company/branch.model";
import log from "../../system/logger.helper";

export const specialInstructionsHelper = async (
    branch: Branch,
    specialInstructions: Repository<SpecialInstructions>
) => {
    await specialInstructions.findOne({
        where: {specialInstructionsId: branch.branchId},
    }).then(async (specialIns) => {
        if (!specialIns) {
            const newInstruction = specialInstructions.create({
                branch,
            });
            await specialInstructions.save(newInstruction);

            log.info(`New special instruction settings created for branch: ${branch.name}`);
            return newInstruction;
        }
        return specialIns;
    }).catch((error) => {
        log.error(error);
        throw error;
    })
};