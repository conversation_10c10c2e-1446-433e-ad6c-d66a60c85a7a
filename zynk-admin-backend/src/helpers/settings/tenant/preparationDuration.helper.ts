import {Repository} from "typeorm";
import {Branch} from "../../../models/company/branch.model";
import {PreparationDuration} from "../../../models/tenantSettings/food/preparation_duration.model";
import log from "../../system/logger.helper";

export const preparationDurationHelper = async (
    branch: Branch,
    preparationDuration: Repository<PreparationDuration>
) => {
    await preparationDuration
        .findOne({
            where: {preparationDurationId: branch.branchId},
        })
        .then(async (prepDuration) => {
            if (!prepDuration) {
                const newPrepduration = preparationDuration.create({
                    branch,
                    duration: 5
                });
                await preparationDuration.save(newPrepduration);

                log.info(
                    `New common preparation duration settings created for branch: ${branch.name}`
                );
                return newPrepduration;
            }
            return prepDuration;
        })
        .catch((error) => {
            log.error(error);
            throw error;
        });
};
