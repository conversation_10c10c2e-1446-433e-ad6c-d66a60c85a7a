import { Repository } from "typeorm";
import { Branch } from "../../../../models/company/branch.model";
import { TurnoverTime } from "../../../../models/tenantSettings/tableReservation/turnoverTime/turnover_time.model";
import { TurnoverRule } from "../../../../models/tenantSettings/tableReservation/turnoverTime/turnover_rules.model";
import log from "../../../system/logger.helper";

export const turnoverTimeInitialHelper = async (
  branch: Branch,
  TurnoverTime: Repository<TurnoverTime>,
  TurnoverRule: Repository<TurnoverRule>
) => {
  try {
    const existingTurnoverTime = await TurnoverTime.findOne({
      where: { branch: {
        branchId: branch.branchId
      } },
    });

    let turnoverTimeEntity = existingTurnoverTime;
    if (!turnoverTimeEntity) {
      const newTurnoverTime = TurnoverTime.create({
        branch,
      });

      turnoverTimeEntity = await TurnoverTime.save(newTurnoverTime);
    }

    const existingTurnOverRule = await TurnoverRule.findOne({
      where: { turnoverTime: {
        turnoverTimeId: turnoverTimeEntity.turnoverTimeId
      }, minGuests: 0, maxGuests: 4 },
    });

    if (!existingTurnOverRule) {
      const newTurnOverRule = TurnoverRule.create({
        minGuests: 0,
        maxGuests: 4,
        turnoverTime: turnoverTimeEntity,
      });

      await TurnoverRule.save(newTurnOverRule);
    }
    
  } catch (error) {
    log.error("Error creating default turnover times and turn over rules.");
  }
};
