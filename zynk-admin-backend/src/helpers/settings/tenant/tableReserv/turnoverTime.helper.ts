
import { Repository, Query<PERSON>unner } from "typeorm";
import { DefaultTurnoverDurations } from "../../../../models/tenantSettings/tableReservation/turnoverTime/turnover.def";
import { TurnoverTime } from "../../../../models/tenantSettings/tableReservation/turnoverTime/turnover_time.model";
import { TurnoverRule } from "../../../../models/tenantSettings/tableReservation/turnoverTime/turnover_rules.model";
import { getRepositories } from "../../../system/RepositoryHelper.helper";
import { APIError } from "../../../../utils/errorHandler";
import { NOT_FOUND } from "../../../../constants/STATUS_CODES";

export interface TurnoverRuleInput {
  turnoverRuleId?: string;
  minGuests?: number;
  maxGuests?: number;
  duration?: DefaultTurnoverDurations;
}

export interface PatchTurnoverTimeInput {
  defaultDuration?: DefaultTurnoverDurations;
  turnoverRules?: TurnoverRuleInput[];
}

function validateGuestRanges(rules: Array<{ minGuests: number; maxGuests: number; turnoverRuleId?: string }>): void {
  const sortedRules = rules.sort((a, b) => a.minGuests - b.minGuests);
  
  for (let i = 0; i < sortedRules.length - 1; i++) {
    const current = sortedRules[i];
    const next = sortedRules[i + 1];
    
    // Check for overlapping ranges
    if (current.maxGuests >= next.minGuests) {
      throw new APIError(NOT_FOUND,
        `Conflicting guest ranges: Rule with range ${current.minGuests}-${current.maxGuests} ` +
        `overlaps with rule with range ${next.minGuests}-${next.maxGuests}`
      );
    }
  }
  
  for (const rule of rules) {
    if (rule.minGuests > rule.maxGuests) {
      throw new APIError(NOT_FOUND, `Invalid range: minGuests (${rule.minGuests}) cannot be greater than maxGuests (${rule.maxGuests})`);
    }
    if (rule.minGuests < 0 || rule.maxGuests < 0) {
      throw new APIError(NOT_FOUND, `Invalid range: Guest counts cannot be negative`);
    }
  }
}

export async function patchTurnoverTimeHelper(
  qRunner: QueryRunner,
  turnoverTimeId: string,
  patchData: PatchTurnoverTimeInput
): Promise<TurnoverTime> {
  const { TurnoverTime: TurnoverTimeRepo, TurnoverRule: TurnoverRuleRepo } = getRepositories(qRunner) as {
    TurnoverTime: Repository<TurnoverTime>,
    TurnoverRule: Repository<TurnoverRule>
  }

  const existingTurnoverTime = await TurnoverTimeRepo.findOne({
    where: { turnoverTimeId },
    relations: ['turnoverRules']
  });

  if (!existingTurnoverTime) {
    throw new APIError(NOT_FOUND, 'Turnover time not found');
  }

  if (patchData.defaultDuration !== undefined) {
    existingTurnoverTime.defaultDuration = patchData.defaultDuration;
  }

  if (patchData.turnoverRules !== undefined) {
    if (patchData.turnoverRules.length === 0) {
      if (existingTurnoverTime.turnoverRules.length > 0) {
        await TurnoverRuleRepo.remove(existingTurnoverTime.turnoverRules);
        existingTurnoverTime.turnoverRules = [];
      }
    } else {
      const updatedRules: TurnoverRule[] = [];
      const existingRulesMap = new Map(
        existingTurnoverTime.turnoverRules.map(rule => [rule.turnoverRuleId, rule])
      );

      for (const ruleData of patchData.turnoverRules) {
        if (ruleData.turnoverRuleId) {
          const existingRule = existingRulesMap.get(ruleData.turnoverRuleId);
          if (!existingRule) {
            throw new Error(`Turnover rule with ID ${ruleData.turnoverRuleId} not found`);
          }

          if (ruleData.minGuests !== undefined) existingRule.minGuests = ruleData.minGuests;
          if (ruleData.maxGuests !== undefined) existingRule.maxGuests = ruleData.maxGuests;
          if (ruleData.duration !== undefined) existingRule.duration = ruleData.duration;

          updatedRules.push(existingRule);
        } else {
          const newRule = new TurnoverRule();
          newRule.minGuests = ruleData.minGuests!;
          newRule.maxGuests = ruleData.maxGuests!;
          newRule.duration = ruleData.duration!;
          newRule.turnoverTime = existingTurnoverTime;

          updatedRules.push(newRule);
        }
      }

      validateGuestRanges(updatedRules.map(rule => ({
        minGuests: rule.minGuests,
        maxGuests: rule.maxGuests,
        turnoverRuleId: rule.turnoverRuleId
      })));

      const patchedRuleIds = new Set(
        patchData.turnoverRules
          .filter(rule => rule.turnoverRuleId)
          .map(rule => rule.turnoverRuleId!)
      );

      const rulesToRemove = existingTurnoverTime.turnoverRules.filter(
        rule => !patchedRuleIds.has(rule.turnoverRuleId)
      );

      if (rulesToRemove.length > 0) {
        await TurnoverRuleRepo.remove(rulesToRemove);
      }

      const newRules = updatedRules.filter(rule => !rule.turnoverRuleId);
      if (newRules.length > 0) {
        await TurnoverRuleRepo.save(newRules);
      }

      const existingRulesToUpdate = updatedRules.filter(rule => rule.turnoverRuleId);
      if (existingRulesToUpdate.length > 0) {
        await TurnoverRuleRepo.save(existingRulesToUpdate);
      }

      existingTurnoverTime.turnoverRules = updatedRules;
    }
  }

  const savedTurnoverTime = await TurnoverTimeRepo.save(existingTurnoverTime);

  return await TurnoverTimeRepo.findOne({
    where: { turnoverTimeId: savedTurnoverTime.turnoverTimeId },
    relations: ['turnoverRules']
  }) as TurnoverTime;
}