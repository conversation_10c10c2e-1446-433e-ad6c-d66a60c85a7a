import { Repository } from "typeorm";
import { Branch } from "../../../../models/company/branch.model";
import log from "../../../system/logger.helper";
import { OnlineReservationConfig } from "../../../../models/tenantSettings/tableReservation/online/online_reserv.model";

export const onlineReservConfigHelper = async (
  branch: Branch,
  OnlineReservationConfig: Repository<OnlineReservationConfig>
) => {
  try {
    const existingReservConfig = await OnlineReservationConfig.findOne({
      where: {
        branch: {
          branchId: branch.branchId,
        },
      },
    });
    if (!existingReservConfig) {
      const newReservConfig = OnlineReservationConfig.create({
        enabled: true,
        branch,
      });
      await OnlineReservationConfig.save(newReservConfig);
    }
  } catch (error) {
    log.error("Error creating default turnover times and turn over rules.");
  }
};
