import { Repository } from "typeorm";
import { Branch } from "../../../../models/company/branch.model";
import log from "../../../system/logger.helper";
import { BufferTime } from "../../../../models/tenantSettings/tableReservation/management/bufferTime.model";

export const bufferTimeCreateHelper = async (
  branch: Branch,
  BufferTime: Repository<BufferTime>
) => {
  try {
    const existingBufferTime = await BufferTime.findOne({
      where: {
        branch: {
          branchId: branch.branchId,
        },
      },
    });
    if (!existingBufferTime) {
      const newBufferTime = BufferTime.create({
        branch,
        time: 30,
      });
      await BufferTime.save(newBufferTime);
    }
  } catch (error) {
    log.error("Error creating default turnover times and turn over rules.");
  }
};
