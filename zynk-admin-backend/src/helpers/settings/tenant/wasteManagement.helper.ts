import {Branch} from "../../../models/company/branch.model";
import {Repository} from "typeorm";
import log from "../../system/logger.helper";
import {WasteManagement} from "../../../models/tenantSettings/wasteManagement/waste_management.model";

export const wasteManagementHelper = async (
    branch: Branch,
    wasteManagement: Repository<WasteManagement>
) => {
    await wasteManagement.findOne({
        where: {wasteManagementId: branch.branchId},
    }).then(async (wasteManage) => {
        if (!wasteManage) {
            const newInstruction = wasteManagement.create({
                branch,
            });
            await wasteManagement.save(newInstruction);
            log.info(`New waste management settings created for branch: ${branch.name}`);
            return newInstruction;
        }
        return wasteManage;
    }).catch((error) => {
        log.error(error);
        throw error;
    })
};