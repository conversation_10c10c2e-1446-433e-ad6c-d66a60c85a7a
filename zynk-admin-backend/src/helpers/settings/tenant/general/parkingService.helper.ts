import { Repository } from "typeorm";
import { Branch } from "../../../../models/company/branch.model";
import { ParkingService } from "../../../../models/tenantSettings/general/parkingService.model";
import log from "../../../system/logger.helper";

export const parkingServiceHelperForBranch = async (
  branch: Branch,
  ParkingService: Repository<ParkingService>
) => {
  try {
    const exParkingService = await ParkingService.findAndCount({
      where: {
        branch: {
          branchId: branch.branchId,
        },
      },
    });

    if (exParkingService[1] < 1) {
        const newParkingService = ParkingService.create({
            enabled: true,
            branch
        })

        await ParkingService.save(newParkingService)
    } else {
      log.info("Parking service already exists for branch!");
    }
  } catch (error) {
    throw error;
  }
};
