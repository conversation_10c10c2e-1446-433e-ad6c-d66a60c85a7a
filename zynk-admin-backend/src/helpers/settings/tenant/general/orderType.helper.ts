import {Repository} from "typeorm";
import {Branch} from "../../../../models/company/branch.model";
import {OrderType, OrderTypeName,} from "../../../../models/tenantSettings/general/orderType.model";
import log from "../../../system/logger.helper";
import {APIError} from "../../../../utils/errorHandler";
import {BAD_REQUEST, CONFLICT} from "../../../../constants/STATUS_CODES";

export const orderTypesHelper = async (
    branch: Branch,
    orderTypeRepo: Repository<OrderType>
) => {
    try {
        const existingOrderTypes = await orderTypeRepo.find({
            where: {branch: {branchId: branch.branchId}},
        });

        const existingReservedNames =
            existingOrderTypes.map((ot) => ot.reservedName) || [];

        const allOrderTypeNames = Object.values(OrderTypeName);

        const missingOrderTypes = allOrderTypeNames.filter(
            (orderTypeName) => !existingReservedNames.includes(orderTypeName)
        );

        if (missingOrderTypes.length > 0) {
            const orderTypesToCreate = missingOrderTypes.map((reservedName) => {
                const orderType = orderTypeRepo.create({
                    name: reservedName,
                    reservedName: reservedName,
                    branch: branch,
                    enabled: true,
                });
                return orderType;
            });

            // Save all missing order types
            await orderTypeRepo.save(orderTypesToCreate);

            log.info(
                `Created ${orderTypesToCreate.length} order types for branch ${branch.branchId}`
            );
        } else {
            log.info(`All order types already exist for branch ${branch.branchId}`);
        }
    } catch (error) {
        log.error("Error seeding order types", error);
        throw error;
    }
};

export const validateReservedNameConflicts = async (
    existingOrderTypes: OrderType[],
    orderTypesToUpdate: any[],
    orderTypesToCreate: any[],
) => {
    try {
        const finalOrderTypes = new Map<
            string,
            { reservedName: string; enabled: boolean }
        >();

        // Add existing order types that won't be updated (and won't be deleted)
        const updateIds = orderTypesToUpdate.map((ot) => ot.orderTypeId);
        existingOrderTypes
            .filter((ot) => !updateIds.includes(ot.orderTypeId))
            .forEach((ot) => {
                finalOrderTypes.set(ot.orderTypeId, {
                    reservedName: ot.reservedName,
                    enabled: ot.enabled,
                });
            });

        for (const updateData of orderTypesToUpdate) {
            const existingOrderType = existingOrderTypes.find(
                (ot) => ot.orderTypeId === updateData.orderTypeId
            );

            if (existingOrderType) {
                finalOrderTypes.set(updateData.orderTypeId, {
                    reservedName: updateData.hasOwnProperty('reservedName')
                        ? updateData.reservedName
                        : existingOrderType.reservedName,
                    enabled: updateData.hasOwnProperty('enabled')
                        ? updateData.enabled
                        : existingOrderType.enabled,
                });
            }
        }

        orderTypesToCreate.forEach((ot, index) => {
            // Validate that required fields are present for new order types
            if (!ot.hasOwnProperty('name') || !ot.hasOwnProperty('reservedName') || !ot.hasOwnProperty('enabled')) {
                throw new APIError(BAD_REQUEST, 'New order types must have name, reservedName, and enabled fields');
            }

            finalOrderTypes.set(`new_${index}`, {
                reservedName: ot.reservedName,
                enabled: ot.enabled,
            });
        });

        const reservedNameGroups = new Map<string, boolean[]>();

        finalOrderTypes.forEach(({reservedName, enabled}) => {
            if (!reservedNameGroups.has(reservedName)) {
                reservedNameGroups.set(reservedName, []);
            }
            reservedNameGroups.get(reservedName)!.push(enabled);
        });

        reservedNameGroups.forEach((enabledStates, reservedName) => {
            const enabledCount = enabledStates.filter((enabled) => enabled).length;
            if (enabledCount > 1) {
                throw new APIError(CONFLICT,
                    `Cannot have multiple enabled order types with reserved name "${reservedName}". ` +
                    `Found ${enabledCount} enabled order types.`
                );
            }
        });

    } catch (error) {
        throw error;
    }
};
