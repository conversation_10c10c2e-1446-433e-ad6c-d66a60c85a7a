import { Repository } from "typeorm";
import { Branch } from "../../../../models/company/branch.model";
import { CurrencyUnit } from "../../../../models/tenantSettings/general/currencyUnit.model";
import log from "../../../system/logger.helper";

export const currencyUnitHelper = async (
  branch: Branch,
  CurrencyUnit: Repository<CurrencyUnit>
) => {
  try {
    const exCurrencyUnit = await CurrencyUnit.findAndCount({
      where: {
        branch: {
          branchId: branch.branchId,
        },
      },
    });

    if (exCurrencyUnit[1] < 1) {
      const newCurrencyUnit = CurrencyUnit.create({
        currentUnit: "£",
        branch,
      });

      await CurrencyUnit.save(newCurrencyUnit);
    } else {
      log.info("Currency unit already exists for branch!");
    }
  } catch (error) {
    log.error("Error seeding currency units", error);
    throw error;
  }
};
