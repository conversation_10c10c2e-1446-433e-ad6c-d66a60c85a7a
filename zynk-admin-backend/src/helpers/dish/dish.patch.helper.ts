import { Request } from "express";
import { In, Repository } from "typeorm";
import { Dish } from "../../models/foodmenu/dish.model";
import { DishSide } from "../../models/foodmenu/custom/dish_side.model";
import { DishBeverage } from "../../models/foodmenu/custom/dish_bev.model";
import { DishDessert } from "../../models/foodmenu/custom/dish_dessert.model";
import { DishIngredient } from "../../models/foodmenu/dish_ingredient.model";
import { Ingredient } from "../../models/reference/ingredient.model";
import { Allergy } from "../../models/reference/allergy.model";
import { APIError } from "../../utils/errorHandler";
import { BAD_REQUEST, NOT_FOUND } from "../../constants/STATUS_CODES";
import { Customization } from "../../models/foodmenu/customization.model";
import { DishSize } from "../../models/foodmenu/custom/dishsize.model";
import { CookingStyle } from "../../models/foodmenu/custom/cookingstyle.model";
import { Spiciness } from "../../models/foodmenu/custom/spiciness.model";
import { DishExclusion } from "../../models/foodmenu/custom/dishexclusion.model";
import { DishAddon } from "../../models/foodmenu/custom/addon.model";
import { DishExtra } from "../../models/foodmenu/custom/extra.model";
import { DISH_ADDON_NOT_FOUND } from "../../constants/reference/dishAddon/err";
import { DISH_EXTRA_NOT_FOUND } from "../../constants/reference/dishExtra/err";
import {
  AMT_REQ_FOR_INGREDIENT,
  BEV_DISH_NOT_FOUND,
  DES_DISH_NOT_FOUND,
  DISH_CKSTYLE_NOT_FOUND,
  DISH_EXCL_NOT_FOUND,
  DISH_SIZE_NOT_FOUND,
  DISH_SPNESS_NOT_FOUND,
  ONEORMORE_ALLERGIES_NOTEXIST,
  SIDE_DISH_NOT_FOUND,
} from "../../constants/tenant/dish/err";

export const patchDishDetails = async (
  req: Request,
  exDish: Dish,
  DishRepo: Repository<Dish>,
  Side: Repository<DishSide>,
  Beverage: Repository<DishBeverage>,
  Dessert: Repository<DishDessert>
) => {
  try {
    const dishDetails = req.body.dishDetails;
    const oldTags = Array.isArray(exDish.tags) ? exDish.tags : [];
    const newTags = Array.isArray(dishDetails.tags) ? dishDetails.tags : [];

    const removedTags = oldTags.filter((tag) => !newTags.includes(tag));
    const addedTags = newTags.filter((tag: any) => !oldTags.includes(tag));

    const { availability, ...otherDetails } = dishDetails;

    exDish.update({
      availability: {
        ...exDish.availability,
        ...availability,
      },
      ...otherDetails,
    });

    for (const remTags of removedTags) {
      if (remTags === "side") {
        const exSide = await Side.findOneBy({
          dishId: exDish.dishId,
        });

        if (exSide) {
          await Side.remove(exSide);
        }
      } else if (remTags === "beverage") {
        const exBev = await Beverage.findOneBy({
          dishId: exDish.dishId,
        });

        if (exBev) {
          await Beverage.remove(exBev);
        }
      } else {
        const exDessert = await Dessert.findOneBy({
          dishId: exDish.dishId,
        });

        if (exDessert) {
          await Dessert.remove(exDessert);
        }
      }
    }

    for (const newTags of addedTags) {
      if (newTags === "side") {
        const newSide = Side.create({
          dish: exDish,
        });

        await Side.save(newSide);
      } else if (newTags === "beverage") {
        const newBev = Beverage.create({
          dish: exDish,
        });

        await Beverage.save(newBev);
      } else {
        const newDessert = Dessert.create({
          dish: exDish,
        });

        await Dessert.save(newDessert);
      }
    }

    await DishRepo.save(exDish);
  } catch (error) {
    throw error;
  }
};

export const patchDishAuxiliaryDetails = async (
  req: Request,
  exDish: Dish,
  DishRepo: Repository<Dish>,
  IngredientRepo: Repository<Ingredient>,
  DishIngredientRepo: Repository<DishIngredient>,
  AllergyRepo: Repository<Allergy>
) => {
  try {
    const { dishIngredients, allergies } = req.body.auxiliaryDetails ?? {};

    if (dishIngredients) {
      const newIngredientMap = new Map<string, { amount: number }>();
      for (const di of dishIngredients) {
        newIngredientMap.set(di.ingredientId, {
          amount: di.amount || undefined,
        });
      }

      const existingDishIngredients = exDish.dishIngredients || [];
      const existingIngredientMap = new Map<string, DishIngredient>();
      for (const di of existingDishIngredients) {
        existingIngredientMap.set(di.ingredient.ingredientId, di);
      }

      // Add or update ingredients
      for (const { ingredientId, amount } of dishIngredients) {
        if (existingIngredientMap.has(ingredientId)) {
          if (amount !== undefined && amount !== null) {
            const exIngredient = existingIngredientMap.get(ingredientId)!;
            if (exIngredient.amount !== amount) {
              exIngredient.amount = amount;
              await DishIngredientRepo.save(exIngredient);
            }
          }
        } else {
          if (amount === undefined || amount === null) {
            throw new APIError(BAD_REQUEST, AMT_REQ_FOR_INGREDIENT);
          }
          const ingredient = await IngredientRepo.findOneByOrFail({
            ingredientId,
          });
          const newDishIng = DishIngredientRepo.create({
            dish: exDish,
            ingredient,
            amount,
          });
          await DishIngredientRepo.save(newDishIng);
          exDish.dishIngredients.push(newDishIng);
        }
      }

      // Remove ingredients not in the new list
      const toRemove = exDish.dishIngredients.filter(
        (di) => !newIngredientMap.has(di.ingredient.ingredientId)
      );
      if (toRemove.length > 0) {
        await DishIngredientRepo.remove(toRemove);
      }
      exDish.dishIngredients = exDish.dishIngredients.filter((di) =>
        newIngredientMap.has(di.ingredient.ingredientId)
      );

      await DishRepo.save(exDish);
    }

    // Handle allergies
    if (allergies && Array.isArray(allergies)) {
      const existingAllergyIds = (exDish.allergies || []).map(
        (a) => a.allergyId
      );
      const toAdd = allergies.filter(
        (id: string) => !existingAllergyIds.includes(id)
      );
      const toRemove = existingAllergyIds.filter(
        (id) => !allergies.includes(id)
      );

      if (toAdd.length > 0) {
        const allergyList = await AllergyRepo.findBy({ allergyId: In(toAdd) });
        if (allergyList.length !== toAdd.length) {
          throw new APIError(BAD_REQUEST, ONEORMORE_ALLERGIES_NOTEXIST);
        }
        exDish.allergies = [
          ...(exDish.allergies || []),
          ...allergyList.filter(
            (a) => !existingAllergyIds.includes(a.allergyId)
          ),
        ];
      }

      if (toRemove.length > 0 && exDish.allergies) {
        exDish.allergies = exDish.allergies.filter(
          (a) => !toRemove.includes(a.allergyId)
        );
      }

      await DishRepo.save(exDish);
    }
  } catch (error) {
    throw error;
  }
};

export const patchDishPreliminaryCustomization = async (
  req: Request,
  exDish: Dish,
  Customization: Repository<Customization>,
  DishSize: Repository<DishSize>,
  CookingStyle: Repository<CookingStyle>,
  Spiciness: Repository<Spiciness>,
  Exclusion: Repository<DishExclusion>
) => {
  try {
    const {
      maxSelection,
      enforceMaxSelection,
      dishSizes,
      dishExclusions,
      cookingStyles,
      spiciness,
    } = req.body.customizations ?? {};

    let customization = exDish.customization;

    if (!customization) {
      const newCustomization = Customization.create({
        dish: exDish,
        dishSizes: [],
        dishExclusions: [],
        cookingStyles: [],
        spiciness: [],
        dishAddons: [],
        dishExtras: [],
        dishSides: [],
        dishBeverages: [],
        dishDesserts: [],
      })

      customization = newCustomization
    } else {
      customization.update({
        maxSelection: maxSelection || customization.maxSelection,
        enforceMaxSelection:
          enforceMaxSelection || customization.enforceMaxSelection,
      });
    }

    await Customization.save(customization);

    if (dishSizes) {
      const incomingMap = new Map<string, any>();
      for (const size of dishSizes) {
        if (size.dishSizeId) {
          incomingMap.set(size.dishSizeId, size);
        }
      }
      const existingDishSizes = customization.dishSizes || [];
      const existingMap = new Map<string, DishSize>();
      for (const ds of existingDishSizes) {
        existingMap.set(ds.dishSizeId, ds);
      }

      let defaultDishSizeId: string | undefined = undefined;
      for (const size of dishSizes) {
        if (size.isDefault) {
          defaultDishSizeId = size.dishSizeId;
          break;
        }
      }

      if (!defaultDishSizeId) {
        for (const ds of existingDishSizes) {
          if (ds.isDefault) {
            defaultDishSizeId = ds.dishSizeId;
            break;
          }
        }
      }

      for (const size of dishSizes) {
        if (size.dishSizeId && existingMap.has(size.dishSizeId)) {
          // Update the entity that's already in the collection
          const existingDishSize = existingMap.get(size.dishSizeId);

          if (!existingDishSize) {
            throw new APIError(NOT_FOUND, DISH_SIZE_NOT_FOUND);
          }

          if (size.name !== undefined) existingDishSize.name = size.name;
          if (size.price !== undefined) existingDishSize.price = size.price;
          existingDishSize.isDefault = size.dishSizeId === defaultDishSizeId;

          // Save the individual entity
          await DishSize.save(existingDishSize);
        } else {
          const newDishSize = DishSize.create({
            ...size,
            isDefault: size.dishSizeId === defaultDishSizeId,
          } as DishSize);
          await DishSize.save(newDishSize);
          customization.dishSizes.push(newDishSize);
        }
      }

      for (const [dishSizeId, ds] of Array.from(existingMap.entries())) {
        if (!incomingMap.has(dishSizeId)) {
          customization.dishSizes = customization.dishSizes.filter(
            (d) => d.dishSizeId !== dishSizeId
          );
          await DishSize.remove(ds);
        }
      }

      // Ensure only one dish size is marked as default
      for (const ds of customization.dishSizes) {
        ds.isDefault = ds.dishSizeId === defaultDishSizeId;
      }

      await Customization.save(customization);
    }

    if (dishExclusions) {
      const incomingMap = new Map<string, any>();

      for (const exclusion of dishExclusions) {
        if (exclusion.dishExclusionId) {
          incomingMap.set(exclusion.dishExclusionId, exclusion);
        }
      }

      const existingExclusions = customization.dishExclusions || [];
      const existingMap = new Map<string, DishExclusion>();
      for (const excl of existingExclusions) {
        existingMap.set(excl.dishExclusionId, excl);
      }

      for (const excl of dishExclusions) {
        if (excl.dishExclusionId && existingMap.has(excl.dishExclusionId)) {
          // Update the entity from the map
          const existingExclusion = existingMap.get(excl.dishExclusionId);

          if (!existingExclusion) {
            throw new APIError(NOT_FOUND, DISH_EXCL_NOT_FOUND);
          }

          if (excl.name !== undefined) existingExclusion.name = excl.name;

          // Save the individual entity
          await Exclusion.save(existingExclusion);
        } else {
          const newExclusion = Exclusion.create(excl as DishExclusion);
          await Exclusion.save(newExclusion);
          customization.dishExclusions.push(newExclusion);
        }
      }

      for (const [dishExclusionId, dexcl] of Array.from(
        existingMap.entries()
      )) {
        if (!incomingMap.has(dishExclusionId)) {
          customization.dishExclusions = customization.dishExclusions.filter(
            (d) => d.dishExclusionId !== dishExclusionId
          );
          await Exclusion.remove(dexcl);
        }
      }

      await Customization.save(customization);
    }

    if (cookingStyles) {
      const incomingMap = new Map<string, any>();

      for (const cookingStyle of cookingStyles) {
        if (cookingStyle.cookingStyleId) {
          incomingMap.set(cookingStyle.cookingStyleId, cookingStyle);
        }
      }

      const existingCookingStyles = customization.cookingStyles || [];
      const existingMap = new Map<string, CookingStyle>();
      for (const ckStyle of existingCookingStyles) {
        existingMap.set(ckStyle.cookingStyleId, ckStyle);
      }

      for (const ckStyle of cookingStyles) {
        if (ckStyle.cookingStyleId && existingMap.has(ckStyle.cookingStyleId)) {
          // Update the entity from the map
          const existingStyle = existingMap.get(ckStyle.cookingStyleId);

          if (!existingStyle) {
            throw new APIError(NOT_FOUND, DISH_CKSTYLE_NOT_FOUND);
          }

          if (ckStyle.name !== undefined) existingStyle.name = ckStyle.name;

          // Save the individual entity
          await CookingStyle.save(existingStyle);
        } else {
          const newCookingStyle = CookingStyle.create(ckStyle as CookingStyle);
          await CookingStyle.save(newCookingStyle);
          customization.cookingStyles.push(newCookingStyle);
        }
      }

      for (const [cookingStyleId, dckStyle] of Array.from(
        existingMap.entries()
      )) {
        if (!incomingMap.has(cookingStyleId)) {
          customization.cookingStyles = customization.cookingStyles.filter(
            (d) => d.cookingStyleId !== cookingStyleId
          );
          await CookingStyle.remove(dckStyle);
        }
      }

      await Customization.save(customization);
    }

    if (spiciness) {
      const incomingMap = new Map<string, any>();

      for (const spness of spiciness) {
        if (spness.spicinessId) {
          incomingMap.set(spness.spicinessId, spness);
        }
      }

      const existingSpiciness = customization.spiciness || [];
      const existingMap = new Map<string, Spiciness>();
      for (const spness of existingSpiciness) {
        existingMap.set(spness.spicinessId, spness);
      }

      for (const spness of spiciness) {
        if (spness.spicinessId && existingMap.has(spness.spicinessId)) {
          // Update the entity from the map
          const existingSpice = existingMap.get(spness.spicinessId);

          if (!existingSpice) {
            throw new APIError(NOT_FOUND, DISH_SPNESS_NOT_FOUND);
          }

          if (spness.name !== undefined) existingSpice.name = spness.name;
          if (spness.price !== undefined) existingSpice.price = spness.price;

          // Save the individual entity
          await Spiciness.save(existingSpice);
        } else {
          const newSpiciness = Spiciness.create(spness as Spiciness);
          await Spiciness.save(newSpiciness);
          customization.spiciness.push(newSpiciness);
        }
      }

      for (const [spicinessId, dSpiciness] of Array.from(
        existingMap.entries()
      )) {
        if (!incomingMap.has(spicinessId)) {
          customization.spiciness = customization.spiciness.filter(
            (d) => d.spicinessId !== spicinessId
          );
          await Spiciness.remove(dSpiciness);
        }
      }

      await Customization.save(customization);

      return customization
    }
  } catch (error) {
    throw error;
  }
};

export const patchDishAddonsAndExtras = async (
  req: Request,
  exDish: Dish,
  customization: Customization,
  AddonRepo: Repository<DishAddon>,
  ExtraRepo: Repository<DishExtra>,
  CustomizationRepo: Repository<Customization>
) => {
  try {
    const { dishAddons, dishExtras } = req.body.customizations ?? {};

    if (dishAddons) {
      const incomingMap = new Set<string>();

      for (const addon of dishAddons) {
        incomingMap.add(addon.addonId);
      }

      const existingMap = new Set<string>();

      for (const addon of customization.dishAddons) {
        existingMap.add(addon.addonId);
      }

      // Add new addons
      for (const addon of dishAddons) {
        if (addon.addonId && !existingMap.has(addon.addonId)) {
          const exAddon = await AddonRepo.findOneBy({
            addonId: addon.addonId,
          });

          if (!exAddon) {
            throw new APIError(NOT_FOUND, DISH_ADDON_NOT_FOUND);
          }
          customization.dishAddons.push(exAddon);
        }
      }

      // Remove addons not in the incoming list
      customization.dishAddons = customization.dishAddons.filter((dishAddon) =>
        incomingMap.has(dishAddon.addonId)
      );

      await CustomizationRepo.save(customization);
    }

    if (dishExtras) {
      const incomingMap = new Set<string>();

      for (const extra of dishExtras) {
        incomingMap.add(extra.extraId);
      }

      const existingMap = new Set<string>();

      for (const extra of customization.dishExtras) {
        existingMap.add(extra.extraId);
      }

      // Add new extras
      for (const extra of dishExtras) {
        if (extra.extraId && !existingMap.has(extra.extraId)) {
          const exExtra = await ExtraRepo.findOneBy({
            extraId: extra.extraId,
          });

          if (!exExtra) {
            throw new APIError(NOT_FOUND, DISH_EXTRA_NOT_FOUND);
          }
          customization.dishExtras.push(exExtra);
        }
      }

      // Remove extras not in the incoming list
      customization.dishExtras = customization.dishExtras.filter((dishExtra) =>
        incomingMap.has(dishExtra.extraId)
      );

      await CustomizationRepo.save(customization);
    }
  } catch (error) {
    throw error;
  }
};

export const patchDishFinalCustomizations = async (
  req: Request,
  customization: Customization,
  Side: Repository<DishSide>,
  Beverage: Repository<DishBeverage>,
  Dessert: Repository<DishDessert>,
  CustomizationRepo: Repository<Customization>
) => {
  try {
    const { dishSides, dishBeverages, dishDesserts } =
      req.body.customizations ?? {};

    // Sides
    if (dishSides) {
      const incomingMap = new Set<string>();

      for (const side of dishSides) {
        incomingMap.add(side.dishSideId);
      }

      const existingMap = new Set<string>();

      for (const side of customization.dishSides) {
        existingMap.add(side.dishSideId);
      }

      // Add new sides
      for (const side of dishSides) {
        if (side.dishSideId && !existingMap.has(side.dishSideId)) {
          const exSide = await Side.findOneBy({
            dishSideId: side.dishSideId,
          });

          if (!exSide) {
            throw new APIError(NOT_FOUND, SIDE_DISH_NOT_FOUND);
          }
          customization.dishSides.push(exSide);
        }
      }

      // Remove sides not in the incoming list
      customization.dishSides = customization.dishSides.filter((dishSide) =>
        incomingMap.has(dishSide.dishSideId)
      );

      await CustomizationRepo.save(customization);
    }

    // Beverages
    if (dishBeverages) {
      const incomingMap = new Set<string>();

      for (const bev of dishBeverages) {
        incomingMap.add(bev.dishBevId);
      }

      const existingMap = new Set<string>();

      for (const bev of customization.dishBeverages) {
        existingMap.add(bev.dishBevId);
      }

      // Add new beverages
      for (const bev of dishBeverages) {
        if (bev.dishBevId && !existingMap.has(bev.dishBevId)) {
          const exBev = await Beverage.findOneBy({
            dishBevId: bev.dishBevId,
          });

          if (!exBev) {
            throw new APIError(NOT_FOUND, BEV_DISH_NOT_FOUND);
          }
          customization.dishBeverages.push(exBev);
        }
      }

      // Remove beverages not in the incoming list
      customization.dishBeverages = customization.dishBeverages.filter(
        (dishBev) => incomingMap.has(dishBev.dishBevId)
      );

      await CustomizationRepo.save(customization);
    }

    // Desserts
    if (dishDesserts) {
      const incomingMap = new Set<string>();

      for (const dessert of dishDesserts) {
        incomingMap.add(dessert.dishDessertId);
      }

      const existingMap = new Set<string>();

      for (const dessert of customization.dishDesserts) {
        existingMap.add(dessert.dishDessertId);
      }

      // Add new desserts
      for (const dessert of dishDesserts) {
        if (dessert.dishDessertId && !existingMap.has(dessert.dishDessertId)) {
          const exDes = await Dessert.findOneBy({
            dishDessertId: dessert.dishDessertId,
          });

          if (!exDes) {
            throw new APIError(NOT_FOUND, DES_DISH_NOT_FOUND);
          }
          customization.dishDesserts.push(exDes);
        }
      }

      // Remove desserts not in the incoming list
      customization.dishDesserts = customization.dishDesserts.filter(
        (dishDes) => incomingMap.has(dishDes.dishDessertId)
      );

      await CustomizationRepo.save(customization);
    }
  } catch (error) {
    throw error;
  }
};
