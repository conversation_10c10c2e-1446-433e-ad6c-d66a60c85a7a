import {In, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Repository} from "typeorm";
import {Dish} from "../../models/foodmenu/dish.model";
import {getRepositories} from "../system/RepositoryHelper.helper";
import {DishUpcomingChange} from "../../models/foodmenu/updates/dish_upcoming_change.model";
import {UpcomingChange} from "../../models/foodmenu/updates/upcoming_change.model";

export const arraysEqual = (a: any[], b: any[]): boolean => {
    if (!a && !b) return true;
    if (!a || !b) return false;
    if (a.length !== b.length) return false;
    return a.every((val, index) => val === b[index]);
};

export const applyUpcomingChangesToDishes = async (
    dishes: Dish | Dish[],
    queryRunner: QueryRunner,
    foodMenuId?: string
): Promise<Dish | Dish[]> => {
    const {DishUpcomingChange, UpcomingChange} = getRepositories(
        queryRunner
    ) as {
        DishUpcomingChange: Repository<DishUpcomingChange>;
        UpcomingChange: Repository<UpcomingChange>;
    };

    const dishArray = Array.isArray(dishes) ? dishes : [dishes];
    const dishIds = dishArray.map((dish) => dish.dishId);

    if (dishIds.length === 0) {
        return dishes;
    }

    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    const whereConditions: any = {
        dishId: In(dishIds),
        upcomingChange: {
            activeFrom: LessThanOrEqual(currentDate),
            isActive: true,
        },
    };

    if (foodMenuId) {
        whereConditions.upcomingChange.foodMenuId = foodMenuId;
    }

    const activeChanges = await DishUpcomingChange.find({
        where: whereConditions,
        relations: ["upcomingChange"],
        order: {
            upcomingChange: {
                activeFrom: "DESC",
            },
        },
    });

    const latestChangesByDish = new Map<string, DishUpcomingChange>();

    for (const change of activeChanges) {
        const dishId = change.dishId;
        if (!latestChangesByDish.has(dishId)) {
            latestChangesByDish.set(dishId, change);
        }
    }

    const modifiedDishes = dishArray.map((dish) => {
        const change = latestChangesByDish.get(dish.dishId);

        if (!change) {
            const {section, ...rest} = dish
            return rest;
        }

        const modifiedDish = Object.assign(Object.create(Object.getPrototypeOf(dish)), dish);

        if (change.newName !== null && change.newName !== undefined) {
            modifiedDish.name = change.newName;
        }

        if (change.newPrice !== null && change.newPrice !== undefined) {
            modifiedDish.price = change.newPrice;
        }

        if (change.newDietaryInfo !== null && change.newDietaryInfo !== undefined) {
            modifiedDish.dietaryInfo = change.newDietaryInfo;
        }

        const {section, ...rest} = modifiedDish

        return rest;
    });

    return Array.isArray(dishes) ? modifiedDishes : modifiedDishes[0];
};

// new

export const applyUpcomingChangesToDishesBatch = async (
  dishIds: string[],
  queryRunner: QueryRunner,
  foodMenuId?: string
): Promise<Map<string, any>> => {
  
  if (dishIds.length === 0) return new Map();

  const { DishUpcomingChange } = getRepositories(queryRunner) as {
    DishUpcomingChange: Repository<DishUpcomingChange>;
  };

  const currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0);

  // SINGLE QUERY for all upcoming changes
  const queryBuilder = DishUpcomingChange.createQueryBuilder("duc")
    .leftJoinAndSelect("duc.upcomingChange", "uc")
    .where("duc.dishId IN (:...dishIds)", { dishIds })
    .andWhere("uc.activeFrom <= :currentDate", { currentDate })
    .andWhere("uc.isActive = :isActive", { isActive: true })
    .orderBy("uc.activeFrom", "DESC");

  if (foodMenuId) {
    queryBuilder.andWhere("uc.foodMenuId = :foodMenuId", { foodMenuId });
  }

  const activeChanges = await queryBuilder.getMany();

  // GROUP BY dishId and get latest change
  const changesByDish = new Map<string, any>();
  for (const change of activeChanges) {
    if (!changesByDish.has(change.dishId)) {
      changesByDish.set(change.dishId, {
        newName: change.newName,
        newPrice: change.newPrice,
        newDietaryInfo: change.newDietaryInfo,
      });
    }
  }

  return changesByDish;
};

export const loadCustomizationDataBatch = async (
  dishIds: string[],
  queryRunner: QueryRunner
): Promise<Map<string, any>> => {
  
  if (dishIds.length === 0) return new Map();

  const { Customization, DishIngredient, Allergy } = getRepositories(queryRunner) as any;

  // CONCURRENT LOADING of customization data
  const [customizations, ingredients, allergies] = await Promise.all([
    Customization.find({
      where: { 
        dish: { 
          dishId: In(dishIds) 
        },
      },
      relations: [
        "dish",
        "dishSizes", "dishExclusions", "cookingStyles", "spiciness",
        "dishAddons", "dishExtras", "dishSides", "dishBeverages", "dishDesserts",
        "dishSides.dish", "dishBeverages.dish", "dishDesserts.dish"
      ]
    }),
    DishIngredient.find({ 
      where: { 
        dish: { dishId: In(dishIds) } 
      },
      relations: ['dish']
    }),
    // Query through the join table with correct column names
    Allergy.createQueryBuilder('allergy')
      .innerJoin('dish_allergies', 'da', 'da.allergiesAllergyId = allergy.allergyId')
      .where('da.dishesDishId IN (:...dishIds)', { dishIds })
      .getMany()
  ]);

  // Map data by dishId for efficient lookup
  const customizationMap = new Map();
  customizations.forEach((c: any) => {
    if (c.dish && c.dish.dishId) {
      const { dish, dishSizes, dishExclusions, cookingStyles, spiciness,
        dishAddons, dishExtras, dishSides, dishBeverages, dishDesserts, ...rest } = c;
      // Filter out sides, beverages, and desserts with invisible dishes
      const filterVisible = (arr: any[]) =>
        Array.isArray(arr)
          ? arr.filter((item: any) => item.dish?.visibility !== false)
          : undefined;

      customizationMap.set(c.dish.dishId, {
        ...rest,
        dishSizes: dishSizes ? dishSizes : undefined,
        dishExclusions: dishExclusions ? dishExclusions : undefined,
        cookingStyles: cookingStyles ? cookingStyles : undefined,
        spiciness: spiciness ? spiciness : undefined,
        dishAddons: dishAddons ? dishAddons : undefined,
        dishExtras: dishExtras ? dishExtras : undefined,
        dishSides: filterVisible(dishSides),
        dishBeverages: filterVisible(dishBeverages),
        dishDesserts: filterVisible(dishDesserts)
      });
    }
  });

  const ingredientMap = new Map();
  ingredients.forEach((ing: any) => {
    if (!ingredientMap.has(ing.dish.dishId)) ingredientMap.set(ing.dish.dishId, []);

    const {dish, ...rest} = ing
    ingredientMap.get(ing.dish.dishId).push(rest);
  });

  const allergyMap = new Map();
  // Fetch dish-to-allergy relationships
  if (allergies.length > 0) {
    const dishAllergyRelations = await queryRunner.manager
      .createQueryBuilder()
      .select(['da.dishesDishId', 'da.allergiesAllergyId'])
      .from('dish_allergies', 'da')
      .where('da.dishesDishId IN (:...dishIds)', { dishIds })
      .getRawMany();

    const allergyById = new Map(allergies.map((a: any) => [a.allergyId, a]));
    dishAllergyRelations.forEach(({ da_dishesDishId, da_allergiesAllergyId }) => {
      if (!allergyMap.has(da_dishesDishId)) {
        allergyMap.set(da_dishesDishId, []);
      }
      const allergy = allergyById.get(da_allergiesAllergyId);
      if (allergy) {
        allergyMap.get(da_dishesDishId).push(allergy);
      }
    });
  }

  return new Map([
    ['customizations', customizationMap],
    ['ingredients', ingredientMap],
    ['allergies', allergyMap]
  ]);
};

// ========== HELPER FUNCTIONS ==========
export const filterUnavailableItems = (menu: any) => {
  if (!menu.sections) return menu;

  menu.sections = menu.sections
    .filter((section: any) => section.availabilityInfo?.isAvailable)
    .map((section: any) => ({
      ...section,
      dishes: section.dishes?.filter((dish: any) => dish.availabilityInfo?.isAvailable) || []
    }))
    .filter((section: any) => section.dishes.length > 0);

  return menu;
};

export const extractDishIds = (menu: any): string[] => {
  if (!menu.sections) return [];
  return menu.sections.flatMap((section: any) => 
    (section.dishes || []).map((dish: any) => dish.dishId)
  );
};

export const mergeMenuData = (
  menu: any,
  upcomingChanges: Map<string, any>,
  customizationData: Map<string, any>,
  forPOS: boolean
) => {
  const customizations = customizationData.get('customizations') || new Map();
  const ingredients = customizationData.get('ingredients') || new Map();
  const allergies = customizationData.get('allergies') || new Map();

  if (menu.sections) {
    for (const section of menu.sections) {
      if (section.dishes) {
        for (const dish of section.dishes) {
          // Apply upcoming changes
          const changes = upcomingChanges.get(dish.dishId);
          if (changes) {
            if (changes.newName !== null) dish.name = changes.newName;
            if (changes.newPrice !== null) dish.price = changes.newPrice;
            if (changes.newDietaryInfo !== null) dish.dietaryInfo = changes.newDietaryInfo;
          }

          // Attach customization data
          dish.customization = customizations.get(dish.dishId);
          dish.dishIngredients = ingredients.get(dish.dishId) || [];
          dish.allergies = allergies.get(dish.dishId) || [];

          // Remove section reference for performance
          delete dish.section;
        }
      }
    }
  }

  // Final filtering for customer app
  if (!forPOS) {
    return filterUnavailableItems(menu);
  }

  return menu;
};