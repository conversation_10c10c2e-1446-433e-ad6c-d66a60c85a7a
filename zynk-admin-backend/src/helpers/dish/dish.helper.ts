import { NextFunction, Request } from "express";
import { In, Repository } from "typeorm";
import { Dish } from "../../models/foodmenu/dish.model";
import { Section } from "../../models/foodmenu/section.model";
import { APIError, errorHandler } from "../../utils/errorHandler";
import { CONFLICT } from "../../constants/STATUS_CODES";
import { DISH_ALREADY_EXISTS, ONEORMORE_ADDONS_NOTEXIST, ONEORMORE_ALLERGIES_NOTEXIST, ONEORMORE_BEVS_NOTEXIST, ONEORMORE_DESSERT_NOTEXIST, ONEORMORE_EXTRAS_NOTEXIST, ONEORMORE_INGREDIENT_NOTEXIST, ONEORMORE_SIDES_NOTEXIST } from "../../constants/tenant/dish/err";
import { Customization } from "../../models/foodmenu/customization.model";
import { Ingredient } from "../../models/reference/ingredient.model";
import { DishIngredient } from "../../models/foodmenu/dish_ingredient.model";
import { Allergy } from "../../models/reference/allergy.model";
import { DishSize } from "../../models/foodmenu/custom/dishsize.model";
import { CookingStyle } from "../../models/foodmenu/custom/cookingstyle.model";
import { Spiciness } from "../../models/foodmenu/custom/spiciness.model";
import { DishExclusion } from "../../models/foodmenu/custom/dishexclusion.model";
import { DishAddon } from "../../models/foodmenu/custom/addon.model";
import { DishExtra } from "../../models/foodmenu/custom/extra.model";
import { DishSide } from "../../models/foodmenu/custom/dish_side.model";
import { DishBeverage } from "../../models/foodmenu/custom/dish_bev.model";
import { DishDessert } from "../../models/foodmenu/custom/dish_dessert.model";

/** Actual dish details */
export const createDishDetails = async (
  req: Request,
  DishRepo: Repository<Dish>,
  exSection: Section,
  DishSideRepo: Repository<DishSide>,
  DishBeverageRepo: Repository<DishBeverage>,
  DishDessertRepo: Repository<DishDessert>
) => {
  try {
    const dishDetails = req.body.dishDetails;

    const exDishName = await DishRepo.findOne({
      where: {
        name: dishDetails.name,
        section: {
          menuSectionId: exSection.menuSectionId,
        },
      },
    });

    if (exDishName) throw new APIError(CONFLICT, DISH_ALREADY_EXISTS);

    const newDish = DishRepo.create({
      ...(dishDetails as Dish),
      section: exSection,
    });

    if (newDish.tags && newDish.tags.length > 0) {
      if (newDish.tags.includes("side")) {
        const dishSide = DishSideRepo.create({
          dish: newDish,
        });
        newDish.asSide = dishSide;

        await DishSideRepo.save(dishSide);
      }

      if (newDish.tags.includes("beverage")) {
        const dishBeverage = DishBeverageRepo.create({
          dish: newDish,
        });
        newDish.asBeverage = dishBeverage;

        await DishBeverageRepo.save(dishBeverage);
      }

      if (newDish.tags.includes("dessert")) {
        const dishDessert = DishDessertRepo.create({
          dish: newDish,
        });
        newDish.asDessert = dishDessert;

        await DishDessertRepo.save(dishDessert);
      }
    }

    await DishRepo.save(newDish);
    return newDish;
  } catch (error) {
    throw error;
  }
};

/** Dish additional/auxiliary data like ingredients, allergies, etc. */
export const createDishAuxiliaryData = async (
  req: Request,
  newDish: Dish,
  DishRepo: Repository<Dish>,
  IngredientRepo: Repository<Ingredient>,
  DishIngredientRepo: Repository<DishIngredient>,
  AllergyRepo: Repository<Allergy>
) => {
  try {
    const { dishIngredients, allergies } = req.body.auxiliaryDetails ?? {};

    if (dishIngredients) {
      const ingredients = await IngredientRepo.findBy({
        ingredientId: In(
          dishIngredients.map(
            (ingredient: { ingredientId: string }) => ingredient.ingredientId
          )
        ),
      });

      if (ingredients.length !== dishIngredients.length) {
        throw new APIError(CONFLICT, ONEORMORE_INGREDIENT_NOTEXIST);
      }

      for (const ingredientData of dishIngredients) {
        const ingredient = ingredients.find(
          (i) => i.ingredientId === ingredientData.ingredientId
        );
        if (ingredient) {
          const dishIngredient = DishIngredientRepo.create({
            dish: newDish,
            ingredient: ingredient,
            amount: ingredientData.amount,
          });
          await DishIngredientRepo.save(dishIngredient);
        }
      }
    }

    if (allergies && Array.isArray(allergies)) {
      const allergyList = await AllergyRepo.findBy({
        allergyId: In(allergies),
      });

      if (allergyList.length !== allergies.length) {
        throw new APIError(CONFLICT, ONEORMORE_ALLERGIES_NOTEXIST);
      }

      newDish.allergies = allergyList;

      await DishRepo.save(newDish);
    }
  } catch (error) {
    throw error;
  }
};

/** Preliminary customizations like actual customization data, dish size,
 * cooking style, spiciness, and exclusions
 */
export const createPreliminaryCustomizations = async (
  req: Request,
  newDish: Dish,
  CustomizationRepo: Repository<Customization>,
  DishSize: Repository<DishSize>,
  CookingStyle: Repository<CookingStyle>,
  Spiciness: Repository<Spiciness>,
  Exclusion: Repository<DishExclusion>
) => {
  try {
    const {
      maxSelection,
      enforceMaxSelection,
      dishSizes,
      dishExclusions,
      cookingStyles,
      spiciness,
    } = req.body.customizations ?? {};

    const customization = CustomizationRepo.create({
      maxSelection: maxSelection ?? 4,
      enforceMaxSelection: enforceMaxSelection ?? true,
      dish: newDish,
      dishSizes: [],
      cookingStyles: [],
      spiciness: [],
      dishExclusions: [],
      dishAddons: [],
      dishExtras: [],
      dishSides: [],
      dishBeverages: [],
      dishDesserts: [],
    });

    await CustomizationRepo.save(customization);

    if (newDish.isCustomizable) {
      if (dishSizes) {
        for (const size of dishSizes) {
          const dishSize = DishSize.create(size as DishSize);
          await DishSize.save(dishSize);
          customization.dishSizes.push(dishSize)
        }
      }

      if (cookingStyles) {
        for (const style of cookingStyles) {
          const cookingStyle = CookingStyle.create(style as CookingStyle);
          await CookingStyle.save(cookingStyle);
          customization.cookingStyles.push(cookingStyle)
        }
      }

      if (spiciness) {
        for (const spice of spiciness) {
          const spiceLevel = Spiciness.create(spice as Spiciness);
          await Spiciness.save(spiceLevel);
          customization.spiciness.push(spiceLevel)
        }
      }

      if (dishExclusions) {
        for (const exclusion of dishExclusions) {
          const dishExclusion = Exclusion.create(exclusion as DishExclusion);
          await Exclusion.save(dishExclusion);
          customization.dishExclusions.push(dishExclusion)
        }
      }
    }

    return customization;
  } catch (error) {
    throw error;
  }
};

/** Add addons and extras to the dish
 */
export const addAddonsAndExtras = async (
  req: Request,
  newDish: Dish,
  customization: Customization,
  AddonRepo: Repository<DishAddon>,
  ExtraRepo: Repository<DishExtra>,
  CustomizationRepo: Repository<Customization>
) => {
  try {
    const { dishAddons, dishExtras } = req.body.customizations ?? {};

    if (newDish.isCustomizable) {
      if (dishAddons) {
        for (const addon of dishAddons) {
          const dishAddon = await AddonRepo.findOne({
            where: {
              addonId: addon.addonId,
            },
          });

          if (!dishAddon) {
            throw new APIError(CONFLICT, ONEORMORE_ADDONS_NOTEXIST);
          }

          customization.dishAddons.push(dishAddon);
        }
      }

      if (dishExtras) {
        for (const extra of dishExtras) {
          const dishExtra = await ExtraRepo.findOne({
            where: {
              extraId: extra.extraId,
            },
          });

          if (!dishExtra) {
            throw new APIError(CONFLICT, ONEORMORE_EXTRAS_NOTEXIST);
          }

          customization.dishExtras.push(dishExtra);
        }
      }
    }

    await CustomizationRepo.save(customization);
  } catch (error) {
    throw error;
  }
};

export const addFinalCustomizations = async (
  req: Request,
  newDish: Dish,
  customization: Customization,
  Side: Repository<DishSide>,
  Beverage: Repository<DishBeverage>,
  Dessert: Repository<DishDessert>,
  CustomizationRepo: Repository<Customization>
) => {
  try {
    const {
      dishSides,

      dishBeverages,

      dishDesserts,
    } = req.body.customizations ?? {};

    if (newDish.isCustomizable) {
      if (dishSides) {
        for (const side of dishSides) {
          const dishSide = await Side.findOne({
            where: {
              dishSideId: side.dishSideId,
            },
          });

          if (!dishSide) {
            throw new APIError(CONFLICT, ONEORMORE_SIDES_NOTEXIST);
          }

          customization.dishSides.push(dishSide);
        }
      }

      if (dishBeverages) {
        for (const beverage of dishBeverages) {
          const dishBeverage = await Beverage.findOne({
            where: {
              dishBevId: beverage.dishBevId,
            },
          });

          if (!dishBeverage) {
            throw new APIError(CONFLICT, ONEORMORE_BEVS_NOTEXIST);
          }

          customization.dishBeverages.push(dishBeverage);
        }
      }

      if (dishDesserts) {
        for (const dessert of dishDesserts) {
          const dishDessert = await Dessert.findOne({
            where: {
              dishDessertId: dessert.dishDessertId,
            },
          });

          if (!dishDessert) {
            throw new APIError(CONFLICT, ONEORMORE_DESSERT_NOTEXIST);
          }

          customization.dishDesserts.push(dishDessert);
        }
      }
    }

    await CustomizationRepo.save(customization);
  } catch (error) {
    throw error;
  }
};

interface DishBasicDetails {
  dishId: string;
  pictureUrl: string | null;
  name: string;
  description: string | null;
  price: number;
  dietaryInfo: string[] | null;
  tags: string[] | null;
  isCustomizable: boolean;
  visibility: boolean;
  customTimes: boolean;
  availability: {
    site: boolean;
    dineIn: boolean;
    mobile: boolean;
    pickup: boolean;
    delivery: boolean;
    phoneOrder: boolean;
    contactlessDineIn: boolean;
  };
  preparationTime: number,
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

export const filterDishResponse = (dish: Dish): DishBasicDetails => {
  return {
    dishId: dish.dishId,
    pictureUrl: dish.pictureUrl,
    name: dish.name,
    description: dish.description,
    price: dish.price,
    dietaryInfo: dish.dietaryInfo,
    tags: dish.tags,
    isCustomizable: dish.isCustomizable,
    visibility: dish.visibility,
    preparationTime: dish.preparationTime,
    customTimes: dish.customTimes,
    availability: dish.availability,
    createdAt: dish.createdAt,
    updatedAt: dish.updatedAt,
    deletedAt: dish.deletedAt || null
  };
};