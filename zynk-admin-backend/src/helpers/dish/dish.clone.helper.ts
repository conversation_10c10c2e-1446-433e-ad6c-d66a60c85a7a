import { In, Repository } from "typeorm";
import { Dish } from "../../models/foodmenu/dish.model";
import { APIError } from "../../utils/errorHandler";
import { CONFLICT } from "../../constants/STATUS_CODES";
import {
  DISH_NOT_FOUND,
  ONEORMORE_ADDONS_NOTEXIST,
  ONEORMORE_ALLERGIES_NOTEXIST,
  ONEORMORE_BEVS_NOTEXIST,
  ONEORMORE_DESSERT_NOTEXIST,
  ONEORMORE_EXTRAS_NOTEXIST,
  ONEORMORE_INGREDIENT_NOTEXIST,
  ONEORMORE_SIDES_NOTEXIST,
} from "../../constants/tenant/dish/err";
import { DishSide } from "../../models/foodmenu/custom/dish_side.model";
import { DishBeverage } from "../../models/foodmenu/custom/dish_bev.model";
import { DishDessert } from "../../models/foodmenu/custom/dish_dessert.model";
import { Ingredient } from "../../models/reference/ingredient.model";
import { DishIngredient } from "../../models/foodmenu/dish_ingredient.model";
import { Allergy } from "../../models/reference/allergy.model";
import { Customization } from "../../models/foodmenu/customization.model";
import { DishSize } from "../../models/foodmenu/custom/dishsize.model";
import { CookingStyle } from "../../models/foodmenu/custom/cookingstyle.model";
import { Spiciness } from "../../models/foodmenu/custom/spiciness.model";
import { DishExclusion } from "../../models/foodmenu/custom/dishexclusion.model";
import { DishAddon } from "../../models/foodmenu/custom/addon.model";
import { DishExtra } from "../../models/foodmenu/custom/extra.model";
import { Section } from "../../models/foodmenu/section.model";
import { SECTION_NOT_FOUND } from "../../constants/tenant/sections/err";
import { CustomHourSlot } from "../../models/common/customhourslot.model";
import { SpecialDay } from "../../models/common/specialday.model";

export const cloneDishBasicDetails = async (
  oldDish: Dish,
  DishRepo: Repository<Dish>,
  DishSideRepo: Repository<DishSide>,
  DishBeverageRepo: Repository<DishBeverage>,
  DishDessertRepo: Repository<DishDessert>,
  Section: Repository<Section>,
  sectionToDuplicate: { default: boolean; sectionId: string } = {
    default: true,
    sectionId: "",
  }
) => {
  try {
    // Find all dishes whose names match the pattern: original, " - Copy", " - Copy(n)"
    const baseName = oldDish.name;
    // Fixed regex to match the actual naming pattern we'll use
    const copyPattern = new RegExp(
      `^${baseName}( - Copy(\\((\\d+)\\))?)?$`,
      "i"
    );
    const allDishes = await DishRepo.find({
      where: {
        section: {
          menuSectionId: oldDish.section.menuSectionId,
        },
      },
    });

    const matchingDishes = allDishes.filter((dish) =>
      copyPattern.test(dish.name)
    );

    let clonedDishName = oldDish.name;

    if (matchingDishes.length === 1) {
      // Only original exists, first copy
      clonedDishName += " - Copy";
    } else if (matchingDishes.length > 1) {
      // Multiple dishes exist, need to find next number
      let hasCopyWithoutNumber = false;
      let maxCopyNumber = 0;

      matchingDishes.forEach((dish) => {
        if (dish.name === baseName) {
          // This is the original, skip
          return;
        }

        if (dish.name === `${baseName} - Copy`) {
          // Found the unnumbered copy
          hasCopyWithoutNumber = true;
        } else {
          // Check for numbered copies like "Name - Copy(1)"
          const numberMatch = dish.name.match(
            new RegExp(`^${baseName} - Copy\\((\\d+)\\)$`, "i")
          );
          if (numberMatch) {
            const copyNumber = parseInt(numberMatch[1], 10);
            maxCopyNumber = Math.max(maxCopyNumber, copyNumber);
          }
        }
      });

      if (!hasCopyWithoutNumber) {
        // No unnumbered copy exists yet, create it
        clonedDishName += " - Copy";
      } else {
        // Unnumbered copy exists, create numbered copy starting from (1)
        clonedDishName += ` - Copy(${maxCopyNumber + 1})`;
      }
    } else {
      // This case should not happen since oldDish should exist
      throw new APIError(CONFLICT, DISH_NOT_FOUND);
    }

    let dishDetails = {
      pictureUrl: oldDish.pictureUrl,
      name: clonedDishName,
      description: oldDish.description || "",
      price: oldDish.price,
      dietaryInfo: oldDish.dietaryInfo,
      tags: oldDish.tags,
      isCustomizable: oldDish.isCustomizable,
      customTimes: oldDish.customTimes,
      preparationTime: oldDish.preparationTime,
      availability: oldDish.availability,
      visibility: oldDish.visibility,
      section: undefined as any,
      customSlots: [],
      specialDays: [],
    };

    if (sectionToDuplicate.default) {
      dishDetails.section = oldDish.section;
    } else {
      const anotherSection = await Section.findOneBy({
        menuSectionId: sectionToDuplicate.sectionId,
      });

      if (!anotherSection) {
        throw new APIError(CONFLICT, SECTION_NOT_FOUND);
      }
      dishDetails.section = anotherSection;
    }

    const clonedDish = DishRepo.create({
      ...dishDetails,
    });

    if (clonedDish.tags && clonedDish.tags.length > 0) {
      if (clonedDish.tags.includes("side")) {
        const dishSide = DishSideRepo.create({
          dish: clonedDish,
        });
        clonedDish.asSide = dishSide;
        await DishSideRepo.save(dishSide);
      }

      if (clonedDish.tags.includes("beverage")) {
        const dishBeverage = DishBeverageRepo.create({
          dish: clonedDish,
        });
        clonedDish.asBeverage = dishBeverage;
        await DishBeverageRepo.save(dishBeverage);
      }

      if (clonedDish.tags.includes("dessert")) {
        const dishDessert = DishDessertRepo.create({
          dish: clonedDish,
        });
        clonedDish.asDessert = dishDessert;
        await DishDessertRepo.save(dishDessert);
      }
    }

    await DishRepo.save(clonedDish);

    return clonedDish;
  } catch (error) {
    throw error;
  }
};

export const cloneDishAuxiliaryDetails = async (
  oldDish: Dish,
  newDish: Dish,
  DishRepo: Repository<Dish>,
  IngredientRepo: Repository<Ingredient>,
  DishIngredientRepo: Repository<DishIngredient>,
  AllergyRepo: Repository<Allergy>
) => {
  try {
    const { dishIngredients, allergies } = oldDish;

    if (dishIngredients && Array.isArray(dishIngredients)) {
      const ingredientIds = dishIngredients.map(
        (ingredient) => ingredient.ingredient.ingredientId
      );
      const ingredients = await IngredientRepo.findBy({
        ingredientId: In(ingredientIds),
      });

      if (ingredients.length !== dishIngredients.length) {
        throw new APIError(CONFLICT, ONEORMORE_INGREDIENT_NOTEXIST);
      }

      for (const ingredientData of dishIngredients) {
        const ingredient = ingredients.find(
          (i) => i.ingredientId === ingredientData.ingredient.ingredientId
        );
        if (ingredient) {
          const dishIngredient = DishIngredientRepo.create({
            dish: newDish,
            ingredient: ingredient,
            amount: ingredientData.amount,
          });
          await DishIngredientRepo.save(dishIngredient);
        }
      }
    }

    if (allergies && Array.isArray(allergies)) {
      const allergyList = await AllergyRepo.findBy({
        allergyId: In(allergies.map((al: Allergy) => al.allergyId)),
      });

      if (allergyList.length !== allergies.length) {
        throw new APIError(CONFLICT, ONEORMORE_ALLERGIES_NOTEXIST);
      }

      newDish.allergies = allergyList;
      await DishRepo.save(newDish);
    }

    await DishRepo.save(newDish);
  } catch (error) {
    throw error;
  }
};

export const cloneDishPreliminaryCustomizations = async (
  oldDish: Dish,
  newDish: Dish,
  CustomizationRepo: Repository<Customization>,
  DishSize: Repository<DishSize>,
  CookingStyle: Repository<CookingStyle>,
  Spiciness: Repository<Spiciness>,
  Exclusion: Repository<DishExclusion>
) => {
  try {
    // Clone Customization
    if (oldDish.customization) {
      const oldCustomization = oldDish.customization;
      const newCustomization = CustomizationRepo.create({
        maxSelection: oldCustomization.maxSelection,
        enforceMaxSelection: oldCustomization.enforceMaxSelection,
        dish: newDish,
      });
      await CustomizationRepo.save(newCustomization);

      // DishSizes
      if (oldCustomization.dishSizes && oldCustomization.dishSizes.length > 0) {
        newCustomization.dishSizes = [];
        for (const oldSize of oldCustomization.dishSizes) {
          const newSize = DishSize.create({
            name: oldSize.name,
            price: oldSize.price,
            isDefault: oldSize.isDefault,
            customization: newCustomization,
          });
          await DishSize.save(newSize);
          newCustomization.dishSizes.push(newSize);
        }
      }

      // DishExclusions
      if (
        oldCustomization.dishExclusions &&
        oldCustomization.dishExclusions.length > 0
      ) {
        newCustomization.dishExclusions = [];
        for (const oldExcl of oldCustomization.dishExclusions) {
          const newExcl = Exclusion.create({
            name: oldExcl.name,
            customization: newCustomization,
          });
          await Exclusion.save(newExcl);
          newCustomization.dishExclusions.push(newExcl);
        }
      }

      // CookingStyles
      if (
        oldCustomization.cookingStyles &&
        oldCustomization.cookingStyles.length > 0
      ) {
        newCustomization.cookingStyles = [];
        for (const oldStyle of oldCustomization.cookingStyles) {
          const newStyle = CookingStyle.create({
            name: oldStyle.name,
            customization: newCustomization,
          });
          await CookingStyle.save(newStyle);
          newCustomization.cookingStyles.push(newStyle);
        }
      }

      // Spiciness
      if (oldCustomization.spiciness && oldCustomization.spiciness.length > 0) {
        newCustomization.spiciness = [];
        for (const oldSpice of oldCustomization.spiciness) {
          const newSpice = Spiciness.create({
            name: oldSpice.name,
            price: oldSpice.price,
            customization: newCustomization,
          });
          await Spiciness.save(newSpice);
          newCustomization.spiciness.push(newSpice);
        }
      }

      await CustomizationRepo.save(newCustomization);
      return newCustomization;
    }
  } catch (error) {
    throw error;
  }
};

export const cloneDishAddonsAndExtras = async (
  oldDish: Dish,
  newDish: Dish,
  customization: Customization,
  AddonRepo: Repository<DishAddon>,
  ExtraRepo: Repository<DishExtra>,
  CustomizationRepo: Repository<Customization>
) => {
  try {
    // Addons
    if (
      oldDish.customization &&
      oldDish.customization.dishAddons &&
      oldDish.customization.dishAddons.length > 0
    ) {
      customization.dishAddons = [];
      for (const oldAddon of oldDish.customization.dishAddons) {
        const addon = await AddonRepo.findOne({
          where: { addonId: oldAddon.addonId },
        });
        if (!addon) {
          throw new APIError(CONFLICT, ONEORMORE_ADDONS_NOTEXIST);
        }
        customization.dishAddons.push(addon);
      }
    }

    // Extras
    if (
      oldDish.customization &&
      oldDish.customization.dishExtras &&
      oldDish.customization.dishExtras.length > 0
    ) {
      customization.dishExtras = [];
      for (const oldExtra of oldDish.customization.dishExtras) {
        const extra = await ExtraRepo.findOne({
          where: { extraId: oldExtra.extraId },
        });
        if (!extra) {
          throw new APIError(CONFLICT, ONEORMORE_EXTRAS_NOTEXIST);
        }
        customization.dishExtras.push(extra);
      }
    }

    if (oldDish.customization) {
      await CustomizationRepo.save(customization);
    }
  } catch (error) {
    throw error;
  }
};

export const cloneDishFinalCustomizations = async (
  oldDish: Dish,
  newDish: Dish,
  customization: Customization,
  Side: Repository<DishSide>,
  Beverage: Repository<DishBeverage>,
  Dessert: Repository<DishDessert>,
  CustomizationRepo: Repository<Customization>
) => {
  try {
    // Sides
    if (
      oldDish.customization &&
      oldDish.customization.dishSides &&
      oldDish.customization.dishSides.length > 0
    ) {
      customization.dishSides = [];
      for (const oldSide of oldDish.customization.dishSides) {
        const dishSide = await Side.findOne({
          where: { dishSideId: oldSide.dishSideId },
        });
        if (!dishSide) {
          throw new APIError(CONFLICT, ONEORMORE_SIDES_NOTEXIST);
        }
        customization.dishSides.push(dishSide);
      }
    }

    // Beverages
    if (
      oldDish.customization &&
      oldDish.customization.dishBeverages &&
      oldDish.customization.dishBeverages.length > 0
    ) {
      customization.dishBeverages = [];
      for (const oldBev of oldDish.customization.dishBeverages) {
        const dishBeverage = await Beverage.findOne({
          where: { dishBevId: oldBev.dishBevId },
        });
        if (!dishBeverage) {
          throw new APIError(CONFLICT, ONEORMORE_BEVS_NOTEXIST);
        }
        customization.dishBeverages.push(dishBeverage);
      }
    }

    // Desserts
    if (
      oldDish.customization &&
      oldDish.customization.dishDesserts &&
      oldDish.customization.dishDesserts.length > 0
    ) {
      customization.dishDesserts = [];
      for (const oldDessert of oldDish.customization.dishDesserts) {
        const dishDessert = await Dessert.findOne({
          where: { dishDessertId: oldDessert.dishDessertId },
        });
        if (!dishDessert) {
          throw new APIError(CONFLICT, ONEORMORE_DESSERT_NOTEXIST);
        }
        customization.dishDesserts.push(dishDessert);
      }
    }

    if (oldDish.customization) {
      await CustomizationRepo.save(customization);
    }
  } catch (error) {
    throw error;
  }
};

export const cloneDishAvailability = async (
  exDish: Dish,
  clonedDish: Dish,
  DishRepo: Repository<Dish>,
  CHSRepo: Repository<CustomHourSlot>,
  SPDayRepo: Repository<SpecialDay>
) => {
  try {
    const exCustomHours = exDish.customSlots;

    if (exCustomHours && exCustomHours.length > 0) {
      for (const customHour of exCustomHours) {
        const clonedCustomHour = CHSRepo.create({
          days: customHour.days,

          is24Hours: customHour.is24Hours,

          firstSeating: customHour.firstSeating,

          lastSeating: customHour.lastSeating,

          name: customHour.name,

          isActive: customHour.isActive,
        });

        clonedDish.customSlots.push(clonedCustomHour);
      }
    }

    const exSpecialDays = exDish.specialDays;

    if (exSpecialDays && exSpecialDays.length > 0) {
      for (const spDay of exSpecialDays) {
        const clonedSpecialDay = SPDayRepo.create({
          eventName: spDay.eventName,

          startTime: spDay.startTime,

          endTime: spDay.endTime,
          availability: spDay.availability,
        });

        clonedDish.specialDays.push(clonedSpecialDay);
      }
    }

    await DishRepo.save(clonedDish);
  } catch (err) {
    throw err;
  }
};
