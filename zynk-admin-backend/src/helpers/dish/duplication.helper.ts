import {Query<PERSON><PERSON>ner, Repository} from "typeorm";
import {Section} from "../../models/foodmenu/section.model";
import {APIError} from "../../utils/errorHandler";
import {CONFLICT, NOT_FOUND} from "../../constants/STATUS_CODES";
import {SECTION_NOT_FOUND} from "../../constants/tenant/sections/err";
import {getRepositories} from "../system/RepositoryHelper.helper";
import {Dish} from "../../models/foodmenu/dish.model";
import {Customization} from "../../models/foodmenu/customization.model";
import {Ingredient} from "../../models/reference/ingredient.model";
import {DishIngredient} from "../../models/foodmenu/dish_ingredient.model";
import {Allergy} from "../../models/reference/allergy.model";
import {DishSize} from "../../models/foodmenu/custom/dishsize.model";
import {CookingStyle} from "../../models/foodmenu/custom/cookingstyle.model";
import {Spiciness} from "../../models/foodmenu/custom/spiciness.model";
import {DishExclusion} from "../../models/foodmenu/custom/dishexclusion.model";
import {DishAddon} from "../../models/foodmenu/custom/addon.model";
import {DishExtra} from "../../models/foodmenu/custom/extra.model";
import {DishSide} from "../../models/foodmenu/custom/dish_side.model";
import {DishBeverage} from "../../models/foodmenu/custom/dish_bev.model";
import {DishDessert} from "../../models/foodmenu/custom/dish_dessert.model";
import {DISH_NOT_FOUND} from "../../constants/tenant/dish/err";
import {
  cloneDishAddonsAndExtras,
  cloneDishAuxiliaryDetails,
  cloneDishAvailability,
  cloneDishBasicDetails,
  cloneDishFinalCustomizations,
  cloneDishPreliminaryCustomizations,
} from "./dish.clone.helper";
import {SectionIcon} from "../../models/reference/sectionicon.model";
import {FoodMenu} from "../../models/foodmenu/foodmenu.model";
import {SECTION_ICON_NOT_FOUND} from "../../constants/reference/sectionicon/err";
import {FOOD_MENU_NOT_FOUND} from "../../constants/tenant/foodmenu/err";
import {CustomHourSlot} from "../../models/common/customhourslot.model";
import {SpecialDay} from "../../models/common/specialday.model";

export const cloneSectionDetails = async (
    exSection: Section,
    SectionRepo: Repository<Section>
) => {
    try {
        const baseName = exSection.name;
        // Fixed regex to match the actual naming pattern we'll use
        const copyPattern = new RegExp(
            `^${baseName}( - Copy(\\((\\d+)\\))?)?$`,
            "i"
        );
        const allSections = await SectionRepo.find({
            where: {
                foodMenu: {
                    foodMenuId: exSection.foodMenu.foodMenuId,
                },
            },
        });

        const matchingSections = allSections.filter((section) =>
            copyPattern.test(section.name)
        );

        let clonedSectionName = exSection.name;

        if (matchingSections.length === 1) {
            clonedSectionName += " - Copy";
        } else if (matchingSections.length > 1) {
            let hasCopyWithoutNumber = false;
            let maxCopyNumber = 0;

            matchingSections.forEach((section) => {
                if (section.name === baseName) {
                    return;
                }

                if (section.name === `${baseName} - Copy`) {
                    hasCopyWithoutNumber = true;
                } else {
                    const numberMatch = section.name.match(
                        new RegExp(`^${baseName} - Copy\\((\\d+)\\)$`, "i")
                    );
                    if (numberMatch) {
                        const copyNumber = parseInt(numberMatch[1], 10);
                        maxCopyNumber = Math.max(maxCopyNumber, copyNumber);
                    }
                }
            });

            if (!hasCopyWithoutNumber) {
                clonedSectionName += " - Copy";
            } else {
                clonedSectionName += ` - Copy(${maxCopyNumber + 1})`;
            }
        } else {
            throw new APIError(CONFLICT, SECTION_NOT_FOUND);
        }

        const newSectionDetails = {
            pictureUrls: exSection.pictureUrls,

            name: clonedSectionName,

            description: exSection.description,

            availability: exSection.availability,

            visibility: exSection.visibility,

            sectionIcon: exSection.sectionIcon,

            customTimes: exSection.customTimes,
        };

        const newSection = SectionRepo.create({
            ...newSectionDetails,
        });
    } catch (error) {
        throw error;
    }
};

export const dishDuplicator = async (
    dishId: string,
    qRunner: QueryRunner,
    sectionToDuplicate: { default: boolean; sectionId: string } = {
        default: true,
        sectionId: "",
    }
) => {
    try {
        const {
            Dish,
            Section,
            Customization,
            Ingredient,
            DishIngredient,
            Allergy,
            // Customizations
            DishSize,
            CookingStyle,
            Spiciness,
            DishExclusion,

            // Addons and Extras
            DishAddon,
            DishExtra,

            // Sides, Beverages, Desserts
            DishSide,
            DishBeverage,
            DishDessert,
            CustomHourSlot,

            SpecialDay,
        } = getRepositories(qRunner) as {
            Dish: Repository<Dish>;
            Section: Repository<Section>;
            Customization: Repository<Customization>;
            Ingredient: Repository<Ingredient>;
            DishIngredient: Repository<DishIngredient>;
            Allergy: Repository<Allergy>;

            // Customizations
            DishSize: Repository<DishSize>;
            CookingStyle: Repository<CookingStyle>;
            Spiciness: Repository<Spiciness>;
            DishExclusion: Repository<DishExclusion>;

            // Addons and Extras
            DishAddon: Repository<DishAddon>;
            DishExtra: Repository<DishExtra>;

            // Sides, Beverages, Desserts
            DishSide: Repository<DishSide>;
            DishBeverage: Repository<DishBeverage>;
            DishDessert: Repository<DishDessert>;
            CustomHourSlot: Repository<CustomHourSlot>;
            SpecialDay: Repository<SpecialDay>;
        };

        const exDish = await Dish.findOne({
            where: {
                dishId,
            },
            relations: [
                "section",
                "dishIngredients",
                "allergies",
                "customization",
                "customization.dishSizes",
                "customization.dishExclusions",
                "customization.cookingStyles",
                "customization.spiciness",
                "customization.dishAddons",
                "customization.dishExtras",
                "customization.dishSides",
                "customization.dishBeverages",
                "customization.dishDesserts",
                "customSlots",
                "specialDays",
            ],
        });

        if (!exDish) throw new APIError(NOT_FOUND, DISH_NOT_FOUND);

        const clonedDish = await cloneDishBasicDetails(
            exDish,
            Dish,
            DishSide,
            DishBeverage,
            DishDessert,
            Section,
            sectionToDuplicate
        );

        await cloneDishAuxiliaryDetails(
            exDish,
            clonedDish,
            Dish,
            Ingredient,
            DishIngredient,
            Allergy
        );

        const clonedCustomization = await cloneDishPreliminaryCustomizations(
            exDish,
            clonedDish,
            Customization,
            DishSize,
            CookingStyle,
            Spiciness,
            DishExclusion
        );

        await cloneDishAddonsAndExtras(
            exDish,
            clonedDish,
            clonedCustomization!,
            DishAddon,
            DishExtra,
            Customization
        );

        await cloneDishFinalCustomizations(
            exDish,
            clonedDish,
            clonedCustomization!,
            DishSide,
            DishBeverage,
            DishDessert,
            Customization
        );

        await cloneDishAvailability(
            exDish,
            clonedDish,
            Dish,
            CustomHourSlot,
            SpecialDay
        );
    } catch (error) {
        throw error;
    }
};

export const duplicateSectionDetails = async (
    sectionId: string,
    qRunner: QueryRunner,
    foodMenuToStore: {
        default: boolean;
        foodMenuId: string;
    } = {
        default: true,
        foodMenuId: "",
    }
) => {
    try {
        const {Section, SectionIcon, FoodMenu, CustomHourSlot, SpecialDay} =
            getRepositories(qRunner) as {
                Section: Repository<Section>;
                SectionIcon: Repository<SectionIcon>;
                FoodMenu: Repository<FoodMenu>;
                CustomHourSlot: Repository<CustomHourSlot>;
                SpecialDay: Repository<SpecialDay>;
            };

        const exSection = await Section.findOne({
            where: {
                menuSectionId: sectionId,
            },
            relations: ["foodMenu", "sectionIcon", "customSlots", "specialDays"],
        });

        if (!exSection) throw new APIError(NOT_FOUND, SECTION_NOT_FOUND);

        const exSectionIcon = await SectionIcon.findOne({
            where: {
                sectionIconId: exSection.sectionIcon.sectionIconId,
            },
        });

        if (!exSectionIcon) throw new APIError(NOT_FOUND, SECTION_ICON_NOT_FOUND);

        // Find all sections whose names match the pattern: original, " - Copy", " - Copy(n)"
        const baseName = exSection.name;
        // Fixed regex to match the actual naming pattern we'll use
        const copyPattern = new RegExp(
            `^${baseName}( - Copy(\\((\\d+)\\))?)?$`,
            "i"
        );
        const allSections = await Section.find({
            where: {
                foodMenu: {
                    foodMenuId: exSection.foodMenu.foodMenuId,
                },
            },
        });

        const matchingSections = allSections.filter((section) =>
            copyPattern.test(section.name)
        );

        let clonedSectionName = exSection.name;

        if (matchingSections.length === 1) {
            clonedSectionName += " - Copy";
        } else if (matchingSections.length > 1) {
            let hasCopyWithoutNumber = false;
            let maxCopyNumber = 0;

            matchingSections.forEach((section) => {
                if (section.name === baseName) {
                    // This is the original, skip
                    return;
                }

                if (section.name === `${baseName} - Copy`) {
                    // Found the unnumbered copy
                    hasCopyWithoutNumber = true;
                } else {
                    // Check for numbered copies like "Name - Copy(1)"
                    const numberMatch = section.name.match(
                        new RegExp(`^${baseName} - Copy\\((\\d+)\\)$`, "i")
                    );
                    if (numberMatch) {
                        const copyNumber = parseInt(numberMatch[1], 10);
                        maxCopyNumber = Math.max(maxCopyNumber, copyNumber);
                    }
                }
            });

            if (!hasCopyWithoutNumber) {
                clonedSectionName += " - Copy";
            } else {
                clonedSectionName += ` - Copy(${maxCopyNumber + 1})`;
            }
        } else {
            throw new APIError(CONFLICT, DISH_NOT_FOUND);
        }

        let newSectionDetails = {
            pictureUrls: exSection.pictureUrls,

            name: clonedSectionName,

            description: exSection.description,

            availability: exSection.availability,

            visibility: exSection.visibility,

            sectionIcon: exSectionIcon,

            customTimes: exSection.customTimes,
            foodMenu: undefined as any,
            customSlots: [],
            specialDays: [],
        };

        if (foodMenuToStore.default) {
            newSectionDetails.foodMenu = exSection.foodMenu;
        } else {
            const foodMenu = await FoodMenu.findOne({
                where: {
                    foodMenuId: foodMenuToStore.foodMenuId,
                },
            });

            if (!foodMenu) throw new APIError(NOT_FOUND, FOOD_MENU_NOT_FOUND);

            newSectionDetails.foodMenu = foodMenu;
        }

        const newSection = Section.create({
            ...newSectionDetails,
        });

        await duplicateSectionAvailability(
            exSection,
            newSection,
            CustomHourSlot,
            SpecialDay
        );

        await Section.save(newSection);
        return newSection;
    } catch (error) {
        throw error;
    }
};

export const duplicateAllSectionsFromFoodMenu = async (
    foodMenuId: string,
    clonedFoodMenu: FoodMenu,
    qRunner: QueryRunner
) => {
    try {
        const {Section} = getRepositories(qRunner) as {
            Section: Repository<Section>;
        };

        const sections = await Section.find({
            where: {
                foodMenu: {
                    foodMenuId,
                },
            },
        });

        if (sections.length > 0) {
            for (const section of sections) {
                await sectionDuplicator(section.menuSectionId, qRunner, {
                    default: false,
                    foodMenuId: clonedFoodMenu.foodMenuId,
                });
            }
        }
    } catch (error) {
        throw error;
    }
};

export const duplicateAllDishesFromSection = async (
    sectionId: string,
    clonedSection: Section,
    qRunner: QueryRunner
) => {
    try {
        const {Dish} = getRepositories(qRunner) as {
            Dish: Repository<Dish>;
        };

        const dishes = await Dish.find({
            where: {
                section: {
                    menuSectionId: sectionId,
                },
            },
        });

        if (dishes.length > 0) {
            for (const dish of dishes) {
                await dishDuplicator(dish.dishId, qRunner, {
                    default: false,
                    sectionId: clonedSection.menuSectionId,
                });
            }
        }
    } catch (error) {
        throw error;
    }
};

export const foodMenuDuplicator = async (
    foodMenuId: string,
    qRunner: QueryRunner
) => {
    try {
        const {FoodMenu, CustomHourSlot, SpecialDay} = getRepositories(
            qRunner
        ) as {
            FoodMenu: Repository<FoodMenu>;
            CustomHourSlot: Repository<CustomHourSlot>;
            SpecialDay: Repository<SpecialDay>;
        };

        const exFoodMenu = await FoodMenu.findOne({
            where: {
                foodMenuId,
            },
            relations: ["branch", "customSlots", "specialDays"],
        });

        if (!exFoodMenu) throw new APIError(NOT_FOUND, FOOD_MENU_NOT_FOUND);

        // Find all foodmenus whose names match the pattern: original, " - Copy", " - Copy(n)"
        const baseName = exFoodMenu.name;
        // Fixed regex to match the actual naming pattern we'll use
        const copyPattern = new RegExp(
            `^${baseName}( - Copy(\\((\\d+)\\))?)?$`,
            "i"
        );
        const allFoodMenu = await FoodMenu.find({
            where: {
                branch: {
                    branchId: exFoodMenu.branch.branchId,
                },
            },
        });

        const matchingFoodMenu = allFoodMenu.filter((foodmenu) =>
            copyPattern.test(foodmenu.name)
        );

        let clonedFoodMenuName = exFoodMenu.name;

        if (matchingFoodMenu.length === 1) {
            clonedFoodMenuName += " - Copy";
        } else if (matchingFoodMenu.length > 1) {
            let hasCopyWithoutNumber = false;
            let maxCopyNumber = 0;

            matchingFoodMenu.forEach((foodmenu) => {
                if (foodmenu.name === baseName) {
                    // This is the original, skip
                    return;
                }

                if (foodmenu.name === `${baseName} - Copy`) {
                    // Found the unnumbered copy
                    hasCopyWithoutNumber = true;
                } else {
                    // Check for numbered copies like "Name - Copy(1)"
                    const numberMatch = foodmenu.name.match(
                        new RegExp(`^${baseName} - Copy\\((\\d+)\\)$`, "i")
                    );
                    if (numberMatch) {
                        const copyNumber = parseInt(numberMatch[1], 10);
                        maxCopyNumber = Math.max(maxCopyNumber, copyNumber);
                    }
                }
            });

            if (!hasCopyWithoutNumber) {
                clonedFoodMenuName += " - Copy";
            } else {
                clonedFoodMenuName += ` - Copy(${maxCopyNumber + 1})`;
            }
        } else {
            throw new APIError(CONFLICT, FOOD_MENU_NOT_FOUND);
        }

        const foodMenuDetails = {
            name: clonedFoodMenuName,
            branch: exFoodMenu.branch,

            pictureUrl: exFoodMenu.pictureUrl,

            isDefault: false,

            description: exFoodMenu.description,

            availability: exFoodMenu.availability,

            visibility: exFoodMenu.visibility,

            customTimes: exFoodMenu.customTimes,
            customSlots: [],
            specialDays: [],
        };

        const newFoodMenu = FoodMenu.create({
            ...foodMenuDetails,
        });

        await duplicateFoodMenuAvailability(
            exFoodMenu,
            newFoodMenu,
            CustomHourSlot,
            SpecialDay
        );

        await FoodMenu.save(newFoodMenu);
        return newFoodMenu;
    } catch (error) {
        throw error;
    }
};

export const duplicateFoodMenuAvailability = async (
    exFoodMenu: FoodMenu,
    newFoodMenu: FoodMenu,
    CHSRepo: Repository<CustomHourSlot>,
    SPDayRepo: Repository<SpecialDay>
) => {
    try {
        const exCustomHours = exFoodMenu.customSlots;

        if (exCustomHours && exCustomHours.length > 0) {
            for (const customHour of exCustomHours) {
                const clonedCustomHour = CHSRepo.create({
                    days: customHour.days,

                    is24Hours: customHour.is24Hours,

                    firstSeating: customHour.firstSeating,

                    lastSeating: customHour.lastSeating,

                    name: customHour.name,

                    isActive: customHour.isActive,
                });

                newFoodMenu.customSlots.push(clonedCustomHour);
            }
        }

        const exSpecialDays = exFoodMenu.specialDays;

        if (exSpecialDays && exSpecialDays.length > 0) {
            for (const spDay of exSpecialDays) {
                const clonedSpecialDay = SPDayRepo.create({
                    eventName: spDay.eventName,

                    startTime: spDay.startTime,

                    endTime: spDay.endTime,
                    availability: spDay.availability,
                });

                newFoodMenu.specialDays.push(clonedSpecialDay);
            }
        }
    } catch (err) {
        throw err;
    }
};

export const duplicateSectionAvailability = async (
    oldSection: Section,
    newSection: Section,
    CHSRepo: Repository<CustomHourSlot>,
    SPDayRepo: Repository<SpecialDay>
) => {
    try {
        const exCustomHours = oldSection.customSlots;

        if (exCustomHours && exCustomHours.length > 0) {
            for (const customHour of exCustomHours) {
                const clonedCustomHour = CHSRepo.create({
                    days: customHour.days,

                    is24Hours: customHour.is24Hours,

                    firstSeating: customHour.firstSeating,

                    lastSeating: customHour.lastSeating,

                    name: customHour.name,

                    isActive: customHour.isActive,
                });

                newSection.customSlots.push(clonedCustomHour);
            }
        }

        const exSpecialDays = oldSection.specialDays;

        if (exSpecialDays && exSpecialDays.length > 0) {
            for (const spDay of exSpecialDays) {
                const clonedSpecialDay = SPDayRepo.create({
                    eventName: spDay.eventName,

                    startTime: spDay.startTime,

                    endTime: spDay.endTime,
                    availability: spDay.availability,
                });

                newSection.specialDays.push(clonedSpecialDay);
            }
        }
    } catch (error) {
        throw error;
    }
};

export const sectionDuplicator = async (
    sectionId: string,
    qRunner: QueryRunner,
    foodMenuToStore: {
        default: boolean;
        foodMenuId: string;
    } = {
        default: true,
        foodMenuId: "",
    }
) => {
    try {
        const clonedSection = await duplicateSectionDetails(
            sectionId,
            qRunner,
            foodMenuToStore
        );
        await duplicateAllDishesFromSection(sectionId, clonedSection, qRunner);
    } catch (error) {
        throw error;
    }
};
