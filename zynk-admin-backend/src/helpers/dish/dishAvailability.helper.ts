import { QueryRunner } from "typeorm";
import { FoodMenu } from "../../models/foodmenu/foodmenu.model";
import { Branch } from "../../models/company/branch.model";
import { Section } from "../../models/foodmenu/section.model";
import { BusinessHour } from "../../models/common/businesshour.model";
import { Dish } from "../../models/foodmenu/dish.model";
import { BusinessHourSlot } from "../../models/common/businesshourslot.model";
import { CustomHourSlot } from "../../models/common/customhourslot.model";
import { SpecialDay } from "../../models/common/specialday.model";

export type Weekday = "M" | "T" | "W" | "Th" | "F" | "Sa" | "Su";
export interface AvailabilityContext {
  currentTime: Date;
  dayOfWeek: Weekday;
  forPOS: boolean; // true for POS, false for customer app
}

export interface AvailabilityResult {
  isAvailable: boolean;
  reason?: string;
  nextAvailableAt?: Date;
  availableUntil?: Date;
}

export interface ItemWithAvailability {
  isAvailable: boolean;
  availabilityReason?: string;
  nextAvailableAt?: Date;
  availableUntil?: Date;
}

export class MenuAvailabilityService {
  private queryRunner: QueryRunner;

  constructor(queryRunner: QueryRunner) {
    this.queryRunner = queryRunner;
  }

  /**
   * Main method to evaluate and annotate the entire menu with availability
   */
  async evaluateMenuAvailability(
    menu: FoodMenu,
    branch: Branch,
    context: AvailabilityContext
  ): Promise<FoodMenu & { availabilityInfo: ItemWithAvailability }> {
    const branchBusinessHour = branch.businessHour;
    const menuAvailability = await this.evaluateItemAvailability(
      menu,
      branchBusinessHour,
      context
    );

    // Modify the original menu object instead of creating a new one
    const sectionsWithAvailability = [];

    if (menu.sections) {
      for (const section of menu.sections) {
        const sectionWithAvailability = await this.evaluateSectionAvailability(
          section,
          menu,
          branchBusinessHour,
          context,
          menuAvailability.isAvailable
        );

        sectionsWithAvailability.push(sectionWithAvailability);
      }
    }

    // Add properties to the original menu object
    (menu as any).availabilityInfo = menuAvailability;
    (menu as any).sections = sectionsWithAvailability;

    return menu as FoodMenu & { availabilityInfo: ItemWithAvailability };
  }

  /**
   * Evaluate section availability
   */
  private async evaluateSectionAvailability(
    section: Section,
    parentMenu: FoodMenu,
    branchBusinessHour: BusinessHour,
    context: AvailabilityContext,
    parentAvailable: boolean
  ): Promise<Section & { availabilityInfo: ItemWithAvailability }> {
    const sectionAvailability = await this.evaluateItemAvailability(
      section,
      branchBusinessHour,
      context,
      parentMenu
    );

    // Section inherits parent unavailability
    const finalAvailability =
      parentAvailable && sectionAvailability.isAvailable;

    const dishesWithAvailability = [];

    // Evaluate dishes
    if (section.dishes) {
      for (const dish of section.dishes) {
        const dishWithAvailability = await this.evaluateDishAvailability(
          dish,
          section,
          parentMenu,
          branchBusinessHour,
          context,
          finalAvailability
        );

        dishesWithAvailability.push(dishWithAvailability);
      }
    }

    // Add properties to the original section object
    (section as any).availabilityInfo = {
      ...sectionAvailability,
      isAvailable: finalAvailability,
      availabilityReason: !parentAvailable
        ? "parent_menu_unavailable"
        : sectionAvailability.reason,
    };
    (section as any).dishes = dishesWithAvailability;

    return section as Section & { availabilityInfo: ItemWithAvailability };
  }

  /**
   * Evaluate dish availability
   */
  private async evaluateDishAvailability(
    dish: Dish,
    parentSection: Section,
    parentMenu: FoodMenu,
    branchBusinessHour: BusinessHour,
    context: AvailabilityContext,
    parentAvailable: boolean
  ): Promise<Dish & { availabilityInfo: ItemWithAvailability }> {
    const dishAvailability = await this.evaluateItemAvailability(
      dish,
      branchBusinessHour,
      context,
      parentMenu,
      parentSection
    );

    // Dish inherits parent unavailability
    const finalAvailability = parentAvailable && dishAvailability.isAvailable;

    // Add properties to the original dish object
    (dish as any).availabilityInfo = {
      ...dishAvailability,
      isAvailable: finalAvailability,
      availabilityReason: !parentAvailable
        ? "parent_section_unavailable"
        : dishAvailability.reason,
    };

    return dish as Dish & { availabilityInfo: ItemWithAvailability };
  }

  /**
   * Core availability evaluation logic for any item (menu/section/dish)
   */
  private async evaluateItemAvailability(
    item: FoodMenu | Section | Dish,
    branchBusinessHour: BusinessHour,
    context: AvailabilityContext,
    parentMenu?: FoodMenu,
    parentSection?: Section
  ): Promise<AvailabilityResult> {
    // 1. Check basic visibility (only for customer app)
    if (!context.forPOS && !item.visibility) {
      return { isAvailable: false, reason: "not_visible" };
    }

    // 2. For dishes, check global availability
    if (
      "availability" in item &&
      "global" in item.availability &&
      !item.availability.global
    ) {
      return { isAvailable: false, reason: "globally_disabled" };
    }

    // 3. Check special days first (highest priority)
    const specialDayResult = this.checkSpecialDays(
      item.specialDays || [],
      context.currentTime
    );
    if (specialDayResult.isSpecialDay) {
      if (!specialDayResult.isAvailable) {
        return {
          isAvailable: false,
          reason: "special_day_unavailable",
          nextAvailableAt: specialDayResult.nextAvailableAt,
        };
      }
      // If special day allows it, continue with other checks
    }

    // 4. Check time-based availability
    const timeSlots = this.resolveTimeSlots(item, branchBusinessHour);
    const timeAvailability = this.checkTimeAvailability(timeSlots, context);

    if (!timeAvailability.isAvailable) {
      return timeAvailability;
    }

    return { isAvailable: true };
  }

  /**
   * Resolve which time slots to use based on customTimes flag and globalCustomSlots
   * Priority: globalCustomSlots > customSlots > branch hours
   */
  private resolveTimeSlots(
    item: FoodMenu | Section | Dish,
    branchBusinessHour: BusinessHour
  ): (BusinessHourSlot | CustomHourSlot)[] {
    // If item uses custom times
    if ("customTimes" in item && item.customTimes) {
      // Priority 1: globalCustomSlots (if present, ignore customSlots)
      if (
        "globalCustomSlots" in item &&
        item.globalCustomSlots &&
        item.globalCustomSlots.length > 0
      ) {
        return item.globalCustomSlots;
      }
      // Priority 2: item's own custom slots (only if no globalCustomSlots)
      if (
        "customSlots" in item &&
        item.customSlots &&
        item.customSlots.length > 0
      ) {
        return item.customSlots;
      }
    }

    // Fall back to branch business hours
    if (
      branchBusinessHour.useDefault &&
      branchBusinessHour.slots &&
      branchBusinessHour.slots.length > 0
    ) {
      return branchBusinessHour.slots;
    }

    // Final fallback to branch custom slots
    if (
      branchBusinessHour.customSlots &&
      branchBusinessHour.customSlots.length > 0
    ) {
      return branchBusinessHour.customSlots;
    }

    return [];
  }

  /**
   * Check if current time falls within any active time slot
   */
  private checkTimeAvailability(
    slots: (BusinessHourSlot | CustomHourSlot)[],
    context: AvailabilityContext
  ): AvailabilityResult {
    if (!slots.length) {
      return { isAvailable: false, reason: "no_time_slots_defined" };
    }

    const currentTime = context.currentTime;
    const dayOfWeek = context.dayOfWeek;

    // Find slots that are active for current day
    const activeSlotsForDay = slots.filter(
      (slot) => slot.isActive && slot.days[dayOfWeek]
    );

    if (!activeSlotsForDay.length) {
      return {
        isAvailable: false,
        reason: "not_operating_today",
        nextAvailableAt: this.getNextOperatingTime(slots, currentTime),
      };
    }

    // Check if current time falls within any active slot
    for (const slot of activeSlotsForDay) {
      if (slot.is24Hours) {
        return { isAvailable: true };
      }

      if (slot.firstSeating && slot.lastSeating) {
        const isWithinHours = this.isTimeWithinSlot(
          currentTime,
          slot.firstSeating,
          slot.lastSeating
        );

        if (isWithinHours) {
          return {
            isAvailable: true,
            availableUntil: this.parseTimeToDate(slot.lastSeating, currentTime),
          };
        }
      }
    }

    // Not currently available, find next available time
    return {
      isAvailable: false,
      reason: "outside_operating_hours",
      nextAvailableAt: this.getNextAvailableTime(
        activeSlotsForDay,
        currentTime
      ),
    };
  }

  /**
   * Check special days
   */
  private checkSpecialDays(
    specialDays: SpecialDay[],
    currentTime: Date
  ): { isSpecialDay: boolean; isAvailable: boolean; nextAvailableAt?: Date } {
    const activeSpecialDays = specialDays.filter(
      (sd) => currentTime >= sd.startTime && currentTime <= sd.endTime
    );

    if (!activeSpecialDays.length) {
      return { isSpecialDay: false, isAvailable: true };
    }

    // If any special day makes it unavailable, it's unavailable
    const unavailableSpecialDay = activeSpecialDays.find(
      (sd) => !sd.availability
    );
    if (unavailableSpecialDay) {
      return {
        isSpecialDay: true,
        isAvailable: false,
        nextAvailableAt: unavailableSpecialDay.endTime,
      };
    }

    return { isSpecialDay: true, isAvailable: true };
  }

  /**
   * Helper method to check if current time is within a time slot
   */
  private isTimeWithinSlot(
    currentTime: Date,
    startTime: string,
    endTime: string
  ): boolean {
    const currentMinutes =
      currentTime.getHours() * 60 + currentTime.getMinutes();
    const startMinutes = this.parseTimeStringToMinutes(startTime);
    const endMinutes = this.parseTimeStringToMinutes(endTime);

    // Handle overnight slots (e.g., 22:00 - 02:00)
    if (startMinutes > endMinutes) {
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }

    return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
  }

  /**
   * Parse time string (HH:MM) to minutes since midnight
   */
  parseTimeStringToMinutes(timeString: string): number {
    const [hours, minutes] = timeString.split(":").map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Parse time string to Date object for today
   */
  parseTimeToDate(timeString: string, referenceDate: Date): Date {
    const [hours, minutes] = timeString.split(":").map(Number);
    const date = new Date(referenceDate);
    date.setHours(hours, minutes, 0, 0);
    return date;
  }

  /**
   * Get next available time from slots
   */
  private getNextAvailableTime(
    slots: (BusinessHourSlot | CustomHourSlot)[],
    currentTime: Date
  ): Date | undefined {
    const today = currentTime.getDay();
    const currentMinutes =
      currentTime.getHours() * 60 + currentTime.getMinutes();

    // Check remaining slots today
    for (const slot of slots) {
      if (slot.firstSeating && slot.days[this.getDayKey(today)]) {
        const startMinutes = this.parseTimeStringToMinutes(slot.firstSeating);
        if (startMinutes > currentMinutes) {
          return this.parseTimeToDate(slot.firstSeating, currentTime);
        }
      }
    }

    // Check next 7 days
    for (let daysAhead = 1; daysAhead <= 7; daysAhead++) {
      const checkDate = new Date(currentTime);
      checkDate.setDate(checkDate.getDate() + daysAhead);
      const dayKey = this.getDayKey(checkDate.getDay());

      for (const slot of slots) {
        if (slot.firstSeating && slot.days[dayKey]) {
          checkDate.setHours(0, 0, 0, 0);
          return this.parseTimeToDate(slot.firstSeating, checkDate);
        }
      }
    }

    return undefined;
  }

  /**
   * Get next operating time (used when not operating today at all)
   */
  private getNextOperatingTime(
    slots: (BusinessHourSlot | CustomHourSlot)[],
    currentTime: Date
  ): Date | undefined {
    // Similar logic to getNextAvailableTime but starts from tomorrow
    for (let daysAhead = 1; daysAhead <= 7; daysAhead++) {
      const checkDate = new Date(currentTime);
      checkDate.setDate(checkDate.getDate() + daysAhead);
      const dayKey = this.getDayKey(checkDate.getDay());

      for (const slot of slots) {
        if (slot.firstSeating && slot.days[dayKey] && slot.isActive) {
          checkDate.setHours(0, 0, 0, 0);
          return this.parseTimeToDate(slot.firstSeating, checkDate);
        }
      }
    }

    return undefined;
  }

  /**
   * Convert JS day (0-6) to your Weekday format
   */
  private getDayKey(jsDay: number): Weekday {
    const dayMap: Record<number, Weekday> = {
      0: "Su",
      1: "M",
      2: "T",
      3: "W",
      4: "Th",
      5: "F",
      6: "Sa",
    };
    return dayMap[jsDay];
  }
}

// new things

export class OptimizedMenuAvailabilityService extends MenuAvailabilityService {
  /**
   * BATCH VERSION - Check special days with pre-filtered list
   */
  private checkSpecialDaysBatch(
    specialDays: SpecialDay[],
    currentTime: Date
  ): { isSpecialDay: boolean; isAvailable: boolean; nextAvailableAt?: Date } {
    // Filter active special days for current time
    const activeSpecialDays = specialDays.filter(
      (sd) => currentTime >= sd.startTime && currentTime <= sd.endTime
    );

    if (!activeSpecialDays.length) {
      return { isSpecialDay: false, isAvailable: true };
    }

    // If any special day makes it unavailable, it's unavailable
    const unavailableSpecialDay = activeSpecialDays.find(
      (sd) => !sd.availability
    );

    if (unavailableSpecialDay) {
      return {
        isSpecialDay: true,
        isAvailable: false,
        nextAvailableAt: unavailableSpecialDay.endTime,
      };
    }

    return { isSpecialDay: true, isAvailable: true };
  }

  /**
   * BATCH AVAILABILITY EVALUATION - Process all items at once
   */
  async evaluateMenuAvailabilityBatch(
    menu: FoodMenu,
    branch: Branch,
    context: AvailabilityContext
  ): Promise<FoodMenu & { availabilityInfo: ItemWithAvailability }> {
    const branchBusinessHour = branch.businessHour;

    // 1. COLLECT ALL ITEMS FOR BATCH PROCESSING
    const allItems: Array<{
      item: FoodMenu | Section | Dish;
      type: "menu" | "section" | "dish";
      parentMenu?: FoodMenu;
      parentSection?: Section;
    }> = [];

    // Add menu
    allItems.push({ item: menu, type: "menu" });

    // Add all sections and dishes
    if (menu.sections) {
      menu.sections = menu.sections.filter((section) => section.visibility);

      for (const section of menu.sections) {
        allItems.push({ item: section, type: "section", parentMenu: menu });

        if (section.dishes) {
          // Remove dishes with visibility === false
          section.dishes = section.dishes.filter((dish) => dish.visibility && dish.availability.global);

          for (const dish of section.dishes) {
            allItems.push({
              item: dish,
              type: "dish",
              parentMenu: menu,
              parentSection: section,
            });
          }
        }
      }
    }

    // 2. BATCH EVALUATE ALL ITEMS
    const availabilityResults = await this.batchEvaluateAvailability(
      allItems,
      branchBusinessHour,
      context
    );

    // 3. APPLY RESULTS BACK TO MENU STRUCTURE
    return this.applyAvailabilityResults(menu, availabilityResults);
  }

  async evaluateMenuAvailabilityOnlineBatch(
    menu: FoodMenu,
    branch: Branch,
    context: AvailabilityContext
  ): Promise<FoodMenu & { availabilityInfo: ItemWithAvailability }> {
    const branchBusinessHour = branch.businessHour;

    // 1. COLLECT ALL ITEMS FOR BATCH PROCESSING
    const allItems: Array<{
      item: FoodMenu | Section | Dish;
      type: "menu" | "section" | "dish";
      parentMenu?: FoodMenu;
      parentSection?: Section;
    }> = [];

    // Add menu
    allItems.push({ item: menu, type: "menu" });

    // Add all sections and dishes
    if (menu.sections) {
      menu.sections = menu.sections.filter((section) => section.visibility && section.availability.site);

      for (const section of menu.sections) {
        allItems.push({ item: section, type: "section", parentMenu: menu });

        if (section.dishes) {
          // Remove dishes with visibility === false
          section.dishes = section.dishes.filter((dish) => dish.visibility && dish.availability.site && dish.availability.global);

          for (const dish of section.dishes) {
            allItems.push({
              item: dish,
              type: "dish",
              parentMenu: menu,
              parentSection: section,
            });
          }
        }
      }
    }

    // 2. BATCH EVALUATE ALL ITEMS
    const availabilityResults = await this.batchEvaluateAvailability(
      allItems,
      branchBusinessHour,
      context
    );

    // 3. APPLY RESULTS BACK TO MENU STRUCTURE
    return this.applyAvailabilityResults(menu, availabilityResults);
  }

  /**
   * BATCH EVALUATE - Single pass through all items
   */
  private async batchEvaluateAvailability(
    items: Array<{
      item: FoodMenu | Section | Dish;
      type: "menu" | "section" | "dish";
      parentMenu?: FoodMenu;
      parentSection?: Section;
    }>,
    branchBusinessHour: BusinessHour,
    context: AvailabilityContext
  ): Promise<Map<string, AvailabilityResult>> {
    const results = new Map<string, AvailabilityResult>();

    // Pre-calculate special days and time slots for reuse
    const currentSpecialDays = this.getCurrentSpecialDays(context.currentTime);
    const branchTimeSlots = this.resolveBranchTimeSlots(branchBusinessHour);

    for (const { item, type, parentMenu, parentSection } of items) {
      const itemId = this.getItemId(item, type);

      const result = await this.evaluateItemAvailabilityOptimized(
        item,
        branchBusinessHour,
        context,
        currentSpecialDays,
        branchTimeSlots,
        parentMenu,
        parentSection
      );

      results.set(itemId, result);
    }

    return results;
  }

  /**
   * OPTIMIZED ITEM EVALUATION - Reuse pre-calculated data
   */
  private async evaluateItemAvailabilityOptimized(
    item: FoodMenu | Section | Dish,
    branchBusinessHour: BusinessHour,
    context: AvailabilityContext,
    currentSpecialDays: SpecialDay[],
    branchTimeSlots: (BusinessHourSlot | CustomHourSlot)[],
    parentMenu?: FoodMenu,
    parentSection?: Section
  ): Promise<AvailabilityResult> {
    // Fast visibility check
    if (!context.forPOS && !item.visibility) {
      return { isAvailable: false, reason: "not_visible" };
    }

    // Fast global availability check for dishes
    if (
      "availability" in item &&
      "global" in item.availability &&
      !item.availability.global
    ) {
      return { isAvailable: false, reason: "globally_disabled" };
    }

    // Special days check (reuse pre-filtered list)
    const relevantSpecialDays = (item.specialDays || []).filter((sd) =>
      currentSpecialDays.some((csd) => csd.specialDayId === sd.specialDayId)
    );

    const specialDayResult = this.checkSpecialDaysBatch(
      relevantSpecialDays,
      context.currentTime
    );
    if (specialDayResult.isSpecialDay && !specialDayResult.isAvailable) {
      return {
        isAvailable: false,
        reason: "special_day_unavailable",
        nextAvailableAt: specialDayResult.nextAvailableAt,
      };
    }

    // Time-based availability (reuse resolved slots when possible)
    const timeSlots = this.resolveTimeSlotsOptimized(item, branchTimeSlots);
    const timeAvailability = this.checkTimeAvailabilityOptimized(
      timeSlots,
      context
    );

    return timeAvailability.isAvailable
      ? { isAvailable: true }
      : timeAvailability;
  }

  // Helper methods for optimization
  private getCurrentSpecialDays(currentTime: Date): SpecialDay[] {
    // This would be pre-fetched and cached
    return [];
  }

  private resolveBranchTimeSlots(
    branchBusinessHour: BusinessHour
  ): (BusinessHourSlot | CustomHourSlot)[] {
    if (branchBusinessHour.useDefault && branchBusinessHour.slots?.length) {
      return branchBusinessHour.slots;
    }
    return branchBusinessHour.customSlots || [];
  }

  private resolveTimeSlotsOptimized(
    item: FoodMenu | Section | Dish,
    branchTimeSlots: (BusinessHourSlot | CustomHourSlot)[]
  ): (BusinessHourSlot | CustomHourSlot)[] {
    if ("customTimes" in item && item.customTimes) {
      if ("globalCustomSlots" in item && item.globalCustomSlots?.length) {
        return item.globalCustomSlots;
      }
      if ("customSlots" in item && item.customSlots?.length) {
        return item.customSlots;
      }
    }
    return branchTimeSlots;
  }

  private checkTimeAvailabilityOptimized(
    slots: (BusinessHourSlot | CustomHourSlot)[],
    context: AvailabilityContext
  ): AvailabilityResult {
    // Optimized version with early returns and cached calculations
    if (!slots.length) {
      return { isAvailable: false, reason: "no_time_slots_defined" };
    }

    const currentMinutes =
      context.currentTime.getHours() * 60 + context.currentTime.getMinutes();
    const dayOfWeek = context.dayOfWeek;

    // Pre-filter active slots for current day
    const activeSlotsForDay = slots.filter(
      (slot) => slot.isActive && slot.days[dayOfWeek]
    );

    if (!activeSlotsForDay.length) {
      return { isAvailable: false, reason: "not_operating_today" };
    }

    // Check 24-hour slots first (fastest)
    if (activeSlotsForDay.some((slot) => slot.is24Hours)) {
      return { isAvailable: true };
    }

    // Check time slots
    for (const slot of activeSlotsForDay) {
      if (slot.firstSeating && slot.lastSeating) {
        const startMinutes = this.parseTimeStringToMinutes(slot.firstSeating);
        const endMinutes = this.parseTimeStringToMinutes(slot.lastSeating);

        // Handle overnight slots
        const isWithinHours =
          startMinutes > endMinutes
            ? currentMinutes >= startMinutes || currentMinutes <= endMinutes
            : currentMinutes >= startMinutes && currentMinutes <= endMinutes;

        if (isWithinHours) {
          return { isAvailable: true };
        }
      }
    }

    return { isAvailable: false, reason: "outside_operating_hours" };
  }

  private getItemId(item: FoodMenu | Section | Dish, type: string): string {
    switch (type) {
      case "menu":
        return `menu_${(item as FoodMenu).foodMenuId}`;
      case "section":
        return `section_${(item as Section).menuSectionId}`;
      case "dish":
        return `dish_${(item as Dish).dishId}`;
      default:
        return "";
    }
  }

  private applyAvailabilityResults(
    menu: FoodMenu,
    results: Map<string, AvailabilityResult>
  ): FoodMenu & { availabilityInfo: ItemWithAvailability } {
    // Apply results back to menu structure efficiently
    const menuResult = results.get(`menu_${menu.foodMenuId}`);
    (menu as any).availabilityInfo = menuResult;

    if (menu.sections) {
      for (const section of menu.sections) {
        const sectionResult = results.get(`section_${section.menuSectionId}`);
        (section as any).availabilityInfo = sectionResult;

        if (!(menu as any).availabilityInfo.isAvailable) {
          const menuAvailability = (menu as any)
            .availabilityInfo as AvailabilityResult;
          (section as any).availabilityInfo = {
            isAvailable: menuAvailability.isAvailable,
            reason: menuAvailability.reason
              ? menuAvailability.reason
              : undefined,
            nextAvailableAt: menuAvailability.nextAvailableAt
              ? menuAvailability.nextAvailableAt
              : undefined,
          };
        }

        if (section.dishes) {
          for (const dish of section.dishes) {
            const dishResult = results.get(`dish_${dish.dishId}`);
            (dish as any).availabilityInfo = dishResult;

            if (!(section as any).availabilityInfo.isAvailable) {
              const sectionAvailability = (section as any)
                .availabilityInfo as AvailabilityResult;
              (dish as any).availabilityInfo = {
                isAvailable: sectionAvailability.isAvailable,
                reason: sectionAvailability.reason
                  ? sectionAvailability.reason
                  : undefined,
                nextAvailableAt: sectionAvailability.nextAvailableAt
                  ? sectionAvailability.nextAvailableAt
                  : undefined,
              };
            }
          }
        }
      }
    }

    return menu as FoodMenu & { availabilityInfo: ItemWithAvailability };
  }
}
