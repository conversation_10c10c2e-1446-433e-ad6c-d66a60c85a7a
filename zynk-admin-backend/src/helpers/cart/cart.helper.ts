import { In, Not, Repository } from "typeorm";
import { APIError } from "../../utils/errorHandler";
import { BAD_REQUEST, CONFLICT } from "../../constants/STATUS_CODES";
import { Cart } from "../../models/cart/cart.model";

// Interface for misc item input
export interface MiscItemInput {
    name: string;
    price: number;
}

// Interface for alert input
export interface AlertInput {
    note: string;
}

// Add multiple misc items to cart
export const addMiscItemsToCart = async (
    CartRepo: Repository<Cart>,
    cartId: string,
    miscItems: MiscItemInput[]
): Promise<string[]> => {
    if (!miscItems || miscItems.length === 0) {
        return [];
    }

    const cart = await CartRepo.findOne({
        where: { cartId },
        select: ['cartId', 'miscItems']
    });

    if (!cart) {
        throw new APIError(BAD_REQUEST, `Cart with ID ${cartId} does not exist`);
    }

    // Validate misc items
    for (const item of miscItems) {
        if (!item.name || typeof item.name !== 'string' || item.name.trim().length === 0) {
            throw new APIError(BAD_REQUEST, 'Misc item name is required and must be a non-empty string');
        }
        if (typeof item.price !== 'number' || item.price < 0) {
            throw new APIError(BAD_REQUEST, 'Misc item price must be a non-negative number');
        }
    }

    // Initialize miscItems array if it doesn't exist
    if (!cart.miscItems) {
        cart.miscItems = [];
    }

    // Add items and collect their IDs
    const addedIds: string[] = [];

    for (const item of miscItems) {
        const miscItem = {
            id: `misc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: item.name.trim(),
            price: item.price,
            addedAt: new Date().toISOString()
        };
        cart.miscItems.push(miscItem);
        addedIds.push(miscItem.id);
    }

    await CartRepo.save(cart);
    return addedIds;
};

// Update misc item by ID
export const updateMiscItemInCart = async (
    CartRepo: Repository<Cart>,
    cartId: string,
    miscItemId: string,
    updates: Partial<Pick<MiscItemInput, 'name' | 'price'>>
): Promise<boolean> => {
    const cart = await CartRepo.findOne({
        where: { cartId },
        select: ['cartId', 'miscItems']
    });

    if (!cart) {
        throw new APIError(BAD_REQUEST, `Cart with ID ${cartId} does not exist`);
    }

    if (!cart.miscItems || cart.miscItems.length === 0) {
        throw new APIError(BAD_REQUEST, `No misc items found in cart ${cartId}`);
    }

    const itemIndex = cart.miscItems.findIndex(item => item.id === miscItemId);
    if (itemIndex === -1) {
        throw new APIError(BAD_REQUEST, `Misc item with ID ${miscItemId} not found in cart`);
    }

    // Validate updates
    if (updates.name !== undefined) {
        if (typeof updates.name !== 'string' || updates.name.trim().length === 0) {
            throw new APIError(BAD_REQUEST, 'Misc item name must be a non-empty string');
        }
        updates.name = updates.name.trim();
    }

    if (updates.price !== undefined) {
        if (typeof updates.price !== 'number' || updates.price < 0) {
            throw new APIError(BAD_REQUEST, 'Misc item price must be a non-negative number');
        }
    }

    // Update the item
    cart.miscItems[itemIndex] = { ...cart.miscItems[itemIndex], ...updates };

    await CartRepo.save(cart);
    return true;
};

// Remove misc item by ID
export const removeMiscItemFromCart = async (
    CartRepo: Repository<Cart>,
    cartId: string,
    miscItemId: string
): Promise<boolean> => {
    const cart = await CartRepo.findOne({
        where: { cartId },
        select: ['cartId', 'miscItems']
    });

    if (!cart) {
        throw new APIError(BAD_REQUEST, `Cart with ID ${cartId} does not exist`);
    }

    if (!cart.miscItems || cart.miscItems.length === 0) {
        throw new APIError(BAD_REQUEST, `No misc items found in cart ${cartId}`);
    }

    const initialLength = cart.miscItems.length;
    cart.miscItems = cart.miscItems.filter(item => item.id !== miscItemId);

    if (cart.miscItems.length === initialLength) {
        throw new APIError(BAD_REQUEST, `Misc item with ID ${miscItemId} not found in cart`);
    }

    await CartRepo.save(cart);
    return true;
};

// Add multiple alerts to cart
export const addAlertsToCart = async (
    CartRepo: Repository<Cart>,
    cartId: string,
    alerts: AlertInput[]
): Promise<string[]> => {
    if (!alerts || alerts.length === 0) {
        return [];
    }

    const cart = await CartRepo.findOne({
        where: { cartId },
        select: ['cartId', 'alerts']
    });

    if (!cart) {
        throw new APIError(BAD_REQUEST, `Cart with ID ${cartId} does not exist`);
    }

    // Validate alerts
    for (const alert of alerts) {
        if (!alert.note || typeof alert.note !== 'string' || alert.note.trim().length === 0) {
            throw new APIError(BAD_REQUEST, 'Alert note is required and must be a non-empty string');
        }
    }

    // Initialize alerts array if it doesn't exist
    if (!cart.alerts) {
        cart.alerts = [];
    }

    // Add alerts and collect their IDs
    const addedIds: string[] = [];

    for (const alert of alerts) {
        const alertItem = {
            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            note: alert.note.trim(),
            addedAt: new Date().toISOString()
        };
        cart.alerts.push(alertItem);
        addedIds.push(alertItem.id);
    }

    await CartRepo.save(cart);
    return addedIds;
};

// Update alert by ID
export const updateAlertInCart = async (
    CartRepo: Repository<Cart>,
    cartId: string,
    alertId: string,
    note: string
): Promise<boolean> => {
    const cart = await CartRepo.findOne({
        where: { cartId },
        select: ['cartId', 'alerts']
    });

    if (!cart) {
        throw new APIError(BAD_REQUEST, `Cart with ID ${cartId} does not exist`);
    }

    if (!cart.alerts || cart.alerts.length === 0) {
        throw new APIError(BAD_REQUEST, `No alerts found in cart ${cartId}`);
    }

    const alertIndex = cart.alerts.findIndex(alert => alert.id === alertId);
    if (alertIndex === -1) {
        throw new APIError(BAD_REQUEST, `Alert with ID ${alertId} not found in cart`);
    }

    // Validate note
    if (!note || typeof note !== 'string' || note.trim().length === 0) {
        throw new APIError(BAD_REQUEST, 'Alert note is required and must be a non-empty string');
    }

    // Update the alert
    cart.alerts[alertIndex] = { ...cart.alerts[alertIndex], note: note.trim() };

    await CartRepo.save(cart);
    return true;
};

// Remove alert by ID
export const removeAlertFromCart = async (
    CartRepo: Repository<Cart>,
    cartId: string,
    alertId: string
): Promise<boolean> => {
    const cart = await CartRepo.findOne({
        where: { cartId },
        select: ['cartId', 'alerts']
    });

    if (!cart) {
        throw new APIError(BAD_REQUEST, `Cart with ID ${cartId} does not exist`);
    }

    if (!cart.alerts || cart.alerts.length === 0) {
        throw new APIError(BAD_REQUEST, `No alerts found in cart ${cartId}`);
    }

    const initialLength = cart.alerts.length;
    cart.alerts = cart.alerts.filter(alert => alert.id !== alertId);

    if (cart.alerts.length === initialLength) {
        throw new APIError(BAD_REQUEST, `Alert with ID ${alertId} not found in cart`);
    }

    await CartRepo.save(cart);
    return true;
};