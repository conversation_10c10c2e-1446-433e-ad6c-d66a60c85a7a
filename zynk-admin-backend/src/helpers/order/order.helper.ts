import { In, Not, Repository } from "typeorm";
import { OrderQueue, QueuePriority, QueueStatus } from "../../models/order/orderQueue.model";
import { ItemType, NamedEntity, OrderItem, PriceNamedEntity, QuantityPriceNamedEntity } from "../../models/order/orderItem.model";
import { APIError } from "../../utils/errorHandler";
import { BAD_REQUEST, CONFLICT } from "../../constants/STATUS_CODES";
import { Staff } from "../../models/staff/staff.model";
import { Customer } from "../../models/customer/customer.model";
import { Branch } from "../../models/company/branch.model";
import { Dish } from "../../models/foodmenu/dish.model";
import { Allergy } from "../../models/reference/allergy.model";
import { DishAddon } from "../../models/foodmenu/custom/addon.model";
import { DishExtra } from "../../models/foodmenu/custom/extra.model";
import { DishSide } from "../../models/foodmenu/custom/dish_side.model";
import { DishBeverage } from "../../models/foodmenu/custom/dish_bev.model";
import { DishDessert } from "../../models/foodmenu/custom/dish_dessert.model";
import { Spiciness } from "../../models/foodmenu/custom/spiciness.model";
import { CookingStyle } from "../../models/foodmenu/custom/cookingstyle.model";
import { DishExclusion } from "../../models/foodmenu/custom/dishexclusion.model";
import { DishSize } from "../../models/foodmenu/custom/dishsize.model";
import { AppprovalStatus, OrderDetails, OrderStatus } from "../../models/order/orderDetail.model";
import { OrderItemInput } from "../../controllers/order/orderDetail.controller";
import { OrderType } from "../../models/tenantSettings/general/orderType.model";
import { TableModel } from "../../models/tenantSettings/tableReservation/management/table.model";

interface AddonInput {
    id: string;
    quantity: number;
}

export const addToQueue = async (
    OrderQueue: Repository<OrderQueue>,
    orderDetailId: string,
    orderItems: OrderItem[],
    estimatedPrepTime: number,
    notes?: string
) => {
    try {
        // Validate inputs
        if (!orderDetailId) {
            throw new APIError(BAD_REQUEST, "orderDetailId is required");
        }
        if (!estimatedPrepTime || estimatedPrepTime <= 0) {
            throw new APIError(BAD_REQUEST, "estimatedPrepTime must be a positive number");
        }
        if (!orderItems || orderItems.length === 0) {
            throw new APIError(BAD_REQUEST, "At least one orderItem is required");
        }

        // // Check for existing active queue entry
        // const existingQueueEntry = await OrderQueue.findOne({
        //     where: {
        //         orderDetailId,
        //         status: Not(In([QueueStatus.CHECKOUT, QueueStatus.SERVED, QueueStatus.CANCELLED]))
        //     },
        //     relations: ['orderItems']
        // });

        // if (existingQueueEntry) {
        //     // Update existing queue entry - merge order items properly
        //     const existingItemIds = existingQueueEntry.orderItems?.map(item => item.orderItemId) || [];
        //     const newItems = orderItems.filter(item => !existingItemIds.includes(item.orderItemId));

        //     existingQueueEntry.orderItems = [...(existingQueueEntry.orderItems || []), ...newItems];
        //     existingQueueEntry.estimatedPrepTime = estimatedPrepTime;
        //     existingQueueEntry.notes = notes || existingQueueEntry.notes;
        //     existingQueueEntry.updatedAt = new Date();

        //     const savedEntry = await OrderQueue.save(existingQueueEntry);
        //     return savedEntry;
        // }

        // Calculate next queue position for new entry
        const activeQueueCount = await OrderQueue.count({
            where: {
                status: Not(In([QueueStatus.CHECKOUT, QueueStatus.SERVED, QueueStatus.CANCELLED]))
            }
        });
        const nextPosition = activeQueueCount + 1;

        // Create new queue entry
        const queueItem = OrderQueue.create({
            orderDetailId,
            orderItems: orderItems, // Make sure orderItems are properly set
            status: QueueStatus.PENDING,
            priority: QueuePriority.NORMAL,
            queuePosition: nextPosition,
            estimatedPrepTime,
            notes
        });

        // Save the queue item with proper cascade
        const savedQueueItem = await OrderQueue.save(queueItem);

        // If using the alternative approach (one-to-many), update orderItems
        if (savedQueueItem && orderItems.length > 0) {
            // For alternative approach, you might need to update orderItems separately
            // This depends on your chosen relationship approach
        }

        return savedQueueItem;
    } catch (error: any) {
        console.error(`Error adding order ${orderDetailId} to queue:`, error);
        throw new APIError(CONFLICT, `Failed to add order ${orderDetailId} to queue: ${error.message}`);
    }
};

export const validateStaffId = async (Staff: Repository<Staff>, staffId: string | null): Promise<NamedEntity | null> => {
    if (!staffId) return Promise.resolve(null);

    const staff = await Staff.findOne({
        where: { staffId },
        select: ['staffId', 'firstName', 'lastName']
    });

    if (!staff) {
        throw new APIError(BAD_REQUEST, `Staff with ID ${staffId} does not exist`);
    }

    return {
        id: staff.staffId,
        name: `${staff.firstName} ${staff.lastName}`
    };
};


export const validateCustomerId = async (Customer: Repository<Customer>, customerId: string | null): Promise<NamedEntity | null> => {
    if (!customerId) return Promise.resolve(null);

    const customer = await Customer.findOne({
        where: { customerId },
        select: ['customerId', 'firstName', 'lastName']
    });

    if (!customer) {
        throw new APIError(BAD_REQUEST, `Customer with ID ${customerId} does not exist`);
    }

    return {
        id: customer.customerId,
        name: `${customer.firstName} ${customer.lastName}`
    };
}


export const validateBranchId = async (Branch: Repository<Branch>, branchId: string | null): Promise<NamedEntity | null> => {
    if (!branchId) return Promise.resolve(null);

    const branch = await Branch.findOne({
        where: { branchId },
        select: ['branchId', 'name']
    });

    if (!branch) {
        throw new APIError(BAD_REQUEST, `Branch with ID ${branchId} does not exist`);
    }

    return {
        id: branch.branchId,
        name: branch.name
    };
}


// todo
export const validateOrderTypeId = async (OrderType: Repository<OrderType>, orderTypeId: string | null): Promise<NamedEntity | null> => {
    if (!orderTypeId) return Promise.resolve(null);

    const orderType = await OrderType.findOne({
        where: { orderTypeId },
        select: ['orderTypeId', 'name']
    });

    if (!orderType) {
        throw new APIError(BAD_REQUEST, `Order Type with ID ${orderTypeId} does not exist`);
    }

    return {
        id: orderType.orderTypeId,
        name: orderType.name
    };
}


export const validateSpicinessId = async (Spiciness: Repository<Spiciness>, spicinessId: string | null): Promise<PriceNamedEntity | null> => {
    if (!spicinessId) return Promise.resolve(null);
    const spiciness = await Spiciness.findOne({
        where: { spicinessId },
        select: ['spicinessId', 'name', 'price']
    });
    if (!spiciness) {
        throw new APIError(BAD_REQUEST, `Spiciness with ID ${spicinessId} does not exist`);
    }
    return {
        id: spiciness.spicinessId,
        name: spiciness.name,
        price: spiciness.price
    };
}


export const validateCookingStyleId = async (CookingStyle: Repository<CookingStyle>, cookingStyleId: string | null): Promise<NamedEntity | null> => {
    if (!cookingStyleId) return Promise.resolve(null);
    const cookingStyle = await CookingStyle.findOne({
        where: { cookingStyleId },
        select: ['cookingStyleId', 'name']
    });
    if (!cookingStyle) {
        throw new APIError(BAD_REQUEST, `Cooking Style with ID ${cookingStyleId} does not exist`);
    }
    return {
        id: cookingStyle.cookingStyleId,
        name: cookingStyle.name
    };
}


export const validateDishId = async (Dish: Repository<Dish>, dishId: string | null): Promise<PriceNamedEntity | null> => {
    if (!dishId) return Promise.resolve(null);

    const dish = await Dish.findOne({
        where: { dishId },
        select: ['dishId', 'name', "price"]
    });
    if (!dish) {
        throw new APIError(BAD_REQUEST, `Dish with ID ${dishId} does not exist`);
    }
    return {
        id: dish.dishId,
        name: dish.name,
        price: dish.price
    };
}

export const validateDishExclusionIds = async (DishExclusion: Repository<DishExclusion>, dishExclusionIds: string[]): Promise<NamedEntity[]> => {
    if (!dishExclusionIds || dishExclusionIds.length === 0) return Promise.resolve([]);
    const exclusions = await DishExclusion.find({
        where: { dishExclusionId: In(dishExclusionIds) },
        select: ['dishExclusionId', 'name']
    });
    if (exclusions.length !== dishExclusionIds.length) {
        const missingIds = dishExclusionIds.filter(id => !exclusions.some(e => e.dishExclusionId === id));
        throw new APIError(BAD_REQUEST, `Dish exclusions with IDs ${missingIds.join(', ')} do not exist`);
    }
    return exclusions.map(exclusion => ({
        id: exclusion.dishExclusionId,
        name: exclusion.name
    }));
}

export const validateDishSizeId = async (DishSize: Repository<DishSize>, dishSizeId: string | null): Promise<PriceNamedEntity | null> => {
    if (!dishSizeId) return Promise.resolve(null);
    const dishSize = await DishSize.findOne({
        where: { dishSizeId },
        select: ['dishSizeId', 'name', 'price']
    });
    if (!dishSize) {
        throw new APIError(BAD_REQUEST, `Dish Size with ID ${dishSizeId} does not exist`);
    }
    return {
        id: dishSize.dishSizeId,
        name: dishSize.name,
        price: dishSize.price
    };
}

export const validateAllergyIds = async (Allergy: Repository<Allergy>, allergyIds: string[]): Promise<NamedEntity[]> => {
    if (!allergyIds || allergyIds.length === 0) return Promise.resolve([]);

    const allergies = await Allergy.find({
        where: { allergyId: In(allergyIds) },
        select: ['allergyId', 'name']
    });

    if (allergies.length !== allergyIds.length) {
        const missingIds = allergyIds.filter(id => !allergies.some(a => a.allergyId === id));
        throw new APIError(BAD_REQUEST, `Allergies with IDs ${missingIds.join(', ')} do not exist`);
    }
    return allergies.map(allergy => ({
        id: allergy.allergyId,
        name: allergy.name
    }));
}

export const validateDishAddonsIds = async (
    DishAddon: Repository<DishAddon>,
    addonsInput: AddonInput[]
): Promise<QuantityPriceNamedEntity[]> => {
    // Return empty array if no addons provided
    if (!addonsInput || addonsInput.length === 0) return Promise.resolve([]);

    // Extract addon IDs
    const addonIds = addonsInput.map(addon => addon.id);

    // Fetch addons from the repository
    const addons = await DishAddon.find({
        where: { addonId: In(addonIds) },
        select: ['addonId', 'name', 'price'],
    });

    // Validate that all requested addon IDs exist
    if (addons.length !== addonIds.length) {
        const missingIds = addonIds.filter(id => !addons.some(a => a.addonId === id));
        throw new APIError(BAD_REQUEST, `Addons with IDs ${missingIds.join(', ')} do not exist`);
    }

    // Map addons to the required output format, including price * quantity
    return addonsInput.map(input => {
        const addon = addons.find(a => a.addonId === input.id)!; // Safe due to validation
        return {
            id: addon.addonId,
            name: addon.name,
            price: addon.price * input.quantity,
            quantity: input.quantity
        };
    });
};

export const validateDishExtrasIds = async (
    DishExtra: Repository<DishExtra>, dishExtrasInput: AddonInput[]
): Promise<QuantityPriceNamedEntity[]> => {

    if (!dishExtrasInput || dishExtrasInput.length === 0) return Promise.resolve([]);
    const extraIds = dishExtrasInput.map(extra => extra.id);

    const extras = await DishExtra.find({
        where: { extraId: In(extraIds) },
        select: ['extraId', 'name', 'price'],
    });

    if (extras.length !== extraIds.length) {
        const missingIds = extraIds.filter(id => !extras.some(e => e.extraId === id));
        throw new APIError(BAD_REQUEST, `Extras with IDs ${missingIds.join(', ')} do not exist`);
    }

    return dishExtrasInput.map(input => {
        const extra = extras.find(e => e.extraId === input.id)!; // Safe due to validation
        return {
            id: extra.extraId,
            name: extra.name,
            price: extra.price * input.quantity,
            quantity: input.quantity
        };
    });
}


export const validateSideDishIds = async (
    Dish: Repository<DishSide>, sideDishesInput: AddonInput[]): Promise<QuantityPriceNamedEntity[]> => {
    if (!sideDishesInput || sideDishesInput.length === 0) return Promise.resolve([]);
    const sideDishIds = sideDishesInput.map(side => side.id);

    const sideDishes = await Dish.createQueryBuilder('dishSide')
        .leftJoinAndSelect('dishSide.dish', 'dish')
        .where('dishSide.dishSideId IN (:...ids)', { ids: sideDishIds })
        .select([
            'dishSide.dishSideId',
            'dish.name',
            'dish.price'
        ])
        .getMany();

    if (sideDishes.length !== sideDishIds.length) {
        const missingIds = sideDishIds.filter(id => !sideDishes.some(sd => sd.dishSideId === id));
        throw new APIError(BAD_REQUEST, `Side dishes with IDs ${missingIds.join(', ')} do not exist`);
    }

    return sideDishesInput.map(input => {
        const sideDish = sideDishes.find(sd => sd.dishSideId === input.id)!; // Safe due to validation
        return {
            id: sideDish.dishSideId,
            name: sideDish.dish.name,
            price: sideDish.dish.price * input.quantity,
            quantity: input.quantity
        };
    });
}


export const validateBeverageDishIds = async (
    Beverage: Repository<DishBeverage>, beverageDishesInput: AddonInput[]): Promise<QuantityPriceNamedEntity[]> => {
    if (!beverageDishesInput || beverageDishesInput.length === 0) return Promise.resolve([]);
    const beverageDishIds = beverageDishesInput.map(beverage => beverage.id);

    const beverageDishes = await Beverage.createQueryBuilder('dishBeverage')
        .leftJoinAndSelect('dishBeverage.dish', 'dish')
        .where('dishBeverage.dishBevId IN (:...ids)', { ids: beverageDishIds })
        .select([
            'dishBeverage.dishBevId',
            'dish.name',
            'dish.price'
        ])
        .getMany();

    if (beverageDishes.length !== beverageDishIds.length) {
        const missingIds = beverageDishIds.filter(id => !beverageDishes.some(bd => bd.dishBevId === id));
        throw new APIError(BAD_REQUEST, `Beverage dishes with IDs ${missingIds.join(', ')} do not exist`);
    }

    return beverageDishesInput.map(input => {
        const beverageDish = beverageDishes.find(bd => bd.dishBevId === input.id)!; // Safe due to validation
        return {
            id: beverageDish.dishBevId,
            name: beverageDish.dish.name,
            price: beverageDish.dish.price * input.quantity,
            quantity: input.quantity
        };
    });
}


export const validateDessertDishIds = async (
    Dessert: Repository<DishDessert>, dessertDishesInput: AddonInput[]): Promise<QuantityPriceNamedEntity[]> => {
    if (!dessertDishesInput || dessertDishesInput.length === 0) return Promise.resolve([]);
    const dessertDishIds = dessertDishesInput.map(dessert => dessert.id);

    const dessertDishes = await Dessert.createQueryBuilder('dishDessert')
        .leftJoinAndSelect('dishDessert.dish', 'dish')
        .where('dishDessert.dishDessertId IN (:...ids)', { ids: dessertDishIds })
        .select([
            'dishDessert.dishDessertId',
            'dish.name',
            'dish.price'
        ])
        .getMany();

    if (dessertDishes.length !== dessertDishIds.length) {
        const missingIds = dessertDishIds.filter(id => !dessertDishes.some(dd => dd.dishDessertId === id));
        throw new APIError(BAD_REQUEST, `Dessert dishes with IDs ${missingIds.join(', ')} do not exist`);
    }

    return dessertDishesInput.map(input => {
        const dessertDish = dessertDishes.find(dd => dd.dishDessertId === input.id)!; // Safe due to validation
        return {
            id: dessertDish.dishDessertId,
            name: dessertDish.dish.name,
            price: dessertDish.dish.price * input.quantity,
            quantity: input.quantity
        };
    });
}

export const validateTableId = async (Table: Repository<TableModel>, tableId: string | null): Promise<NamedEntity | null> => {
    if (!tableId) return Promise.resolve(null);

    const table = await Table.findOne({
        where: { tableId },
        select: ['tableId', 'name'] // Assuming table has a name field
    });

    if (!table) {
        throw new APIError(BAD_REQUEST, `Table with ID ${tableId} does not exist`);
    }

    return {
        id: table.tableId,
        name: table.name
    };
};


interface CreateOrderDetailsInput {
    tableId: string;
    orderTypeId: string;
    orderItems: OrderItemInput[];
    total: number;
    notes?: string;
    estimatedPrepTime?: number;
    orderedById?: string;
    customerId?: string;
    branchId?: string;
    orderApproval?: AppprovalStatus;
    assignedWaiterId?: string;
    numberOfPeople?: number;
    alerts?: any[];
    miscItems?: any[];
}

export const createOrderDetailsHelper = async (
    input: CreateOrderDetailsInput,
    repositories: {
        OrderDetails: Repository<OrderDetails>;
        OrderItem: Repository<OrderItem>;
        OrderQueue: Repository<OrderQueue>;
        Staff: Repository<Staff>;
        Customer: Repository<Customer>;
        Branch: Repository<Branch>;
        OrderType: Repository<OrderType>;
        TableModel: Repository<TableModel>;
        Dish: Repository<Dish>;
        Spiciness: Repository<Spiciness>;
        CookingStyle: Repository<CookingStyle>;
        DishExclusion: Repository<DishExclusion>;
        DishSize: Repository<DishSize>;
        Allergy: Repository<Allergy>;
        DishAddon: Repository<DishAddon>;
        DishExtra: Repository<DishExtra>;
        DishSide: Repository<DishSide>;
        DishBeverage: Repository<DishBeverage>;
        DishDessert: Repository<DishDessert>;
    },
    queryRunner: any // TypeORM QueryRunner
): Promise<OrderDetails> => {
    const {
        tableId,
        orderTypeId,
        orderItems,
        notes,
        estimatedPrepTime,
        orderedById,
        customerId,
        branchId,
        orderApproval,
        total,
        assignedWaiterId,
        numberOfPeople,
        alerts,
        miscItems
    } = input;

    // Validate required fields
    if (!tableId || !orderTypeId || !orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
        throw new Error(`${BAD_REQUEST}: tableId, orderTypeId, and orderItems are required`);
    }
    if (typeof total !== 'number' || total <= 0) {
        throw new Error(`${BAD_REQUEST}: total must be a positive number`);
    }

    // Validate and get entity objects with names
    const [
        tableEntity,
        orderTypeEntity,
        orderedByEntity,
        customerEntity,
        branchEntity,
        assignedWaiterEntity
    ] = await Promise.all([
        validateTableId(repositories.TableModel, tableId),
        validateOrderTypeId(repositories.OrderType, orderTypeId),
        orderedById ? validateStaffId(repositories.Staff, orderedById) : Promise.resolve(null),
        customerId ? validateCustomerId(repositories.Customer, customerId) : Promise.resolve(null),
        branchId ? validateBranchId(repositories.Branch, branchId) : Promise.resolve(null),
        assignedWaiterId ? validateStaffId(repositories.Staff, assignedWaiterId) : Promise.resolve(null)
    ]);

    // Create order details
    const orderDetailData: Partial<OrderDetails> = {
        table: tableEntity!,
        total,
        orderCode: await generateIncrementalOrderCodeOptimized(repositories.OrderDetails),
        orderType: orderTypeEntity!,
        orderApproval,
        estimatedPrepTime,
        notes,
        orderedBy: orderedByEntity || undefined,
        customerInfo: customerEntity || undefined,
        branchId: branchEntity?.id || undefined,
        assignedWaiter: assignedWaiterEntity || undefined,
        status: OrderStatus.PENDING,
        numberOfPeople,
        alerts: alerts || [],
        miscItems: miscItems || []
    };

    const orderDetail = repositories.OrderDetails.create(orderDetailData);
    const savedOrderDetail = await repositories.OrderDetails.save(orderDetail);


    // Process order items with customizations
    const orderItemEntities = await Promise.all(
        orderItems.map(async (item) => {
            // Validate dish
            const dishEntity = await validateDishId(repositories.Dish, item.dishId);

            // Validate customizations in parallel
            const [
                allergyEntities,
                dishSizeEntity,
                dishExclusionEntities,
                cookingStyleEntity,
                spicinessEntity,
                dishAddonEntities,
                dishExtraEntities,
                dishSideEntities,
                dishBeverageEntities,
                dishDessertEntities
            ] = await Promise.all([
                item.allergyIds ? validateAllergyIds(repositories.Allergy, item.allergyIds) : Promise.resolve([]),
                item.dishSizeId ? validateDishSizeId(repositories.DishSize, item.dishSizeId) : Promise.resolve(null),
                item.dishExclusionIds ? validateDishExclusionIds(repositories.DishExclusion, item.dishExclusionIds) : Promise.resolve([]),
                item.cookingStyleId ? validateCookingStyleId(repositories.CookingStyle, item.cookingStyleId) : Promise.resolve(null),
                item.spicinessId ? validateSpicinessId(repositories.Spiciness, item.spicinessId) : Promise.resolve(null),
                item.dishAddons ? validateDishAddonsIds(repositories.DishAddon, item.dishAddons) : Promise.resolve([]),
                item.dishExtras ? validateDishExtrasIds(repositories.DishExtra, item.dishExtras) : Promise.resolve([]),
                item.dishSides ? validateSideDishIds(repositories.DishSide, item.dishSides) : Promise.resolve([]),
                item.dishBeverages ? validateBeverageDishIds(repositories.DishBeverage, item.dishBeverages) : Promise.resolve([]),
                item.dishDesserts ? validateDessertDishIds(repositories.DishDessert, item.dishDesserts) : Promise.resolve([])
            ]);

            const orderItemData: Partial<OrderItem> = {
                name: item.name,
                price: item.price,
                quantity: item.quantity,
                baseItem: dishEntity || undefined,
                orderDetails: savedOrderDetail,
                type: item.type || ItemType.STANDARD,
                allergies: allergyEntities.length > 0 ? allergyEntities : undefined,
                dishSizes: dishSizeEntity || undefined,
                dishExclusions: dishExclusionEntities.length > 0 ? dishExclusionEntities : undefined,
                cookingStyles: cookingStyleEntity || undefined,
                spiciness: spicinessEntity || undefined,
                dishAddons: dishAddonEntities.length > 0 ? dishAddonEntities : undefined,
                dishExtras: dishExtraEntities.length > 0 ? dishExtraEntities : undefined,
                dishSides: dishSideEntities.length > 0 ? dishSideEntities : undefined,
                dishBeverages: dishBeverageEntities.length > 0 ? dishBeverageEntities : undefined,
                dishDesserts: dishDessertEntities.length > 0 ? dishDessertEntities : undefined,
                notes: item.notes,
            };

            // Add baseItem for customized items
            if (item.type === ItemType.CUSTOMIZED && item.baseItemId) {
                const baseItemEntity = await validateDishId(repositories.Dish, item.baseItemId);
                orderItemData.baseItem = baseItemEntity || undefined;
            }

            // Set creation time if provided
            if (item.addedOn) {
                orderItemData.createdAt = new Date(item.addedOn);
            }

            return repositories.OrderItem.create(orderItemData);
        })
    );

    const savedOrderItems = await repositories.OrderItem.save(orderItemEntities);

    // If order is approved, add to queue
    if (orderApproval === AppprovalStatus.APPROVED) {
        if (!estimatedPrepTime || estimatedPrepTime <= 0) {
            throw new Error(`${BAD_REQUEST}: estimatedPrepTime is required and must be positive for approved orders`);
        }
        try {
            await addToQueue(repositories.OrderQueue, savedOrderDetail.orderDetailId, savedOrderItems, estimatedPrepTime, notes);
            console.log(`Order ${savedOrderDetail.orderDetailId} added to queue successfully`);
        } catch (queueError: any) {
            throw new Error(`Failed to add order ${savedOrderDetail.orderDetailId} to queue: ${queueError.message}`);
        }
    }

    // Fetch complete order with items
    const completeOrder = await repositories.OrderDetails.findOne({
        where: { orderDetailId: savedOrderDetail.orderDetailId },
        relations: ['orderItems', 'orderItems.dishEntity']
    });

    if (!completeOrder) {
        throw new Error('Failed to fetch created order');
    }

    return completeOrder;
};

// Helper function to calculate total price for cart items including all customizations
export const calculateCartItemPrice = async (cartItem: any): Promise<number> => {
    try {
        let totalPrice = 0;

        // Base price multiplied by quantity
        totalPrice += cartItem.price * cartItem.quantity;

        // Add dish size price if exists
        if (cartItem.dishSizes?.price) {
            totalPrice += cartItem.dishSizes.price * cartItem.quantity;
        }

        // Add spiciness price if exists
        if (cartItem.spiciness?.price) {
            totalPrice += cartItem.spiciness.price * cartItem.quantity;
        }

        // Add dish addons price
        if (cartItem.dishAddons?.length > 0) {
            const addonsPrice = cartItem.dishAddons.reduce((sum: number, addon: any) => {
                return sum + (addon.price);
            }, 0);
            totalPrice += addonsPrice * cartItem.quantity;
        }

        // Add dish extras price
        if (cartItem.dishExtras?.length > 0) {
            const extrasPrice = cartItem.dishExtras.reduce((sum: number, extra: any) => {
                return sum + (extra.price);
            }, 0);
            totalPrice += extrasPrice * cartItem.quantity;
        }

        // Add dish sides price
        if (cartItem.dishSides?.length > 0) {
            const sidesPrice = cartItem.dishSides.reduce((sum: number, side: any) => {
                return sum + (side.price);
            }, 0);
            totalPrice += sidesPrice * cartItem.quantity;
        }

        // Add dish beverages price
        if (cartItem.dishBeverages?.length > 0) {
            const beveragesPrice = cartItem.dishBeverages.reduce((sum: number, beverage: any) => {
                return sum + (beverage.price);
            }, 0);
            totalPrice += beveragesPrice * cartItem.quantity;
        }

        // Add dish desserts price
        if (cartItem.dishDesserts?.length > 0) {
            const dessertsPrice = cartItem.dishDesserts.reduce((sum: number, dessert: any) => {
                return sum + (dessert.price);
            }, 0);
            totalPrice += dessertsPrice * cartItem.quantity;
        }

        return Math.round(totalPrice * 100) / 100; // Round to 2 decimal places
    } catch (error: any) {
        throw new APIError(BAD_REQUEST, `Failed to calculate cart item price: ${error.message}`);
    }
};

export const calculateTotalCartPrice = async (cartItems: any[], miscItems: PriceNamedEntity[] = []): Promise<number> => {
    try {
        if (!Array.isArray(cartItems) || cartItems.length === 0) {
            throw new APIError(BAD_REQUEST, "Cart items must be a non-empty array");
        }

        let totalPrice = 0;
        for (const item of cartItems) {
            const itemPrice = Number(item.totalPrice) || await calculateCartItemPrice(item);
            totalPrice += itemPrice;
        }

        if (Array.isArray(miscItems) && miscItems.length > 0) {
            for (const miscItem of miscItems) {
                const miscPrice = Number(miscItem.price) || 0;
                totalPrice += miscPrice;
            }
        }

        return Math.round(totalPrice * 100) / 100; // Round to 2 decimal places
    } catch (error: any) {
        throw new APIError(BAD_REQUEST, `Failed to calculate total cart price: ${error.message}`);
    }
}


export const calculateOrderItemPrice = async (orderItem: any): Promise<number> => {
    try {
        let totalPrice = 0;

        // Base price multiplied by quantity
        totalPrice += orderItem.baseItem.price * orderItem.quantity;

        // Add dish size price if exists
        if (orderItem.dishSizes?.price) {
            totalPrice += orderItem.dishSizes.price * orderItem.quantity;
        }

        // Add spiciness price if exists
        if (orderItem.spiciness?.price) {
            totalPrice += orderItem.spiciness.price * orderItem.quantity;
        }

        // Add dish addons price
        if (orderItem.dishAddons?.length > 0) {
            const addonsPrice = orderItem.dishAddons.reduce((sum: number, addon: any) => {
                return sum + (addon.price);
            }, 0);
            totalPrice += addonsPrice * orderItem.quantity;
        }

        // Add dish extras price
        if (orderItem.dishExtras?.length > 0) {
            const extrasPrice = orderItem.dishExtras.reduce((sum: number, extra: any) => {
                return sum + (extra.price);
            }, 0);
            totalPrice += extrasPrice * orderItem.quantity;
        }

        // Add dish sides price
        if (orderItem.dishSides?.length > 0) {
            const sidesPrice = orderItem.dishSides.reduce((sum: number, side: any) => {
                return sum + (side.price);
            }, 0);
            totalPrice += sidesPrice * orderItem.quantity;
        }

        // Add dish beverages price
        if (orderItem.dishBeverages?.length > 0) {
            const beveragesPrice = orderItem.dishBeverages.reduce((sum: number, beverage: any) => {
                return sum + (beverage.price);
            }, 0);
            totalPrice += beveragesPrice * orderItem.quantity;
        }

        // Add dish desserts price
        if (orderItem.dishDesserts?.length > 0) {
            const dessertsPrice = orderItem.dishDesserts.reduce((sum: number, dessert: any) => {
                return sum + (dessert.price);
            }, 0);
            totalPrice += dessertsPrice * orderItem.quantity;
        }

        return Math.round(totalPrice * 100) / 100; // Round to 2 decimal places
    } catch (error: any) {
        throw new APIError(BAD_REQUEST, `Failed to calculate order item price: ${error.message}`);
    }
};

export const calculateTotalOrderPrice = async (orderItems: any[]): Promise<number> => {
    try {
        if (!Array.isArray(orderItems) || orderItems.length === 0) {
            throw new APIError(BAD_REQUEST, "Order items must be a non-empty array");
        }

        let totalPrice = 0;
        for (const item of orderItems) {
            const itemPrice = Number(item.price) || await calculateOrderItemPrice(item);
            totalPrice += itemPrice;
        }

        return Math.round(totalPrice * 100) / 100; // Round to 2 decimal places
    } catch (error: any) {
        throw new APIError(BAD_REQUEST, `Failed to calculate total order price: ${error.message}`);
    }
}

export const generateIncrementalOrderCodeOptimized = async (OrderDetails: Repository<OrderDetails>): Promise<string> => {
    try {
        // More efficient for large datasets - extract the numeric part and find MAX
        const result = await OrderDetails.createQueryBuilder('orderDetails')
            .select('MAX(CAST(SUBSTRING(orderDetails.orderCode FROM 4) AS INTEGER))', 'maxNumber')
            .where('orderDetails.orderCode ~ :pattern', { pattern: '^ODR[0-9]+$' })
            .getRawOne();

        const maxNumber = result?.maxNumber || 0;
        const nextNumber = maxNumber + 1;

        return `ODR${nextNumber}`;

    } catch (error) {
        console.error('Error generating optimized order code:', error);
        return `ODR${Date.now()}${Math.floor(Math.random() * 1000)}`;
    }
};







