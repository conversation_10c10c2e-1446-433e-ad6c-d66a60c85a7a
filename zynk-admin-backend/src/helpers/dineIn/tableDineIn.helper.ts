import { QueryRunner, Repository } from 'typeorm';
import { TableModel, TableStatus, TableCleaningStatus } from "../../models/tenantSettings/tableReservation/management/table.model"
import { getRepositories } from '../system/RepositoryHelper.helper';
import { APIError } from '../../utils/errorHandler';
import { CONFLICT, NOT_FOUND } from '../../constants/STATUS_CODES';
import { TABLE_NOT_FOUND } from '../../constants/tenant/settings/tableReserv/err';
import { GUESTS_LEAVING_EXCEEDS_BOOKED_SEATS, INVALID_GUESTS_LEAVING_COUNT } from '../../constants/tenant/dineIn/err';
import log from '../system/logger.helper';

export const completeTableService = async (
    qRunner: QueryRunner,
    tableId: string,
    guestsLeaving: number,
    branchId?: string
) => {
    try {
        const { TableModel: TableRepository } = getRepositories(qRunner) as {
            TableModel: Repository<TableModel>
        };

        const whereCondition: any = { tableId };
        if (branchId) {
            whereCondition.branch = { branchId };
        }

        const table = await TableRepository.findOne({
            where: whereCondition
        });

        if (!table) {
            throw new APIError(NOT_FOUND, TABLE_NOT_FOUND);
        }

        if (guestsLeaving <= 0) {
            throw new APIError(CONFLICT, INVALID_GUESTS_LEAVING_COUNT);
        }

        if (guestsLeaving > table.bookedSeats) {
            throw new APIError(CONFLICT, GUESTS_LEAVING_EXCEEDS_BOOKED_SEATS);
        }

        const newBookedSeats = table.bookedSeats - guestsLeaving;
        
        let newStatus: TableStatus = table.status;
        let newCleaningStatus: TableCleaningStatus = table.cleaning;

        if (newBookedSeats === 0) {
            newStatus = TableStatus.AVAILABLE;
            newCleaningStatus = TableCleaningStatus.NEEDSCLEANING;
        } else if (newBookedSeats < table.maxSeats && table.status === TableStatus.OCCUPIED) {
            newStatus = TableStatus.AVAILABLE;
            newCleaningStatus = TableCleaningStatus.NEEDSCLEANING;
        }

        await TableRepository.update(
            { tableId },
            {
                bookedSeats: newBookedSeats,
                status: newStatus,
                cleaning: newCleaningStatus
            }
        );

        // return {
        //     tableStatus: newStatus,
        //     bookedSeats: newBookedSeats,
        //     availableSeats: table.maxSeats - newBookedSeats,
        //     message: newBookedSeats === 0 
        //         ? 'TABLE_SERVICE_COMPLETED_TABLE_AVAILABLE' 
        //         : 'PARTIAL_TABLE_SERVICE_COMPLETED'
        // };

    } catch (error) {
        log.error("Error completing table service", error)
        throw error;
    }
};


export const getTableDisplayStatus = (
    table: TableModel, 
    currentReservation: any, 
    availableSeats: number
): string => {
    if (!table.enabled) return 'DISABLED'
    if (table.cleaning === TableCleaningStatus.DIRTY) return 'DIRTY'
    if (table.cleaning === TableCleaningStatus.NEEDSCLEANING) return 'NEEDS_CLEANING'
    
    if (currentReservation) {
        return `RESERVED_FOR_${currentReservation.customerName.toUpperCase()}`
    }
    
    if (table.bookedSeats >= table.maxSeats) return 'FULLY_OCCUPIED'
    if (table.bookedSeats > 0) return `PARTIALLY_OCCUPIED_${availableSeats}_AVAILABLE`
    
    return 'AVAILABLE'
}


export const canTableTakeOrder = (
    table: TableModel, 
    currentReservation: any, 
    availableSeatsAfterReservation: number
): boolean => {
    if (!table.enabled) return false
    if (table.cleaning !== TableCleaningStatus.CLEAN) return false
    if (availableSeatsAfterReservation <= 0) return false
    
    return true
}

export const determineNewTableStatus = (
  newBookedSeats: number, 
  maxSeats: number, 
  activeReservation: any, 
  confirmationCode?: string,
  hasExpiredReservations: boolean = false
): TableStatus => {
  // If there are expired reservations and no active ones, prioritize clearing the reserved status
  if (hasExpiredReservations && !activeReservation) {
    if (newBookedSeats === 0) {
      return TableStatus.AVAILABLE;
    } else if (newBookedSeats < maxSeats) {
      return TableStatus.OCCUPIED;
    } else {
      return TableStatus.OCCUPIED; // Full but not reserved
    }
  }

  // Original logic with active reservation consideration
  if (activeReservation && !confirmationCode) {
    // There's an active reservation but this isn't the reservation holder
    return TableStatus.RESERVED;
  }
  
  if (newBookedSeats === 0) {
    return activeReservation && !confirmationCode ? TableStatus.RESERVED : TableStatus.AVAILABLE;
  } else if (newBookedSeats < maxSeats) {
    return TableStatus.OCCUPIED;
  } else {
    return TableStatus.OCCUPIED; // Table is full
  }
};

export const determineNewTableStatusAfterCancellation = (
  newBookedSeats: number, 
  maxSeats: number, 
  activeReservation: any,
  shouldRevertReservation: boolean,
  hasExpiredReservations: boolean = false
): TableStatus => {
  // If there are expired reservations and no active ones, clear reserved status
  if (hasExpiredReservations && !activeReservation) {
    if (newBookedSeats === 0) {
      return TableStatus.AVAILABLE;
    } else {
      return TableStatus.OCCUPIED;
    }
  }

  // If reverting a reservation, check if there are other active reservations
  if (shouldRevertReservation) {
    if (activeReservation) {
      return TableStatus.RESERVED;
    } else if (newBookedSeats === 0) {
      return TableStatus.AVAILABLE;
    } else {
      return TableStatus.OCCUPIED;
    }
  }
  
  // Standard cancellation logic
  if (newBookedSeats === 0) {
    return activeReservation ? TableStatus.RESERVED : TableStatus.AVAILABLE;
  } else {
    return TableStatus.OCCUPIED;
  }
};

// Enhanced response type for frontend
export interface EnhancedTableInfo {
    // Original table fields
    tableId: string
    name: string
    minSeats: number
    maxSeats: number
    bookedSeats: number
    status: TableStatus
    cleaning: TableCleaningStatus
    enabled: boolean
    location: string
    
    // Calculated fields
    availableSeats: number
    occupancyPercentage: number
    
    // Reservation info
    hasReservations: boolean
    reservationCount: number
    nextReservation?: any
    currentReservation?: any
    todaysReservations: any[]
    
    // Display helpers
    displayStatus: string
    isFullyOccupied: boolean
    isPartiallyOccupied: boolean
    isEmpty: boolean
    needsCleaning: boolean
    
    // Availability summary
    availabilitySummary: {
        status: TableStatus
        bookedSeats: number
        availableSeats: number
        maxSeats: number
        cleaningStatus: TableCleaningStatus
        isEnabled: boolean
        canTakeOrder: boolean
    }
}

