import { Between, In, Not, QueryRunner, Repository } from "typeorm";
import { TableModel } from "../../models/tenantSettings/tableReservation/management/table.model";
import { ReservationStatus, TableReservation } from "../../models/dineIn/tableReservation.model";
import { TableCombination } from "../../models/tenantSettings/tableReservation/management/tableCombination.model";
import { getRepositories } from "../system/RepositoryHelper.helper";
import { 
  SYSTEM_ERROR
} from "../../constants/tenant/dineIn/err";
import log from "../system/logger.helper";
import { GUEST_COUNT_OUT_OF_RANGE_FOR_COMBINATION, SOME_TABLES_IN_COMBINATION_DISABLED, TABLE_COMBINATION_DISABLED, TABLE_COMBINATION_NOT_FOUND, TIME_SLOT_UNAVAILABLE_FOR_COMBINATION } from "../../constants/tenant/settings/tableReserv/err";

interface TableAvailabilityResult {
  available: boolean;
  reason?: string;
  tableCombination?: TableCombination;
  unavailableTables?: string[];
}

export const checkTableCombinationAvailabilityForReservation = async (
  qRunner: QueryRunner,
  tableCombinationId: string,
  reservationTime: Date,
  durationMinutes: number,
  numberOfGuests: number,
  branchId: string,
  excludeConfirmationCode?: string,
  bufferTime?: number,
): Promise<TableAvailabilityResult> => {
  try {
    const { 
      TableModel: TableRepository, 
      TableReservation: ReservationRepository,
      TableCombination: TableCombinationRepository
    } = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
      TableReservation: Repository<TableReservation>;
      TableCombination: Repository<TableCombination>;
    };

    const tableCombination = await TableCombinationRepository.findOne({
      where: {
        tableCombinationId,
        branch: { branchId }
      },
      relations: ['tables']
    });

    if (!tableCombination) {
      return { 
        available: false, 
        reason: TABLE_COMBINATION_NOT_FOUND 
      };
    }

    if (!tableCombination.enabled) {
      return { 
        available: false, 
        reason: TABLE_COMBINATION_DISABLED,
        tableCombination 
      };
    }

    if (numberOfGuests < tableCombination.minSeats || numberOfGuests > tableCombination.maxSeats) {
      return { 
        available: false, 
        reason: GUEST_COUNT_OUT_OF_RANGE_FOR_COMBINATION,
        tableCombination 
      };
    }

    const disabledTables = tableCombination.tables.filter(table => !table.enabled);
    if (disabledTables.length > 0) {
      return { 
        available: false, 
        reason: SOME_TABLES_IN_COMBINATION_DISABLED,
        tableCombination,
        unavailableTables: disabledTables.map(t => t.name)
      };
    }

    const reservationStart = new Date(reservationTime);
    const reservationEnd = new Date(reservationTime.getTime() + (durationMinutes * 60000));

    const bufferStart = new Date(reservationStart.getTime() - (bufferTime! * 60000));
    const bufferEnd = new Date(reservationEnd.getTime() + (bufferTime! * 60000));

    const tableIds = tableCombination.tables.map(table => table.tableId);

    const whereCondition: any = {
      table: { tableId: In(tableIds) },
      status: In([ReservationStatus.CONFIRMED, ReservationStatus.PENDING]),
      reservationTime: Between(bufferStart, bufferEnd)
    };

    if (excludeConfirmationCode) {
      whereCondition.confirmationCode = Not(excludeConfirmationCode);
    }

    const overlappingReservations = await ReservationRepository.find({
      where: whereCondition,
      relations: ['table']
    });

    if (overlappingReservations.length > 0) {
      const conflictingTables = Array.from(new Set(overlappingReservations.map(r => r.table.name)));
      return { 
        available: false, 
        reason: `${TIME_SLOT_UNAVAILABLE_FOR_COMBINATION}. Conflicting reservations on: ${conflictingTables.join(', ')}`,
        tableCombination,
        unavailableTables: conflictingTables
      };
    }

    const unavailableTables = [];
    for (const table of tableCombination.tables) {
      const currentTable = await TableRepository.findOne({
        where: { tableId: table.tableId }
      });

      if (currentTable) {
        const estimatedGuestsPerTable = Math.ceil(numberOfGuests / tableCombination.tables.length);
        const wouldExceedCapacity = (currentTable.bookedSeats + estimatedGuestsPerTable) > currentTable.maxSeats;
        
        if (wouldExceedCapacity) {
          unavailableTables.push(currentTable.name);
        }
      }
    }

    if (unavailableTables.length > 0) {
      return {
        available: false,
        reason: `${SOME_TABLES_IN_COMBINATION_DISABLED}. Insufficient capacity on: ${unavailableTables.join(', ')}`,
        tableCombination,
        unavailableTables
      };
    }

    return { 
      available: true, 
      tableCombination 
    };

  } catch (error) {
    log.error("Error checking table combination availability", error);
    return { 
      available: false, 
      reason: SYSTEM_ERROR 
    };
  }
};

export const checkTableCombinationAvailabilityForWalkIn = async (
  qRunner: QueryRunner,
  tableCombinationId: string,
  numberOfGuests: number,
  branchId: string,
  bufferTime: number
): Promise<TableAvailabilityResult> => {
  try {
    const { 
      TableModel: TableRepository, 
      TableReservation: ReservationRepository,
      TableCombination: TableCombinationRepository
    } = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
      TableReservation: Repository<TableReservation>;
      TableCombination: Repository<TableCombination>;
    };

    const tableCombination = await TableCombinationRepository.findOne({
      where: {
        tableCombinationId,
        branch: { branchId }
      },
      relations: ['tables']
    });

    if (!tableCombination) {
      return { available: false, reason: TABLE_COMBINATION_NOT_FOUND };
    }

    if (!tableCombination.enabled) {
      return { available: false, reason: TABLE_COMBINATION_DISABLED };
    }

    if (numberOfGuests < tableCombination.minSeats || numberOfGuests > tableCombination.maxSeats) {
      return { available: false, reason: GUEST_COUNT_OUT_OF_RANGE_FOR_COMBINATION };
    }

    const currentTime = new Date();
    const nearFutureTime = new Date(currentTime.getTime() + (bufferTime * 60000));

    const unavailableTables = [];
    let totalAvailableSeats = 0;

    for (const table of tableCombination.tables) {
      if (!table.enabled) {
        unavailableTables.push(`${table.name} (disabled)`);
        continue;
      }

      const upcomingReservations = await ReservationRepository.find({
        where: {
          table: { tableId: table.tableId },
          status: In([ReservationStatus.CONFIRMED, ReservationStatus.PENDING]),
          reservationTime: Between(currentTime, nearFutureTime)
        }
      });

      if (upcomingReservations.length > 0) {
        unavailableTables.push(`${table.name} (reserved soon)`);
        continue;
      }

      const availableSeats = table.maxSeats - table.bookedSeats;
      if (availableSeats <= 0) {
        unavailableTables.push(`${table.name} (fully occupied)`);
        continue;
      }

      totalAvailableSeats += availableSeats;
    }

    if (unavailableTables.length > 0) {
      return {
        available: false,
        reason: `Some tables unavailable: ${unavailableTables.join(', ')}`,
        unavailableTables: unavailableTables
      };
    }

    if (totalAvailableSeats < numberOfGuests) {
      return {
        available: false,
        reason: `Insufficient total capacity. Available: ${totalAvailableSeats}, Required: ${numberOfGuests}`,
        tableCombination
      };
    }

    return { 
      available: true, 
      tableCombination 
    };

  } catch (error) {
    log.error("Error checking table combination walk-in availability", error);
    return { available: false, reason: SYSTEM_ERROR };
  }
};

export const getAvailableTableCombinations = async (
  qRunner: QueryRunner,
  branchId: string,
  reservationTime: Date,
  durationMinutes: number,
  numberOfGuests: number,
  bufferTime: number
): Promise<TableCombination[]> => {
  try {
    const { TableCombination: TableCombinationRepository } = getRepositories(qRunner) as {
      TableCombination: Repository<TableCombination>;
    };

    const tableCombinations = await TableCombinationRepository.find({
      where: {
        branch: { branchId },
        enabled: true,
        availableOnline: true
      },
      relations: ['tables']
    });

    const availableCombinations = [];

    for (const combination of tableCombinations) {
      if (numberOfGuests >= combination.minSeats && numberOfGuests <= combination.maxSeats) {
        const availability = await checkTableCombinationAvailabilityForReservation(
          qRunner,
          combination.tableCombinationId,
          reservationTime,
          durationMinutes,
          numberOfGuests,
          branchId,
          undefined,
          bufferTime
        );

        if (availability.available) {
          availableCombinations.push(combination);
        }
      }
    }

    return availableCombinations;

  } catch (error) {
    log.error("Error getting available table combinations", error);
    return [];
  }
};