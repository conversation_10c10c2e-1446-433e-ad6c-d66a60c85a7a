import { Between, In, Not, QueryRunner, Repository } from "typeorm";
import { TableModel } from "../../models/tenantSettings/tableReservation/management/table.model";
import { ReservationStatus, TableReservation } from "../../models/dineIn/tableReservation.model";
import { getRepositories } from "../system/RepositoryHelper.helper";
import { TABLE_NOT_FOUND } from "../../constants/tenant/settings/tableReserv/err";
import { ERR_CHECKING_TABLE_AVAIL, GUEST_COUNT_OUT_OF_RANGE_FOR_TABLE, SYSTEM_ERROR, TABLE_DISABLED, TIME_SLOT_UNAVAILABLE } from "../../constants/tenant/dineIn/err";
import log from "../system/logger.helper";

export const checkTableAvailabilityForReservation = async (
  qRunner: QueryRunner,
  tableId: string,
  reservationTime: Date,
  durationMinutes: number,
  numberOfGuests: number,
  branchId: string,
  excludeReservationId?: string,
  bufferTime?: number,
): Promise<{ available: boolean; reason?: string, exTable: TableModel | null}> => {
  try {
    const { TableModel: TableRepository, TableReservation: ReservationRepository } = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
      TableReservation: Repository<TableReservation>;
    };

    const table = await TableRepository.findOne({
      where: {
        tableId,
        branch: { branchId }
      }
    });

    if (!table) {
      return { available: false, reason: TABLE_NOT_FOUND, exTable: null};
    }

    if (!table.enabled) {
      return { available: false, reason: TABLE_DISABLED, exTable: table};
    }

    if (numberOfGuests < table.minSeats || numberOfGuests > table.maxSeats) {
      return { available: false, reason: GUEST_COUNT_OUT_OF_RANGE_FOR_TABLE, exTable: table};
    }

    // Check for overlapping reservations
    const reservationStart = new Date(reservationTime);
    const reservationEnd = new Date(reservationTime.getTime() + (durationMinutes * 60000));

    const bufferStart = new Date(reservationStart.getTime() - (bufferTime! * 60000));
    const bufferEnd = new Date(reservationEnd.getTime() + (bufferTime! * 60000));

    const whereCondition: any = {
      table: { tableId },
      status: In([ReservationStatus.CONFIRMED, ReservationStatus.PENDING]),
      reservationTime: Between(bufferStart, bufferEnd)
    };

    if (excludeReservationId) {
      whereCondition.reservationId = Not(excludeReservationId);
    }

    const overlappingReservations = await ReservationRepository.find({
      where: whereCondition
    });

    if (overlappingReservations.length > 0) {
      return { available: false, reason: TIME_SLOT_UNAVAILABLE, exTable: table};
    }

    return { available: true, exTable: table};

  } catch (error) {
    log.error(ERR_CHECKING_TABLE_AVAIL, error);
    return { available: false, reason: SYSTEM_ERROR, exTable: null};
  }
};