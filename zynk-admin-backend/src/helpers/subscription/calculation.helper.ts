import {Subscription} from "../../models/subscription/subscriptions.model";
import {Duration} from "../../types/subscription";
import {SubscriptionTier} from "../../models/subscription/subTier/subscriptiontier.model";

export const convertDurationToDays = (duration: Duration, currentDate?: Date): number => {
    const date = currentDate || new Date();
    const year = date.getFullYear();
    const month = date.getMonth();

    switch (duration) {
        case Duration.DAILY:
            return 1;
        case Duration.WEEKLY:
            return 7;
        case Duration.MONTHLY:
            return new Date(year, month + 1, 0).getDate();
        case Duration.YEARLY:
            return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0) ? 366 : 365;
        default:
            throw new Error(`Invalid duration: ${duration}`);
    }
};

export const calculatePerDayTierPrice = (subTier: SubscriptionTier): number => {
    const daysPerUnit = convertDurationToDays(subTier.tierType);
    const totalDays = daysPerUnit * subTier.validityDuration;
    return subTier.price / totalDays;
};
export const calculateDuePrice = (perDayTierPrice: number, durationInDays: number, paidPrice: number) => (perDayTierPrice * durationInDays) - paidPrice;

export const calculateExpiryDate = (
    duration: Duration,
    durationValue: number,
    currentDate: Date = new Date()
): Date => {
    const expiryDate = new Date(currentDate);
    switch (duration) {
        case Duration.DAILY:
            expiryDate.setDate(currentDate.getDate() + durationValue);
            break;
        case Duration.WEEKLY:
            expiryDate.setDate(currentDate.getDate() + durationValue * 7);
            break;
        case Duration.MONTHLY:
            expiryDate.setMonth(currentDate.getMonth() + durationValue);
            break;
        case Duration.YEARLY:
            expiryDate.setFullYear(currentDate.getFullYear() + durationValue);
            break;
        default:
            throw new Error(`Invalid duration: ${duration}`);
    }
    return expiryDate;
};

export const calculatePrice = (
    duration: Duration,
    durationValue: number,
    price: number,
    tierBaseDuration: Duration,
    tierBaseDurationValue: number,
    currentDate = new Date()
): number => {
    const tierEndDate = calculateExpiryDate(tierBaseDuration, tierBaseDurationValue);
    const requestedEndDate = calculateExpiryDate(duration, durationValue);

    const oneDayMs = 24 * 60 * 60 * 1000;
    const tierDays = Math.round((tierEndDate.getTime() - currentDate.getTime()) / oneDayMs);
    const requestedDays = Math.round((requestedEndDate.getTime() - currentDate.getTime()) / oneDayMs);

    const pricePerDay = price / tierDays;
    const finalPrice = pricePerDay * requestedDays;

    return Math.round(finalPrice * 100) / 100;
};


export const extendNewPlan = (activePlan: Subscription, newPlan: Subscription, currentDate = new Date()): void => {
    const activeTotalDays = Math.round((activePlan.activeTill.getTime() - activePlan.activeFrom.getTime()) / (24 * 60 * 60 * 1000));
    const activeDaysUsed = Math.round((currentDate.getTime() - activePlan.activeFrom.getTime()) / (24 * 60 * 60 * 1000));
    const activeDaysRemaining = Math.max(0, activeTotalDays - activeDaysUsed);

    const taxFactor = activePlan.presentTaxPercentage / 100 + 1;
    const basePresentTierPrice = activePlan.totalCost / taxFactor;

    const dailyPriceRate = basePresentTierPrice / activeTotalDays;

    const newPrice = dailyPriceRate * activeDaysRemaining;
    const newEndDate = new Date(activePlan.activeFrom);
    newEndDate.setDate(activePlan.activeFrom.getDate() + activeDaysRemaining);

    // return {
    //     // activeFrom: newPlan.activeFrom,
    //     // activeTill: newEndDate,
    //     // presentTierPricePerDay: newPrice,
    //     // totalCost: newPrice * (1 + (newPlan.presentTaxPercentage || activePlan.presentTaxPercentage) / 100),
    //     // presentTaxPercentage: newPlan.presentTaxPercentage || activePlan.presentTaxPercentage,
    //     // subscriptionId: "",
    //     // status: SubscriptionStatus.ACTIVE,
    //     // activatedFeatures: undefined,
    //     // presentTierPricePerDay: 0,
    //     // paidPrice: 0,
    //     // duePrice: 0,
    //     // presentTaxPercentage: 0,
    //     // totalCost: 0,
    //     // activeFrom: undefined,
    //     // activeTill: undefined,
    //     // createdAt: undefined,
    //     // updatedAt: undefined,
    //     // subTier: new SubscriptionTier,
    //     // tenant: new Tenant,
    // };
};
