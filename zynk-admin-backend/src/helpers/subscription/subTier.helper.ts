import {ResponseTypeConfig} from "../../utils/Common.util";

export const subTierResTypeConfig: ResponseTypeConfig = {
    FULL: {
        relations: [],
        select: ["subTierId", "name", "description", "isActive", "price", "tierType", "validityDuration", "createdAt", "updatedAt"]
    },
    FULL_WITH_ASSOCIATION: {
        relations: [
            "features"
        ],
        select: ["subTierId", "name", "description", "isActive", "price", "tierType", "validityDuration", "createdAt", "updatedAt"]
    },
    MINIMAL: {
        relations: [],
        select: ["subTierId", "name", "description", "isActive"]
    },
    MINIMAL_WITH_ASSOCIATION: {
        relations: ["features"],
        select: ["subTierId", "name", "description", "isActive"]
    }
}