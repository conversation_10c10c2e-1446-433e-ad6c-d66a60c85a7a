import {ResponseTypeConfig} from "../../utils/Common.util";
import {Repository} from "typeorm";
import {Tax} from "../../models/subscription/tax/tax.model";

export const taxResTypeConfig: ResponseTypeConfig = {
    FULL: {
        relations: [],
        select: ["taxId", "name", "description", "taxGovId", "country", "isCentral", "state", "taxPercentage", "upperLimit", "lowerLimit", "isActive", "createdAt", "updatedAt"]
    },
    FULL_WITH_ASSOCIATION: {
        relations: [],
        select: ["taxId", "name", "description", "taxGovId", "country", "isCentral", "state", "taxPercentage", "upperLimit", "lowerLimit", "isActive", "createdAt", "updatedAt"]
    },
    MINIMAL: {
        relations: [],
        select: ["taxId", "name", "country", "isCentral", "state"]
    },
    MINIMAL_WITH_ASSOCIATION: {
        relations: [],
        select: ["taxId", "name", "country", "isCentral", "state"]
    }
}

export const getTaxBasedOnUpAndLoAndAmount = async (Tax: Repository<Tax>, countryCode: string, amount: number) => {
    return await Tax.createQueryBuilder("tax")
        .where("tax.countryCode = :countryCode", {countryCode: countryCode})
        .andWhere("tax.isActive = true")
        .andWhere(":amount BETWEEN tax.lowerLimit AND tax.upperLimit", {
            amount: amount,
        })
        .getMany();
}
