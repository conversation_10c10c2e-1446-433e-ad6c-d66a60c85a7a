import {SubscriptionTier} from "../../models/subscription/subTier/subscriptiontier.model";
import {SubscriptionRequestData} from "./subscription.helper";
import {calculateDuePrice, calculatePerDayTierPrice, convertDurationToDays} from "./calculation.helper";
import {Repository} from "typeorm";
import {Tax} from "../../models/subscription/tax/tax.model";
import {getTaxBasedOnUpAndLoAndAmount} from "./tax.helper";
import log from "../system/logger.helper";

export interface taxData {
    countryCode: string;
    taxGovId?: string;
    stateCode?: string;
    taxPercentage: number;
    upperLimit: number;
    lowerLimit: number;
    isCentral: boolean;
}

export const costValidation = async (Tax: Repository<Tax>, subTier: SubscriptionTier, requestData: SubscriptionRequestData, currentDate = new Date()) => {
    const presentTierPricePerDay = calculatePerDayTierPrice(subTier);
    const features = JSON.stringify(subTier.features);
    const paidPrice = requestData.paidPrice;
    const totalRequestedDays = convertDurationToDays(requestData.duration, currentDate) * requestData.durationValue;
    const duePrice = calculateDuePrice(presentTierPricePerDay, totalRequestedDays, paidPrice);
    if (requestData.allowDue && duePrice > 0) throw new Error("due is not allowed please pay the full amount");
    if (duePrice < 0) throw new Error("dont try to bride me with money");
    const totalAmountWithoutTax = totalRequestedDays * presentTierPricePerDay;
    let taxData: taxData = {
        countryCode: requestData.countryCode,
        taxPercentage: 0,
        upperLimit: 0,
        lowerLimit: 0,
        isCentral: false
    }
    const taxList = await getTaxBasedOnUpAndLoAndAmount(Tax, requestData.countryCode, totalAmountWithoutTax);
    if (taxList.length) {
        const stateTax = taxList.find(t => !t.isCentral && t.stateCode === requestData.stateCode);
        if (stateTax) {
            taxData = {
                countryCode: requestData.countryCode,
                stateCode: requestData.stateCode,
                taxPercentage: stateTax.taxPercentage,
                upperLimit: stateTax.upperLimit,
                lowerLimit: stateTax.lowerLimit,
                taxGovId: stateTax.taxGovId,
                isCentral: false
            }
        } else {
            const centralTax = taxList.find(t => t.isCentral);
            if (centralTax) taxData = {
                countryCode: requestData.countryCode,
                taxPercentage: centralTax.taxPercentage,
                upperLimit: centralTax.upperLimit,
                lowerLimit: centralTax.lowerLimit,
                isCentral: false,
                taxGovId: centralTax.taxGovId
            }
        }
    }
    const totalAmountWithTax = totalAmountWithoutTax * (1 + Number(taxData.taxPercentage) / 100);
    const returnData = {
        presentTierPricePerDay,
        features,
        duePrice,
        paidPrice,
        totalDays: totalRequestedDays,
        taxData,
        totalAmountWithTax
    };
    log.info("calculatedTaxPrice", returnData)
    return returnData
}