import {ResponseTypeConfig} from "../../utils/Common.util";

export const featureResTypeConfig: ResponseTypeConfig = {
    FULL: {
        relations: [],
        select: ["featureId", "name", "description", "isActive", "createdAt", "updatedAt"]
    },
    FULL_WITH_ASSOCIATION: {
        relations: ["featurePermissionActions", "featurePermissionActions.permissionAction", "featurePermissionActions.permissionAction.permission", "featurePermissionActions.permissionAction.action"],
        select: ["featureId", "name", "description", "isActive", "createdAt", "updatedAt"]
    },
    MINIMAL: {relations: [], select: ["featureId", "name"]},
    MINIMAL_WITH_ASSOCIATION: {
        relations: ["featurePermissionActions", "featurePermissionActions.permissionAction", "featurePermissionActions.permissionAction.permission", "featurePermissionActions.permissionAction.action"],
        select: ["featureId", "name"]
    }
}
//TODO: write a util here with uses the rootQueryRunner and updates all the tenantDb based on featurePermissionActionChanges Applied Here
