import {Query<PERSON><PERSON><PERSON>, Repository} from "typeorm";
import {check<PERSON>lockList} from "../settings/admin/blocker/blockList.helper";
import {LockedType} from "../../types/system";
import {getRepositories} from "../system/RepositoryHelper.helper";
import {Subscription} from "../../models/subscription/subscriptions.model";
import {
    addNewSubscriptionResult,
    Duration,
    removeExSubscription,
    SubscriptionStatus,
    SubsOperationType,
    syncFeaturePAResult
} from "../../types/subscription";
import {Tenant} from "../../models/admin/Tenant/tenant.model";
import {SubscriptionTier} from "../../models/subscription/subTier/subscriptiontier.model";
import {FormatType} from "../../types/pBAC/formatterTypes";
import {Permission} from "../../models/pBAC/Permission.model";
import {TPermissions} from "../../models/pBAC/tPBAC/TPermissions.model";
import {TAction} from "../../models/pBAC/tPBAC/TAction.entity";
import {TPermissionAction} from "../../models/pBAC/tPBAC/TPermissionAction";
import log from "../system/logger.helper";
import {
    NoActiveSubscriptionFoundException
} from "../../exceptions/controller/subscription/subscriptionTrans/impl/NoActiveSubscriptionFoundException";
import {
    SubscriptionTransException
} from "../../exceptions/controller/subscription/subscriptionTrans/SubscriptionTransException";
import {TenantNotFoundException} from "../../exceptions/controller/tenant/admin/impl/TenantNotFoundException";
import {getTenantDS} from "../../config/data-source";
import {SubscriptionException} from "../../exceptions/controller/subscription/SubscriptionException";
import {BAD_REQUEST, NOT_FOUND} from "../../constants/STATUS_CODES";
import {calculateExpiryDate} from "./calculation.helper";
import {costValidation} from "./validation.helper";
import {Tax} from "../../models/subscription/tax/tax.model";

const modifier: LockedType = LockedType.SUBSCRIPTION_MANAGER_ONLY

export interface SubscriptionRequestData {
    tenantId: string;
    subscriptionTierId: string,
    duration: Duration,
    durationValue: number,
    overrideType: SubsOperationType;
    paidPrice: number;
    allowDue: boolean;
    countryCode: string;
    stateCode: string;
}

export const activatePlan = async (
    tQRunner: QueryRunner | null,
    rQRunner: QueryRunner,
    requestData: SubscriptionRequestData,
    subDomain?: string,
) => {
    let tenantQRunner: QueryRunner | null = null;
    if (!tQRunner) {
        if (!subDomain) throw new Error("subDomain must be provided to acquire the connection");
        tenantQRunner = (await getTenantDS(subDomain)).createQueryRunner()
    }
    if (tenantQRunner) {
        await tenantQRunner.connect();
        await tenantQRunner.startTransaction();
    }
    if (!tenantQRunner) throw new Error("oops failed to capture tenant query runner");
    try {
        const {tenantId, overrideType, subscriptionTierId, duration, durationValue} = requestData
        const checkData = {
            qRunner: rQRunner,
            currentModifier: modifier,
            tenantId: tenantId,
        }
        await checkBlockList({
            ...checkData,
            lockedTypes: [LockedType.SUBSCRIPTION_MANAGER_ONLY],
            isNotOperation: true,
            throwError: true,
        });

        await checkBlockList({
            ...checkData,
            lockedTypes: [LockedType.SUBSCRIPTION_MANAGER_ONLY],
            unBlock: true,
            throwError: true,
        })
        await performLogicBasedOnOverRideType(overrideType, rQRunner, requestData);
        await syncFeaturePA(tQRunner ?? tenantQRunner, rQRunner, subscriptionTierId, tenantId);
    } catch (error) {
        if (tenantQRunner)
            await tenantQRunner.rollbackTransaction();
        log.error("error during Activating Subscription", error)
        throw error;
    } finally {
        if (tenantQRunner)
            await tenantQRunner.release();

    }
}

const performLogicBasedOnOverRideType = async (
    overRideType: SubsOperationType,
    qRunner: QueryRunner,
    requestData: SubscriptionRequestData
) => {
    switch (overRideType) {
        case SubsOperationType.CANCEL_EXISTING_AND_ADD_NEW:
            await checkExistingPlan(qRunner, requestData.tenantId, false);
            await createSubscriptionRecord(qRunner, requestData);
            break;
        case SubsOperationType.ADD_NEW:
            await checkExistingPlan(qRunner, requestData.tenantId, true, false)
            await createSubscriptionRecord(qRunner, requestData);
            break;
        case SubsOperationType.EXTEND_EXISTING_DURATION_AND_ADD_NEW:
            await createSubscriptionRecord(qRunner, requestData, false);
            break;
        case SubsOperationType.MINUS_BALANCE_AND_ADD_NEW:
            await checkExistingPlan(qRunner, requestData.tenantId, false);
            await createSubscriptionRecord(qRunner, requestData);
            break;
        default:
            throw new Error("Invalid Operation Type");
    }
}

const createSubscriptionRecord = async (
    qRunner: QueryRunner,
    requestData: SubscriptionRequestData,
    throwError: boolean = true,
    activeSubscription?: Subscription,
) => {
    const {subscriptionTierId, tenantId, duration, durationValue, overrideType, paidPrice} = requestData
    const result: addNewSubscriptionResult = {
        success: false,
        warnings: [],
        error: [],
        isAdded: false,
    }
    try {
        const currentDate = new Date();
        const {Subscription, SubscriptionTier, Tax} = getRepositories(qRunner) as {
            Subscription: Repository<Subscription>,
            SubscriptionTier: Repository<SubscriptionTier>,
            Tax: Repository<Tax>,
        }

        const subTier = await SubscriptionTier.findOne({
            where: {subTierId: subscriptionTierId},
            relations: [
                "features",
                "features.featurePermissionActions",
                "features.featurePermissionActions.permissionAction",
                "features.featurePermissionActions.permissionAction.permission",
                "features.featurePermissionActions.permissionAction.action"
            ]
        })

        if (!subTier) throw new SubscriptionException(NOT_FOUND, "Subscription Tier not found");
        if (!subTier.isActive) throw new SubscriptionException(BAD_REQUEST, "Subscription Tier is not Active");
        const {
            presentTierPricePerDay,
            features,
            duePrice,
            paidPrice,
            totalDays,
            taxData,
            totalAmountWithTax
        } = await costValidation(
            Tax,
            subTier,
            requestData,
            currentDate
        );
        let newSubscription = Subscription.create({
            subTier: {subTierId: subscriptionTierId},
            activatedFeatures: features,
            subTiersPurchasePricePerDay: presentTierPricePerDay,

            tenant: {tenantId: tenantId},
            durationType: duration,
            durationValue: durationValue,
            paidPrice: paidPrice,
            duePrice: duePrice,
            performedOperation: requestData.overrideType,
            activeFrom: currentDate,
            activeTill: calculateExpiryDate(duration, durationValue, currentDate),

            ...taxData,

            totalCost: totalAmountWithTax,
            status: SubscriptionStatus.ACTIVE,
        })
        if (overrideType === SubsOperationType.EXTEND_EXISTING_DURATION_AND_ADD_NEW) {
            if (!activeSubscription) throw new Error("active Subscription is required")
            newSubscription = await extendSubscriptionCalculations(activeSubscription, newSubscription);
        }
        if (overrideType === SubsOperationType.MINUS_BALANCE_AND_ADD_NEW) {
            if (!activeSubscription) throw new Error("active Subscription is required")
            newSubscription = await minusBalanceLogic(activeSubscription, newSubscription)
        }
        await Subscription.save(newSubscription);
        result.isAdded = true;
        result.success = true;
        return result;
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error during creating subscription Record';
        result.error.push(errorMessage);
        result.success = false;
        result.isAdded = false;
        if (throwError) throw error;
        return result;
    }
}

const minusBalanceLogic = async (activePlan: Subscription, newSubscription: Subscription) => {
    return newSubscription;
}

const extendSubscriptionCalculations = async (activePlan: Subscription, newSubscription: Subscription) => {
    return newSubscription;
}

const checkExistingPlan = async (
    qRunner: QueryRunner,
    tenantId: string,
    throwError: boolean = true,
    remove: boolean = true,
    reason: string = "As part of subscription renewal or activation"
) => {
    const result: removeExSubscription = {
        success: false,
        warnings: [],
        error: [],
        isRemoved: false,
        hasActivePlan: false
    }
    try {
        const {Subscription} = getRepositories(qRunner) as {
            Subscription: Repository<Subscription>
        }
        const exSubscription = await Subscription.findOne({
            where: {
                tenant: {tenantId: tenantId},
                status: SubscriptionStatus.ACTIVE,
            },
            relations: ["tenantSubscriptions"]
        })
        if (exSubscription) {
            if (remove) {
                exSubscription.status = SubscriptionStatus.CANCELLED;
                exSubscription.reasonForCancellation = reason;
                await Subscription.save(exSubscription);
                result.isRemoved = true;
                result.success = true;
                return result;
            } else {
                if (throwError) throw new Error("Tenant has Active Subscription.")
                result.hasActivePlan = true;
                result.success = true;
                return result;

            }
        } else {
            if (throwError) throw new NoActiveSubscriptionFoundException();
            result.error.push("No existing subscription to remove")
            result.success = false;
            result.isRemoved = false;
        }
        return result;
    } catch (error) {
        const errorMessage = error instanceof SubscriptionTransException ? error.message : 'Error During Removing Active Subscription';
        result.error.push(errorMessage);
        result.success = false;
        result.isRemoved = false;
        if (throwError) throw error;
        return result;
    }
}

// #TODO: make to sure to seed the default role in the template
const syncFeaturePA = async (
    tQRunner: QueryRunner,
    rQRunner: QueryRunner,
    subscriptionTierId: string,
    tenantId: string,
    throwError: boolean = true,
) => {
    const result: syncFeaturePAResult = {
        success: false,
        error: [],
        warnings: []
    }
    try {
        const {Tenant} = getRepositories(rQRunner) as {
            Tenant: Repository<Tenant>,
        }
        const exTenant = await Tenant.findOne({
            where: {tenantId: tenantId},
        })

        if (exTenant) {
            await syncPAIntoTenantDB(
                tQRunner,
                await extractNonDuplicatePAFromFeatures(rQRunner, subscriptionTierId)
            )
            result.success = true;
        } else {
            result.error.push("No tenant found to sync PA")
            if (throwError) throw new TenantNotFoundException();
            return result;
        }
        return result;

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error during Syncing PA to Tenant DB';
        result.error.push(errorMessage);
        if (throwError) throw error;
        return result;
    }
}

// #TODO: make sure to seed modules into template as well or else it will cause problem here dont forgot that and during
// #TODO: dont forgot to update that in tenantDb right way dont wait for api to get triggered ok or else if someone
// #TODO: tried to do a subscription then it will cause problem because the name has changed right
// #TODO: use id to save and it will keep the problem of changing name and all during seeding make sure to keep the same id in the tenant and global db for the non changeable things
const syncPAIntoTenantDB = async (qRunner: QueryRunner, permissionAction: Permission[]) => {
    try {
        const {TPermission, TAction, TPermissionAction} = getRepositories(qRunner) as {
            TPermission: Repository<TPermissions>,
            TAction: Repository<TAction>
            TPermissionAction: Repository<TPermissionAction>
        }
        const actionIds: string[] = [];
        const permissionId: string[] = [];
        for (const pa of permissionAction) {
            let exP = await TPermission.findOne({where: {permissionId: pa.permissionId}, relations: ["module"]});
            const permissionData = {
                name: pa.name,
                isActive: true,
                description: pa.description,
                accessType: pa.accessType,
                module: {moduleName: pa.module.moduleId},
            }
            if (!exP) exP = TPermission.create({
                permissionId: pa.permissionId,
                ...permissionData
            })
            else exP.update({...permissionData})
            exP = await TPermission.save(exP);
            permissionId.push(pa.permissionId);
            for (const a of pa.actions) {
                const actionData = {
                    name: a.name,
                    description: a.description,
                    accessType: a.accessType,
                    isActive: true,
                }
                let exA = await TAction.findOne({where: {actionId: a.actionId}});
                if (!exA) exA = TAction.create({
                    actionId: a.actionId,
                    ...actionData
                })
                else exA.update({...actionData})
                exA = await TAction.save(exA);
                actionIds.push(a.actionId);
                let exPA = await TPermissionAction.findOne({
                    where: {
                        action: {actionId: exA.actionId},
                        permission: {permissionId: exP.permissionId}
                    }
                })
                if (!exPA) await TPermissionAction.save(TPermissionAction.create({
                    action: {actionId: exA.actionId},
                    permission: {permissionId: exP.permissionId}
                }))
            }

        }
        const uniqueActionIds = actionIds.filter((id, index) => actionIds.indexOf(id) === index);
        const uniquePermissionIds = permissionId.filter((id, index) => permissionId.indexOf(id) === index);

        if (uniquePermissionIds.length > 0) {
            await TPermission
                .createQueryBuilder()
                .update(TPermission)
                .set({isActive: false})
                .where('permissionId NOT IN (:...ids)', {ids: uniquePermissionIds})
                .execute();
        } else {
            await TPermission
                .createQueryBuilder()
                .update(Permission)
                .set({isActive: false})
                .execute();
        }

        if (uniqueActionIds.length > 0) {
            await TAction
                .createQueryBuilder()
                .update(TAction)
                .set({isActive: false})
                .where('actionId NOT IN (:...ids)', {ids: uniqueActionIds})
                .execute();
        } else {
            await TAction
                .createQueryBuilder()
                .update(TAction)
                .set({isActive: false})
                .execute();
        }

    } catch (error) {
        throw error;
    }
}

const extractNonDuplicatePAFromFeatures = async (
    qRunner: QueryRunner,
    subscriptionTierId: string
): Promise<Permission[]> => {
    try {
        const {SubscriptionTier} = getRepositories(qRunner) as {
            SubscriptionTier: Repository<SubscriptionTier>,
        }
        const subTier = await SubscriptionTier.findOne({
            where: {subTierId: subscriptionTierId},
            relations: [
                "features",
                "features.featurePermissionActions",
                "features.featurePermissionActions.permissionAction",
                "features.featurePermissionActions.permissionAction.permission",
                "features.featurePermissionActions.permissionAction.",
                "features.featurePermissionActions.permissionAction.action"
            ]
        })
        if (!subTier?.features) {
            return [];
        }

        const allPermissionActions: Permission[] = [];
        for (const feature of subTier.features) {
            const permissionActions = (await feature.getPermissionAction(FormatType.OBJECT_ARR_OBJECT)).formattedPermissions;
            if (permissionActions?.length) {
                allPermissionActions.push(...permissionActions);
            }
        }
        const permissionMap = new Map<string, any>();
        for (const pa of allPermissionActions) {
            if (permissionMap.has(pa.permissionId)) {
                const existing = permissionMap.get(pa.permissionId)!;
                const existingActionIds = new Set(existing.actions.map((action: any) => action.actionId));
                const newActions = pa.actions.filter((action: any) => !existingActionIds.has(action.actionId));
                existing.actions.push(...newActions);
            } else {
                permissionMap.set(pa.permissionId, {
                    permissionId: pa.permissionId,
                    actions: [...pa.actions],
                });
            }
        }

        return Array.from(permissionMap.values());
    } catch (error) {
        throw new Error(`Failed to extract unique PermissionActions: ${(error as any).message}`);
    }
};