import {Country} from "country-state-city/lib/cjs";
import {State} from "country-state-city";

export const getCountry = (countryCode?: string) => {
    if (countryCode) {
        return Country.getCountryByCode(countryCode);
    }
    return Country.getAllCountries().map(country => ({
        name: country.name,
        isoCode: country.isoCode
    }));
}
export const getState = (countryCode?: string) => {
    if (countryCode) {
        return State.getStatesOfCountry(countryCode).map(state => ({
            name: state.name,
            isoCode: state.isoCode,
            countryCode: state.countryCode
        }));
    }
    const allStates: { name: string; isoCode: string; countryCode: string; }[] = [];
    Country.getAllCountries().forEach(country => {
        State.getStatesOfCountry(country.isoCode).forEach(state => {
            allStates.push({
                name: state.name,
                isoCode: state.isoCode,
                countryCode: state.countryCode
            });
        });
    });
    return allStates;
};

export const validateCountryState = (countryCode: string, stateCode: string): boolean => {
    const states = State.getStatesOfCountry(countryCode);
    return states.some(state => state.isoCode === stateCode);
};