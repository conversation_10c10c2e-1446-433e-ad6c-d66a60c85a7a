import { Repository } from "typeorm";
import { Customer } from "../../models/customer/customer.model";

export const generateRandomPassword = (length: number = 8): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
};


export const generateIncrementalCustomerCode = async (Customer: Repository<Customer>): Promise<string> => {
    try {
        // More efficient for large datasets - extract the numeric part and find MAX
        const result = await Customer.createQueryBuilder('customer')
            .select('MAX(CAST(SUBSTRING(customer.customerCode FROM 5) AS INTEGER))', 'maxNumber')
            .where('customer.customerCode ~ :pattern', { pattern: '^CUST[0-9]+$' })
            .getRawOne();

        const maxNumber = result?.maxNumber || 0;
        const nextNumber = maxNumber + 1;

        return `CUST${nextNumber}`;

    } catch (error) {
        console.error('Error generating optimized customer code:', error);
        return `CUST${Date.now()}${Math.floor(Math.random() * 1000)}`;
    }
};