import {Request} from "express";
import {In, Que<PERSON><PERSON><PERSON><PERSON>, Repository} from "typeorm";
import {Staff} from "../../models/staff/staff.model";
import {APIError} from "../../utils/errorHandler";
import {BAD_REQUEST, CONFLICT, NOT_FOUND, UNAUTHORIZED,} from "../../constants/STATUS_CODES";
import {STAFF_ALREADY_EXISTS, STAFF_CERT_CREATION_ISSUE,} from "../../constants/tenant/staff/err";
import argon2 from "argon2";
import {StaffAuth} from "../../models/staff/staffAuth.model";
import {DaysAvailable} from "../../models/staff/availability/days_available.model";
import {DefaultShiftTiming} from "../../models/staff/availability/default_shift_timing.model";
import {StaffCertification} from "../../models/staff/certs/staff_certification.model";
import {PrefBranch, PrefUserType} from "../../models/common/prefbranch.model";
import {Branch} from "../../models/company/branch.model";
import {BRANCH_NOT_FOUND,} from "../../constants/tenant/company/err";
import log from "../system/logger.helper";
import {StaffSession} from "../../models/staff/staffSession.model";
import {AESHelper} from "../system/AESHelper";
import {LoginRateLimitService} from "../../services/loginAttempt.service";
import {IP_REVOKED} from "../../constants/admin/err";
import {StaffPermissionAction} from "../../models/staff/StaffPermissionAction";
import {TRole} from "../../models/pBAC/tRole/TRole.model";

export const createStaffBasicDetails = async (
    req: Request,
    StaffRepo: Repository<Staff>,
    StaffAuth: Repository<StaffAuth>,
    PrefBranchRepo: Repository<PrefBranch>,
    BranchRepo: Repository<Branch>,
    StaffPermissionAction: Repository<StaffPermissionAction>
) => {
    try {
        const basicDetails = req.body.basicDetails;

        const exStaff = await StaffRepo.findOne({
            where: [
                {
                    emailAddress: basicDetails.emailAddress,
                },
                {
                    phoneNumber: basicDetails.phoneNumber,
                },
            ],
        });

        if (exStaff) throw new APIError(CONFLICT, STAFF_ALREADY_EXISTS);

        const {password, pin, role, ...rest} = basicDetails;

        const hashedPassword = await argon2.hash(password);
        const hashedPin = await argon2.hash(pin.toString());

        let reservedBranchesRes;

        if (rest.branches && Array.isArray(rest.branches)) {
            reservedBranchesRes = await BranchRepo.findBy({
                branchId: In(rest.branches),
            });
            if (reservedBranchesRes.length !== rest.branches.length) {
                throw new APIError(NOT_FOUND, BRANCH_NOT_FOUND);
            }
        }

        const newStaff = StaffRepo.create({
            ...(rest as Staff),
            tempRole: role,
            pin: hashedPin,
            staffAuth: StaffAuth.create({
                password: hashedPassword,
            }),
            reservedBranches: reservedBranchesRes,
            // role: staffRole
        });

        await StaffRepo.save(newStaff);

        // const newStaffPermissionAction: StaffPermissionAction[] = [];
        // for (const rolePermAction of staffRole.rolePermissionActions) {
        //     const permAction = StaffPermissionAction.create({
        //         staff: newStaff,
        //         permissionAction: rolePermAction.permissionAction,
        //     });
        //     newStaffPermissionAction.push(permAction);
        // }

        // await StaffPermissionAction.save(newStaffPermissionAction);

        const newStaffPrefBranch = PrefBranchRepo.create({
            prefBranchId: reservedBranchesRes![0].branchId,
            userId: newStaff.staffId,
            userType: PrefUserType.STAFF,
        });

        await PrefBranchRepo.save(newStaffPrefBranch);

        return newStaff;
    } catch (error) {
        throw error;
    }
};

export const createStaffDaysAvailable = async (
    staffId: string,
    daysAvailableData: any,
    DaysAvailableRepo: Repository<DaysAvailable>
) => {
    try {
        const daysAvailable = DaysAvailableRepo.create({
            staffId,
            days: daysAvailableData.days,
        });

        await DaysAvailableRepo.save(daysAvailable);
        return daysAvailable;
    } catch (error) {
        throw error;
    }
};

export const createStaffDefaultShiftTiming = async (
    staffId: string,
    defaultShiftTimingData: any,
    DefaultShiftTimingRepo: Repository<DefaultShiftTiming>
) => {
    try {
        const defaultShiftTiming = DefaultShiftTimingRepo.create({
            staffId,
            weeklyTimings: defaultShiftTimingData.weeklyTimings,
        });

        await DefaultShiftTimingRepo.save(defaultShiftTiming);
        return defaultShiftTiming;
    } catch (error) {
        throw error;
    }
};

export const createStaffCertifications = async (
    staff: Staff,
    certificationsData: any[],
    StaffCertificationRepo: Repository<StaffCertification>
) => {
    try {
        if (!certificationsData || certificationsData.length === 0) {
            return [];
        }

        const certifications = certificationsData.map((cert) =>
            StaffCertificationRepo.create({
                ...cert,
                staff,
            } as StaffCertification)
        );

        await StaffCertificationRepo.save(certifications);
        return certifications;
    } catch (error) {
        throw error;
    }
};

export const updateStaffBasicDetails = async (
    basicDetails: any,
    exStaff: Staff,
    StaffRepo: Repository<Staff>
) => {
    try {
        if (Object.keys(basicDetails).length > 0) {
            exStaff.update({
                ...basicDetails,
            });

            await StaffRepo.save(exStaff);
        }

        return exStaff;
    } catch (error) {
        throw error;
    }
};

export const updateStaffPassword = async (
    newPassword: any,
    exStaff: Staff,
    StaffRepo: Repository<Staff>
) => {
    try {
        const newHashPass = await argon2.hash(newPassword);

        exStaff.staffAuth.update({
            password: newHashPass,
        });

        await StaffRepo.save(exStaff);
    } catch (error) {
        log.error("some rel error", error);
        throw error;
    }
};

export const updateStaffReservedBranches = async (
    newBranches: any[],
    exStaff: Staff,
    StaffRepo: Repository<Staff>,
    BranchRepo: Repository<Branch>
) => {
    try {
        let reservedBranchesRes;

        if (newBranches && Array.isArray(newBranches)) {
            reservedBranchesRes = await BranchRepo.findBy({
                branchId: In(newBranches),
            });
            if (reservedBranchesRes.length !== newBranches.length) {
                throw new APIError(NOT_FOUND, BRANCH_NOT_FOUND);
            }
        }

        exStaff.update({
            reservedBranches: reservedBranchesRes,
        });

        await StaffRepo.save(exStaff);
    } catch (error) {
        throw error;
    }
};

export const updateStaffDaysAvailable = async (
    staffId: string,
    daysAvailableData: Partial<{ days: Record<string, string> }>,
    DaysAvailableRepo: Repository<DaysAvailable>
) => {
    try {
        const existingDaysAvailable = await DaysAvailableRepo.findOne({
            where: {staffId},
        });

        if (!existingDaysAvailable) {
            throw new APIError(
                NOT_FOUND,
                `Days available record not found for staff ID: ${staffId}`
            );
        }

        if (daysAvailableData.days) {
            existingDaysAvailable.days = {
                ...existingDaysAvailable.days,
                ...daysAvailableData.days,
            };
        }

        await DaysAvailableRepo.save(existingDaysAvailable);
        return existingDaysAvailable;
    } catch (error) {
        throw error;
    }
};

export const updateStaffDefaultShiftTiming = async (
    staffId: string,
    defaultShiftTimingData: Partial<{ weeklyTimings: any }>,
    DefaultShiftTimingRepo: Repository<DefaultShiftTiming>
) => {
    try {
        const existingShiftTiming = await DefaultShiftTimingRepo.findOne({
            where: {staffId},
        });

        if (!existingShiftTiming) {
            throw new APIError(
                NOT_FOUND,
                `Default shift timing record not found for staff ID: ${staffId}`
            );
        }

        if (defaultShiftTimingData.weeklyTimings) {
            existingShiftTiming.weeklyTimings = deepMergeWeeklyTimings(
                existingShiftTiming.weeklyTimings,
                defaultShiftTimingData.weeklyTimings
            );
        }

        await DefaultShiftTimingRepo.save(existingShiftTiming);
        return existingShiftTiming;
    } catch (error) {
        throw error;
    }
};

const deepMergeWeeklyTimings = (existing: any, incoming: any) => {
    const merged = {...existing};

    Object.keys(incoming).forEach((day) => {
        if (incoming[day] && typeof incoming[day] === "object") {
            merged[day] = {
                ...merged[day],
                ...incoming[day],
            };

            Object.keys(incoming[day]).forEach((shift) => {
                if (incoming[day][shift] && typeof incoming[day][shift] === "object") {
                    merged[day][shift] = {
                        ...merged[day][shift],
                        ...incoming[day][shift],
                    };
                }
            });
        }
    });

    return merged;
};

export const updateStaffCertifications = async (
    staff: Staff,
    certificationsData: any[],
    StaffCertificationRepo: Repository<StaffCertification>
) => {
    try {
        const existingCertifications = await StaffCertificationRepo.find({
            where: {staff: {staffId: staff.staffId}},
        });

        const incomingCertIds = certificationsData
            .filter((cert) => cert.staffCertificationId)
            .map((cert) => cert.staffCertificationId);

        const certificationsToDelete = existingCertifications.filter(
            (existing) => !incomingCertIds.includes(existing.staffCertificationId)
        );

        if (certificationsToDelete.length > 0) {
            await StaffCertificationRepo.remove(certificationsToDelete);
        }

        const certificationsToCreate = [];
        const certificationsToUpdate = [];

        for (const certData of certificationsData) {
            if (certData.staffCertificationId) {
                const existingCert = existingCertifications.find(
                    (existing) =>
                        existing.staffCertificationId === certData.staffCertificationId
                );

                if (existingCert) {
                    const hasUpdateFields = Object.keys(certData).some(
                        (key) =>
                            key !== "staffCertificationId" && certData[key] !== undefined
                    );

                    if (hasUpdateFields) {
                        if (certData.certId !== undefined) {
                            existingCert.certId = certData.certId;
                        }
                        if (certData.certName !== undefined) {
                            existingCert.certName = certData.certName;
                        }
                        if (certData.attachmentUrl !== undefined) {
                            existingCert.attachmentUrl = certData.attachmentUrl;
                        }
                        if (certData.expiryDate !== undefined) {
                            existingCert.expiryDate = certData.expiryDate;
                        }
                        certificationsToUpdate.push(existingCert);
                    }
                } else {
                    if (certData.certName && certData.attachmentUrl) {
                        const newCert = StaffCertificationRepo.create({
                            ...certData,
                            staff,
                        } as StaffCertification);
                        certificationsToCreate.push(newCert);
                    } else {
                        throw new APIError(BAD_REQUEST, STAFF_CERT_CREATION_ISSUE);
                    }
                }
            } else {
                if (certData.certName && certData.attachmentUrl) {
                    const newCert = StaffCertificationRepo.create({
                        ...certData,
                        staff,
                    } as StaffCertification);
                    certificationsToCreate.push(newCert);
                } else {
                    throw new APIError(BAD_REQUEST, STAFF_CERT_CREATION_ISSUE);
                }
            }
        }

        if (certificationsToUpdate.length > 0) {
            await StaffCertificationRepo.save(certificationsToUpdate);
        }

        if (certificationsToCreate.length > 0) {
            await StaffCertificationRepo.save(certificationsToCreate);
        }

        return {
            created: certificationsToCreate,
            updated: certificationsToUpdate,
            deleted: certificationsToDelete,
        };
    } catch (error) {
        throw error;
    }
};

export const checkStaffSession = async (
    ip: string,
    exStaff: Staff,
    StaffSession: Repository<StaffSession>,
    userAgent: string,
    qRunner: QueryRunner
) => {
    try {
        const allSessions = exStaff.staffSessions;
        const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

        let exSession: StaffSession | null = null;

        for (const session of allSessions) {
            const decryptedIp = await AESHelper.decrypt(
                session.ipAddress,
                secret_key
            );
            if (decryptedIp === ip) {
                exSession = session;
                break;
            }
        }

        if (!exSession) {
            const newSession = StaffSession.create({
                staff: exStaff,
                ipAddress: await AESHelper.encrypt(ip, secret_key),
                userAgent: userAgent,
                lastLogin: new Date(),
            });

            await StaffSession.save(newSession);
        } else {
            if (exSession.isRevoked) {
                await LoginRateLimitService.checkAndLogAttempt(
                    qRunner,
                    ip,
                    exStaff.emailAddress,
                    "STAFF",
                    userAgent,
                    false,
                    exStaff.staffId,
                    IP_REVOKED
                );
                throw new APIError(UNAUTHORIZED, IP_REVOKED);
            }

            if (exSession.userAgent && exSession.userAgent !== userAgent) {
                exSession.userAgent = userAgent;
            }

            exSession.lastLogin = new Date();
            await StaffSession.save(exSession);
        }
    } catch (error) {
        log.error("Error checking staff session", error);
        throw new APIError(BAD_REQUEST, "Error checking staff session");
    }
};
