import { DataSource } from "typeorm";
import * as path from "path";
import * as fs from "fs";
import { loadEnv } from "../config/envConfig";
import {
  getAllSuperAdminModels,
  getAllTenantModels,
} from "../config/entity-config";
import { writeFile } from "fs/promises";
import { join } from "path";

loadEnv();

const baseMigrationConfig = {
  host: process.env.DB_HOSTNAME,
  port: parseInt(process.env.DB_PORT!) || 5434,
  username: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  type: "postgres" as const,
  synchronize: process.env.MIGRATION_TOGGLE === "false" ? true : false,
  logging: process.env.LOGGING_TOGGLE === "true",
};

export function getSuperAdminMigrationDS() {
  return new DataSource({
    ...baseMigrationConfig,
    database: process.env.SUPERADMIN_DB,
    entities: getAllSuperAdminModels(),
    migrations: [path.join(__dirname, "superadmin", "*.{ts,js}")],
    migrationsTableName: "migrations_superadmin",
  });
}

export function getTenantMigrationDS(tenantName: string) {
  return new DataSource({
    ...baseMigrationConfig,
    database: tenantName,
    entities: getAllTenantModels(),
    migrations: [path.join(__dirname, "tenant", "*.{ts,js}")],
    migrationsTableName: "migrations_tenant",
  });
}

export function createMigrationFile(
  name: string,
  type: "superadmin" | "tenant"
) {
  const timestamp = new Date().getTime();
  const migrationName = `${name}${timestamp}`;
  const migrationDir = path.join(__dirname, type);

  if (!fs.existsSync(migrationDir)) {
    fs.mkdirSync(migrationDir, { recursive: true });
  }

  const migrationContent = `
import { MigrationInterface, QueryRunner } from "typeorm";

export class ${migrationName.replace(/-/g, "_")} implements MigrationInterface {
    name = '${migrationName}'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Your migration logic here
        // Example: await queryRunner.query(\`ALTER TABLE "user" ADD "newColumn" character varying\`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert migration logic here
        // Example: await queryRunner.query(\`ALTER TABLE "user" DROP COLUMN "newColumn"\`);
    }
}`;

  const migrationPath = path.join(migrationDir, `${migrationName}.ts`);
  fs.writeFileSync(migrationPath, migrationContent);

  console.log(`Migration created at: ${migrationPath}`);
  return migrationPath;
}

export async function runSuperAdminMigrations() {
  const dataSource = getSuperAdminMigrationDS();
  try {
    await dataSource.initialize();
    console.log("Running SuperAdmin migrations...");
    await dataSource.runMigrations();
    console.log("SuperAdmin migrations completed.");
    await dataSource.destroy();
  } catch (error) {
    console.error("Error running SuperAdmin migrations:", error);
    if (dataSource.isInitialized) {
      await dataSource.destroy();
    }
    throw error;
  }
}

export async function runTenantMigrations(tenantName: string) {
  const dataSource = getTenantMigrationDS(tenantName);
  try {
    await dataSource.initialize();
    console.log(`Running migrations for tenant: ${tenantName}...`);
    await dataSource.runMigrations();
    console.log(`Migrations for tenant ${tenantName} completed.`);
    await dataSource.destroy();
  } catch (error) {
    console.error(`Error running migrations for tenant ${tenantName}:`, error);
    if (dataSource.isInitialized) {
      await dataSource.destroy();
    }
    throw error;
  }
}

export async function revertLastMigration(
  type: "superadmin" | "tenant",
  tenantName?: string
) {
  if (type === "tenant" && !tenantName) {
    throw new Error("Tenant name is required when reverting tenant migrations");
  }

  const dataSource =
    type === "superadmin"
      ? getSuperAdminMigrationDS()
      : getTenantMigrationDS(tenantName!);

  try {
    await dataSource.initialize();
    console.log(
      `Reverting last migration for ${type}${
        tenantName ? ` (${tenantName})` : ""
      }...`
    );
    await dataSource.undoLastMigration();
    console.log("Migration reverted successfully.");
    await dataSource.destroy();
  } catch (error) {
    console.error(`Error reverting migration for ${type}:`, error);
    if (dataSource.isInitialized) {
      await dataSource.destroy();
    }
    throw error;
  }
}

/**
 * Generates a migration by comparing entity models with the database schema
 */

export async function generateMigration(name: string, type: 'superadmin' | 'tenant', tenantName?: string) {
  // Validate inputs
  if (type === 'tenant' && !tenantName) {
    throw new Error('Tenant name is required when generating tenant migrations');
  }
  if (type === 'tenant' && !/^[a-zA-Z0-9_]+$/.test(tenantName!)) {
    throw new Error('Invalid tenant name');
  }
  if (!/^[a-zA-Z0-9_]+$/.test(name)) {
    throw new Error('Invalid migration name');
  }

  const dataSource: DataSource = type === 'superadmin'
    ? getSuperAdminMigrationDS()
    : getTenantMigrationDS(tenantName!);

  try {
    await dataSource.initialize();
    await dataSource.query('SELECT 1');
    console.log(`Generating migration for ${type}${tenantName ? ` (${tenantName})` : ''}...`);

    const schemaBuilder = dataSource.driver.createSchemaBuilder();
    const sqlInMemory = await schemaBuilder.log();
    if (!sqlInMemory.upQueries.length) {
      console.log('No schema changes detected.');
      await dataSource.destroy();
      return;
    }

    const timestamp = Date.now();
    const migrationFileName = `${name}${timestamp}.ts`;
    const migrationDir = join(__dirname, '..', 'migrations', type);
    const migrationFilePath = join(migrationDir, migrationFileName);

    fs.mkdirSync(migrationDir, { recursive: true });

    const migrationFileContent = generateMigrationFileContent(`${name}${timestamp}`, timestamp, sqlInMemory.upQueries, sqlInMemory.downQueries);
    await writeFile(migrationFilePath, migrationFileContent);
    console.log(`Migration file created: ${migrationFilePath}`);

    await dataSource.destroy();
  } catch (error) {
    console.error(`Error generating migration for ${type}${tenantName ? ` (${tenantName})` : ''}:`, error);
    if (dataSource.isInitialized) {
      await dataSource.destroy();
    }
    throw error;
  }
}

function generateMigrationFileContent(name: string, timestamp: number, upQueries: any[], downQueries: any[]): string {
  const migrationName = `${name}${timestamp}`;
  const upSql = upQueries
    .map((q) => `await queryRunner.query(\`${q.query}\`);`)
    .join('\n        ') || '// Add your migration logic here';
  const downSql = downQueries
    .map((q) => `await queryRunner.query(\`${q.query}\`);`)
    .join('\n        ') || '// Add revert migration logic here';

  return `
import { MigrationInterface, QueryRunner } from 'typeorm';

export class ${migrationName} implements MigrationInterface {
  name = '${migrationName}'

  public async up(queryRunner: QueryRunner): Promise<void> {
    ${upSql}
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    ${downSql}
  }
}
`;
}
