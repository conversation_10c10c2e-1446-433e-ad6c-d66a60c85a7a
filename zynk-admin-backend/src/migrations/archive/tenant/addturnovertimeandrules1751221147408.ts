
import { MigrationInterface, QueryRunner } from 'typeorm';

export class addturnovertimeandrules17512211474081751221147408 implements MigrationInterface {
  name = 'addturnovertimeandrules17512211474081751221147408'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."turnover_rules_duration_enum" AS ENUM('0.5', '1', '1.5', '2', '2.5', '3', '3.5', '4', '4.5', '5', '5.5', '6', '6.5', '7', '7.5', '8', '8.5')`);
        await queryRunner.query(`CREATE TABLE "turnover_rules" ("turnoverRuleId" uuid NOT NULL DEFAULT uuid_generate_v4(), "minGuests" integer NOT NULL DEFAULT '0', "maxGuests" integer NOT NULL, "duration" "public"."turnover_rules_duration_enum" NOT NULL DEFAULT '1', "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "turnoverTimeTurnoverTimeId" uuid, CONSTRAINT "PK_f93e6e38d80e4d787f00f194ce9" PRIMARY KEY ("turnoverRuleId"))`);
        await queryRunner.query(`CREATE TYPE "public"."turnover_times_defaultduration_enum" AS ENUM('0.5', '1', '1.5', '2', '2.5', '3', '3.5', '4', '4.5', '5', '5.5', '6', '6.5', '7', '7.5', '8', '8.5')`);
        await queryRunner.query(`CREATE TABLE "turnover_times" ("turnoverTimeId" uuid NOT NULL, "defaultDuration" "public"."turnover_times_defaultduration_enum" NOT NULL DEFAULT '1', "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_b8d7d92de82541010f56587d14b" PRIMARY KEY ("turnoverTimeId"))`);
        await queryRunner.query(`ALTER TABLE "order_details" ADD "orderCode" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "order_details" ADD CONSTRAINT "UQ_dd9d06e7316dfa2fe7a9feb2b1b" UNIQUE ("orderCode")`);
        await queryRunner.query(`ALTER TABLE "order_details" ADD "miscItems" jsonb DEFAULT '[]'`);
        await queryRunner.query(`COMMENT ON COLUMN "order_details"."miscItems" IS 'Array of miscellaneous items with id, name, price, and addedAt'`);
        await queryRunner.query(`ALTER TABLE "order_details" ADD "alerts" jsonb DEFAULT '[]'`);
        await queryRunner.query(`COMMENT ON COLUMN "order_details"."alerts" IS 'Array of alerts with id, note, and addedAt'`);
        await queryRunner.query(`ALTER TABLE "cart" ADD "miscItems" jsonb DEFAULT '[]'`);
        await queryRunner.query(`COMMENT ON COLUMN "cart"."miscItems" IS 'Array of miscellaneous items with id, name, price, and addedAt'`);
        await queryRunner.query(`ALTER TABLE "cart" ADD "alerts" jsonb DEFAULT '[]'`);
        await queryRunner.query(`COMMENT ON COLUMN "cart"."alerts" IS 'Array of alerts with id, note, and addedAt'`);
        await queryRunner.query(`ALTER TABLE "dish_addons" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_extras" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_ingredients" ALTER COLUMN "amount" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_sizes" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_spiciness" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dishes" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_upcoming_changes" ALTER COLUMN "newPrice" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "turnover_rules" ADD CONSTRAINT "FK_6b196e6c4ee961154c177b7aa75" FOREIGN KEY ("turnoverTimeTurnoverTimeId") REFERENCES "turnover_times"("turnoverTimeId") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "turnover_times" ADD CONSTRAINT "FK_b8d7d92de82541010f56587d14b" FOREIGN KEY ("turnoverTimeId") REFERENCES "branches"("branchId") ON DELETE CASCADE ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TYPE "public"."turnover_rules_duration_enum"`);
        await queryRunner.query(`DROP TABLE "turnover_rules"`);
        await queryRunner.query(`DROP TYPE "public"."turnover_times_defaultduration_enum"`);
        await queryRunner.query(`DROP TABLE "turnover_times"`);
        await queryRunner.query(`ALTER TABLE "order_details" DROP COLUMN "orderCode"`);
        await queryRunner.query(`ALTER TABLE "order_details" DROP CONSTRAINT "UQ_dd9d06e7316dfa2fe7a9feb2b1b"`);
        await queryRunner.query(`ALTER TABLE "order_details" DROP COLUMN "miscItems"`);
        await queryRunner.query(`COMMENT ON COLUMN "order_details"."miscItems" IS 'Array of miscellaneous items with id, name, price, and addedAt'`);
        await queryRunner.query(`ALTER TABLE "order_details" DROP COLUMN "alerts"`);
        await queryRunner.query(`COMMENT ON COLUMN "order_details"."alerts" IS 'Array of alerts with id, note, and addedAt'`);
        await queryRunner.query(`ALTER TABLE "cart" DROP COLUMN "miscItems"`);
        await queryRunner.query(`COMMENT ON COLUMN "cart"."miscItems" IS 'Array of miscellaneous items with id, name, price, and addedAt'`);
        await queryRunner.query(`ALTER TABLE "cart" DROP COLUMN "alerts"`);
        await queryRunner.query(`COMMENT ON COLUMN "cart"."alerts" IS 'Array of alerts with id, note, and addedAt'`);
        await queryRunner.query(`ALTER TABLE "dish_addons" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_extras" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_ingredients" ALTER COLUMN "amount" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_sizes" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_spiciness" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dishes" ALTER COLUMN "price" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "dish_upcoming_changes" ALTER COLUMN "newPrice" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "turnover_rules" DROP CONSTRAINT "FK_6b196e6c4ee961154c177b7aa75"`);
        await queryRunner.query(`ALTER TABLE "turnover_times" DROP CONSTRAINT "FK_b8d7d92de82541010f56587d14b"`);
  }
}
