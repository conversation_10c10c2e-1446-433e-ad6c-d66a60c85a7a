import {NextFunction, Request, Response} from "express";
import {RouteType} from "../types/pBAC";
import {routeIdentifier} from "./routeIdentifier.middleware";
import {middleWareLogger} from "../utils/middleWareErrorLoggerUtil";
import {verifyToken} from "./verifyToken.middleware";
import {userDetector} from "./userDetector.middleware";
import {accessControl} from "./accessControl.middleware";
import {MiddleWareException} from "../exceptions/middleware/MiddlewareException";

export const rootAuthenticator = (verbose: boolean = false) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        const rootRunner = req.pr_rootQueryRunner;
        const tenantRunner = req.pr_queryRunner;

        await rootRunner.connect();
        await rootRunner.startTransaction();

        if (req.tenantName) {
            await tenantRunner.connect();
            await tenantRunner.startTransaction();
        }
        try {
            const responseSent = await routeIdentifier(req, res, rootRunner);
            if (responseSent) {
                await rootRunner.commitTransaction();
                if (req.tenantName) {
                    await tenantRunner.commitTransaction();
                }
                return;
            }
            if (req.authMap.routeType === RouteType.PUBLIC) {
                middleWareLogger(req, "Skipping Validation For Public Path !!!!..");
                return next();
            }

            /**
             * add a middleWare which check for tenantBlockList here
             */

            await verifyToken(req, res, rootRunner, tenantRunner);
            userDetector(req);
            accessControl(req);
            // await routeAuthenticator(
            //     req, res,
            //     [UserType.STAFF, UserType.CUSTOMER].includes(req.USER_TYPE) ? tenantRunner : rootRunner
            // );

            await rootRunner.commitTransaction();
            if (req.tenantName) {
                await tenantRunner.commitTransaction();
            }

            return next();
        } catch (error) {
            await rootRunner.rollbackTransaction();

            if (req.tenantName) {
                await tenantRunner.rollbackTransaction();
            }
            if (error instanceof MiddleWareException) next(error);
            middleWareLogger(req, "Unknown Error occurred in MiddleWare", true, error);
            return next(error);
        } finally {
            await rootRunner.release();
            if (req.tenantName) {
                await tenantRunner.release();
            }
        }
    };
};
