import {NextFunction, Request, Response} from "express";
import {NOT_FOUND} from "../constants/STATUS_CODES";
import {AuthenticationMapper} from "../models/authMap/AuthenticationMapper.model";
import log from "../helpers/system/logger.helper";
import {TemplateManager, TemplateType} from "../utils/templateManager";
import {AuthMap} from "../types/authMap/AuthMap";
import {Like, QueryRunner} from "typeorm";
import {getRouteSpecificity, matchRoutePattern} from "../utils/routeUtil";
import {AuthMapProcessingExp} from "../exceptions/middleware/route/impl/AuthMapProcessingExp";
import {AuthMapBindingException} from "../exceptions/middleware/route/impl/AuthMapBindingException";
import {MiddleWareException} from "../exceptions/middleware/MiddlewareException";

export const routeIdentifier = async (
    req: Request,
    res: Response,
    queryRunner: QueryRunner,
) => {
    try {
        // FIXES applied for LAMBDA
        // Use normalized path without trailing slash for DB lookup prefix generation
        const normalizedPath = req.path.replace(/\/+$/, "");
        const pathSegments = normalizedPath.split('/').filter(Boolean);

        // Build LIKE patterns with trailing wildcard `%` for SQL LIKE queries
        const possiblePrefixes = pathSegments.reduce<string[]>((acc, _, index) => {
            const prefix = `/${pathSegments.slice(0, index + 1).join('/')}/%`;
            acc.push(prefix);
            return acc;
        }, ['/%']);

        const likeConditions = possiblePrefixes.map((prefix) => ({
            isActive: true,
            routePath: Like(prefix),
            method: req.method.toUpperCase(),
        }));

        // Find candidate auth maps matching prefixes and method
        const authMaps = await queryRunner.manager.find(AuthenticationMapper, {
            where: likeConditions,
        });

        // Sort by specificity (assumes getRouteSpecificity is correct)
        const sortedAuthMaps = authMaps.sort(
            (a, b) => getRouteSpecificity(b.routePath) - getRouteSpecificity(a.routePath)
        );
        // Find the first authMap that matches the request path
        const authMap = sortedAuthMaps.find((map) =>
            matchRoutePattern(req.path, map.routePath)
        );
        if (!authMap) {
            const templateManager = new TemplateManager({
                appName: process.env.APPLICATION_NAME ?? "EasyDine",
                logoUrl: "",
                primaryColor: "#d9534f",
            });
            const template = templateManager.updateTemplateData(TemplateType.RouteNotFound, {
                method: req.method,
                path: req.path,
            });
            res.status(NOT_FOUND).render(template.path, template.data);
            return true;
        }
        req.authMap = new AuthMap(
            authMap.routePath,
            authMap.method,
            authMap.description,
            authMap.accessType,
            authMap.permissionActions,
            authMap.isOrOperation,
            authMap.routeType,
            authMap.isActive
        );
        if (!req.authMap) throw new AuthMapBindingException();
        return false;
    } catch (error) {
        log.error("Route Authenticator Error", error);
        if (error instanceof MiddleWareException)
            throw error;
        throw new AuthMapProcessingExp();
    }
};