import {Query<PERSON><PERSON><PERSON>, Repository} from "typeorm";
import {Request, Response} from "express";
import {getUserModelAssociation, ModelType, UserType} from "../types/pBAC";
import {getRepositories} from "../helpers/system/RepositoryHelper.helper";
import {InvalidUserTypeMW} from "../exceptions/middleware/route/impl/InvalidUserTypeMW";
import {APIError} from "../utils/errorHandler";
import {INTERNAL_SERVER_ERROR} from "../constants/STATUS_CODES";
import {SuperAdmin} from "../models/admin/superadmin.model";
import {Tenant} from "../models/admin/Tenant/tenant.model";
import {Staff} from "../models/staff/staff.model";
import {NoUserFoundMW} from "../exceptions/middleware/route/impl/NoUserFoundMW";
import {Customer} from "../models/customer/customer.model";
import {FormatType} from "../types/pBAC/formatterTypes";
import {UserPermissionAuthenticator} from "./UserPermissionAuthenticator.middleware";
import {PermissionActionProcessingEx} from "../exceptions/middleware/route/impl/PermissionActionProcessingEx";
import {UnAuthorizedUserException} from "../exceptions/middleware/route/impl/UnAuthorizedUserException";
import {ModelException} from "../exceptions/model/ModelException";
import {RouteMWException} from "../exceptions/middleware/route/RouteMWException";
import {middleWareLogger} from "../utils/middleWareErrorLoggerUtil";

const userTypeConfig = {
    [UserType.SUPER_ADMIN]: {
        key: "SuperAdmin",
        idKey: "superAdminId",
        relation: getUserModelAssociation(ModelType.ADMIN)
    },
    [UserType.TENANT_ADMIN]: {
        key: "Tenant",
        idKey: "tenantId",
        relation: getUserModelAssociation(ModelType.TENANT)
    },
    [UserType.STAFF]: {
        key: "Staff",
        idKey: "staffId",
        relation: getUserModelAssociation(ModelType.STAFF)
    },
    [UserType.CUSTOMER]: {
        key: "Customer",
        idKey: "customerId",
        relation: getUserModelAssociation(ModelType.CUSTOMER)
    },
};

type UserEntity =
    InstanceType<typeof SuperAdmin>
    | InstanceType<typeof Tenant>
    | InstanceType<typeof Staff>
    | InstanceType<typeof Customer>

export const routeAuthenticator = async (req: Request, res: Response, qRunner: QueryRunner) => {
    try {
        const config = userTypeConfig[req.USER_TYPE];
        if (!config) throw new InvalidUserTypeMW();

        const repositories = getRepositories(qRunner) as {
            [key: string]: Repository<UserEntity>;
        };

        const user = await repositories[config.key].findOne({
            where: {
                [config.idKey]: req.userId,
            },
            relations: config.relation
        })
        if (!user) throw new NoUserFoundMW();
        const {success, formattedPermissions, ...rest} = await user.getPermissionAction(FormatType.STRING_STRING);
        if (!success) throw new PermissionActionProcessingEx(rest);
        if (!UserPermissionAuthenticator(formattedPermissions, req.authMap.permissionActions, req.authMap.isOrOperation)) throw new UnAuthorizedUserException();
    } catch (error) {
        if (error instanceof ModelException) {
            middleWareLogger(req, "Error happened during trying to getPermissionAction!!!....", true, error);
            throw error;
        }
        if (error instanceof RouteMWException) {
            middleWareLogger(req, "Error happened during trying to getPermissionAction!!!....", true, error);
            throw error;
        }
        middleWareLogger(req, "An error occurred during route authentication!!!!!....", true, error);
        throw new APIError(INTERNAL_SERVER_ERROR, "An error occurred during route authentication")
    }
};