import { NextFunction, Request, Response } from "express";
import { TENANT_CANT_ACCESS } from "../constants/admin/err";
import { errorHandler } from "../utils/errorHandler";
import { FORBIDDEN } from "../constants/STATUS_CODES";

export const superChecker = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (req.tenantName || req.isTenant) {
    return next(errorHandler(FORBIDDEN, TENANT_CANT_ACCESS));
  }
  next();
};
