import {NextFunction, Request, Response} from "express";
import {ERR_INITIALIZING_CLIENT_INFO} from "../constants/middleware/err";
import log from "../helpers/system/logger.helper";

export const requestInfoInitializer = (verbose: boolean = false) => {
    return (req: Request, res: Response, next: NextFunction) => {
        try {
            let ip = req.ip;
            const forwardedIps = req.headers['x-forwarded-for'];

            if (forwardedIps) {
                ip = (typeof forwardedIps === 'string' ? forwardedIps : forwardedIps[0]).split(',')[0];
            }

            req.clientIP = ip ? ip : null;

            req.userAgent = req.headers['user-agent'] || '';

            if (verbose) {
                log.info('Client IP:', req.clientIP);
                log.info('User-Agent:', req.userAgent);
            }

            next();
        } catch (error) {
            log.error(ERR_INITIALIZING_CLIENT_INFO);
            next(error);
        }
    };
};