import {NextFunction, Request, Response} from "express";
import {errorHandler} from "../utils/errorHandler";
import {BAD_REQUEST, INTERNAL_SERVER_ERROR, UNAUTHORIZED,} from "../constants/STATUS_CODES";
import {AUTHENTICATION_FAILED, INVALID_TOKEN_FORMAT, TOKEN_NOT_FOUND,} from "../constants/middleware/err";
import {RouteType} from "../types/pBAC";

export const tokenDirector = (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    try {
        if (req.authMap.routeType === RouteType.PUBLIC) return next();

        const raw = req.signedCookies["access-token"] || req.headers.authorization;

        if (!raw) {
            return next(errorHandler(UNAUTHORIZED, TOKEN_NOT_FOUND));
        }

        if (!raw.startsWith("Bearer "))
            return next(errorHandler(BAD_REQUEST, INVALID_TOKEN_FORMAT));

        const token = raw.split(" ")[1];

        if (!token) {
            return next(errorHandler(UNAUTHORIZED, INVALID_TOKEN_FORMAT));
        }

        return next();
    } catch (error) {
        return next(errorHandler(INTERNAL_SERVER_ERROR, AUTHENTICATION_FAILED));
    }
};
