import { parse } from "tldts";
import { NextFunction, Request, Response } from "express";
import { BAD_REQUEST } from "../constants/STATUS_CODES";
import { errorHandler } from "../utils/errorHandler";
import {
  ERR_RESOLVING_HOSTNAME,
  HOSTNAME_NOT_PROVIDED,
  TENANT_NOT_RESOLVED,
} from "../constants/middleware/err";
import log from "../helpers/system/logger.helper";

export const resolveTenant = (verbose: boolean = false) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      let hostname;
      if (req.path === "/reference" || req.path.startsWith("/reference/")) {
        hostname = "easydine.com";
      } else {
        if (process.env.NODE_ENV === "development") {
          hostname = req.headers["x-host"] as string;
        } else {
          const isMobile = req.headers["x-device"] === "mobile";
          if (isMobile && req.headers["x-host"]) {
            hostname = req.headers["x-host"] as string;
          } else {
            hostname = req.get("origin") as string;
          }
        }
      }

      if (!hostname) {
        return next(errorHandler(BAD_REQUEST, HOSTNAME_NOT_PROVIDED));
      }

      const parsed = parse(hostname);
      const subdomain = parsed.subdomain;

      if (subdomain) {
        const parts = subdomain.split(".");
        const tenant = parts[parts.length - 1];

        if (!tenant) {
          return next(errorHandler(BAD_REQUEST, TENANT_NOT_RESOLVED));
        }

        req.tenantName = tenant;
      } else {
        req.tenantName = null;
      }

      next();
    } catch (error) {
      log.error(ERR_RESOLVING_HOSTNAME, error);
      next(error);
    }
  };
};
