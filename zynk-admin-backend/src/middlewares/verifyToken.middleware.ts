import { Request, Response } from "express";
import { errorHandler } from "../utils/errorHandler";
import { PAYMENT_REQUIRED } from "../constants/STATUS_CODES";
import {
  ERR_VALIDATING_TOKEN,
  SUBSCRIPTION_ENDED,
  TOKEN_EXPIRED,
} from "../constants/middleware/err";
import {
  isTokenVerified,
  isTokenVerifiedWithErrors,
} from "../helpers/system/token.helper";
import JWTHelper from "../helpers/system/JWTHelper.helper";
import { JWTPayload } from "../../types/jwt";
import { getRepositories } from "../helpers/system/RepositoryHelper.helper";
import { QueryRunner, Repository } from "typeorm";
import { AESHelper } from "../helpers/system/AESHelper";
import { genNewRefreshToken } from "../helpers/system/superadmin.helper";
import { clearCookie, setCookie } from "../helpers/system/CookieHelper.helper";
import { SuperAdmin } from "../models/admin/superadmin.model";
import { Tenant } from "../models/admin/Tenant/tenant.model";
import { TenantAuth } from "../models/admin/Tenant/tenantAuth.model";
import { Staff } from "../models/staff/staff.model";
import { StaffAuth } from "../models/staff/staffAuth.model";
import { BearerNotFoundException } from "../exceptions/middleware/token/impl/BearerNotFoundException";
import { InvalidTokenException } from "../exceptions/middleware/token/impl/InvalidTokenException";
import { middleWareLogger } from "../utils/middleWareErrorLoggerUtil";
import { AdminNotFoundMWException } from "../exceptions/middleware/token/impl/AdminNotFoundMWException";
import { TenantSubDomainNotExistEx } from "../exceptions/middleware/token/impl/TenantSubDomainNotExistEx";
import { StaffNotFoundMWEX } from "../exceptions/middleware/token/impl/StaffNotFoundMWEX";
import { TokenMWException } from "../exceptions/middleware/token/TokenMWException";
import { Customer } from "../models/customer/customer.model";
import { CustomerAuth } from "../models/customer/customerAuth.model";
import { CustomerNotFoundMWEX } from "../exceptions/middleware/token/impl/CustomerNotFoundMWEX";
import { isKnownIpForUser } from "../helpers/system/verifyToken.helper";

export const verifyToken = async (
  req: Request,
  res: Response,
  rootRunner: QueryRunner,
  tenantRunner: QueryRunner
) => {
  try {
    const raw = req.signedCookies["access-token"] || req.headers.authorization;

    if (!raw || !raw.startsWith("Bearer ")) {
      middleWareLogger(
        req,
        "No Bearer Token Found In Request Headers!!!!...",
        true
      );
      throw new BearerNotFoundException();
    }

    const token = raw.split(" ")[1];

    const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

    let decodedJwtData = await isTokenVerifiedWithErrors(token);

    let genNewToken = false;

    if (decodedJwtData.isErr) {
      if (decodedJwtData.err_message === TOKEN_EXPIRED) {
        genNewToken = true;

        decodedJwtData = JWTHelper.decodeToken(token) as JWTPayload;
        
        // This check can ensure that no one uses stolen cookies.
        // This can thwart cookie stealers from doing their job.
        const currentIp = req.clientIP;

        if (!currentIp) throw new InvalidTokenException();

        const isKnownIp = await isKnownIpForUser(rootRunner,
          tenantRunner,
          decodedJwtData.userId!,
          currentIp,
          decodedJwtData
        )

        if (!isKnownIp) {
          middleWareLogger(
            req,
            `Unknown IP detected for user ${req.userId}: ${currentIp}. Requiring re-authentication.`,
            true
          );

          clearCookie(res);

          throw new InvalidTokenException();
        }
      } else {
        middleWareLogger(req, "Invalid Token Detected!!!!!...", true);
        throw new InvalidTokenException();
      }
    }

    if (!decodedJwtData.isTenant) {
      const { SuperAdmin } = getRepositories(rootRunner) as {
        SuperAdmin: Repository<SuperAdmin>;
      };

      const superAdminId = decodedJwtData.userId;
      const exSuperAdmin = await SuperAdmin.findOne({
        where: { superAdminId },
      });

      if (!exSuperAdmin) {
        middleWareLogger(
          req,
          "SuperAdmin Not Found From The UserID Inside Token !!!!!!....",
          true
        );
        throw new AdminNotFoundMWException();
      }

      req.userId = exSuperAdmin.superAdminId;

      if (genNewToken) {
        const payload = {
          userId: exSuperAdmin.superAdminId,
          domainName: (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
          isTenant: false,
          inSuper: true,
        };
        const decrypted_RefToken = await AESHelper.decrypt(
          exSuperAdmin.refreshToken,
          secret_key
        );

        const tokenVerify = isTokenVerified(decrypted_RefToken, true);

        if (!tokenVerify) {
          exSuperAdmin.refreshToken = await genNewRefreshToken(
            payload,
            secret_key
          );
          await SuperAdmin.save(exSuperAdmin);
          clearCookie(res);
        } else setCookie(res, payload);
      }
    } else {
      if (decodedJwtData.inSuper) {
        req.isTenant = true;
        const tenantId = decodedJwtData.userId;

        const { Tenant, TenantAuth } = getRepositories(rootRunner) as {
          Tenant: Repository<Tenant>;
          TenantAuth: Repository<TenantAuth>;
        };

        if (!req.tenantName) {
          middleWareLogger(req, "No Host Name Provided", true);
          throw new TenantSubDomainNotExistEx();
        }

        const exTenant = await Tenant.findOne({
          where: {
            subDomain: req.tenantName,
            tenantId,
          },
          relations: ["tenantAuth"],
        });

        if (!exTenant) {
          middleWareLogger(
            req,
            "No Tenant Exist With This AccessToken!!!!!!!...",
            true
          );
          throw new TenantSubDomainNotExistEx();
        }
        req.userId = exTenant.tenantId;
        if (decodedJwtData.branchId) {
          req.branchId = decodedJwtData.branchId;
        }

        if (decodedJwtData.checkedInStaffId) {
          req.checkedInStaffId = decodedJwtData.checkedInStaffId;
        }

        /**
         * subscription validation
         */
        const today = new Date();
        if (exTenant.activeTill < today) {
          throw errorHandler(PAYMENT_REQUIRED, SUBSCRIPTION_ENDED);
        }

        if (genNewToken) {
          const payload = {
            userId: exTenant.tenantId,
            branchId: req.branchId,
            checkedInStaffId: req.checkedInStaffId
              ? req.checkedInStaffId
              : undefined,
            domainName:
              (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
            isTenant: true,
            inSuper: true,
          };

          const decrypted_RefToken = await AESHelper.decrypt(
            exTenant.tenantAuth.refreshToken,
            secret_key
          );

          const tokenVerif = isTokenVerified(decrypted_RefToken, true);

          if (!tokenVerif) {
            exTenant.tenantAuth.refreshToken = await genNewRefreshToken(
              payload,
              secret_key
            );

            await TenantAuth.save(exTenant.tenantAuth);

            clearCookie(res);
          } else {
            setCookie(res, payload);
          }
        }
      } else if (decodedJwtData.isCustomer === undefined) {
        req.isTenant = false;
        const staffId = decodedJwtData.userId;

        const { Tenant } = getRepositories(rootRunner) as {
          Tenant: Repository<Tenant>;
        };

        const { Staff, StaffAuth } = getRepositories(tenantRunner) as {
          Staff: Repository<Staff>;
          StaffAuth: Repository<StaffAuth>;
        };

        if (!req.tenantName) {
          middleWareLogger(req, "No Host Name Provided", true);
          throw new TenantSubDomainNotExistEx();
        }

        const exTenant = await Tenant.findOne({
          where: {
            subDomain: req.tenantName,
          },
        });

        if (!exTenant) {
          middleWareLogger(
            req,
            "No Tenant Exist With This AccessToken!!!!!!!...",
            true
          );
          throw new TenantSubDomainNotExistEx();
        }

        const exStaff = await Staff.findOne({
          where: {
            staffId,
          },
          relations: ["staffAuth"],
        });

        if (!exStaff) {
          middleWareLogger(
            req,
            "No Staff Found With This AccessToken!!!!!!!...",
            true
          );
          throw new StaffNotFoundMWEX();
        }

        req.userId = exStaff.staffId;
        if (decodedJwtData.branchId) {
          req.branchId = decodedJwtData.branchId;
        }

        if (decodedJwtData.checkedInStaffId) {
          req.checkedInStaffId = decodedJwtData.checkedInStaffId;
        }

        /**
         * subscription validation
         */

        const today = new Date();
        if (exTenant.activeTill < today) {
          throw errorHandler(PAYMENT_REQUIRED, SUBSCRIPTION_ENDED);
        }

        if (genNewToken) {
          const payload = {
            userId: exStaff.staffId,
            branchId: req.branchId,
            checkedInStaffId: req.checkedInStaffId
              ? req.checkedInStaffId
              : undefined,
            domainName:
              (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
            isTenant: true,
            inSuper: false,
          };

          const decrypted_RefToken = await AESHelper.decrypt(
            exStaff.staffAuth.refreshToken,
            secret_key
          );

          const tokenVerify = isTokenVerified(decrypted_RefToken, true);

          if (!tokenVerify) {
            exStaff.staffAuth.refreshToken = await genNewRefreshToken(
              payload,
              secret_key
            );

            await StaffAuth.save(exStaff.staffAuth);

            clearCookie(res);
          } else {
            setCookie(res, payload);
          }
        }
      } else {
        req.isTenant = false;
        req.isCustomer = true;

        const customerId = decodedJwtData.userId;

        const { Tenant } = getRepositories(rootRunner) as {
          Tenant: Repository<Tenant>;
        };

        const { Customer, CustomerAuth } = getRepositories(tenantRunner) as {
          Customer: Repository<Customer>;
          CustomerAuth: Repository<CustomerAuth>;
        };

        if (!req.tenantName) {
          middleWareLogger(req, "No Host Name Provided", true);
          throw new TenantSubDomainNotExistEx();
        }

        const exTenant = await Tenant.findOne({
          where: {
            subDomain: req.tenantName,
          },
        });

        if (!exTenant) {
          middleWareLogger(
            req,
            "No Tenant Exist With This AccessToken!!!!!!!...",
            true
          );
          throw new TenantSubDomainNotExistEx();
        }

        const exCustomer = await Customer.findOne({
          where: {
            customerId,
          },
          relations: ["customerAuth"],
        });

        if (!exCustomer) {
          middleWareLogger(
            req,
            "No Customer Found With This AccessToken!!!!!!!...",
            true
          );
          throw new CustomerNotFoundMWEX();
        }

        req.userId = exCustomer.customerId;

        if (decodedJwtData.branchId) {
          req.branchId = decodedJwtData.branchId;
        }

        /**
         * subscription validation
         */

        const today = new Date();
        if (exTenant.activeTill < today) {
          throw errorHandler(PAYMENT_REQUIRED, SUBSCRIPTION_ENDED);
        }

        if (genNewToken) {
          const payload = {
            userId: exCustomer.customerId,
            branchId: req.branchId,
            isCustomer: true,
            domainName:
              (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
            isTenant: true,
            inSuper: false,
          };

          const decrypted_RefToken = await AESHelper.decrypt(
            exCustomer.customerAuth.refreshToken,
            secret_key
          );

          const tokenVerify = isTokenVerified(decrypted_RefToken, true);

          if (!tokenVerify) {
            exCustomer.customerAuth.refreshToken = await genNewRefreshToken(
              payload,
              secret_key
            );

            await CustomerAuth.save(exCustomer.customerAuth);

            clearCookie(res);
          } else {
            setCookie(res, payload);
          }
        }
      }
    }
    return;
  } catch (error) {
    if (error instanceof TokenMWException) throw error;
    middleWareLogger(req, ERR_VALIDATING_TOKEN, true, error);
    throw error;
  }
};
