import {UserType} from "../types/pBAC";
import {Request} from "express";

export const userDetector = (req: Request): void => {
    if (req.tenantName === null) {
        req.USER_TYPE = UserType.SUPER_ADMIN;
        return;
    }
    if (req.isTenant) {
        req.USER_TYPE = UserType.TENANT_ADMIN;
        return;
    }
    if (req.isCustomer) {
        req.USER_TYPE = UserType.CUSTOMER;
        return;
    }
    req.USER_TYPE = UserType.STAFF;
    return;
}