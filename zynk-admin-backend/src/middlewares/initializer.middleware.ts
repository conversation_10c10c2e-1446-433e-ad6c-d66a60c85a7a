import {NextFunction, Request, Response} from "express";
import {getTenantDS, SuperAdminDS} from "../config/data-source";
import {ERR_INITIALIZING_QUERY_RUNNER} from "../constants/middleware/err";
import log from "../helpers/system/logger.helper";
import {requestContext} from "../config/async-context";

const dsCache = new Map();

export const queryInitializer = (verbose: boolean = false) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            if (!SuperAdminDS.isInitialized) {
                await SuperAdminDS.initialize();
                if (verbose) log.info("Initialized SuperAdminDS");
            }
            req.rootQueryRunner = SuperAdminDS.createQueryRunner();
            req.pr_rootQueryRunner = SuperAdminDS.createQueryRunner();

            if (req.tenantName) {
                let tenantDS = dsCache.get(req.tenantName);

                if (!tenantDS) {
                    tenantDS = await getTenantDS(req.tenantName);

                    if (!tenantDS.isInitialized) {
                        await tenantDS.initialize();
                        if (verbose)
                            log.info(`Initialized DS for tenant: ${req.tenantName}`);
                    }

                    dsCache.set(req.tenantName, tenantDS);
                }

                req.queryRunner = tenantDS.createQueryRunner();
                req.pr_queryRunner = tenantDS.createQueryRunner();

                const contextData = {
                    tenantName: req.tenantName,
                    queryRunner: req.queryRunner,
                    rootQueryRunner: req.rootQueryRunner,
                };

                requestContext.run(contextData, () => {
                    next();
                });
            } else {
                const contextData = {
                    tenantName: '',
                    queryRunner: req.rootQueryRunner, // root as fallback
                    rootQueryRunner: req.rootQueryRunner,
                };

                requestContext.run(contextData, () => {
                    next();
                });
            }
        } catch (error) {
            log.error(ERR_INITIALIZING_QUERY_RUNNER, error);
            next(error);
        }
    };
};

export const clearTenantDSCache = (tenantName?: string) => {
    if (tenantName) {
        dsCache.delete(tenantName);
    } else {
        dsCache.clear();
    }
};