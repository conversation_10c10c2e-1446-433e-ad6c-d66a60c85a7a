import { NextFunction, Request, Response } from "express";
import { AnyZodObject, Zod<PERSON>rror, ZodTypeAny } from "zod";
import { BAD_REQUEST, INTERNAL_SERVER_ERROR } from "../constants/STATUS_CODES";

export const validate = (
  schema: ZodTypeAny,
  source: "body" | "query" | "params" = "body"
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      Promise.resolve(schema.parseAsync(req[source]))
        .then((data) => {
          if (source === "query") {
            req.validatedQuery = data;
          } else {
            req[source] = data;
          }
          next();
        })
        .catch((error) => {
          if (error instanceof ZodError) {
            const errorMsgMap = error.errors.map((err) => err.message);

            res.status(400).json({
              statusCode: "error",
              message: errorMsgMap.join(" "),
              data: error.errors.map((err) => ({
                path: err.path.join("."),
                message: err.message,
              })),
            });
          } else {
            next(error);
          }
        });
    } catch (error) {
      next(error);
    }
  };
};
