import { NextFunction, Request, Response } from "express";
import { ONLY_TENANT_ACCESS } from "../constants/admin/err";
import { errorHandler } from "../utils/errorHandler";
import { FORBIDDEN } from "../constants/STATUS_CODES";

export const tenantChecker = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!(req.isTenant)) {
    return next(errorHandler(FORBIDDEN, ONLY_TENANT_ACCESS));
  }
  next();
};
