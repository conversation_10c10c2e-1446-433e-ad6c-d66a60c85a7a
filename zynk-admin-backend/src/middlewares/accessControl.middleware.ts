import {Request} from "express";
import {
    ONLY_CUSTOMER_ACCESS,
    ONLY_STAFF_ACCESS,
    ONLY_SUPER_ADMIN_ACCESS,
    ONLY_TENANT_ADMIN_ACCESS,
    ONLY_TENANTS_ACCESS
} from "../constants/admin/err";
import {RestrictedUserAccess} from "../exceptions/middleware/route/impl/RestrictedUserAccess";
import log from "../helpers/system/logger.helper";
import {AccessType, UserType} from "../types/pBAC";

export const accessControl = (req: Request) => {
    const {accessType} = req.authMap;
    const {USER_TYPE} = req;
    const accessRules: Record<AccessType, UserType[] | null> = {
        [AccessType.ADMIN_ONLY]: [UserType.SUPER_ADMIN],
        [AccessType.TENANT_ADMIN_ONLY]: [UserType.TENANT_ADMIN],
        [AccessType.STAFF_ONLY]: [UserType.STAFF],
        [AccessType.TENANT_ONLY]: [UserType.TENANT_ADMIN, UserType.STAFF, UserType.CUSTOMER],
        [AccessType.CUSTOMER_ONLY]: [UserType.CUSTOMER],
        [AccessType.ALL]: null,
    };

    if (accessType !== AccessType.ALL && !accessRules[accessType]?.includes(USER_TYPE)) {
        const errorMessages: Record<AccessType, string> = {
            [AccessType.ADMIN_ONLY]: ONLY_SUPER_ADMIN_ACCESS,
            [AccessType.TENANT_ADMIN_ONLY]: ONLY_TENANT_ADMIN_ACCESS,
            [AccessType.STAFF_ONLY]: ONLY_STAFF_ACCESS,
            [AccessType.TENANT_ONLY]: ONLY_TENANTS_ACCESS,
            [AccessType.CUSTOMER_ONLY]: ONLY_CUSTOMER_ACCESS,
            [AccessType.ALL]: 'Access denied',
        };
        log.error("Access denied for user type:", USER_TYPE, req.tenantName ?? process.env.SU_DOMAIN_NAME);
        throw new RestrictedUserAccess(errorMessages[accessType]);
    }
};