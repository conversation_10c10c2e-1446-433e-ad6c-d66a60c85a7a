import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { createSectionIconSchema, patchSectionIconSchema, sectionIconIdSchema } from "../../validation/reference/sectionicon.validation";
import {
  createSectionIcon,
  getAllSectionIcons,
  getSectionIconById,
  updateSectionIcon,
  deleteSectionIcon,
} from "../../controllers/reference/sectionicon.controller";

const router: Router = Router();

router.post("/create", validate(createSectionIconSchema), createSectionIcon);
router.get("/view/:sectionIconId", validate(sectionIconIdSchema, "params"), getSectionIconById);
router.get("/view_all", getAllSectionIcons);
router.patch("/patch/:sectionIconId", validate(sectionIconIdSchema, "params"), validate(patchSectionIconSchema), updateSectionIcon);
router.delete("/delete/:sectionIconId", validate(sectionIconIdSchema, "params"), deleteSectionIcon);

export default router;