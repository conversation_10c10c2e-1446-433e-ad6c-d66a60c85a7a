import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { createDishAddonSchema, patchDishAddonSchema, dishAddonIdSchema } from "../../validation/reference/dishAddon.validation";
import {
  createDishAddon,
  getAllDishAddons,
  getDishAddonById,
  updateDishAddon,
  deleteDishAddon,
} from "../../controllers/reference/dishAddon.controller";

const router: Router = Router();

router.post("/create", validate(createDishAddonSchema), createDishAddon);
router.get("/view/:addonId", validate(dishAddonIdSchema, "params"), getDishAddonById);
router.get("/view_all", getAllDishAddons);
router.patch("/patch/:addonId", validate(dishAddonIdSchema, "params"), validate(patchDishAddonSchema), updateDishAddon);
router.delete("/delete/:addonId", validate(dishAddonIdSchema, "params"), deleteDishAddon);

export default router;