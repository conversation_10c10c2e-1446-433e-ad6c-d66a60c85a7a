import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { createAllergySchema, patchAllergySchema, allergyIdSchema } from "../../validation/reference/allergy.validation";
import {
  createAllergy,
  getAllAllergies,
  getAllergyById,
  updateAllergy,
  deleteAllergy,
} from "../../controllers/reference/allergy.controller";

const router: Router = Router();

router.post("/create", validate(createAllergySchema), createAllergy);
router.get("/view/:allergyId", validate(allergyIdSchema, "params"), getAllergyById);
router.get("/view_all", getAllAllergies);
router.patch("/patch/:allergyId", validate(allergyIdSchema, "params"), validate(patchAllergySchema), updateAllergy);
router.delete("/delete/:allergyId", validate(allergyIdSchema, "params"), deleteAllergy);

export default router;