import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { createDishExtraSchema, patchDishExtraSchema, dishExtraIdSchema } from "../../validation/reference/dishExtra.validation";
import {
  createDishExtra,
  getAllDishExtras,
  getDishExtraById,
  updateDishExtra,
  deleteDishExtra,
} from "../../controllers/reference/dishExtra.controller";

const router: Router = Router();

router.post("/create", validate(createDishExtraSchema), createDishExtra);
router.get("/view/:extraId", validate(dishExtraIdSchema, "params"), getDishExtraById);
router.get("/view_all", getAllDishExtras);
router.patch("/patch/:extraId", validate(dishExtraIdSchema, "params"), validate(patchDishExtraSchema), updateDishExtra);
router.delete("/delete/:extraId", validate(dishExtraIdSchema, "params"), deleteDishExtra);

export default router;