import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { createIngredientSchema, patchIngredientSchema, ingredientIdSchema } from "../../validation/reference/ingredient.validation";
import {
  createIngredient,
  getAllIngredients,
  getIngredientById,
  updateIngredient,
  deleteIngredient,
} from "../../controllers/reference/ingredient.controller";

const router: Router = Router();

router.post("/create", validate(createIngredientSchema), createIngredient);
router.get("/view/:ingredientId", validate(ingredientIdSchema, "params"), getIngredientById);
router.get("/view_all", getAllIngredients);
router.patch("/patch/:ingredientId", validate(ingredientIdSchema, "params"), validate(patchIngredientSchema), updateIngredient);
router.delete("/delete/:ingredientId", validate(ingredientIdSchema, "params"), deleteIngredient);

export default router;