import { Router } from 'express';
import {
    addItemToCart,
    updateCartItem,
    removeCartItem,
    clearCart,
    holdCart,
    activateCart,
    listCarts,
    confirmCart,
    getCartById,
    getOrCreateCart,
    updateCartItemCustomization,
    addCartDetails,
    patchCartMiscItem,
    patchCartAlert,
    deleteMiscItemFromCart,
    deleteAlertFromCart,
    deleteCart
} from '../../controllers/cart/cart.controller'

const router = Router();

// Get or create active cart for staff
router.get('/staff', getOrCreateCart);

// List carts (with optional query parameters for status)
router.get('/list', listCarts);

// Get specific cart by ID
router.get('/:cartId', getCartById);

// Add item to cart
router.post('/staff/items', addItemToCart);

// Update cart item quantity
router.put('/staff/items/:itemId', updateCartItem);

router.patch('/staff/add-details/:cartId', addCartDetails);

router.patch('/staff/update-misc/:cartId', patchCartMiscItem);

router.patch('/staff/update-alert/:cartId', patchCartAlert);

router.delete('/staff/delete-misc/:cartId/:miscItemId', deleteMiscItemFromCart);

router.delete('/staff/delete-alert/:cartId/:alertId', deleteAlertFromCart);

router.delete('/staff/delete-cart/:cartId', deleteCart);

// Remove item from cart
router.delete('/staff/items/:itemId', removeCartItem);

// Clear all items from cart
router.delete('/staff/clear', clearCart);

// Hold cart (change status to HOLD)
router.patch('/staff/hold', holdCart);

// Activate held cart (change status back to ACTIVE)
router.patch('/staff/:cartId/activate', activateCart);

// Confirm cart (convert to order)
router.post('/staff/confirm', confirmCart);

router.patch('/staff/update/:itemId', updateCartItemCustomization);



export default router;