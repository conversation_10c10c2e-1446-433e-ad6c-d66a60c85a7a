import { Router } from 'express';
import {
    createCustomer,
    patchCustomer,
    patchCustomerAddress,
    getAllCustomers,
    deleteCustomer,
    deleteCustomerAddress,
    addCustomerAddress,
    getCustomerById,
    getCustomerByEmailOrPhone,
    logoutCustomer,
    loginCustomer,
    revokeCustomer,
    reinstateCustomer
} from '../../controllers/customer/customer.controller';
import { validate } from '../../middlewares/validator.middleware';
import { loginSchema } from '../../validation/common.validation';
import { customerIdSchema } from '../../validation/customer/customer.validation';
import { revokeTenantSchema } from '../../validation/admin/tenantManage.validation';

const router = Router();

// Customer Management Routes
// FIXME: When u do validation, make addresses and middleName optional, this can make customer sign up easy
router.post('/create', createCustomer); // Create a new customer

router.patch('/update-customer/:customerId', patchCustomer); // Partial update of customer details
router.delete('/delete-customer/:customerId', deleteCustomer); // Delete a customer
router.get('/get-customer/:customerId', getCustomerById); // Get customer by ID
router.get('/view_all', getAllCustomers); // Get all customers with filters
router.get('/get-customer-email-phone', getCustomerByEmailOrPhone); // Get customer by email or phone number

// Customer Address Management Routes
router.post('/add-address/:customerId', addCustomerAddress); // Add a new address for a customer
router.patch('/update-address/:addressId', patchCustomerAddress); // Partial update of a customer address
router.delete('/delete-address/:addressId', deleteCustomerAddress); // Delete a customer address

router.patch("/revoke/:customerId", validate(customerIdSchema, "params"), validate(revokeTenantSchema), revokeCustomer)
router.patch("/reinstate/:customerId", validate(customerIdSchema, "params"), validate(revokeTenantSchema), reinstateCustomer)

// login routes
router.post("/login", validate(loginSchema), loginCustomer)
router.post("/logout", logoutCustomer)

export default router;