import express from "express";
import multer from "multer";
import { viewAttachmentSchema } from "../../validation/attachment/attachment.validation";
import { validate } from "../../middlewares/validator.middleware";
import { attachmentUpload, deleteAttachment, viewAttachment } from "../../controllers/attachment/attachment.controller";

const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

router.post("/upload", upload.single("file"), attachmentUpload);
router.get("/view", validate(viewAttachmentSchema, 'query'), viewAttachment);
router.delete("/delete", validate(viewAttachmentSchema, 'query'), deleteAttachment);

export default router;