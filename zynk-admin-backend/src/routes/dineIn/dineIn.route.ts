import { Router } from 'express';
import { validate } from '../../middlewares/validator.middleware';
import { tableAttrsPatchSchema, takeOrderFromDineInSchema } from '../../validation/dineIn/dineIn.validation';
import { cancelOrderFromDineIn, cancelOrderFromTableCombination, patchTableAttrs, takeOrderFromDineIn, takeOrderFromTableCombination } from '../../controllers/dineIn/dineIn.controller';
import { tableCombinationIdSchema, tableIdSchema } from '../../validation/dineIn/tableReserv.validation';

const router = Router();

router.patch("/table-attrs/:tableId", validate(tableAttrsPatchSchema), patchTableAttrs)
router.post("/take-order/:tableId", validate(tableIdSchema, "params"), validate(takeOrderFromDineInSchema), takeOrderFromDineIn)
router.delete("/cancel-order/:tableId", validate(tableIdSchema, "params"), validate(takeOrderFromDineInSchema), cancelOrderFromDineIn)

// table combinations
router.post("/take-order-combined/:tableCombinationId", validate(tableCombinationIdSchema, "params"), validate(takeOrderFromDineInSchema), takeOrderFromTableCombination)
router.delete("/cancel-order-combined/:tableCombinationId", validate(tableCombinationIdSchema, "params"), validate(takeOrderFromDineInSchema), cancelOrderFromTableCombination)


export default router;