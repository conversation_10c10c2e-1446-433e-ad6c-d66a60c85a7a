import { Router } from 'express';
import { validate } from '../../middlewares/validator.middleware';
import { confirmationCodeSchema, createReservationSchema, getTableCombinationsSchema, getTableResSchema, reservationIdSchema, tableCombinationIdSchema, tableIdSchema, updateReservationSchema } from '../../validation/dineIn/tableReserv.validation';
import { cancelReservation, checkInReservation, createReservation, getReservation, getReservations, updateReservation } from '../../controllers/reservation/tableReserv.controller';
import { cancelTableCombinationReservation, checkInTableCombinationReservation, createTableCombinationReservation, getTableCombinationReservations, updateTableCombinationReservation } from '../../controllers/reservation/tableComb.controller';

const router = Router();

router.post('/tables/:tableId/reservations', validate(tableIdSchema, "params"), validate(createReservationSchema), createReservation);

// has filters
router.get('/reservations', validate(getTableResSchema, "query"), getReservations);

router.get('/reservations/:reservationId', validate(reservationIdSchema, "params"), getReservation);

router.put('/reservations/:reservationId', validate(reservationIdSchema, "params"), validate(updateReservationSchema), updateReservation);

router.delete('/reservations/:reservationId', validate(reservationIdSchema, "params"), cancelReservation);

// Check-in reservation (customer arrives)
router.post('/reservations/:reservationId/checkin', validate(reservationIdSchema, "params"), checkInReservation);

// Table combinations

router.post("/table-combinations/:tableCombinationId/reservations", validate(tableCombinationIdSchema, "params"), validate(createReservationSchema), createTableCombinationReservation)
router.get("/getTableCombinationReservations", validate(getTableCombinationsSchema, "query"), getTableCombinationReservations)
router.put("/updateTableCombinationReservation/:confirmationCode", validate(confirmationCodeSchema, "params"), validate(updateReservationSchema), updateTableCombinationReservation)
router.delete("/cancelTableCombinationReservation/:confirmationCode", validate(confirmationCodeSchema, "params"), cancelTableCombinationReservation)
router.post("/table-combinations/:confirmationCode/checkin", validate(confirmationCodeSchema, "params"), checkInTableCombinationReservation)

export default router;