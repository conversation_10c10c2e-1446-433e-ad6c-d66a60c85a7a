import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { staffIdSchema } from "../../validation/staff/staff.validation";
import { checkInStaff, checkOutStaff, clockInStaff, clockOutStaff } from "../../controllers/attendance/attendance.controller";
import { createCheckInRecordSchema, createCheckOutRecordSchema, createClockInRecordSchema, createClockOutRecordSchema } from "../../validation/attendance/attendance.validation";

const router: Router = Router();

router.post("/check-in/:staffId", validate(staffIdSchema, "params"), validate(createCheckInRecordSchema), checkInStaff)
router.post("/check-out/:staffId", validate(staffIdSchema, "params"), validate(createCheckOutRecordSchema), checkOutStaff)

router.post("/clock-in/:staffId", validate(staffIdSchema, "params"), validate(createClockInRecordSchema), clockInStaff)
router.post("/clock-out/:staffId", validate(staffIdSchema, "params"), validate(createClockOutRecordSchema), clockOutStaff)

export default router;
