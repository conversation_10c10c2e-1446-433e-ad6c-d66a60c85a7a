import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { loginSchema } from "../../validation/common.validation";
import { loginTenant, logoutTenant } from "../../controllers/tenant/tenant.controller";

const router: Router = Router()

router.post("/login", validate(loginSchema), loginTenant)
router.post("/logout", logoutTenant)

export default router