import express from 'express';
import businessUnitRoute from "./general/businessUnit.route";
import parkingServiceRoute from "./general/parkingService.route";
import orderTypesRoute from "./general/orderTypes.route";
import currencyUnitRoute from "./general/currencyUnit.route";
import notificationRoute from "./general/notification.route";

const router = express.Router();

// NOTE: always comment unfinished routes before git push!
// router.use("/notification", notificationRoute);
// router.use("/currency-unit", currencyUnitRoute);
router.use("/order-types", orderTypesRoute);
router.use("/parking-service", parkingServiceRoute);
// router.use("/business-unit", businessUnitRoute);

export default router;