import express from 'express';
import { validate } from '../../../../middlewares/validator.middleware';
import { patchBufferTime, viewBufferTime } from '../../../../controllers/tenantSettings/tableReserv/bufferTime.controller';
import { patchBufferTimeSchema } from '../../../../validation/tenantSettings/tableReserv/bufferTime.validation';

const router = express.Router();

router.get("/view", viewBufferTime)
router.patch("/patch", validate(patchBufferTimeSchema), patchBufferTime)

export default router;