import express from 'express';
import { validate } from '../../../../middlewares/validator.middleware';
import { patchTurnoverTime, viewTurnoverTime } from '../../../../controllers/tenantSettings/tableReserv/turnoverTime.controller';
import { patchTurnoverTimeSchema } from '../../../../validation/tenantSettings/tableReserv/turnoverTime.validation';

const router = express.Router();

router.get("/view", viewTurnoverTime)
router.patch("/patch", validate(patchTurnoverTimeSchema), patchTurnoverTime)

export default router;