import express from 'express';
import { patchTablesAndCombinations, viewAllTablesAndCombinations } from '../../../../controllers/tenantSettings/tableReserv/tableManage.controller';
import { validate } from '../../../../middlewares/validator.middleware';
import { patchTablesAndCombinationsSchema } from '../../../../validation/tenantSettings/tableReserv/tableManage.validation';

const router = express.Router();

router.get("/view_all", viewAllTablesAndCombinations)
router.patch("/patch", validate(patchTablesAndCombinationsSchema), patchTablesAndCombinations)

export default router;