import express from 'express';
import { validate } from '../../../../middlewares/validator.middleware';
import { getOnlineReservConfig, getPartySizeConfig, getTimeSlotConfig, patchOnlineReservConfig, patchOnlineReservCustomHours, patchOnlineReservSpecialDays, patchPartySizeConfig, patchTimeSlotConfig } from '../../../../controllers/tenantSettings/tableReserv/onlineReservConfig.controller';
import { onlineResConfigIdSchema, patchOnlineReservConfigSchema } from '../../../../validation/tenantSettings/tableReserv/onlineReservConfig.validation';
import { patchFoodMenuCustomHoursSchema, patchFoodMenuSpecialDaysSchema } from '../../../../validation/foodmenu/foodMenu.validation';
import { patchPartySizeConfigSchema } from '../../../../validation/tenantSettings/tableReserv/partySize.validation';
import { timeSlotPatchSchema } from '../../../../validation/tenantSettings/tableReserv/timeSlotConfig.validation';

const router = express.Router();

router.get("/view", getOnlineReservConfig)
router.patch("/patch", validate(patchOnlineReservConfigSchema), patchOnlineReservConfig)

router.patch("/custom-hours/:onlineResConfigId", validate(onlineResConfigIdSchema, "params"), validate(patchFoodMenuCustomHoursSchema), patchOnlineReservCustomHours)

router.patch("/special-days/:onlineResConfigId", validate(onlineResConfigIdSchema, "params"), validate(patchFoodMenuSpecialDaysSchema), patchOnlineReservSpecialDays)

router.get("/party-size/view", getPartySizeConfig)

router.patch("/party-size/patch", validate(patchPartySizeConfigSchema), patchPartySizeConfig)

router.get("/time-slot/view", getTimeSlotConfig)

router.patch("/time-slot/patch", validate(timeSlotPatchSchema), patchTimeSlotConfig)

export default router;