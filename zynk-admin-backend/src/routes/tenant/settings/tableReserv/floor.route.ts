import express from 'express';
import { validate } from '../../../../middlewares/validator.middleware';
import { patchFloors, viewAllFloors } from '../../../../controllers/tenantSettings/tableReserv/floor.controller';
import { patchFloorsSchema } from '../../../../validation/tenantSettings/tableReserv/floor.validation';

const router = express.Router();

router.get("/view_all", viewAllFloors)
router.patch("/patch", validate(patchFloorsSchema), patchFloors)

export default router;