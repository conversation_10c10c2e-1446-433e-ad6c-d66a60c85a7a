import { Router } from "express";
import { patchPreparationTime, viewPreparationTime } from "../../../../controllers/tenantSettings/waiter/preparationDuration.controller";
import { validate } from "../../../../middlewares/validator.middleware";
import { patchPrepDurationSchema } from "../../../../validation/tenantSettings/waiter/preparationDur.validation";

const router: Router = Router();

router.get("/view", viewPreparationTime)
router.patch("/patch", validate(patchPrepDurationSchema), patchPreparationTime)

export default router;