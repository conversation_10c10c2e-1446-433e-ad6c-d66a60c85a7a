import { Router } from "express";
import { validate } from "../../../../middlewares/validator.middleware";
import { patchSpecialIns, viewSpecialInstructionConfig } from "../../../../controllers/tenantSettings/waiter/specialInstruction.controller";
import { patchSpecialInsSchema } from "../../../../validation/tenantSettings/kitchen/specialIns.validation";

const router: Router = Router();

router.get("/view", viewSpecialInstructionConfig)
router.patch("/patch", validate(patchSpecialInsSchema), patchSpecialIns)

export default router;