import { Router } from "express";
import { validate } from "../../../../middlewares/validator.middleware";
import { patchAllergyColor, viewAllAllergyColors, viewAllergyColor } from "../../../../controllers/tenantSettings/kitchen/allergyColor.controller";
import { patchAllergyColorSchema, viewAllergyColorParamsSchema } from "../../../../validation/tenantSettings/kitchen/allergyColor.validation";

const router: Router = Router();

router.get("/view_all", viewAllAllergyColors)
router.get("/view/:name", validate(viewAllergyColorParamsSchema, "params"), viewAllergyColor)
router.patch("/patch", validate(patchAllergyColorSchema), patchAllergyColor)

export default router;