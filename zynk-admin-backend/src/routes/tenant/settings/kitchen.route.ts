import express from 'express';
import {getKitchenSettings, patchKitchenSettings} from "../../../controllers/tenantSettings/kitchen.controller";
import allergyColorRoute from "./kitchen/allergyColor.route"
import specialInsRoute from "./kitchen/specialInstruction.route"

const router = express.Router();


router.get('/get', getKitchenSettings);
router.patch('/patch', patchKitchenSettings);

router.use("/allergy-color", allergyColorRoute)
router.use("/special-ins", specialInsRoute)

export default router;