import express from 'express';
import { patchParkingService, viewParkingService } from '../../../../controllers/tenantSettings/general/parkingService.controller';
import { validate } from '../../../../middlewares/validator.middleware';
import { patchParkingServiceSchema } from '../../../../validation/tenantSettings/general/parkingSrv.validation';

const router = express.Router();

router.get("/view", viewParkingService)
router.patch("/patch", validate(patchParkingServiceSchema), patchParkingService)

export default router;