import express from 'express';
import { patchCurrencyUnit, viewCurrencyUnit } from '../../../../controllers/tenantSettings/general/currencyUnit.controller';
import { patchCurrencyUnitSchema } from '../../../../validation/tenantSettings/general/currencyUnit.validation';
import { validate } from '../../../../middlewares/validator.middleware';

const router = express.Router();

router.get("/view", viewCurrencyUnit)
router.patch("/patch", validate(patchCurrencyUnitSchema), patchCurrencyUnit)

export default router;