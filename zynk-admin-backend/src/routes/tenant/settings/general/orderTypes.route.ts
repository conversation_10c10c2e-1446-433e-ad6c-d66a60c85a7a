import express from 'express';
import { validate } from '../../../../middlewares/validator.middleware';
import { patchOrderTypes, viewAllOrderTypes } from '../../../../controllers/tenantSettings/general/orderType.controller';
import { patchOrderTypesSchema } from '../../../../validation/tenantSettings/general/orderType.validation';

const router = express.Router();

router.get("/view_all", viewAllOrderTypes)
router.patch("/patch", validate(patchOrderTypesSchema), patchOrderTypes)

export default router;