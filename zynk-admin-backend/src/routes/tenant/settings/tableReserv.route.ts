import express from 'express';
import tableManageRoute from "./tableReserv/tableManage.route"
import floorRoute from "./tableReserv/floor.route"
import turnoverTimeRoute from "./tableReserv/turnoverTime.route"
import bufferTimeRoute from "./tableReserv/bufferTime.route"
import onlineReservConfigRoute from "./tableReserv/onlineReservConfig.route"

const router = express.Router();

router.use("/table-manage", tableManageRoute);
router.use("/floors", floorRoute);
router.use("/turnover-time", turnoverTimeRoute)
router.use("/buffer-time", bufferTimeRoute)
router.use("/online-tablereserv-config", onlineReservConfigRoute)

export default router;