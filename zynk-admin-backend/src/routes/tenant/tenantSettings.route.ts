import express from 'express';
import waiterRoute from "./settings/waiter.route";
import kitchenRoute from "./settings/kitchen.route";
import onlineOrderRoute from "./settings/onlineOrder.route";
import generalRoute from "./settings/general.route";
import tableReservRoute from "./settings/tableReserv.route"

const router = express.Router();

router.use("/general", generalRoute)
router.use("/table-reservation", tableReservRoute)
router.use('/waiter', waiterRoute);
router.use('/kitchen', kitchenRoute);
router.use('/online-order', onlineOrderRoute);

export default router;