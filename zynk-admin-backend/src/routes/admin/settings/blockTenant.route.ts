import {Router} from "express";
import {
    blockTenant,
    createBlockedType,
    deleteBlockType,
    patchBlockType,
    unblockTenant,
    listBlockedTenants,
    listAllBlockTypes
} from "../../../controllers/admin/settings/blockTenant.controller";
import {validate} from "../../../middlewares/validator.middleware";
import {
    blockTenantFilterSchema,
    blockTenantSchema,
    blockTypeFilterSchema,
    createBlockTypeSchema,
    deleteBlockTypeSchema,
    patchBlockTypeSchema,
    unblockTenantSchema
} from "../../../validation/admin/settings/blockTenant.validation";

const router: Router = Router();

// TenantBlockList
router.get("/list", validate(blockTenantFilterSchema, "query"), listBlockedTenants);
router.post("/block", validate(blockTenantSchema), blockTenant);
router.delete("/unblock", validate(unblockTenantSchema, "query"), unblockTenant);

//BlockType
router.get("/block_type/list", validate(blockTypeFilterSchema, "query"), listAllBlockTypes);
router.post("/block_type/create", validate(createBlockTypeSchema), createBlockedType);
router.patch("/block_type/patch", validate(patchBlockTypeSchema), patchBlockType);
router.delete("/block_type/delete/:blockTypeId", validate(deleteBlockTypeSchema, "params"), deleteBlockType)

export default router;