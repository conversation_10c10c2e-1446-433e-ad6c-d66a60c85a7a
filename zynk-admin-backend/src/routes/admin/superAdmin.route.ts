import {Router} from "express";
import {validate} from "../../middlewares/validator.middleware";
import {
    createSuperAdminSchema,
    deleteSuperAdminSchema,
    loginSuperAdminSchema,
    patchSuperAdminSchema,
    revokeSuperAdminSchema,
    viewSuperSessionParams,
} from "../../validation/admin/superAdmin.validation";
import {
    createSuperAdmin,
    deleteSuperAdmin,
    getAllSessions,
    getAllSuperAdmins,
    loginSuperAdmin,
    logoutSuperAdmin,
    patchSuperAdmin,
    reinstateSuperAdmin,
    revokeSuperAdmin,
} from "../../controllers/admin/superadmin.controller";

const router: Router = Router();

router.post("/create", validate(createSuperAdminSchema), createSuperAdmin);

router.post("/login", validate(loginSuperAdminSchema), loginSuperAdmin);
router.post("/logout", logoutSuperAdmin);
router.patch(
    "/patch/:superAdminId",
    validate(viewSuperSessionParams, "params"),
    validate(patchSuperAdminSchema),
    patchSuperAdmin
);
router.delete(
    "/delete/:superAdminId",
    validate(viewSuperSessionParams, "params"),
    validate(deleteSuperAdminSchema),
    deleteSuperAdmin
);
router.get("/view_all", getAllSuperAdmins);
router.get(
    "/view_sessions/:superAdminId",
    validate(viewSuperSessionParams, "params"),
    getAllSessions
);
router.patch("/revoke", validate(revokeSuperAdminSchema), revokeSuperAdmin);
router.patch("/reinstate", validate(revokeSuperAdminSchema), reinstateSuperAdmin);

export default router;
