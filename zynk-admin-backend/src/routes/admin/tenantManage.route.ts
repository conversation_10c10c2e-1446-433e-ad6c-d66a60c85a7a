import {Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { createTenantSchema, patchTenantSchema, revokeTenantSchema, tenantParamsSchema } from "../../validation/admin/tenantManage.validation";
import { createTenant, deleteTenant, getAllTenants, getAllTenantSessions, getTenant, patchTenant, reinstateTenant, revokeTenant } from "../../controllers/admin/tenantManage.controller";

const router: Router = Router()

router.post("/create", validate(createTenantSchema), createTenant)
router.patch("/patch/:tenantId", validate(tenantParamsSchema, "params"), validate(patchTenantSchema), patchTenant)
router.delete("/delete/:tenantId", validate(tenantParamsSchema, "params"), deleteTenant)
router.get("/view_all", getAllTenants)
router.get("/view/:tenantId", validate(tenantParamsSchema, "params"), getTenant)
router.get("/view_sessions/:tenantId", validate(tenantParamsSchema, "params"), getAllTenantSessions)
router.patch("/revoke/:tenantId", validate(tenantParamsSchema, "params"), validate(revokeTenantSchema), revokeTenant)
router.patch("/reinstate/:tenantId", validate(tenantParamsSchema, "params"), validate(revokeTenantSchema), reinstateTenant)

export default router