import {Router} from "express";
import {getAllCountries, getAllStates, validateState} from "../../controllers/subscription/country.controller";
import {validate} from "../../middlewares/validator.middleware";
import {countryCodeSchemaOptional, validateSchema} from "../../validation/subscription/country.validation";

const router: Router = Router();

router.get("/view_all", validate(countryCodeSchemaOptional, "query"), getAllCountries);
router.get("/state/view_all", validate(countryCodeSchemaOptional, "query"), getAllStates);
router.get("/state/validate", validate(validateSchema, "query"), validateState);

export default router;