import {Router} from "express";
import {createTax, deleteTax, listTax, patchTax} from "../../controllers/subscription/tax.controller";
import {validate} from "../../middlewares/validator.middleware";
import {
    createTaxSchema,
    deleteTaxSchema,
    filterTaxSchema,
    patchTaxSchema
} from "../../validation/subscription/tax.validation";

const router: Router = Router();

router.get("/list", validate(filterTaxSchema, "query"), listTax);
router.post("/create", validate(createTaxSchema), createTax);
router.patch("/patch", validate(patchTaxSchema), patchTax);
router.delete("/delete/:taxId", validate(deleteTaxSchema, "params"), deleteTax);

export default router;