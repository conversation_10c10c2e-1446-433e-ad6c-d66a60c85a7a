import {Router} from "express";
import {validate} from "../../middlewares/validator.middleware";
import {
    createSubsTierSchema,
    filterSubTierSchema,
    patchSubTierSchema,
    subTierIdSchema,
} from "../../validation/subscription/subTier.validation";
import {
    createSubsTier,
    deleteSubsTier,
    listSubsTier,
    patchSubsTier
} from "../../controllers/subscription/SubscriptionTier.controller";

const router: Router = Router();

router.get("/list", validate(filterSubTierSchema, "query"), listSubsTier);
router.post("/create", validate(createSubsTierSchema), createSubsTier);
router.patch("/patch", validate(patchSubTierSchema), patchSubsTier);
router.delete("/delete/:subTierId", validate(subTierIdSchema, "params"), deleteSubsTier);

export default router;
