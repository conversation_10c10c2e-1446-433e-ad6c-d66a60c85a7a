import {Router} from "express";
import {
    createFeature,
    deleteFeature,
    listFeatures,
    patchFeature
} from "../../controllers/subscription/feature.controller";
import {validate} from "../../middlewares/validator.middleware";
import {
    createFeatureSchema,
    featureFilterSchema,
    featureIdSchema,
    patchFeatureSchema
} from "../../validation/subscription/feature.validation";

const router: Router = Router();

router.get("/list", validate(featureFilterSchema, "query"), listFeatures);
router.post("/create", validate(createFeatureSchema), createFeature);
router.patch("/patch", validate(patchFeatureSchema), patchFeature);
router.delete("/delete/:featureId", validate(featureIdSchema, "params"), deleteFeature);

export default router;