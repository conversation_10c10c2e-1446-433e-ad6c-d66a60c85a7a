import {Router} from "express";
import {
    mangeSubscription,
    terminateSubscription,
    viewAllSubscription
} from "../../controllers/subscription/subscription.controller";
import {validate} from "../../middlewares/validator.middleware";
import {manageSubscriptionSchema} from "../../validation/subscription/subscription.validation";

const router: Router = Router();

router.get("/list", viewAllSubscription);
router.post("/activate", validate(manageSubscriptionSchema), mangeSubscription);
router.delete("/terminate/:subscriptionId", terminateSubscription);

export default router;