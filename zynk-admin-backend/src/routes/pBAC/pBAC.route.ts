import pBACActionRoute from "./child/action.route";
import pBACModuleRoute from "./child/module.route";
import pBACPermissionRoute from "./child/permission.route";
import pBACRoleRoute from "./child/role.route";
import {Router} from "express";

const router: Router = Router();

router.use("/module", pBACModuleRoute);
router.use("/action", pBACActionRoute);
router.use("/permission", pBACPermissionRoute);
router.use("/role", pBACRoleRoute);

export default router;