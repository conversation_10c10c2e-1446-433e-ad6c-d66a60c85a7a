import { Router } from "express"
import { validate } from "../../middlewares/validator.middleware"
import { createStaffSchema, patchStaffAvailabilitySchema, patchStaffBasicDetailSchema, patchStaffCertificationSchema, staffIdSchema } from "../../validation/staff/staff.validation"
import { createStaff, deleteStaff, getAllStaffSessions, patchStaffAvailability, patchStaffCertifications, patchStaffDetails, reinstateStaff, revokeStaff, viewAllClockedInStaff, viewAllStaffByBranch, viewStaffById } from "../../controllers/staff/staff.controller"
import { branchIdSchema } from "../../validation/company/branch.validation"
import { revokeTenantSchema } from "../../validation/admin/tenantManage.validation"

const router: Router = Router()

router.post("/create", validate(createStaffSchema), createStaff)
router.get("/view_all/:branchId", validate(branchIdSchema, "params"), viewAllStaffByBranch)
router.get("/view/:staffId", validate(staffIdSchema, "params"), viewStaffById)
router.get("/viewAllClockedInStaff/:branchId", validate(branchIdSchema, "params"), viewAllClockedInStaff)

// Edit staff details
router.patch("/patch/staff-details/:staffId", validate(staffIdSchema, "params"), validate(patchStaffBasicDetailSchema), patchStaffDetails)
router.patch("/patch/default-availability/:staffId", validate(staffIdSchema, "params"), validate(patchStaffAvailabilitySchema), patchStaffAvailability)
router.patch("/patch/staff-certifications/:staffId", validate(staffIdSchema, "params"), validate(patchStaffCertificationSchema), patchStaffCertifications)

// sessions
router.get("/view_sessions/:staffId", validate(staffIdSchema, "params"), getAllStaffSessions)
router.patch("/revoke/:staffId", validate(staffIdSchema, "params"), validate(revokeTenantSchema), revokeStaff)
router.patch("/reinstate/:staffId", validate(staffIdSchema, "params"), validate(revokeTenantSchema), reinstateStaff)

router.delete("/delete/:staffId", validate(staffIdSchema, "params"), deleteStaff)

export default router