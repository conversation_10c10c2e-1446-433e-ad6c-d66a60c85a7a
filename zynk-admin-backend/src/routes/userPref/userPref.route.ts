import {Router} from "express";
import { getPrefBranch, patchPrefBranch } from "../../controllers/userPref/userPref.controller";
import { prefBranchPatchSchema, prefBranchUserTypeSchema } from "../../validation/userPref/userPref.validation";
import { validate } from "../../middlewares/validator.middleware";

const router: Router = Router();

router.get("/pref-branch", getPrefBranch)
router.patch("/pref-branch", validate(prefBranchPatchSchema), patchPrefBranch)

export default router;