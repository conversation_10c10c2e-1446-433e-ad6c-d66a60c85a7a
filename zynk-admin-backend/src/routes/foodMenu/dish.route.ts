import {Router} from "express";
import {validate} from "../../middlewares/validator.middleware";
import {createDishSchema, dishIdSchema, patchDishSchema} from "../../validation/foodmenu/dish.validation";
import {
    createDish,
    deleteDish,
    duplicateDish,
    moveToSection,
    patchDish,
    patchDishAvailability,
    patchDishCustomHours,
    patchDishSpecialDays,
    viewAllDishesBySection,
    viewDishById
} from "../../controllers/foodmenu/dish.controller";
import {sectionIdSchema} from "../../validation/foodmenu/section.validation";
import {
    patchFoodMenuAvailabilitySchema,
    patchFoodMenuCustomHoursSchema,
    patchFoodMenuSpecialDaysSchema
} from "../../validation/foodmenu/foodMenu.validation";
// @ts-ignore
import dishSideRoute from "./custom/dishSide.route"
// @ts-ignore
import dishBevRoute from "./custom/dishBev.route"
// @ts-ignore
import dishDesRoute from "./custom/dishDes.route";

const router: Router = Router()

router.post("/create/:sectionId", validate(sectionIdSchema, "params"), validate(createDishSchema), createDish)
router.get("/view_all/:sectionId", validate(sectionIdSchema, "params"), viewAllDishesBySection)
router.get("/view/:dishId", validate(dishIdSchema, "params"), viewDishById)
router.patch("/patch/:dishId", validate(dishIdSchema, "params"), validate(patchDishSchema), patchDish)
router.delete("/delete/:dishId", validate(dishIdSchema, "params"), deleteDish)

router.patch("/custom-hours/:dishId", validate(dishIdSchema, "params"), validate(patchFoodMenuCustomHoursSchema), patchDishCustomHours)
router.patch("/availability/:dishId", validate(dishIdSchema, "params"), validate(patchFoodMenuAvailabilitySchema), patchDishAvailability)

router.patch("/special-days/:dishId", validate(dishIdSchema, "params"), validate(patchFoodMenuSpecialDaysSchema), patchDishSpecialDays)

// duplication
router.post("/duplicate/:dishId", validate(dishIdSchema, "params"), duplicateDish)
router.patch("/move/:dishId", validate(dishIdSchema, "params"), validate(sectionIdSchema), moveToSection)

// dish sides
router.use("/side", dishSideRoute)

// dish beverages
router.use("/beverage", dishBevRoute)

// dish desserts
router.use("/dessert", dishDesRoute)

export default router