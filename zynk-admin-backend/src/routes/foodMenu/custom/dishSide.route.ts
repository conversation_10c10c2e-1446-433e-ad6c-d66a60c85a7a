import { Router } from "express"
import { validate } from "../../../middlewares/validator.middleware"
import { foodMenuIdSchema } from "../../../validation/foodmenu/foodMenu.validation"
import { viewAllDishSides, viewDishSide } from "../../../controllers/foodmenu/custom/dishSide.controller"
import { dishSideIdSchema } from "../../../validation/foodmenu/custom/dishSide.validation"

const router: Router = Router()

router.get("/view_all/:foodMenuId", validate(foodMenuIdSchema, "params"), viewAllDishSides)
router.get("/view/:dishSideId", validate(dishSideIdSchema, "params"), viewDishSide)

export default router