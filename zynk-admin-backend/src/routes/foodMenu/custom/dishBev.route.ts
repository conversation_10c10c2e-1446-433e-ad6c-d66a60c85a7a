import { Router } from "express"
import { validate } from "../../../middlewares/validator.middleware"
import { foodMenuIdSchema } from "../../../validation/foodmenu/foodMenu.validation"
import { dishBevIdSchema } from "../../../validation/foodmenu/custom/dishBev.validation"
import { viewAllDishBevs, viewDishBev } from "../../../controllers/foodmenu/custom/dishBev.controller"

const router: Router = Router()

router.get("/view_all/:foodMenuId", validate(foodMenuIdSchema, "params"), viewAllDishBevs)
router.get("/view/:dishBevId", validate(dishBevIdSchema, "params"), viewDishBev)

export default router