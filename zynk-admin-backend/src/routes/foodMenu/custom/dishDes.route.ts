import { Router } from "express"
import { validate } from "../../../middlewares/validator.middleware"
import { foodMenuIdSchema } from "../../../validation/foodmenu/foodMenu.validation"
import { viewAllDishDes, viewDishDes } from "../../../controllers/foodmenu/custom/dishDes.controller"
import { dishDesIdSchema } from "../../../validation/foodmenu/custom/dishDes.validation"

const router: Router = Router()

router.get("/view_all/:foodMenuId", validate(foodMenuIdSchema, "params"), viewAllDishDes)
router.get("/view/:dishDesId", validate(dishDesIdSchema, "params"), viewDishDes)

export default router