import { Router } from "express"
import { validate } from "../../../middlewares/validator.middleware"
import { foodMenuIdSchema, patchUpcomingChangeSchema, upcomingChangeParamsSchema } from "../../../validation/foodmenu/foodMenu.validation"
import { deleteUpcomingChange, getUpcomingChanges, patchUpcomingChange } from "../../../controllers/foodmenu/updates/upcoming_change.controller"

const router: Router = Router()

router.get("/upcoming-changes/:foodMenuId", validate(foodMenuIdSchema, "params"), getUpcomingChanges)
router.patch("/upcoming-changes/:foodMenuId", validate(foodMenuIdSchema, "params"), validate(patchUpcomingChangeSchema), patchUpcomingChange)

router.delete("/delete/:changeId", validate(upcomingChangeParamsSchema, "params"), deleteUpcomingChange)

export default router