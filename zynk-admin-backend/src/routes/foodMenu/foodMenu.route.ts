import {Router} from "express";
import {
    createFoodMenuSchema,
    foodMenuIdSchema,
    foodMenuWithAvailabilityParams,
    onlineMenuFetchSchema,
    patchFoodMenuAttrSchema,
    patchFoodMenuAvailabilitySchema,
    patchFoodMenuCustomHoursSchema,
    patchFoodMenuSpecialDaysSchema,
    patchFoodMenuStatusSchema
} from "../../validation/foodmenu/foodMenu.validation";
import {validate} from "../../middlewares/validator.middleware";
import {
    createFoodMenu,
    deleteFoodMenu,
    duplicateFoodMenu,
    getActiveMenu,
    getActiveMenuWithAvailability,
    getAllFoodMenuByBranch,
    getFoodMenu,
    getOnlineMenu,
    patchFoodMenuAttr,
    patchFoodMenuAvailability,
    patchFoodMenuCustomHours,
    patchFoodMenuSpecialDays,
    patchFoodMenuStatus
} from "../../controllers/foodmenu/foodMenu.controller";
import {branchIdSchema} from "../../validation/company/branch.validation";
// @ts-ignore
import upcomingChangeRoute from "./updates/upcoming_change.route"

const router: Router = Router()

router.post("/create/:branchId", validate(branchIdSchema, "params"), validate(createFoodMenuSchema), createFoodMenu)

router.get("/view_all/:branchId", validate(branchIdSchema, "params"), getAllFoodMenuByBranch)
router.get("/view/:foodMenuId", validate(foodMenuIdSchema, "params"), getFoodMenu)

// status patch
router.patch("/patch/status/:foodMenuId", validate(foodMenuIdSchema, "params"), validate(patchFoodMenuStatusSchema), patchFoodMenuStatus)

// attr patch
router.patch("/patch/menu/:foodMenuId", validate(foodMenuIdSchema, "params"), validate(patchFoodMenuAttrSchema), patchFoodMenuAttr)

// custom hours for foodMenu
router.patch("/custom-hours/:foodMenuId", validate(foodMenuIdSchema, "params"), validate(patchFoodMenuCustomHoursSchema), patchFoodMenuCustomHours)

// availability for foodMenu
router.patch("/availability/:foodMenuId", validate(foodMenuIdSchema, "params"), validate(patchFoodMenuAvailabilitySchema), patchFoodMenuAvailability)

router.patch("/special-days/:foodMenuId", validate(foodMenuIdSchema, "params"), validate(patchFoodMenuSpecialDaysSchema), patchFoodMenuSpecialDays)

router.delete("/delete/:foodMenuId", validate(foodMenuIdSchema, "params"), deleteFoodMenu)

// Most used routes
router.get("/active-menu-withoutAvailability/:branchId", validate(branchIdSchema, "params"), getActiveMenu)
router.get("/active-menu-withAvailability/", validate(foodMenuWithAvailabilityParams, "query"), getActiveMenuWithAvailability)

router.get("/online-menu/:branchId", validate(onlineMenuFetchSchema, "query"), getOnlineMenu)

router.post("/duplicate/:foodMenuId", validate(foodMenuIdSchema, "params"), duplicateFoodMenu)

router.use("/updates", upcomingChangeRoute)

export default router