import {Router} from "express";
import {validate} from "../../middlewares/validator.middleware";
import {createSectionSchema, patchSectionSchema, sectionIdSchema} from "../../validation/foodmenu/section.validation";
import {
    foodMenuIdSchema,
    patchFoodMenuAvailabilitySchema,
    patchFoodMenuCustomHoursSchema,
    patchFoodMenuSpecialDaysSchema
} from "../../validation/foodmenu/foodMenu.validation";
import {
    createSection,
    deleteSection,
    duplicateSection,
    getAllSections,
    getSection,
    patchSection,
    patchSectionAvailability,
    patchSectionCustomHours,
    patchSectionSpecialDays
} from "../../controllers/foodmenu/section.controller";

const router: Router = Router()

router.post("/create/:foodMenuId", validate(foodMenuIdSchema, "params"), validate(createSectionSchema), createSection)
router.get("/view_all/:foodMenuId", validate(foodMenuIdSchema, "params"), getAllSections)
router.get("/view/:sectionId", validate(sectionIdSchema, "params"), getSection)

router.patch("/patch/:sectionId", validate(sectionIdSchema, "params"), validate(patchSectionSchema), patchSection)

router.patch("/custom-hours/:sectionId", validate(sectionIdSchema, "params"), validate(patchFoodMenuCustomHoursSchema), patchSectionCustomHours)

// availability for section
router.patch("/availability/:sectionId", validate(sectionIdSchema, "params"), validate(patchFoodMenuAvailabilitySchema), patchSectionAvailability)

router.delete("/delete/:sectionId", validate(sectionIdSchema, "params"), deleteSection)

router.patch("/special-days/:sectionId", validate(sectionIdSchema, "params"), validate(patchFoodMenuSpecialDaysSchema), patchSectionSpecialDays)

// duplication
router.post("/duplicate/:sectionId", validate(sectionIdSchema, "params"), duplicateSection)

export default router