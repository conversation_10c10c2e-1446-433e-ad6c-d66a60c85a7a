import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { branchIdSchema, createBranchSchema, patchBranchStatusSchema, patchBranchDetailsSchema, patchBranchContactSchema, patchBranchBusinessHoursSchema} from "../../validation/company/branch.validation";
import { createBranch, deleteBranch, getAllBranches, getBranchBusinessHours, getBranchContacts, getBranchDetails, patchBranchBusinessHours, patchBranchContacts, patchBranchDetails, patchBranchStatus } from "../../controllers/company/branch.controller";

const router: Router = Router()

router.post("/create", validate(createBranchSchema), createBranch)
router.get("/view_all", getAllBranches)

router.patch("/status/:branchId", validate(branchIdSchema, "params"), validate(patchBranchStatusSchema), patchBranchStatus)

// details
router.get("/details/:branchId", validate(branchIdSchema, "params"), getBranchDetails)
router.patch("/details/:branchId", validate(branchIdSchema, "params"), validate(patchBranchDetailsSchema), patchBranchDetails)

// contacts
router.get("/contacts/:branchId", validate(branchIdSchema, "params"), getBranchContacts)
router.patch("/contacts/:branchId", validate(branchIdSchema, "params"), validate(patchBranchContactSchema), patchBranchContacts)

// business hours
router.get("/business-hours/:branchId", validate(branchIdSchema, "params"), getBranchBusinessHours)
router.patch("/business-hours/:branchId", validate(branchIdSchema, "params"), validate(patchBranchBusinessHoursSchema), patchBranchBusinessHours)

router.delete("/delete/:branchId", validate(branchIdSchema, "params"), deleteBranch)

export default router