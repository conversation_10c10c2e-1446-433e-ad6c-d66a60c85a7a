import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { accountRepIdSchema, bankDetailPatchSchema, createCompanyRepSchema, patchCompanyRepSchema, patchCompanySchema } from "../../validation/company/company.validation";
import { createCompanyRep, deleteCompanyRep, getAllCompanyReps, getBankDetails, getCompanyInfo, patchBankDetail, patchCompanyInfo, patchCompanyRep } from "../../controllers/company/company.controller";

const router: Router = Router()

router.get("/company-info", getCompanyInfo)
router.patch("/patch", validate(patchCompanySchema), patchCompanyInfo)

router.post("/company-rep", validate(createCompanyRepSchema), createCompanyRep)
router.get("/company-rep/view_all", getAllCompanyReps)
router.patch("/company-rep/patch/:accountRepId", validate(accountRepIdSchema, "params"), validate(patchCompanyRepSchema), patchCompanyRep)
router.delete("/company-rep/delete/:accountRepId", validate(accountRepIdSchema, "params"), deleteCompanyRep)

// bank details
router.get("/bank-details", getBankDetails)
router.patch("/bank-details", validate(bankDetailPatchSchema), patchBankDetail)

export default router