import { Router } from "express";
import { validate } from "../../middlewares/validator.middleware";
import { addItemsToOrder, createOrderDetails, deleteOrderDetails, getActiveOrderDetailsByWaiter, getOrderDetails, getOrderDetailsById, patchOrderDetails, updateOrderDetailsStatus, verifyOrderCancel } from "../../controllers/order/orderDetail.controller";
import { getActiveQueueOrdersByWaiter, getQueueView, updateQueueStatus } from "../../controllers/order/orderQueue.controller";
import { verifyCancelSchema } from "../../validation/order/order.validation";

const router: Router = Router();

router.post('/create', createOrderDetails);
router.patch('/update-status/:orderDetailId', updateOrderDetailsStatus); // Update order status and approval
router.patch('/update/:orderDetailId', patchOrderDetails); // Partial update of order details
router.post('/items/:orderDetailId', addItemsToOrder); // Add items to an existing order
router.get('/get-order/:orderDetailId', getOrderDetailsById); // Get order details by ID
router.get('/view_all', getOrderDetails); // Get all orders with filters
router.delete('/delete/:orderDetailId', deleteOrderDetails); // Delete order details by ID


// Queue Management Routes - kitchen
router.get('/queue', getQueueView); // Get queue view
router.patch('/queue-status/:orderQueueId', updateQueueStatus); // Update queue status

// waiter-specific routes
router.get('/queue/get-waiter-orders', getActiveQueueOrdersByWaiter)
router.get('/active-order-details', getActiveOrderDetailsByWaiter)

router.post("/verify-cancellation", validate(verifyCancelSchema), verifyOrderCancel)

export default router;