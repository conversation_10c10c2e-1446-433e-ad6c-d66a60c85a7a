import {JSONSchema4} from 'json-schema';

export function jsonSchemaToZod(schema: JSONSchema4, depth: number = 0): string {
    if (!schema || typeof schema !== 'object') return 'z.any()';

    const indent = ' '.repeat(depth * 2);
    let zodString = '';

    if (schema.type === 'object' && schema.properties) {
        const props = Object.entries(schema.properties).map(([key, prop]) => {
            let propZod = jsonSchemaToZod(prop as JSONSchema4, depth + 1);
            if (prop.minLength) propZod += `.min(${prop.minLength})`;
            if (prop.maxLength) propZod += `.max(${prop.maxLength})`;
            if (prop.format === 'email') propZod += `.email()`;
            if (prop.pattern) propZod += `.regex(new RegExp("${prop.pattern}"))`;
            if (!Array.isArray(schema.required) || !schema.required.includes(key)) propZod += `.optional()`;
            return `${indent}  ${key}: ${propZod}`;
        }).join(',\n');
        zodString = `z.object({\n${props}\n${indent}})`;
    } else if (schema.type === 'array') {
        const itemZod = jsonSchemaToZod(schema.items as JSONSchema4 || {}, depth + 1);
        zodString = `z.array(${itemZod})`;
        if (schema.minItems) zodString += `.min(${schema.minItems})`;
        if (schema.maxItems) zodString += `.max(${schema.maxItems})`;
    } else if (schema.type === 'string') {
        zodString = 'z.string()';
        if (schema.minLength) zodString += `.min(${schema.minLength})`;
        if (schema.maxLength) zodString += `.max(${schema.maxLength})`;
        if (schema.format === 'email') zodString += `.email()`;
        if (schema.pattern) zodString += `.regex(new RegExp("${schema.pattern}"))`;
    } else if (schema.type === 'number' || schema.type === 'integer') {
        zodString = schema.type === 'integer' ? 'z.number().int()' : 'z.number()';
        if (schema.minimum) zodString += `.min(${schema.minimum})`;
        if (schema.maximum) zodString += `.max(${schema.maximum})`;
    } else if (schema.type === 'boolean') {
        zodString = 'z.boolean()';
    } else if (schema.$ref) {
        zodString = `z.lazy(() => ${schema.$ref.split('/').pop()})`;
    } else {
        zodString = 'z.any()';
    }

    return zodString;
}