import log from "../helpers/system/logger.helper";

/**
 * Logs a crash event with the provided error and crash type.
 *
 * @param {Error | unknown} error - The error that caused the crash.
 * @param {'uncaughtException' | 'unhandledRejection'} [crashType] - The type of crash event.
 */
export const crashLogger = (error: Error | unknown, crashType?: 'uncaughtException' | 'unhandledRejection'): void => {
    if (crashType === 'uncaughtException') {
        log.fatal("Server crashed - Uncaught Exception", error, "server");
    } else if (crashType === 'unhandledRejection') {
        const errorMessage = error instanceof Error ? error.message : String(error);
        log.fatal(`Server crashed - Unhandled Promise Rejection: ${errorMessage}`, error, "server");
    } else {
        log.fatal("Server crashed", null, "server");
    }
};