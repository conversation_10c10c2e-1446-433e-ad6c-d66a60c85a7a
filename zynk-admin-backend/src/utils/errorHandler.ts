import {Error<PERSON>equestHand<PERSON>} from "express";
import {CUSTOM_CODE} from "./customCode";
import {BAD_REQUEST, INTERNAL_SERVER_ERROR} from "../constants/STATUS_CODES";

export class APIError extends Error {
    constructor(public statusCode: number, message: string) {
        super(message);
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, APIError.prototype)
    }
}

export const errorHandler = (statusCode: number, message: string) => {
    statusCode = statusCode || INTERNAL_SERVER_ERROR;
    message = message || "Internal Server Error";

    return new APIError(statusCode, message);
};

export const serverErrorHandler: ErrorRequestHandler = (err, req, res, next) => {
    const statusCode = err.statusCode || INTERNAL_SERVER_ERROR;
    const message = err.message || "Internal Server Error";

    const isCustomCode = statusCode >= CUSTOM_CODE.START;
    const customCodeStr = isCustomCode
        ? CUSTOM_CODE.getKeyByValue(statusCode)
        : undefined;

    res.status(isCustomCode ? BAD_REQUEST : statusCode).json({
        success: false,
        message: message,
        statusCode,
        data: {},
        customCode: isCustomCode ? customCodeStr : undefined,
    });
};
