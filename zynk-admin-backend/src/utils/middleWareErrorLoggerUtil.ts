import log from "../helpers/system/logger.helper";
import {Request} from "express";

export const middleWareLogger = (req: Request, message: string, isError: boolean = false, error?: any) => {
    if (!isError)
        log.warn(message, {
            Path: req.authMap?.routePath,
            RouteType: req.authMap?.routeType,
            ClientIP: req.clientIP,
            UserAgent: req.userAgent,
        }, req.tenantName ?? process.env.SU_DOMAIN_NAME ?? "easydine.com")
    else
        log.error(message, {
            Path: req.authMap?.routePath,
            RouteType: req.authMap?.routeType,
            ClientIP: req.clientIP,
            UserAgent: req.userAgent,
            error: error
        }, req.tenantName ?? process.env.SU_DOMAIN_NAME ?? "easydine.com")
}