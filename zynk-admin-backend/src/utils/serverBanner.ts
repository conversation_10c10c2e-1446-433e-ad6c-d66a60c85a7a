import { Express } from "express";
import { DataSource } from "typeorm";
import * as path from "path";
import * as fs from "fs";

/**
 * ANSI color codes for terminal output
 */
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  dim: "\x1b[2m",
  cyan: "\x1b[36m",
  green: "\x1b[32m",
  violet: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  red: "\x1b[31m",
};

/**
 * Colorize text for console output
 * @param text Text to colorize
 * @param color Color to apply
 * @returns Colorized text
 */
export const colorize = (text: string, color: keyof typeof colors): string => {
  return `${colors[color]}${text}${colors.reset}`;
};

/**
 * Get the version of a package safely
 * @param packageName Package name
 * @returns Package version or 'Unknown'
 */
const getPackageVersion = (packageName: string): string => {
  try {
    const packageJsonPath = path.join(
      process.cwd(),
      "node_modules",
      packageName,
      "package.json"
    );
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
      return packageJson.version || "Unknown";
    }
    return "Unknown";
  } catch (error) {
    return "Unknown";
  }
};

/**
 * Displays a stylish ASCII art banner with server metadata
 * @param app Express application instance
 * @param dataSource TypeORM DataSource instance
 */
export const displayServerBanner = (
  app: Express,
  dataSource: DataSource
): void => {
  const expressVersion = getPackageVersion("express");
  const typeormVersion = getPackageVersion("typeorm");
  const nodeVersion = process.version;
  const environment = process.env.NODE_ENV || "development";

  const asciiArt = `
  ███████╗ █████╗ ███████╗██╗   ██╗██████╗ ██╗███╗   ██╗███████╗
  ██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝██╔══██╗██║████╗  ██║██╔════╝
  █████╗  ███████║███████╗ ╚████╔╝ ██║  ██║██║██╔██╗ ██║█████╗  
  ██╔══╝  ██╔══██║╚════██║  ╚██╔╝  ██║  ██║██║██║╚██╗██║██╔══╝  
  ███████╗██║  ██║███████║   ██║   ██████╔╝██║██║ ╚████║███████╗
  ╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝   ╚═════╝ ╚═╝╚═╝  ╚═══╝╚══════╝

        ...-"""-....
         / .===. \\
         \\/ 6 6 \\/
         ( \\___/ )
  _ooo____\\_____/______
 /                     \\
| RESTAURANT MANAGEMENT |
 \\_______________ooo___/
         |  |  |
         |_ | _|
         |  |  |
         |__|__|
         /-'Y'-\\
        (__/ \\__)
  `;

  // Calculate server start time
  const startTime = new Date();
  const formattedTime = startTime.toLocaleString();

  // Create divider lines
  const divider = "=".repeat(80);
  const subDivider = "-".repeat(80);

  console.log(colorize(asciiArt, "red"));
  console.log(colorize(divider, "violet"));
  console.log(
    `${colorize("✓", "green")} ${colorize("Server Information", "bright")}`
  );
  console.log(colorize(subDivider, "violet"));
  console.log(
    `${colorize("➤", "blue")} ${colorize(
      "Application:",
      "bright"
    )}   ${colorize("EasyDine API Server", "green")}`
  );
  console.log(
    `${colorize("➤", "blue")} ${colorize(
      "Environment:",
      "bright"
    )}   ${colorize(environment, "green")}`
  );
  console.log(
    `${colorize("➤", "blue")} ${colorize(
      "Start Time:",
      "bright"
    )}    ${colorize(formattedTime, "green")}`
  );
  console.log(
    `${colorize("➤", "blue")} ${colorize(
      "Host:",
      "bright"
    )}          ${colorize(process.env.HOSTNAME || "localhost", "green")}`
  );
  console.log(
    `${colorize("➤", "blue")} ${colorize(
      "Port:",
      "bright"
    )}          ${colorize(process.env.PORT || "3000", "green")}`
  );
  console.log(colorize(subDivider, "violet"));
  console.log(
    `${colorize("✓", "green")} ${colorize("Dependencies", "bright")}`
  );
  console.log(colorize(subDivider, "violet"));
  console.log(
    `${colorize("➤", "blue")} ${colorize(
      "Node.js:",
      "bright"
    )}       ${colorize(nodeVersion, "green")}`
  );
  console.log(
    `${colorize("➤", "blue")} ${colorize(
      "Express:",
      "bright"
    )}       ${colorize(expressVersion, "green")}`
  );
  console.log(
    `${colorize("➤", "blue")} ${colorize(
      "TypeORM:",
      "bright"
    )}       ${colorize(typeormVersion, "green")}`
  );
  console.log(colorize(divider, "violet"));

  // Database information
  if (dataSource && dataSource.isInitialized) {
    const dbType = dataSource.options.type;
    const dbName = dataSource.options.database || "Unknown";

    console.log(
      `${colorize("✓", "green")} ${colorize("Database Connection", "bright")}`
    );
    console.log(colorize(subDivider, "violet"));
    console.log(
      `${colorize("➤", "blue")} ${colorize(
        "Type:",
        "bright"
      )}          ${colorize(String(dbType), "green")}`
    );
    console.log(
      `${colorize("➤", "blue")} ${colorize(
        "Database:",
        "bright"
      )}      ${colorize(String(dbName), "green")}`
    );
    console.log(
      `${colorize("➤", "blue")} ${colorize(
        "Status:",
        "bright"
      )}        ${colorize("Connected", "green")}`
    );
    console.log(colorize(divider, "violet"));
  }

  console.log(
    colorize("\n🍽️  EasyDine server is ready to serve! 🍽️\n", "cyan")
  );
};
