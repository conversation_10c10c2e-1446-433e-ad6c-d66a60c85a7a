import path from 'path';
import {AuthenticationMapper} from "../models/authMap/AuthenticationMapper.model";

// Enum for template types
enum TemplateType {
    RouteNotFound = 'RouteNotFound',
    API_DOCS = 'API_DOCS',
    TEST_API = 'TEST_API',
    ServerError = 'ServerError',
    Unauthorized = 'Unauthorized',
}

// Interface for template data structures
interface TemplateData {
    RouteNotFound: {
        method: string;
        path: string;
        timestamp: string;
        appName: string;
        logoUrl: string;
        domainName: string;
    };
    API_DOCS: {
        apiDocs: AuthenticationMapper[];
        appName: string;
    };
    TEST_API: {
        testApiData: AuthenticationMapper;
        appName: string;
    }
    ServerError: {
        errorCode: number;
        errorMessage: string;
        timestamp: string;
        tenantName: string;
        logoUrl: string;
        primaryColor: string;
    };
    Unauthorized: {
        message: string;
        tenantName: string;
        logoUrl: string;
        primaryColor: string;
    };
}

// Template configuration interface
interface TemplateConfig {
    type: TemplateType;
    path: string;
    data: TemplateData[keyof TemplateData];
}

class TemplateManager {
    private templates: Map<TemplateType, TemplateConfig>;

    constructor(data: any) {
        this.templates = new Map<TemplateType, TemplateConfig>();
        this.initializeTemplates(data);
    }

    private initializeTemplates(data: any): void {
        // RouteNotFound template configuration
        this.templates.set(TemplateType.RouteNotFound, {
            type: TemplateType.RouteNotFound,
            path: path.join('middleware', 'PageNotFound.hbs'),
            data: {
                method: data.method || '',
                path: data.path || '',
                timestamp: new Date().toLocaleString('en-IN', {timeZone: 'Asia/Kolkata'}),
                domainName: data.domainName || '',
                appName: data.appName || '',
                logoUrl: data.logoUrl || '',
            },
        });

        // API_DOCS template configuration
        this.templates.set(TemplateType.API_DOCS, {
            type: TemplateType.API_DOCS,
            path: path.join('apiDocs', 'apiDocs.hbs'),
            data: {
                apiDocs: data.apiDocs || [],
                appName: data.appName || 'EasyDine',
            },
        });

        this.templates.set(TemplateType.TEST_API, {
            type: TemplateType.TEST_API,
            path: path.join('apiDocs', 'apiTester.hbs'),
            data: {
                testApiData: data.testApiData || [],
                appName: data.appName || 'EasyDine',
            },
        });

        // ServerError template configuration
        this.templates.set(TemplateType.ServerError, {
            type: TemplateType.ServerError,
            path: path.join('middleware', 'ServerError.hbs'),
            data: {
                errorCode: 500,
                errorMessage: '',
                timestamp: new Date().toLocaleString('en-IN', {timeZone: 'Asia/Kolkata'}),
                tenantName: data.tenantName || '',
                logoUrl: data.logoUrl || '',
                primaryColor: data.primaryColor || '',
            },
        });

        // Unauthorized template configuration
        this.templates.set(TemplateType.Unauthorized, {
            type: TemplateType.Unauthorized,
            path: path.join('middleware', 'Unauthorized.hbs'),
            data: {
                message: '',
                tenantName: data.tenantName || '',
                logoUrl: data.logoUrl || '',
                primaryColor: data.tenantName || '',
            },
        });
    }

    // Get template configuration by type
    public getTemplate(type: TemplateType): TemplateConfig {
        const template = this.templates.get(type);
        if (!template) {
            throw new Error(`Template type ${type} not found`);
        }
        return template;
    }

    // Update template data for a specific type
    public updateTemplateData<T extends keyof TemplateData>(
        type: TemplateType,
        data: Partial<TemplateData[T]>
    ): TemplateConfig {
        const template = this.templates.get(type);
        if (!template) {
            throw new Error(`Template type ${type} not found`);
        }

        // Create new data object with updated values
        const updatedData = {
            ...template.data,
            ...data
        } as TemplateData[T];

        const updatedTemplate: TemplateConfig = {
            ...template,
            data: updatedData
        };

        this.templates.set(type, updatedTemplate);
        return updatedTemplate;
    }
}

// Export enum and manager
export {TemplateManager, TemplateType, TemplateData};