import log from "../helpers/system/logger.helper";
import {execSync} from "child_process"
import fs from "fs"
import {colorize} from "./serverBanner";

export const checkForUpdates = async () => {
    try {
        if (!fs.existsSync('.git')) {
            log.warn('[!] Not a git repository - skipping update check');
            return;
        }

        const currentBranch = execSync('git rev-parse --abbrev-ref HEAD', {encoding: 'utf8'}).trim();
        const currentCommit = execSync('git rev-parse HEAD', {encoding: 'utf8'}).trim().substring(0, 7);

        log.info('[📡] Checking for updates from repository...');
        execSync('git fetch origin', {stdio: 'pipe'});

        const remoteCommit = execSync(`git rev-parse origin/${currentBranch}`, {encoding: 'utf8'}).trim().substring(0, 7);

        if (currentCommit !== remoteCommit) {
            const commitsAhead = execSync(`git rev-list --count origin/${currentBranch}..HEAD`, {encoding: 'utf8'}).trim();
            const commitsBehind = execSync(`git rev-list --count HEAD..origin/${currentBranch}`, {encoding: 'utf8'}).trim();

            const ahead = parseInt(commitsAhead);
            const behind = parseInt(commitsBehind);

            if (behind > 0 && ahead === 0) {
                // We are behind - show update available
                showUpdateAvailable(currentCommit, remoteCommit, currentBranch, behind);
            } else if (ahead > 0 && behind === 0) {
                // We are ahead - show push reminder
                showPushReminder(currentCommit, remoteCommit, currentBranch, ahead);
            } else if (ahead > 0 && behind > 0) {
                // Branches have diverged - show diverged status
                showDivergedStatus(currentCommit, remoteCommit, currentBranch, ahead, behind);
            }

        } else {
            log.info(`[✓] Repository is up to date (${currentCommit})`);
        }

    } catch (error) {
        log.warn('[!] Could not check for updates:', (error as Error).message);
    }
};

const showUpdateAvailable = (currentCommit: string, remoteCommit: string, branch: string, behind: number) => {
    const latestCommitMsg = execSync(`git log origin/${branch} -1 --pretty=format:"%s"`, {encoding: 'utf8'}).trim();

    const title = '🚀 UPDATE AVAILABLE';
    const currentLine = `Current:  ${currentCommit} (${branch})`;
    const latestLine = `Latest:   ${remoteCommit} (${behind} commit${behind > 1 ? 's' : ''} behind)`;
    const messageLine = `Message:  ${latestCommitMsg.length > 50 ? latestCommitMsg.substring(0, 47) + '...' : latestCommitMsg}`;
    const actionLine = 'Run "git pull" to update to the latest version';

    displayBox([title, currentLine, latestLine, messageLine, actionLine], 'update');
};

const showPushReminder = (currentCommit: string, remoteCommit: string, branch: string, ahead: number) => {
    const latestLocalMsg = execSync(`git log -1 --pretty=format:"%s"`, {encoding: 'utf8'}).trim();

    const title = '📤 PUSH REMINDER';
    const currentLine = `Local:    ${currentCommit} (${branch})`;
    const remoteLine = `Remote:   ${remoteCommit} (${ahead} commit${ahead > 1 ? 's' : ''} ahead)`;
    const messageLine = `Message:  ${latestLocalMsg.length > 50 ? latestLocalMsg.substring(0, 47) + '...' : latestLocalMsg}`;
    const actionLine = 'Run "git push" to sync your changes to remote';

    displayBox([title, currentLine, remoteLine, messageLine, actionLine], 'push');
};

const showDivergedStatus = (currentCommit: string, remoteCommit: string, branch: string, ahead: number, behind: number) => {
    const title = '🔀 BRANCHES DIVERGED';
    const currentLine = `Local:    ${currentCommit} (${ahead} commit${ahead > 1 ? 's' : ''} ahead)`;
    const remoteLine = `Remote:   ${remoteCommit} (${behind} commit${behind > 1 ? 's' : ''} behind)`;
    const actionLine1 = 'Run "git pull --rebase" or "git pull" to sync';
    const actionLine2 = 'Then "git push" to update remote';

    displayBox([title, currentLine, remoteLine, actionLine1, actionLine2], 'diverged');
};

const displayBox = (lines: string[], type: 'update' | 'push' | 'diverged') => {
    const maxContentWidth = Math.max(...lines.map(line => line.length));
    const totalWidth = Math.max(maxContentWidth + 4, 50);

    const createLine = (content: string, align: 'center' | 'left' = 'left') => {
        const availableSpace = totalWidth - 4;
        let paddedContent: string;

        if (align === 'center') {
            const padding = Math.max(0, availableSpace - content.length);
            const leftPad = Math.floor(padding / 2);
            const rightPad = padding - leftPad;
            paddedContent = ' '.repeat(leftPad) + content + ' '.repeat(rightPad);
        } else {
            paddedContent = content + ' '.repeat(Math.max(0, availableSpace - content.length));
        }

        return `│ ${paddedContent} │`;
    };

    const topBorder = '┌' + '─'.repeat(totalWidth - 2) + '┐';
    const middleBorder = '├' + '─'.repeat(totalWidth - 2) + '┤';
    const bottomBorder = '└' + '─'.repeat(totalWidth - 2) + '┘';

    const borderColor = type === 'update' ? 'blue' : type === 'push' ? 'cyan' : 'magenta';
    const titleColor = type === 'update' ? 'green' : type === 'push' ? 'cyan' : 'magenta';
    const actionColor = type === 'update' ? 'green' : type === 'push' ? 'cyan' : 'magenta';

    console.log(colorize(topBorder, borderColor));
    console.log(colorize(createLine(lines[0], 'center'), titleColor));
    console.log(colorize(middleBorder, borderColor));

    for (let i = 1; i < lines.length - (type === 'diverged' ? 2 : 1); i++) {
        console.log(colorize(createLine(lines[i]), borderColor));
    }

    console.log(colorize(middleBorder, borderColor));

    if (type === 'diverged') {
        console.log(colorize(createLine(lines[lines.length - 2]), actionColor));
        console.log(colorize(createLine(lines[lines.length - 1]), actionColor));
    } else {
        console.log(colorize(createLine(lines[lines.length - 1]), actionColor));
    }

    console.log(colorize(bottomBorder, borderColor));
};