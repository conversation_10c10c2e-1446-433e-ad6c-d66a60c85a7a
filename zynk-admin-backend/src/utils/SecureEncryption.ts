import {webcrypto} from "crypto";

const ENCRYPTION_KEY_BASE64 = process.env.ENCRYPTION_SECRET_KEY!;
const IV_LENGTH = parseInt(process.env.IV_LENGTH || "12", 10);
const ALGORITHM = "AES-GCM";
const KEY_LENGTH = 256;

if (!ENCRYPTION_KEY_BASE64) {
    throw new Error("ENCRYPTION_SECRET_KEY environment variable is required");
}

if (IV_LENGTH < 12 || IV_LENGTH > 16) {
    throw new Error("IV_LENGTH must be between 12 and 16 bytes for AES-GCM");
}

export class SecureEncryption {
    private static cryptoKey: CryptoKey | null = null;

    private static async getCryptoKey(): Promise<CryptoKey> {
        if (!this.cryptoKey) {
            try {
                const keyBuffer = Buffer.from(ENCRYPTION_KEY_BASE64, "base64");

                if (keyBuffer.length !== 32) {
                    throw new Error("Encryption key must be exactly 32 bytes (256 bits)");
                }

                this.cryptoKey = await webcrypto.subtle.importKey(
                    "raw",
                    keyBuffer,
                    {name: ALGORITHM},
                    false,
                    ["encrypt", "decrypt"]
                );
            } catch (error) {
                throw new Error(`Failed to initialize encryption key: ${(error as any).message}`);
            }
        }
        return this.cryptoKey;
    }

    static async encrypt(plaintext: string): Promise<string> {
        try {
            const key = await this.getCryptoKey();
            const iv = webcrypto.getRandomValues(new Uint8Array(IV_LENGTH));
            const encoder = new TextEncoder();
            const data = encoder.encode(plaintext);

            const encrypted = await webcrypto.subtle.encrypt(
                {name: ALGORITHM, iv},
                key,
                data
            );

            const combined = new Uint8Array(iv.length + encrypted.byteLength);
            combined.set(iv, 0);
            combined.set(new Uint8Array(encrypted), iv.length);

            return "ENC_V2_" + Buffer.from(combined).toString("base64");
        } catch (error) {
            throw new Error(`Encryption failed: ${(error as any).message}`);
        }
    }

    static async decrypt(encryptedData: string): Promise<string> {
        try {
            if (!encryptedData.startsWith("ENC_V2_")) {
                throw new Error("Invalid encrypted data format");
            }

            const key = await this.getCryptoKey();
            const base64Data = encryptedData.replace("ENC_V2_", "");
            const combined = Buffer.from(base64Data, "base64");

            if (combined.length < IV_LENGTH) {
                throw new Error("Invalid encrypted data: too short");
            }

            const iv = combined.slice(0, IV_LENGTH);
            const encryptedBytes = combined.slice(IV_LENGTH);

            const decrypted = await webcrypto.subtle.decrypt(
                {name: ALGORITHM, iv},
                key,
                encryptedBytes
            );

            const decoder = new TextDecoder();
            return decoder.decode(decrypted);
        } catch (error) {
            throw new Error(`Decryption failed: ${(error as any).message}`);
        }
    }

    static isEncrypted(data: string): boolean {
        return data.startsWith("ENC_V2_");
    }
}
