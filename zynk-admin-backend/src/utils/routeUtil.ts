export const getRouteSpecificity = (pattern: string): number => {
    const segments = pattern.split('/').filter((s) => s);
    let score = segments.length * 10;
    if (!pattern.includes('*') && !pattern.includes(':')) score += 100;
    if (pattern.includes(':')) score -= 5;
    if (pattern.includes('*')) score -= 10;
    return score;
};

export const matchRoutePattern = (routePath: string, pattern: string): boolean => {
    // Escape regex special chars except * and :params
    const regexPattern = pattern
        .replace(/([.+?^${}()|[\]\\])/g, '\\$1') // escape regex special chars
        .replace(/\*/g, '.*')                     // wildcard * → .*
        .replace(/:([a-zA-Z]+)/g, '([^/]+)');    // params like :id → capture group

    // Make the slash before wildcard optional, so /foo/* matches /foo and /foo/bar
    const adjustedPattern = regexPattern.replace(/\/\.\*$/, '(\/.*)?');

    const regex = new RegExp(`^${adjustedPattern}$`);
    return regex.test(routePath);
};

