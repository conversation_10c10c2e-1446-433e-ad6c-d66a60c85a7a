export const buildWhereClause = (query: Record<string, any>): Record<string, any> => {
    const where: Record<string, any> = {};

    for (const [key, value] of Object.entries(query)) {
        if (value !== undefined && value !== null && value !== '') {
            if (typeof value === 'string' && value.toLowerCase() === 'true') {
                where[key] = true;
            } else if (typeof value === 'string' && value.toLowerCase() === 'false') {
                where[key] = false;
            } else if (typeof value === 'string' && !isNaN(Number(value))) {
                where[key] = Number(value);
            } else {
                where[key] = value;
            }
        }
    }

    return where;
};