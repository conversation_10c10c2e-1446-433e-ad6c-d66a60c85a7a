import {ObjectLiteral, Repository} from "typeorm";
import {ResponseType} from "../types/system";
import {FormatType} from "../types/pBAC/formatterTypes";

export type ResponseTypeConfig = Record<ResponseType, { select: string[], relations: string[] }>;

export const getRecords = async (repo: any, where: Record<string, any>, relation: string[] = []) => {
    try {
        const records = await repo.find({where: where, relation: relation});
        return {
            isArray: !(records.length === 1),
            isEmpty: records.length === 0,
            result: records.length === 1 ? records[0] : records
        }
    } catch (error) {
        throw error;
    }
}

export const getRecordsAd = async <T extends ObjectLiteral>(
    repo: Repository<T>,
    where: Record<string, any>,
    responseTypeConfig: ResponseTypeConfig,
    responseType: ResponseType = ResponseType.FULL
) => {
    try {
        const config = responseTypeConfig[responseType] || responseTypeConfig[ResponseType.FULL] || {
            select: [],
            relations: []
        };

        const records = await repo.find({
            where,
            select: config.select as any,
            relations: config.relations
        });
        return {
            isArray: !(records.length === 1),
            isEmpty: records.length === 0,
            result: records.length === 1 ? records[0] : records
        };
    } catch (error) {
        throw error;
    }
}

interface HasPermissionAction {
    getPermissionAction(formatType: FormatType): Promise<any>;
}

export const getRecordsForPermAction = async <T extends ObjectLiteral & HasPermissionAction>(
    repo: Repository<T>,
    where: Record<string, any>,
    key: string,
    formatType: FormatType = FormatType.OBJECT_ARR_OBJECT,
    responseType: ResponseType = ResponseType.FULL,
    responseTypeConfig: ResponseTypeConfig
) => {
    try {
        const config = responseTypeConfig[responseType] || responseTypeConfig[ResponseType.FULL] || {
            select: [],
            relations: []
        };

        const records = await repo.find({
            where,
            select: config.select as any,
            relations: config.relations
        });

        let result = {
            isArray: !(records.length === 1),
            isEmpty: records.length === 0,
            result: records.length === 1 ? records[0] : records
        };

        if (result.isArray && [ResponseType.FULL_WITH_ASSOCIATION, ResponseType.MINIMAL_WITH_ASSOCIATION].includes(responseType)) {
            result.result = await Promise.all(
                records.map(async (record) => ({
                    ...record,
                    [key]: (await record.getPermissionAction(formatType)).formattedPermissions
                }))
            );
        } else if (!result.isArray && [ResponseType.FULL_WITH_ASSOCIATION, ResponseType.MINIMAL_WITH_ASSOCIATION].includes(responseType)) {
            result.result = {
                ...result.result,
                [key]: (await (result.result as T).getPermissionAction(formatType)).formattedPermissions
            };
        }

        return result;
    } catch (error) {
        throw error;
    }
};