import * as fs from 'fs';
import * as yaml from 'js-yaml';
import log from '../helpers/system/logger.helper';

interface OpenAPISpec {
    openapi: string;
    info: any;
    servers?: any[];
    tags?: any[];
    paths: Record<string, any>;
    components?: any;
}

interface PathFile {
    [key: string]: any;
}

async function bundleOpenAPI(): Promise<void> {
    try {
        const mainSpecPath = 'src/openapi/swagger.yaml';
        const mainSpecContent = fs.readFileSync(mainSpecPath, 'utf8');
        const mainSpec = yaml.load(mainSpecContent) as OpenAPISpec;

        const bundledSpec: OpenAPISpec = {
            openapi: mainSpec.openapi,
            info: mainSpec.info,
            servers: mainSpec.servers,
            tags: mainSpec.tags,
            paths: {},
            components: {
                securitySchemes: {
                    cookieAuth: {
                        type: 'apiKey',
                        in: 'cookie',
                        name: 'access-token',
                        description: 'Access token stored in an HTTP-only cookie for authentication.'
                    },
                    bearerAuth: {
                        type: 'http',
                        scheme: 'bearer',
                        bearerFormat: 'JWT',
                        description: 'Bearer token (JWT) passed in the Authorization header.'
                    }
                }
            }
        };

        const pathFiles = [
            'src/openapi/paths/admin/superAdmin.yaml',
            'src/openapi/paths/admin/subscription.yaml',
            'src/openapi/paths/pBAC/pBACAction.yaml',
            'src/openapi/paths/pBAC/pBACModule.yaml',
        ];

        // Read and merge each path file
        for (const pathFile of pathFiles) {
            if (fs.existsSync(pathFile)) {
                const pathContent = fs.readFileSync(pathFile, 'utf8');
                const pathData = yaml.load(pathContent) as PathFile;

                // Merge paths
                Object.assign(bundledSpec.paths, pathData);

                log.info(`✓ Merged paths from ${pathFile}`);
            } else {
                console.warn(`⚠ Path file not found: ${pathFile}`);
            }
        }

        const outputPath = 'src/openapi/swagger.json';
        fs.writeFileSync(outputPath, JSON.stringify(bundledSpec, null, 2));

        log.info(`✅ Successfully bundled OpenAPI spec to ${outputPath}`);
        log.info(`📊 Total paths: ${Object.keys(bundledSpec.paths).length}`);

    } catch (error: any) {
        log.error('❌ Error bundling OpenAPI spec:', error.message);
        process.exit(1);
    }
}

bundleOpenAPI();