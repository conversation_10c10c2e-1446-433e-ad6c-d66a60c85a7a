#!/usr/bin/env node

import { DataSource, QueryRunner } from 'typeorm';
import * as readline from 'readline';
import { getAllTenantModels } from '../config/entity-config';
import { SeedTemplateDB } from '../seeds/templateSeeds/seedTemplateDB';

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

/**
 * Promisified readline question
 */
const askQuestion = (question: string): Promise<string> => {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
};

/**
 * Create datasource for tenant database
 */
const createTenantDataSource = (databaseName: string): DataSource => {
    return new DataSource({
        type: 'postgres',
        host: process.env.DB_HOSTNAME || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        username: process.env.POSTGRES_USER || 'postgres',
        password: process.env.POSTGRES_PASSWORD || 'password',
        database: databaseName,
        entities: getAllTenantModels(),
        synchronize: false,
        logging: false,
        // Add any other configuration options you need
    });
};

/**
 * Seed a specific tenant database
 */
const seedTenantDatabase = async (databaseName: string): Promise<void> => {
    let dataSource: DataSource | null = null;
    let queryRunner: QueryRunner | null = null;

    try {
        console.log(`🔄 Connecting to database: ${databaseName}`);
        
        // Create and initialize datasource
        dataSource = createTenantDataSource(databaseName);
        await dataSource.initialize();
        
        console.log(`✅ Connected to database: ${databaseName}`);
        
        // Create query runner
        queryRunner = dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        
        console.log(`🌱 Starting seeding process for: ${databaseName}`);
        
        // Run the seeding using the same method as template
        await SeedTemplateDB(queryRunner);
        
        // Commit transaction
        await queryRunner.commitTransaction();
        
        console.log(`✅ Successfully seeded database: ${databaseName}`);
        
    } catch (error) {
        console.error(`❌ Error seeding database ${databaseName}:`, error);
        
        // Rollback transaction if it exists
        if (queryRunner && queryRunner.isTransactionActive) {
            try {
                await queryRunner.rollbackTransaction();
                console.log(`🔄 Transaction rolled back for: ${databaseName}`);
            } catch (rollbackError) {
                console.error(`❌ Error rolling back transaction:`, rollbackError);
            }
        }
        
        throw error;
    } finally {
        // Clean up resources
        if (queryRunner) {
            try {
                await queryRunner.release();
            } catch (releaseError) {
                console.error(`❌ Error releasing query runner:`, releaseError);
            }
        }
        
        if (dataSource && dataSource.isInitialized) {
            try {
                await dataSource.destroy();
            } catch (destroyError) {
                console.error(`❌ Error destroying datasource:`, destroyError);
            }
        }
    }
};

/**
 * Validate database name
 */
const validateDatabaseName = (dbName: string): boolean => {
    // Basic validation for database name
    const dbNameRegex = /^[a-zA-Z0-9_]+$/;
    return dbNameRegex.test(dbName) && dbName.length > 0 && dbName.length <= 63;
};

/**
 * Check if database exists
 */
const checkDatabaseExists = async (databaseName: string): Promise<boolean> => {
    let adminDataSource: DataSource | null = null;
    
    try {
        // Connect to postgres database to check if target database exists
        adminDataSource = new DataSource({
            type: 'postgres',
            host: process.env.DB_HOSTNAME || 'localhost',
            port: parseInt(process.env.DB_PORT || '5432'),
            username: process.env.POSTGRES_USER || 'postgres',
            password: process.env.POSTGRES_PASSWORD || 'password',
            database: 'postgres', // Connect to postgres database to check others
        });
        
        await adminDataSource.initialize();
        
        const result = await adminDataSource.query(
            'SELECT 1 FROM pg_database WHERE datname = $1',
            [databaseName]
        );
        
        return result.length > 0;
        
    } catch (error) {
        console.error(`❌ Error checking database existence:`, error);
        return false;
    } finally {
        if (adminDataSource && adminDataSource.isInitialized) {
            await adminDataSource.destroy();
        }
    }
};

/**
 * Main function
 */
const main = async (): Promise<void> => {
    try {
        console.log('🚀 Tenant Database Seeding Script');
        console.log('===================================');
        
        // Get database name from user
        const databaseName = await askQuestion('Enter the tenant database name to seed: ');
        
        // Validate database name
        if (!validateDatabaseName(databaseName)) {
            console.error('❌ Invalid database name. Please use only alphanumeric characters and underscores.');
            process.exit(1);
        }
        
        // Check if database exists
        console.log(`🔍 Checking if database "${databaseName}" exists...`);
        const dbExists = await checkDatabaseExists(databaseName);
        
        if (!dbExists) {
            console.error(`❌ Database "${databaseName}" does not exist.`);
            process.exit(1);
        }
        
        // Confirm seeding
        const confirm = await askQuestion(`⚠️  This will seed the database "${databaseName}". Are you sure? (y/N): `);
        
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
            console.log('❌ Seeding cancelled.');
            process.exit(0);
        }
        
        // Perform seeding
        await seedTenantDatabase(databaseName);
        
        console.log('🎉 Seeding completed successfully!');
        
    } catch (error) {
        console.error('❌ Script failed:', error);
        process.exit(1);
    } finally {
        rl.close();
    }
};

// Handle script termination
process.on('SIGINT', () => {
    console.log('\n👋 Script interrupted. Cleaning up...');
    rl.close();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 Script terminated. Cleaning up...');
    rl.close();
    process.exit(0);
});

// Run the script
if (require.main === module) {
    main().catch((error) => {
        console.error('❌ Unhandled error:', error);
        process.exit(1);
    });
}

export { seedTenantDatabase, createTenantDataSource };