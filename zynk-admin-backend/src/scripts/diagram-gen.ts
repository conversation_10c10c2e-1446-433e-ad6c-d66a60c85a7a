#!/usr/bin/env node

import * as fs from 'fs';
import * as path from 'path';
import { DataSource, EntityMetadata } from 'typeorm';
import { getAllSuperAdminModels, getAllTenantModels } from '../config/entity-config';

interface EntityInfo {
  name: string;
  tableName: string;
  columns: ColumnInfo[];
  relations: RelationInfo[];
  database?: string;
}

interface ColumnInfo {
  name: string;
  type: string;
  isPrimary?: boolean;
  isNullable?: boolean;
  isUnique?: boolean;
  length?: number;
  default?: string;
  isGenerated?: boolean;
}

interface RelationInfo {
  type: 'one-to-one' | 'one-to-many' | 'many-to-one' | 'many-to-many';
  target: string;
  propertyName: string;
  joinColumn?: string;
  inverseProperty?: string;
}

class TypeORMERDiagramGenerator {
  private superAdminEntities: EntityInfo[] = [];
  private tenantEntities: EntityInfo[] = [];
  private allEntities: EntityInfo[] = [];

  constructor(
    private superAdminModels: any[],
    private tenantModels: any[],
    private outputDir: string = 'diagrams'
  ) {}

  async generateDiagram(): Promise<void> {
    console.log('🔍 Processing TypeORM entities from model arrays...');
    
    // Create temporary data sources to extract metadata
    await this.extractMetadataFromModels();
    
    console.log(`📊 Found ${this.allEntities.length} total entities`);
    console.log(`🏢 SuperAdmin entities: ${this.superAdminEntities.length}`);
    console.log(`🏬 Tenant entities: ${this.tenantEntities.length}`);
    
    // Log found entities for debugging
    console.log('\n🏢 SuperAdmin entities found:');
    this.superAdminEntities.forEach(e => console.log(`  - ${e.name} (${e.tableName})`));
    console.log('\n🏬 Tenant entities found:');
    this.tenantEntities.forEach(e => console.log(`  - ${e.name} (${e.tableName})`));
    
    // Generate separate diagrams for each database
    await this.generateSeparateDiagrams();
    
    console.log('✅ ER Diagrams generated successfully!');
  }

  private async extractMetadataFromModels(): Promise<void> {
    // Process SuperAdmin models
    if (this.superAdminModels.length > 0) {
      console.log('📋 Processing SuperAdmin models...');
      const superAdminDataSource = new DataSource({
        type: 'postgres',
        host: process.env.DB_HOSTNAME,
        port: parseInt(process.env.DB_PORT!) || 5434,
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.SUPERADMIN_DB,
        entities: this.superAdminModels,
        synchronize: false,
      });

      try {
        await superAdminDataSource.initialize();
        
        for (const entityMetadata of superAdminDataSource.entityMetadatas) {
          const entityInfo = this.extractEntityInfo(entityMetadata, 'SuperAdmin');
          this.superAdminEntities.push(entityInfo);
          this.allEntities.push(entityInfo);
        }
        
        await superAdminDataSource.destroy();
      } catch (error) {
        console.warn('⚠️ Could not initialize SuperAdmin DataSource, falling back to direct metadata extraction');
        this.extractDirectMetadata(this.superAdminModels, 'SuperAdmin');
      }
    }

    // Process Tenant models
    if (this.tenantModels.length > 0) {
      console.log('📋 Processing Tenant models...');
      const tenantDataSource = new DataSource({
        type: 'postgres',
        host: process.env.DB_HOSTNAME,
        port: parseInt(process.env.DB_PORT!) || 5434,
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: "tenant_template",
        entities: this.tenantModels,
        synchronize: false,
      });

      try {
        await tenantDataSource.initialize();
        
        for (const entityMetadata of tenantDataSource.entityMetadatas) {
          const entityInfo = this.extractEntityInfo(entityMetadata, 'Tenant');
          this.tenantEntities.push(entityInfo);
          this.allEntities.push(entityInfo);
        }
        
        await tenantDataSource.destroy();
      } catch (error) {
        console.warn('⚠️ Could not initialize Tenant DataSource, falling back to direct metadata extraction');
        this.extractDirectMetadata(this.tenantModels, 'Tenant');
      }
    }
  }

  private extractDirectMetadata(models: any[], database: string): void {
    console.log(`📋 Extracting metadata directly from ${database} models...`);
    
    for (const model of models) {
      try {
        const entityInfo: EntityInfo = {
          name: model.name || 'Unknown',
          tableName: this.getTableName(model),
          columns: this.getColumnsFromModel(model),
          relations: this.getRelationsFromModel(model),
          database
        };

        if (database === 'SuperAdmin') {
          this.superAdminEntities.push(entityInfo);
        } else {
          this.tenantEntities.push(entityInfo);
        }
        this.allEntities.push(entityInfo);
        
        console.log(`  ✅ Processed ${entityInfo.name} (${entityInfo.columns.length} columns, ${entityInfo.relations.length} relations)`);
      } catch (error) {
        console.warn(`  ❌ Failed to process model ${model.name}:`, error);
      }
    }
  }

  private getTableName(model: any): string {
    // Try to get table name from various possible metadata locations
    if (model.prototype && model.prototype.constructor) {
      const metadata = Reflect.getMetadata('__tableName__', model.prototype.constructor) ||
                      Reflect.getMetadata('entity:table_name', model.prototype.constructor) ||
                      Reflect.getMetadata('entity', model.prototype.constructor);
      
      if (metadata) {
        if (typeof metadata === 'string') return metadata;
        if (metadata.name) return metadata.name;
        if (metadata.tableName) return metadata.tableName;
      }
    }
    
    // Fallback to converting class name to snake_case
    return this.camelToSnakeCase(model.name || 'unknown');
  }

  private getColumnsFromModel(model: any): ColumnInfo[] {
    const columns: ColumnInfo[] = [];
    
    if (!model.prototype) return columns;
    
    // Get all property names
    const propertyNames = Object.getOwnPropertyNames(model.prototype);
    
    for (const propName of propertyNames) {
      if (propName === 'constructor') continue;
      
      try {
        // Try to get column metadata
        const columnMetadata = Reflect.getMetadata('entity:column', model.prototype, propName) ||
                              Reflect.getMetadata('design:type', model.prototype, propName);
        
        const isPrimary = Reflect.getMetadata('entity:primary', model.prototype, propName) ||
                         Reflect.getMetadata('entity:primary_generated', model.prototype, propName);
        
        const isGenerated = Reflect.getMetadata('entity:generated', model.prototype, propName);
        
        if (columnMetadata || isPrimary) {
          columns.push({
            name: propName,
            type: this.getTypeString(columnMetadata),
            isPrimary: !!isPrimary,
            isGenerated: !!isGenerated,
            isNullable: this.getColumnNullable(model.prototype, propName),
            isUnique: this.getColumnUnique(model.prototype, propName)
          });
        }
      } catch (error) {
        // Silently continue if we can't get metadata for this property
      }
    }
    
    // If no columns found through metadata, add some common ones
    if (columns.length === 0) {
      columns.push(
        { name: 'id', type: 'uuid', isPrimary: true, isGenerated: true },
        { name: 'createdAt', type: 'timestamptz' },
        { name: 'updatedAt', type: 'timestamptz' }
      );
    }
    
    return columns;
  }

  private cleanTypeName(type: any): string {
    // Handle null, undefined, or non-string types
    if (!type) return 'text';
    
    // Convert to string if it's not already
    const typeStr = String(type);
    
    // Remove function declarations and native code references
    if (typeStr.includes('function') && typeStr.includes('[native code]')) {
      // Extract just the function name
      const match = typeStr.match(/function\s+(\w+)/);
      if (match) {
        const typeName = match[1].toLowerCase();
        const typeMapping: { [key: string]: string } = {
          'string': 'text',
          'number': 'int',
          'date': 'timestamptz',
          'boolean': 'boolean'
        };
        return typeMapping[typeName] || typeName;
      }
    }
    
    // Clean up other problematic characters
    return typeStr
      .replace(/function\s+\w+\(\)\s*\{\s*\[native code\]\s*\}/g, 'text')
      .replace(/[{}()\[\]]/g, '')
      .trim() || 'text';
  }

  private getRelationsFromModel(model: any): RelationInfo[] {
    const relations: RelationInfo[] = [];
    
    if (!model.prototype) return relations;
    
    const propertyNames = Object.getOwnPropertyNames(model.prototype);
    
    for (const propName of propertyNames) {
      if (propName === 'constructor') continue;
      
      try {
        const relationMetadata = Reflect.getMetadata('entity:relation', model.prototype, propName);
        
        if (relationMetadata) {
          relations.push({
            type: relationMetadata.type || 'many-to-one',
            target: relationMetadata.target?.name || 'Unknown',
            propertyName: propName,
            joinColumn: relationMetadata.joinColumn,
            inverseProperty: relationMetadata.inverseSide
          });
        }
      } catch (error) {
        // Silently continue
      }
    }
    
    return relations;
  }

  private getTypeString(columnMetadata: any): string {
    if (!columnMetadata) return 'string';
    
    if (typeof columnMetadata === 'function') {
      const typeName = columnMetadata.name.toLowerCase();
      
      const typeMapping: { [key: string]: string } = {
        'string': 'text',
        'number': 'int',
        'date': 'timestamp',
        'boolean': 'boolean',
        'object': 'json',
        'array': 'json'
      };
      
      return typeMapping[typeName] || typeName;
    }
    
    if (columnMetadata.type) {
      return String(columnMetadata.type);
    }
    
    return 'string';
  }

  private getColumnNullable(prototype: any, propName: string): boolean {
    const nullable = Reflect.getMetadata('entity:nullable', prototype, propName);
    return nullable !== false; // Default to true if not specified
  }

  private getColumnUnique(prototype: any, propName: string): boolean {
    const unique = Reflect.getMetadata('entity:unique', prototype, propName);
    return !!unique;
  }

  private camelToSnakeCase(str: string): string {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase().replace(/^_/, '');
  }

  private extractEntityInfo(metadata: EntityMetadata, database: string): EntityInfo {
    const columns: ColumnInfo[] = metadata.columns.map(column => ({
      name: column.propertyName,
      type: column.type as string,
      isPrimary: column.isPrimary,
      isNullable: column.isNullable,
      isUnique: metadata.uniques.some(u => u.columns.some(c => c.propertyName === column.propertyName)),
      length: column.length as unknown as number,
      default: column.default as string,
      isGenerated: column.isGenerated
    }));

    const relations: RelationInfo[] = metadata.relations.map(relation => ({
      type: relation.relationType as any,
      target: relation.inverseEntityMetadata.name,
      propertyName: relation.propertyName,
      joinColumn: relation.joinColumns?.[0]?.databaseName,
      inverseProperty: relation.inverseSidePropertyPath
    }));

    return {
      name: metadata.name,
      tableName: metadata.tableName,
      columns,
      relations,
      database
    };
  }

  private async generateSeparateDiagrams(): Promise<void> {
    // Ensure output directory exists
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }

    // Generate SuperAdmin database diagram
    if (this.superAdminEntities.length > 0) {
      await this.generateSchemaSpecificDiagrams('SuperAdmin', this.superAdminEntities);
    }
    
    // Generate Tenant database diagram
    if (this.tenantEntities.length > 0) {
      await this.generateSchemaSpecificDiagrams('Tenant', this.tenantEntities);
    }

    // Generate combined diagram
    if (this.allEntities.length > 0) {
      await this.generateSchemaSpecificDiagrams('Combined', this.allEntities);
    }
  }

  private async generateSchemaSpecificDiagrams(schemaName: string, entities: EntityInfo[]): Promise<void> {
    const prefix = schemaName.toLowerCase();
    
    // Mermaid
    await this.generateMermaidDiagramForSchema(
      entities, 
      path.join(this.outputDir, `${prefix}-er-diagram.mmd`), 
      schemaName
    );
    
    // PlantUML
    await this.generatePlantUMLDiagramForSchema(
      entities, 
      path.join(this.outputDir, `${prefix}-er-diagram.puml`), 
      schemaName
    );

    // Markdown documentation
    await this.generateMarkdownDocumentation(
      entities,
      path.join(this.outputDir, `${prefix}-schema-docs.md`),
      schemaName
    );
  }

  private async generateMermaidDiagramForSchema(entities: EntityInfo[], filename: string, schemaName: string): Promise<void> {
    const mermaidCode = this.generateMermaidCodeForEntities(entities, schemaName);
    fs.writeFileSync(filename, mermaidCode);
    console.log(`📄 ${schemaName} Mermaid diagram saved to ${filename}`);
  }

  private async generatePlantUMLDiagramForSchema(entities: EntityInfo[], filename: string, schemaName: string): Promise<void> {
    let plantUMLCode = `@startuml\n!theme sketchy\nskinparam linetype ortho\nskinparam roundcorner 10\n`;
    plantUMLCode += `title ${schemaName} Database Schema\n\n`;

    // Add entities
    entities.forEach(entity => {
      plantUMLCode += `entity "${entity.name}" as ${entity.name} {\n`;
      entity.columns.forEach(column => {
        const key = column.isPrimary ? '🔑 ' : '  ';
        const generated = column.isGenerated ? ' (auto)' : '';
        const nullable = column.isNullable ? ' (nullable)' : '';
        const unique = column.isUnique ? ' (unique)' : '';
        plantUMLCode += `${key}${column.name} : ${column.type}${generated}${nullable}${unique}\n`;
      });
      plantUMLCode += '}\n\n';
    });

    // Add relationships
    entities.forEach(entity => {
      entity.relations.forEach(relation => {
        const targetExists = entities.some(e => e.name === relation.target);
        if (targetExists) {
          const relationSymbol = this.getPlantUMLRelationSymbol(relation.type);
          plantUMLCode += `${entity.name} ${relationSymbol} ${relation.target} : ${relation.propertyName}\n`;
        }
      });
    });

    plantUMLCode += '\n@enduml';
    fs.writeFileSync(filename, plantUMLCode);
    console.log(`📄 ${schemaName} PlantUML diagram saved to ${filename}`);
  }

  private async generateMarkdownDocumentation(entities: EntityInfo[], filename: string, schemaName: string): Promise<void> {
    let markdown = `# ${schemaName} Database Schema Documentation\n\n`;
    markdown += `Generated on: ${new Date().toISOString()}\n\n`;
    markdown += `## Overview\n\nThis document describes the ${schemaName} database schema with ${entities.length} entities.\n\n`;

    // Table of contents
    markdown += `## Table of Contents\n\n`;
    entities.forEach(entity => {
      markdown += `- [${entity.name}](#${entity.name.toLowerCase()})\n`;
    });
    markdown += '\n';

    // Entity details
    entities.forEach(entity => {
      markdown += `## ${entity.name}\n\n`;
      markdown += `**Table Name:** \`${entity.tableName}\`\n\n`;
      
      if (entity.columns.length > 0) {
        markdown += `### Columns\n\n`;
        markdown += `| Column | Type | Primary | Nullable | Unique | Generated |\n`;
        markdown += `|--------|------|---------|----------|--------|-----------|\n`;
        
        entity.columns.forEach(column => {
          markdown += `| ${column.name} | ${column.type} | ${column.isPrimary ? '✓' : ''} | ${column.isNullable ? '✓' : ''} | ${column.isUnique ? '✓' : ''} | ${column.isGenerated ? '✓' : ''} |\n`;
        });
        markdown += '\n';
      }

      if (entity.relations.length > 0) {
        markdown += `### Relations\n\n`;
        markdown += `| Property | Type | Target | Join Column |\n`;
        markdown += `|----------|------|--------|--------------|\n`;
        
        entity.relations.forEach(relation => {
          markdown += `| ${relation.propertyName} | ${relation.type} | ${relation.target} | ${relation.joinColumn || ''} |\n`;
        });
        markdown += '\n';
      }
    });

    fs.writeFileSync(filename, markdown);
    console.log(`📄 ${schemaName} documentation saved to ${filename}`);
  }

  private generateMermaidCodeForEntities(entities: EntityInfo[], schemaName?: string): string {
    let mermaidCode = 'erDiagram\n';
    
    if (schemaName) {
      mermaidCode += `    %% ${schemaName} Database Schema\n`;
    }

    // Add entities
    entities.forEach(entity => {
      mermaidCode += `    ${entity.name} {\n`;
      entity.columns.forEach(column => {
        // Clean the type name to avoid parsing errors
        const type = this.cleanTypeName(column.type);
        const key = column.isPrimary ? ' PK' : '';
        
        // Combine all attributes into a single string to avoid multiple quoted attributes
        const attributes: string[] = [];
        
        if (column.isGenerated) attributes.push('auto');
        if (column.isNullable && !column.isPrimary) attributes.push('nullable');
        if (column.isUnique) attributes.push('unique');
        
        const attributeString = attributes.length > 0 ? ` "${attributes.join(', ')}"` : '';
        
        mermaidCode += `        ${type} ${column.name}${key}${attributeString}\n`;
      });
      mermaidCode += '    }\n\n';
    });

    // Add relationships
    entities.forEach(entity => {
      entity.relations.forEach(relation => {
        const targetExists = entities.some(e => e.name === relation.target);
        if (targetExists) {
          const relationSymbol = this.getMermaidRelationSymbol(relation.type);
          mermaidCode += `    ${entity.name} ${relationSymbol} ${relation.target} : ${relation.propertyName}\n`;
        }
      });
    });

    return mermaidCode;
  }

  private getMermaidRelationSymbol(relationType: string): string {
    switch (relationType) {
      case 'one-to-one': return '||--||';
      case 'one-to-many': return '||--o{';
      case 'many-to-one': return '}o--||';
      case 'many-to-many': return '}o--o{';
      default: return '--';
    }
  }

  private getPlantUMLRelationSymbol(relationType: string): string {
    switch (relationType) {
      case 'one-to-one': return '||--||';
      case 'one-to-many': return '||--o{';
      case 'many-to-one': return '}o--||';
      case 'many-to-many': return '}o--o{';
      default: return '--';
    }
  }
}

export { TypeORMERDiagramGenerator };

export async function generateDiagrams(
  superAdminModels: any[],
  tenantModels: any[],
  outputDir: string = 'diagrams'
): Promise<void> {
  const generator = new TypeORMERDiagramGenerator(superAdminModels, tenantModels, outputDir);
  await generator.generateDiagram();
}

async function main() {
  try {
    require('dotenv').config()
    console.log('🚀 Starting ER diagram generation...');
    
    const superAdminModels = getAllSuperAdminModels();
    const tenantModels = getAllTenantModels();
    
    console.log(`📊 SuperAdmin models: ${superAdminModels.length}`);
    console.log(`📊 Tenant models: ${tenantModels.length}`);
    
    // Generate diagrams
    await generateDiagrams(
      superAdminModels,
      tenantModels,
      'diagrams' // Output directory
    );
    
    console.log('🎉 All diagrams generated successfully!');
    console.log('Check the diagrams directory for:');
    console.log('  - superadmin-er-diagram.mmd (Mermaid)');
    console.log('  - superadmin-er-diagram.puml (PlantUML)');
    console.log('  - superadmin-schema-docs.md (Documentation)');
    console.log('  - tenant-er-diagram.mmd (Mermaid)');
    console.log('  - tenant-er-diagram.puml (PlantUML)');
    console.log('  - tenant-schema-docs.md (Documentation)');
    console.log('  - combined-er-diagram.mmd (All entities)');
    console.log('  - combined-er-diagram.puml (All entities)');
    console.log('  - combined-schema-docs.md (All entities)');
    
  } catch (error) {
    console.error('❌ Error generating diagrams:', error);
    process.exit(1);
  }
}

main()