import * as readline from "readline";
import {DataSource} from "typeorm";
import {baseConfig} from "../config/data-source";
import {getAllTenantModels} from "../config/entity-config";
import {seedDishAddons} from "../seeds/templateSeeds/addon.seed";
import {seedAllergies} from "../seeds/templateSeeds/allergy.seed";
import {seedAllergyColors} from "../seeds/templateSeeds/allergyColor.seed";
import {seedDishExtras} from "../seeds/templateSeeds/extra.seed";
import {seedIngredients} from "../seeds/templateSeeds/ingredient.seed";
import {seedSectionIcons} from "../seeds/templateSeeds/sectionIcon.seed";
import {seedPrimaryBranch} from "../seeds/templateSeeds/primaryBranch.seed";
import log from "../helpers/system/logger.helper";
import { seedOptionalFoodMenu } from "../seeds/templateSeeds/optional/foodMenu.seed";

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
});

function promptUser(question: string): Promise<string> {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

async function updateSeeds() {
    try {
        const whereToRun = await promptUser(
            "Where do you want to run the seeds? (tenant/template): "
        );

        if (whereToRun !== "tenant" && whereToRun !== "template") {
            log.error("Invalid input. Please enter 'tenant' or 'template'.");
            rl.close();
            return;
        }

        let tenantName = "";

        if (whereToRun === "tenant") {
            tenantName = await promptUser(
                "Enter the tenant name (e.g., 'tenant1'): "
            );
            if (!tenantName) {
                log.error("Tenant name cannot be empty.");
                rl.close();
                return;
            }
            log.info(`Running seeds for tenant: ${tenantName}`);
        }

        if (whereToRun === "template") {
            tenantName = "tenant_template";
            log.info("Running seeds for template database");
        }

        const TenantDS = new DataSource({
            ...baseConfig,
            database: tenantName,
            synchronize: process.env.MIGRATION_TOGGLE === "false",
            entities: getAllTenantModels(),
        });

        if (!TenantDS.isInitialized) {
            await TenantDS.initialize();
        }

        const queryRunner = TenantDS.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        // Add new seeds here
        await seedPrimaryBranch(queryRunner)
        // await seedDishAddons(queryRunner);
        // await seedAllergies(queryRunner);
        // await seedAllergyColors(queryRunner);
        // await seedDishExtras(queryRunner);
        // await seedIngredients(queryRunner);
        // await seedSectionIcons(queryRunner);
        await seedOptionalFoodMenu(queryRunner);

        await queryRunner.commitTransaction();
        await queryRunner.release();

        log.info("Seeds updated successfully!");
        process.exit(0)
    } catch (error: any) {
        log.error("Error updating seeds in database", error.message);
        process.exit(1);
    }
}

updateSeeds();
