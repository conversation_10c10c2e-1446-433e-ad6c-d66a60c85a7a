#!/usr/bin/env node
import * as readline from "readline";
import {
  createMigrationFile,
  runSuperAdminMigrations,
  runTenantMigrations,
  revertLastMigration,
  generateMigration,
} from "../migrations/MigrationManager";
import { SuperAdminDS } from "../config/data-source";

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function promptUser(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  console.log("=== TypeORM Migration CLI ===");
  console.log("1. Create empty migration");
  console.log("2. Generate migration from schema changes");
  console.log("3. Run pending migrations");
  console.log("4. Revert last migration");
  console.log("5. Exit");

  const choice = await promptUser("\nEnter your choice (1-5): ");

  switch (choice) {
    case "1": {
      const type = await promptUser(
        "Select database type (superadmin/tenant): "
      );
      if (type !== "superadmin" && type !== "tenant") {
        console.error(
          'Invalid database type. Must be "superadmin" or "tenant"'
        );
        break;
      }

      const name = await promptUser("Enter migration name: ");

      let tenantName: string | undefined;
      if (type === "tenant") {
        tenantName = await promptUser("Enter tenant database name: ");
      }

      createMigrationFile(name, type as "superadmin" | "tenant");
      break;
    }
    case "2": {
      let type = await promptUser(
        "Select database type (superadmin/tenant/template): "
      );
      if (type !== "superadmin" && type !== "tenant" && type !== "template") {
        console.error(
          'Invalid database type. Must be "superadmin" or "tenant"'
        );
        break;
      }

      const name = await promptUser("Enter migration name: ");

      let tenantName: string | undefined;
      if (type === "tenant") {
        tenantName = await promptUser("Enter tenant database name: ");
      }

      if (type === "template") {
        type = "tenant";
        tenantName = "tenant_template";
      }

      await generateMigration(
        name,
        type as "superadmin" | "tenant",
        tenantName
      );
      break;
    }
    case "3": {
      const type = await promptUser(
        "Select database type (superadmin/tenant/template/all): "
      );

      if (type === "superadmin") {
        await runSuperAdminMigrations();
      } else if (type === "tenant") {
        const tenantName = await promptUser("Enter tenant database name: ");
        await runTenantMigrations(tenantName);
      } else if (type === "template") {
        await runTenantMigrations("tenant_template");
      }
      else if (type === "all") {
        await runSuperAdminMigrations();
        await runTenantMigrations("tenant_template");

        try {
          await SuperAdminDS.initialize();

          const tenants = await SuperAdminDS.query(`
            SELECT subDomain FROM tenants
          `);

          for (const tenant of tenants) {
            await runTenantMigrations(tenant.subDomain);
          }
        } catch (error) {
          console.error("Error running migrations for all tenants:", error);
        } finally {
          if (SuperAdminDS.isInitialized) {
            await SuperAdminDS.destroy();
          }
        }
      } else {
        console.error(
          'Invalid database type. Must be "superadmin", "tenant", "template" or "all"'
        );
      }
      break;
    }
    case "4": {
      let type = await promptUser(
        "Select database type (superadmin/tenant/template): "
      );
      if (type !== "superadmin" && type !== "tenant" && type !== "template") {
        console.error(
          'Invalid database type. Must be "superadmin" or "tenant"'
        );
        break;
      }

      let tenantName: string | undefined;
      if (type === "tenant") {
        tenantName = await promptUser("Enter tenant database name: ");
      }

      if (type === "template") {
        type = "tenant";
        tenantName = "tenant_template";
      }

      await revertLastMigration(type as "superadmin" | "tenant", tenantName);
      break;
    }
    case "5":
      console.log("Exiting...");
      rl.close();
      return;
    default:
      console.error("Invalid choice");
  }

  rl.close();
}

main().catch((error) => {
  console.error("An error occurred:", error);
  process.exit(1);
});
