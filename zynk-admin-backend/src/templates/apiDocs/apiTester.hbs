<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Tester - {{appName}}</title>
    <script src="https://cdn.jsdelivr.net/npm/zod@4/lib/xai"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #64748b;
            font-size: 16px;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .request-container {
            display: grid;
            grid-template-columns: 1fr 3fr 100px;
            gap: 16px;
            align-items: center;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
            font-size: 14px;
        }

        .form-control {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: #f8fafc;
            height: 44px;
        }

        .form-control[readonly] {
            background: #e5e7eb;
            cursor: not-allowed;
        }

        .form-control:disabled {
            background: #e5e7eb;
            color: #9ca3af;
            cursor: not-allowed;
        }

        textarea.form-control {
            height: auto;
            resize: vertical;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            height: 44px;
            width: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:disabled {
            background: #d1d5db;
            color: #9ca3af;
            cursor: not-allowed;
            box-shadow: none;
        }

        .btn-secondary {
            background: #f8fafc;
            color: #475569;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }

        .btn-secondary:disabled {
            background: #e5e7eb;
            color: #9ca3af;
            border-color: #d1d5db;
            cursor: not-allowed;
        }

        .tab-container {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 16px;
        }

        .tab {
            padding: 12px 24px;
            font-weight: 600;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tab.active {
            color: #374151;
            border-bottom: 2px solid #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .key-value-container {
            display: grid;
            grid-template-columns: 1fr 1fr 50px 50px;
            gap: 8px;
            margin-bottom: 8px;
            align-items: center;
        }

        .response-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .response-title {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
        }

        .response-stats {
            font-size: 14px;
            color: #666;
            font-weight: bold;
        }

        .code-block {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            color: #1f2937;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .response-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .response-table th,
        .response-table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: left;
        }

        .response-table th {
            background: #f1f5f9;
            font-weight: 600;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-error {
            background: #fecaca;
            color: #dc2626;
        }

        .method-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 44px;
            height: 44px;
            text-align: center;
        }

        .method-get {
            background: #dcfce7;
            color: #166534;
        }

        .method-post {
            background: #dbeafe;
            color: #1e40af;
        }

        .method-put {
            background: #fef3c7;
            color: #92400e;
        }

        .method-delete {
            background: #fecaca;
            color: #dc2626;
        }

        .method-patch {
            background: #e0e7ff;
            color: #5b21b6;
        }

        .method-options {
            background: #f3e8ff;
            color: #7c3aed;
        }

        .method-head {
            background: #e7e5e4;
            color: #57534e;
        }

        .inactive-message {
            background: #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-weight: 600;
            text-align: center;
        }

        .response-type {
            font-size: 14px;
            color: #475569;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .error-message {
            color: #dc2626;
            font-size: 12px;
            margin-top: 8px;
            font-weight: 600;
        }

        .json-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        @media (max-width: 1024px) {
            .request-container {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .key-value-container {
                grid-template-columns: 1fr 1fr 50px 50px;
            }

            .form-control,
            .btn,
            .method-badge {
                height: 40px;
            }

            textarea.form-control {
                height: auto;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <!-- Header -->
    <div class="header">
        <h1>API Tester</h1>
        <p>Test API Endpoints for {{appName}}</p>
    </div>

    <!-- Inactive Route Message -->
    {{#unless testApiData.isActive}}
        <div class="inactive-message">
            This route is deactivated and cannot be tested.
        </div>
    {{/unless}}

    <!-- Request Controls -->
    <div class="controls">
        <div class="request-container">
            <div class="form-group">
                <label>Method</label>
                <div class="method-badge method-{{toLowerCase testApiData.method}}"
                     id="methodDisplay">{{testApiData.method}}</div>
                <input type="hidden" id="methodInput" value="{{testApiData.method}}">
            </div>
            <div class="form-group">
                <label>URL</label>
                <input
                        type="text"
                        id="urlInput"
                        class="form-control"
                        value="{{testApiData.routePath}}"
                        readonly
                        {{#unless testApiData.isActive}}disabled{{/unless}}
                >
            </div>
            <div class="form-group">
                <label> </label>
                <button class="btn btn-primary" onclick="sendRequest()"
                        {{#unless testApiData.isActive}}disabled{{/unless}}>
                    Send
                </button>
            </div>
        </div>

        <!-- Tabs for Headers, Query Params, Request Params, Body, Schema -->
        <div class="tab-container">
            <div class="tab" data-tab="headers">Headers</div>
            <div class="tab" data-tab="query">Query Parameters</div>
            <div class="tab" data-tab="params">Parameters</div>
            <div class="tab active" data-tab="body">Body</div>
            <div class="tab" data-tab="schema">Schema</div>
        </div>

        <!-- Headers Tab -->
        <div class="tab-content" id="headers">
            <div id="headerContainer">
                <div class="key-value-container">
                    <div class="form-group">
                        <label>Key</label>
                        <input type="text" class="form-control header-key" placeholder="Header Key"
                               {{#unless testApiData.isActive}}disabled{{/unless}}>
                    </div>
                    <div class="form-group">
                        <label>Value</label>
                        <input type="text" class="form-control header-value" placeholder="Value"
                               {{#unless testApiData.isActive}}disabled{{/unless}}>
                    </div>
                    <div class="form-group">
                        <label> </label>
                        <button class="btn btn-secondary" onclick="addHeaderRow()"
                                {{#unless testApiData.isActive}}disabled{{/unless}}>
                            +
                        </button>
                    </div>
                    <div class="form-group">
                        <label> </label>
                        <button class="btn btn-secondary" onclick="removeRow(this)"
                                {{#unless testApiData.isActive}}disabled{{/unless}}>
                            -
                        </button>
                    </div>
                </div>
            </div>
            <div class="error-message" id="headersError"></div>
        </div>

        <!-- Query Parameters Tab -->
        <div class="tab-content" id="query">
            <div id="queryContainer">
                {{#if testApiData.queryParams.properties}}
                    {{#each testApiData.queryParams.properties}}
                        <div class="key-value-container">
                            <div class="form-group">
                                <label>Key</label>
                                <input type="text" class="form-control query-key" placeholder="Key" value="{{@key}}"
                                       readonly {{#unless ../testApiData.isActive}}disabled{{/unless}}>
                            </div>
                            <div class="form-group">
                                <label>Value</label>
                                <input type="text" class="form-control query-value"
                                       placeholder="example-{{toCamelCase @key}}"
                                       oninput="updateUrlWithParams()"
                                       {{#unless ../testApiData.isActive}}disabled{{/unless}}>
                            </div>
                            <div class="form-group">
                                <label> </label>
                                <button class="btn btn-secondary" style="visibility: hidden;">+</button>
                            </div>
                            <div class="form-group">
                                <label> </label>
                                <button class="btn btn-secondary" style="visibility: hidden;">-</button>
                            </div>
                        </div>
                    {{/each}}
                {{else}}
                    <div class="key-value-container">
                        <div class="form-group">
                            <label>Key</label>
                            <input type="text" class="form-control query-key" placeholder="Key"
                                   oninput="updateUrlWithParams()"
                                   {{#unless testApiData.isActive}}disabled{{/unless}}>
                        </div>
                        <div class="form-group">
                            <label>Value</label>
                            <input type="text" class="form-control query-value" placeholder="Value"
                                   oninput="updateUrlWithParams()"
                                   {{#unless testApiData.isActive}}disabled{{/unless}}>
                        </div>
                        <div class="form-group">
                            <label> </label>
                            <button class="btn btn-secondary" onclick="addQueryRow()"
                                    {{#unless testApiData.isActive}}disabled{{/unless}}>
                                +
                            </button>
                        </div>
                        <div class="form-group">
                            <label> </label>
                            <button class="btn btn-secondary" onclick="removeRow(this); updateUrlWithParams()"
                                    {{#unless testApiData.isActive}}disabled{{/unless}}>
                                -
                            </button>
                        </div>
                    </div>
                {{/if}}
            </div>
            <div class="error-message" id="queryError"></div>
        </div>

        <!-- Request Parameters Tab -->
        <div class="tab-content" id="params">
            <div id="paramsContainer">
                {{#if testApiData.requestParams.properties}}
                    {{#each testApiData.requestParams.properties}}
                        <div class="key-value-container">
                            <div class="form-group">
                                <label>Key</label>
                                <input type="text" class="form-control param-key" placeholder="Key" value="{{@key}}"
                                       readonly {{#unless ../testApiData.isActive}}disabled{{/unless}}>
                            </div>
                            <div class="form-group">
                                <label>Value</label>
                                <input type="text" class="form-control param-value"
                                       placeholder="example-{{toCamelCase @key}}"
                                       oninput="updateUrlWithParams()"
                                       {{#unless ../testApiData.isActive}}disabled{{/unless}}>
                            </div>
                            <div class="form-group">
                                <label> </label>
                                <button class="btn btn-secondary" style="visibility: hidden;">+</button>
                            </div>
                            <div class="form-group">
                                <label> </label>
                                <button class="btn btn-secondary" style="visibility: hidden;">-</button>
                            </div>
                        </div>
                    {{/each}}
                {{else}}
                    <div class="key-value-container">
                        <div class="form-group">
                            <label>Key</label>
                            <input type="text" class="form-control param-key" placeholder="Key"
                                   oninput="updateUrlWithParams()"
                                   {{#unless testApiData.isActive}}disabled{{/unless}}>
                        </div>
                        <div class="form-group">
                            <label>Value</label>
                            <input type="text" class="form-control param-value" placeholder="Value"
                                   oninput="updateUrlWithParams()"
                                   {{#unless testApiData.isActive}}disabled{{/unless}}>
                        </div>
                        <div class="form-group">
                            <label> </label>
                            <button class="btn btn-secondary" onclick="addParamRow()"
                                    {{#unless testApiData.isActive}}disabled{{/unless}}>
                                +
                            </button>
                        </div>
                        <div class="form-group">
                            <label> </label>
                            <button class="btn btn-secondary" onclick="removeRow(this); updateUrlWithParams()"
                                    {{#unless testApiData.isActive}}disabled{{/unless}}>
                                -
                            </button>
                        </div>
                    </div>
                {{/if}}
            </div>
            <div class="error-message" id="paramsError"></div>
        </div>

        <!-- Body Tab -->
        <div class="tab-content active" id="body">
            <div class="json-controls">
                <button class="btn btn-secondary" onclick="formatJson()">Format JSON</button>
                <button class="btn btn-secondary" onclick="clearJson()">Clear</button>
            </div>
            <div class="form-group">
                <label for="bodyInput">Request Body (JSON)</label>
                <textarea
                        id="bodyInput"
                        class="form-control"
                        rows="16"
                        placeholder='Enter JSON body (e.g., {"key": "value"})'
                        {{#unless testApiData.isActive}}disabled{{/unless}}
                ></textarea>
            </div>
            <div class="error-message" id="bodyError"></div>
        </div>

        <!-- Schema Tab -->
        <div class="tab-content" id="schema">
            <div class="form-group">
                <label>Zod Schema</label>
                <div class="code-block">
                    <pre>{{zod testApiData.queryParams "Query Parameters"}}</pre>
                    <pre>{{zod testApiData.requestParams "Request Parameters"}}</pre>
                    <pre>{{zod testApiData.requestBody "Request Body"}}</pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Response Container -->
    <div class="response-container">
        <div class="response-header">
            <div class="response-title">Response</div>
            <div class="response-stats">
                Status: <span id="responseStatus">N/A</span> |
                Time: <span id="responseTime">N/A</span> ms
                <div class="tab-container" style="display: inline-flex; margin-left: 16px;">
                    <div class="tab active" data-tab="response-table">Table</div>
                    <div class="tab" data-tab="response-json">JSON</div>
                </div>
            </div>
        </div>
        <div class="tab-content active" id="response-table">
            <table class="response-table" id="responseTable">
                <thead>
                <tr>
                    <th>Key</th>
                    <th>Value</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td colspan="2">No response data available</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="tab-content" id="response-json">
            <div class="code-block" id="responseBody">No response yet.</div>
        </div>
    </div>
</div>

<script>
    // Safely initialize JSON data
    let originalUrl = '{{testApiData.routePath}}';
    let isRouteActive = {{testApiData.isActive}};
    let queryParamsSchema = {};
    let requestParamsSchema = {};
    let requestBodySchema = {};
    let responseData = null;

    try {
        queryParamsSchema = {{{JSONstringify testApiData.queryParams 2}}} ||
        {
        }
        ;
    } catch (e) {
        console.warn('Failed to parse queryParamsSchema:', e);
    }

    try {
        requestParamsSchema = {{{JSONstringify testApiData.requestParams 2}}} ||
        {
        }
        ;
    } catch (e) {
        console.warn('Failed to parse requestParamsSchema:', e);
    }

    try {
        requestBodySchema = {{{JSONstringify testApiData.requestBody 2}}} ||
        {
        }
        ;
    } catch (e) {
        console.warn('Failed to parse requestBodySchema:', e);
    }

    // Utility to convert snake_case or kebab-case to camelCase
    function toCamelCase(str) {
        return str.replace(/([-_][a-z])/g, group =>
                group.toUpperCase().replace('-', '').replace('_', '')
        );
    }

    // Simplified JSON Schema to Zod schema converter
    function jsonSchemaToZod(schema, name = 'Schema', depth = 0) {
        if (!schema || typeof schema !== 'object') return 'z.any()';

        const indent = ' '.repeat(depth * 2);
        let zodString = '';

        if (schema.type === 'object' && schema.properties) {
            const props = Object.entries(schema.properties).map(([key, prop]) => {
                let propZod = jsonSchemaToZod(prop, key, depth + 1);
                if (prop.minLength) propZod += `.min(${prop.minLength})`;
                if (prop.maxLength) propZod += `.max(${prop.maxLength})`;
                if (prop.format === 'email') propZod += `.email()`;
                if (prop.pattern) propZod += `.regex(new RegExp("${prop.pattern}"))`;
                if (!schema.required?.includes(key)) propZod += `.optional()`;
                return `${indent}  ${key}: ${propZod}`;
            }).join(',\n');
            zodString = `z.object({\n${props}\n${indent}})`;
        } else if (schema.type === 'array') {
            const itemZod = jsonSchemaToZod(schema.items || {}, 'Item', depth + 1);
            zodString = `z.array(${itemZod})`;
            if (schema.minItems) zodString += `.min(${schema.minItems})`;
            if (schema.maxItems) zodString += `.max(${schema.maxItems})`;
        } else if (schema.type === 'string') {
            zodString = 'z.string()';
            if (schema.minLength) zodString += `.min(${schema.minLength})`;
            if (schema.maxLength) zodString += `.max(${schema.maxLength})`;
            if (schema.format === 'email') zodString += `.email()`;
            if (schema.pattern) zodString += `.regex(new RegExp("${prop.pattern}"))`;
        } else if (schema.type === 'number' || schema.type === 'integer') {
            zodString = schema.type === 'integer' ? 'z.number().int()' : 'z.number()';
            if (schema.minimum) zodString += `.min(${schema.minimum})`;
            if (schema.maximum) zodString += `.max(${schema.maximum})`;
        } else if (schema.type === 'boolean') {
            zodString = 'z.boolean()';
        } else if (schema.$ref) {
            zodString = `z.lazy(() => ${schema.$ref.split('/').pop()})`;
        } else {
            zodString = 'z.any()';
        }

        return zodString;
    }

    // Generate Zod schema for validation
    function generateZodSchema(schema) {
        if (!schema.properties) return z.any();
        const zodCode = jsonSchemaToZod(schema);
        try {
            return eval(`(${zodCode})`);
        } catch (e) {
            console.error('Failed to generate Zod schema:', e);
            return z.any();
        }
    }

    // Initialize method and URL
    function initializeRequest() {
        const method = '{{testApiData.method}}'.trim().toUpperCase() || 'GET';
        document.querySelector('#methodDisplay').textContent = method;
        document.querySelector('#methodDisplay').classList.add(`method-${method.toLowerCase()}`);
        document.querySelector('#methodInput').value = method;
        document.querySelector('#urlInput').value = originalUrl;
        updateUrlWithParams();
        initializeResponseTabs();
        initializeBodyJson();
    }

    // Initialize body JSON with schema defaults
    function initializeBodyJson() {
        const bodyInput = document.querySelector('#bodyInput');
        if (requestBodySchema.properties && Object.keys(requestBodySchema.properties).length > 0) {
            const defaultBody = {};
            Object.entries(requestBodySchema.properties).forEach(([key, prop]) => {
                if (requestBodySchema.required?.includes(key)) {
                    defaultBody[key] = prop.example || '';
                }
            });
            bodyInput.value = JSON.stringify(defaultBody, null, 2);
        }
    }

    // Format JSON input
    function formatJson() {
        if (!isRouteActive) return;
        const bodyInput = document.querySelector('#bodyInput');
        const bodyError = document.querySelector('#bodyError');
        try {
            const parsed = JSON.parse(bodyInput.value.trim() || '{}');
            bodyInput.value = JSON.stringify(parsed, null, 2);
            bodyError.textContent = '';
        } catch (e) {
            bodyError.textContent = 'Invalid JSON: ' + e.message;
        }
    }

    // Clear JSON input
    function clearJson() {
        if (!isRouteActive) return;
        const bodyInput = document.querySelector('#bodyInput');
        bodyInput.value = '';
        document.querySelector('#bodyError').textContent = '';
    }

    // Update URL with query and request parameters
    function updateUrlWithParams() {
        let url = originalUrl;
        const queryParams = {};
        const requestParams = {};

        // Collect query parameters
        document.querySelectorAll('#queryContainer .key-value-container').forEach(row => {
            const key = row.querySelector('.query-key').value.trim();
            const value = row.querySelector('.query-value').value.trim();
            if (key && value) queryParams[key] = value;
        });

        // Collect request parameters
        document.querySelectorAll('#paramsContainer .key-value-container').forEach(row => {
            const key = row.querySelector('.param-key').value.trim();
            const value = row.querySelector('.param-value').value.trim();
            if (key && value) requestParams[key] = value;
        });

        // Replace request parameters in URL
        let baseUrl = url.split('?')[0];
        Object.entries(requestParams).forEach(([key, value]) => {
            const placeholder = `:${key}`;
            if (baseUrl.includes(placeholder)) {
                baseUrl = baseUrl.replace(placeholder, encodeURIComponent(value));
            } else if (baseUrl.endsWith('/*')) {
                baseUrl = baseUrl.replace(/\/\*$/, `/${encodeURIComponent(value)}`);
            }
        });

        // Append query parameters
        if (Object.keys(queryParams).length > 0) {
            const queryString = new URLSearchParams(queryParams).toString();
            baseUrl = baseUrl.includes('?') ? `${baseUrl}&${queryString}` : `${baseUrl}?${queryString}`;
        }

        document.querySelector('#urlInput').value = baseUrl;
    }

    // Initialize Response sub-tabs
    function initializeResponseTabs() {
        const activeSubTab = document.querySelector('.response-container .tab.active') || document.querySelector('.response-container .tab[data-tab="response-table"]');
        const subTabId = activeSubTab.dataset.tab;
        document.querySelectorAll('.response-container .tab').forEach(tab => tab.classList.remove('active'));
        activeSubTab.classList.add('active');
        document.querySelectorAll('.response-container .tab-content').forEach(content => content.classList.remove('active'));
        document.querySelector(`#${subTabId}`).classList.add('active');
        updateResponseTable(responseData);
        document.querySelector('#responseBody').textContent = 'No response yet.';
    }

    // Tab Switching for main tabs
    document.querySelectorAll('.controls .tab-container .tab').forEach(tab => {
        tab.addEventListener('click', () => {
            if (!isRouteActive && tab.dataset.tab !== 'schema') return;
            const parent = tab.parentElement;
            parent.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            document.querySelectorAll('.controls .tab-content').forEach(c => c.classList.remove('active'));
            document.querySelector(`#${tab.dataset.tab}`).classList.add('active');
        });
    });

    // Tab Switching for Response sub-tabs
    document.querySelectorAll('.response-container .tab-container .tab').forEach(tab => {
        tab.addEventListener('click', () => {
            const parent = tab.parentElement;
            parent.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            document.querySelectorAll('.response-container .tab-content').forEach(c => c.classList.remove('active'));
            document.querySelector(`#${tab.dataset.tab}`).classList.add('active');
            if (tab.dataset.tab === 'response-table') {
                updateResponseTable(responseData);
            } else {
                document.querySelector('#responseBody').textContent = responseData
                        ? (typeof responseData === 'string' ? responseData : JSON.stringify(responseData, null, 2))
                        : 'No response yet.';
            }
        });
    });

    function removeRow(button) {
        const row = button.closest('.key-value-container');
        const container = row.parentElement;
        const containerId = container.id || container.closest('[id]').id;

        if (container.querySelectorAll('.key-value-container').length > 1) {
            row.remove();
        } else {
            row.querySelectorAll('input').forEach(input => input.value = '');
        }

        if (containerId === 'queryContainer' && !queryParamsSchema.properties) {
            updateUrlWithParams();
            if (container.querySelectorAll('.key-value-container').length === 0) addQueryRow();
        } else if (containerId === 'paramsContainer' && !requestParamsSchema.properties) {
            updateUrlWithParams();
            if (container.querySelectorAll('.key-value-container').length === 0) addParamRow();
        }
    }

    function addHeaderRow() {
        if (!isRouteActive) return;
        const container = document.querySelector('#headerContainer');
        const row = document.createElement('div');
        row.className = 'key-value-container';
        row.innerHTML = `
            <div class="form-group">
                <label>Key</label>
                <input type="text" class="form-control header-key" placeholder="Header Key">
            </div>
            <div class="form-group">
                <label>Value</label>
                <input type="text" class="form-control header-value" placeholder="Value">
            </div>
            <div class="form-group">
                <label> </label>
                <button class="btn btn-secondary" onclick="addHeaderRow()">+</button>
            </div>
            <div class="form-group">
                <label> </label>
                <button class="btn btn-secondary" onclick="removeRow(this)">-</button>
            </div>
        `;
        container.appendChild(row);
    }

    function addQueryRow() {
        if (!isRouteActive) return;
        const container = document.querySelector('#queryContainer');
        const row = document.createElement('div');
        row.className = 'key-value-container';
        row.innerHTML = `
            <div class="form-group">
                <label>Key</label>
                <input type="text" class="form-control query-key" placeholder="Key" oninput="updateUrlWithParams()">
            </div>
            <div class="form-group">
                <label>Value</label>
                <input type="text" class="form-control query-value" placeholder="Value" oninput="updateUrlWithParams()">
            </div>
            <div class="form-group">
                <label> </label>
                <button class="btn btn-secondary" onclick="addQueryRow()">+</button>
            </div>
            <div class="form-group">
                <label> </label>
                <button class="btn btn-secondary" onclick="removeRow(this); updateUrlWithParams()">-</button>
            </div>
        `;
        container.appendChild(row);
    }

    function addParamRow() {
        if (!isRouteActive) return;
        const container = document.querySelector('#paramsContainer');
        const row = document.createElement('div');
        row.className = 'key-value-container';
        row.innerHTML = `
            <div class="form-group">
                <label>Key</label>
                <input type="text" class="form-control param-key" placeholder="Key" oninput="updateUrlWithParams()">
            </div>
            <div class="form-group">
                <label>Value</label>
                <input type="text" class="form-control param-value" placeholder="Value" oninput="updateUrlWithParams()">
            </div>
            <div class="form-group">
                <label> </label>
                <button class="btn btn-secondary" onclick="addParamRow()">+</button>
            </div>
            <div class="form-group">
                <label> </label>
                <button class="btn btn-secondary" onclick="removeRow(this); updateUrlWithParams()">-</button>
            </div>
        `;
        container.appendChild(row);
    }

    function updateResponseTable(data) {
        const tableBody = document.querySelector('#responseTable tbody');
        tableBody.innerHTML = '';

        if (!data) {
            const row = document.createElement('tr');
            row.innerHTML = `<td colspan="2">No response data available</td>`;
            tableBody.appendChild(row);
            return;
        }

        const flattenData = (obj, prefix = '') => {
            if (typeof obj !== 'object' || obj === null) {
                const row = document.createElement('tr');
                row.innerHTML = `<td>${prefix}</td><td>${JSON.stringify(obj)}</td>`;
                tableBody.appendChild(row);
                return;
            }

            if (Array.isArray(obj)) {
                obj.forEach((item, index) => {
                    flattenData(item, `${prefix}[${index}]`);
                });
            } else {
                Object.entries(obj).forEach(([key, value]) => {
                    flattenData(value, prefix ? `${prefix}.${key}` : key);
                });
            }
        };

        flattenData(data);
    }

    async function sendRequest() {
        if (!isRouteActive) return;

        document.querySelectorAll('.error-message').forEach(el => el.textContent = '');

        const method = document.querySelector('#methodInput').value;
        let url = originalUrl;
        const headers = {};
        const queryParams = {};
        const requestParams = {};
        let body = null;

        // Collect headers
        document.querySelectorAll('#headerContainer .key-value-container').forEach(row => {
            const key = row.querySelector('.header-key').value.trim();
            const value = row.querySelector('.header-value').value.trim();
            if (key && value) headers[key] = value;
        });

        // Collect query parameters
        document.querySelectorAll('#queryContainer .key-value-container').forEach(row => {
            const key = row.querySelector('.query-key').value.trim();
            const value = row.querySelector('.query-value').value.trim();
            if (key && value) queryParams[key] = value;
        });

        // Collect request parameters
        document.querySelectorAll('#paramsContainer .key-value-container').forEach(row => {
            const key = row.querySelector('.param-key').value.trim();
            const value = row.querySelector('.param-value').value.trim();
            if (key && value) requestParams[key] = value;
        });

        // Prepare request body
        const bodyInput = document.querySelector('#bodyInput').value.trim();
        if (bodyInput) {
            try {
                body = JSON.parse(bodyInput);
                headers['Content-Type'] = 'application/json';
            } catch (e) {
                document.querySelector('#bodyError').textContent = 'Invalid JSON in request body';
                return;
            }
        }

        // Replace request parameters in URL
        let baseUrl = url.split('?')[0];
        Object.entries(requestParams).forEach(([key, value]) => {
            const placeholder = `:${key}`;
            if (baseUrl.includes(placeholder)) {
                baseUrl = baseUrl.replace(placeholder, encodeURIComponent(value));
            } else if (baseUrl.endsWith('/*')) {
                baseUrl = baseUrl.replace(/\/\*$/, `/${encodeURIComponent(value)}`);
            }
        });

        // Append query parameters to URL
        if (Object.keys(queryParams).length > 0) {
            const queryString = new URLSearchParams(queryParams).toString();
            url = baseUrl.includes('?') ? `${baseUrl}&${queryString}` : `${baseUrl}?${queryString}`;
        } else {
            url = baseUrl;
        }

        // Update response UI
        const responseBody = document.querySelector('#responseBody');
        const responseStatus = document.querySelector('#responseStatus');
        const responseTime = document.querySelector('#responseTime');

        responseBody.textContent = 'Submitting request...';
        responseStatus.textContent = 'N/A';
        responseTime.textContent = 'N/A';

        try {
            const startTime = performance.now();
            const response = await fetch(url, {
                method,
                headers,
                body: body ? JSON.stringify(body) : null,
            });
            const endTime = performance.now();

            const responseText = await response.text();
            let parsedData = responseText;
            let formattedResponse = responseText;

            try {
                parsedData = JSON.parse(responseText);
                formattedResponse = JSON.stringify(parsedData, null, 2);
            } catch (e) {
            }

            responseData = parsedData;
            updateResponseTable(parsedData);
            responseBody.textContent = formattedResponse;
            responseStatus.innerHTML = `<span class="status-badge status-${response.ok ? 'success' : 'error'}">${response.status} ${response.statusText}</span>`;
            responseTime.textContent = Math.round(endTime - startTime);
        } catch (error) {
            responseData = `Error: ${error.message}`;
            responseBody.textContent = responseData;
            updateResponseTable(responseData);
            responseStatus.innerHTML = `<span class="status-badge status-error">Error</span>`;
            responseTime.textContent = 'N/A';
        }
    }

    document.addEventListener('DOMContentLoaded', initializeRequest);
</script>
</body>
</html>