<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Routes - {{appName}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #64748b;
            font-size: 16px;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .search-filter-container {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
            gap: 16px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
            font-size: 14px;
        }

        .form-control {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8fafc;
            color: #475569;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }

        .btn-pagination {
            padding: 8px 16px;
            background: #f8fafc;
            color: #475569;
            border: 2px solid #e2e8f0;
            font-size: 12px;
        }

        .btn-pagination:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }

        .btn-pagination:disabled {
            background: #e5e7eb;
            color: #9ca3af;
            cursor: not-allowed;
            border-color: #d1d5db;
        }

        .table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .table-header {
            padding: 24px;
            border-bottom: 2px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
        }

        .table-stats {
            font-size: 14px;
            color: #6b7280;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8fafc;
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            border-bottom: 2px solid #e5e7eb;
            position: sticky;
            top: 0;
        }

        .table td {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 14px;
            vertical-align: top;
        }

        .table tbody tr:hover {
            background-color: #f8fafc;
        }

        .method-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 60px;
            text-align: center;
        }

        .method-get {
            background: #dcfce7;
            color: #166534;
        }

        .method-post {
            background: #dbeafe;
            color: #1e40af;
        }

        .method-put {
            background: #fef3c7;
            color: #92400e;
        }

        .method-delete {
            background: #fecaca;
            color: #dc2626;
        }

        .method-patch {
            background: #e0e7ff;
            color: #5b21b6;
        }

        .method-options {
            background: #f3e8ff;
            color: #7c3aed;
        }

        .method-head {
            background: #e7e5e4;
            color: #57534e;
        }

        .route-path {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f1f5f9;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 13px;
            color: #1f2937;
            word-break: break-all;
        }

        .route-path a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .route-path a:hover {
            background: #e5e7eb;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fecaca;
            color: #dc2626;
        }

        .route-type-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .route-public {
            background: #dcfce7;
            color: #166534;
        }

        .route-private {
            background: #fef3c7;
            color: #92400e;
        }

        .access-type-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .access-admin_only {
            background: #fef3c7;
            color: #92400e;
        }

        .access-tenant_admin_only {
            background: #dbeafe;
            color: #1e40af;
        }

        .access-tenant_only {
            background: #f3e8ff;
            color: #7c3aed;
        }

        .access-user_only {
            background: #e7e5e4;
            color: #57534e;
        }

        .access-both {
            background: #e0e7ff;
            color: #5b21b6;
        }

        .permissions-list {
            max-width: 200px;
            word-wrap: break-word;
        }

        .permission-tag {
            display: inline-block;
            background: #f1f5f9;
            color: #475569;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin: 2px;
        }

        .description {
            max-width: 250px;
            word-wrap: break-word;
            color: #6b7280;
            font-style: italic;
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #6b7280;
        }

        .empty-state-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .pagination-container {
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 2px solid #f1f5f9;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .pagination-info {
            font-size: 14px;
            color: #6b7280;
        }

        @media (max-width: 1024px) {
            .search-filter-container {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .table-wrapper {
                font-size: 12px;
            }

            .table th,
            .table td {
                padding: 12px 8px;
            }

            .pagination-container {
                flex-direction: column;
                gap: 12px;
            }
        }

        .filter-active {
            position: relative;
        }

        .filter-active::after {
            content: '';
            position: absolute;
            top: -2px;
            right: -2px;
            width: 8px;
            height: 8px;
            background: #f5576c;
            border-radius: 50%;
        }
    </style>
</head>
<body>
<div class="container">
    <!-- Header -->
    <div class="header">
        <h1>API Routes Monitoring</h1>
        <p>monitor all API endpoints for {{appName}}</p>
    </div>

    <!-- Search and Filter Controls -->
    <div class="controls">
        <div class="search-filter-container">
            <div class="form-group">
                <label for="searchInput">🔍 Search Routes</label>
                <input
                        type="text"
                        id="searchInput"
                        class="form-control"
                        placeholder="Search by route path, method, or description..."
                        onkeyup="filterTable()"
                >
            </div>

            <div class="form-group">
                <label for="methodFilter">HTTP Method</label>
                <select id="methodFilter" class="form-control" onchange="filterTable()">
                    <option value="">All Methods</option>
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                    <option value="PATCH">PATCH</option>
                    <option value="OPTIONS">OPTIONS</option>
                    <option value="HEAD">HEAD</option>
                </select>
            </div>

            <div class="form-group">
                <label for="routeTypeFilter">Route Type</label>
                <select id="routeTypeFilter" class="form-control" onchange="filterTable()">
                    <option value="">All Types</option>
                    <option value="PUBLIC">Public</option>
                    <option value="PRIVATE">Private</option>
                </select>
            </div>

            <div class="form-group">
                <label for="statusFilter">Status</label>
                <select id="statusFilter" class="form-control" onchange="filterTable()">
                    <option value="">All Status</option>
                    <option value="true">Active</option>
                    <option value="false">Inactive</option>
                </select>
            </div>

            <div class="form-group">
                <label for="accessTypeFilter">Access Type</label>
                <select id="accessTypeFilter" class="form-control" onchange="filterTable()">
                    <option value="">All Access Types</option>
                    <option value="ADMIN_ONLY">Admin Only</option>
                    <option value="TENANT_ADMIN_ONLY">Tenant Admin Only</option>
                    <option value="TENANT_ONLY">Tenant Only</option>
                    <option value="STAFF_ONLY">Staff Only</option>
                    <option value="CUSTOMER_ONLY">Customer Only</option>
                    <option value="BOTH">Both</option>
                </select>
            </div>
        </div>

        <div style="margin-top: 16px; text-align: right;">
            <button class="btn btn-secondary" onclick="clearFilters()">
                🔄 Clear Filters
            </button>
            <button class="btn btn-primary" onclick="exportData()">
                📥 Export Data
            </button>
        </div>
    </div>

    <!-- Table Container -->
    <div class="table-container">
        <div class="table-header">
            <div class="table-title">API Routes</div>
            <div class="table-stats">
                <span id="visible-count">0</span> of <span id="total-count">{{authMaps.length}}</span> routes
            </div>
        </div>

        <div class="table-wrapper">
            <table class="table" id="routesTable">
                <thead>
                <tr>
                    <th style="width: 100px;">Method</th>
                    <th style="width: 300px;">Route Path</th>
                    <th style="width: 250px;">Description</th>
                    <th style="width: 100px;">Route Type</th>
                    <th style="width: 100px;">Access Type</th>
                    <th style="width: 150px;">Permissions</th>
                    <th style="width: 80px;">isOrOperator</th>
                    <th style="width: 80px;">Status</th>
                    <th style="width: 120px;">Created</th>
                </tr>
                </thead>
                <tbody>
                {{#each apiDocs}}
                    <tr data-method="{{this.method}}" data-route-type="{{this.routeType}}"
                        data-status="{{this.isActive}}" data-route-path="{{this.routePath}}"
                        data-description="{{this.description}}" data-access-type="{{this.accessType}}">
                        <td>
                            <span class="method-badge method-{{toLowerCase this.method}}">{{this.method}}</span>
                        </td>
                        <td>
                            <div class="route-path">
                                <a href="http://localhost:4000/api-docs/test/{{this.authMapId}}" target="_blank">
                                    {{this.routePath}}
                                </a>
                            </div>
                        </td>
                        <td>
                            <div class="description">
                                {{#if this.description}}
                                    {{this.description}}
                                {{else}}
                                    <em>No description</em>
                                {{/if}}
                            </div>
                        </td>
                        <td>
                            <span class="route-type-badge route-{{toLowerCase
                                    this.routeType}}">{{this.routeType}}</span>
                        </td>
                        <td>
                            <span class="access-type-badge access-{{toLowerCase
                                    this.accessType}}">{{this.accessType}}</span>
                        </td>
                        <td>
                            <div class="permissions-wrapper">
                                {{#if this.permissionActions}}
                                    {{#each this.permissionActions}}
                                        <span class="permission-tag">{{this}}</span>
                                    {{/each}}
                                {{else}}
                                    <em>No permissions</em>
                                {{/if}}
                            </div>
                        </td>
                        <td>
                            {{#if this.isOrOperation}}
                                <span style="color: #059669; font-weight: 600;">✓ OR</span>
                            {{else}}
                                <span style="color: #dc2626; font-weight: 600;">✗ AND</span>
                            {{/if}}
                        </td>
                        <td>
                            {{#if this.isActive}}
                                <span class="status-badge">
                                        <span style="width: 6px; height: 6px; background: #007bff; border-radius: 50%; display: inline-block;"></span>
                                        Active
                                    </span>
                            {{else}}
                                <span class="status-badge">
                                        <span style="width: 6px; height: 6px; background: #6c757d; border-radius: 50%; display: inline-block;"></span>
                                        Inactive
                                    </span>
                            {{/if}}
                        </td>
                        <td>
                            <small style="color: #6b7280;">
                                {{formatDate this.createdAt}}
                            </small>
                        </td>
                    </tr>
                {{else}}
                    <tr class="empty-row">
                        <td colspan="9">
                            <div class="empty-state">
                                <div class="empty-state-icon">📭</div>
                                <h3>No Records Found</h3>
                                <p>No routes configured yet.</p>
                            </div>
                        </td>
                        <td>
                        </td>
                    </tr>
                {{/each}}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                Showing <span id="page-range">1-10</span> of <span id="total-records">0</span> records
            </div>
            <div class="pagination-controls">
                <div class="form-group">
                    <select id="pageSizeSelector" class="form-control" onchange="changePageSize()">
                        <option value="10" selected>10 per page</option>
                        <option value="20">20 per page</option>
                        <option value="30">30 per page</option>
                        <option value="50">50 per page</option>
                    </select>
                </div>
                <button class="btn btn-primary" onclick="previousPage()" id="prevPageBtn" disabled>Previous</button>
                <span id="page-number">Page 1</span>
                <button class="btn btn-primary" onclick="nextPage()" id="nextPageBtn" disabled>Next</button>
            </div>
        </div>
    </div>
</div>

<script>
    let currentPage = 1;
    let pageSize = 10;
    let filteredRows = []; // Store filtered rows globally to avoid re-filtering

    function filterTable() {
        const searchInput = document.getElementById('searchInput').value.toLowerCase();
        const methodFilter = document.getElementById('methodFilter').value;
        const routeTypeFilter = document.getElementById('routeTypeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const accessTypeFilter = document.getElementById('accessTypeFilter').value;

        const table = document.getElementById('routesTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        filteredRows = []; // Reset filtered rows

        // Update filter indicators
        updateFilterIndicators();

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            if (row.classList.contains('empty-row')) continue;

            const method = row.getAttribute('data-method') || '';
            const routeType = row.getAttribute('data-route-type') || '';
            const status = row.getAttribute('data-status') || '';
            const routePath = row.getAttribute('data-route-path') || '';
            const description = row.getAttribute('data-description') || '';
            const accessType = row.getAttribute('data-access-type') || '';

            const searchText = (method + ' ' + routePath + ' ' + description).toLowerCase();

            const matchesSearch = searchText.includes(searchInput);
            const matchesMethod = methodFilter === '' || method === methodFilter;
            const matchesRouteType = routeTypeFilter === '' || routeType === routeTypeFilter;
            const matchesStatus = statusFilter === '' || status === statusFilter;
            const matchesAccessType = accessTypeFilter === '' || accessType === accessTypeFilter;

            if (matchesSearch && matchesMethod && matchesRouteType && matchesStatus && matchesAccessType) {
                filteredRows.push(row);
            }
        }

        // Reset to page 1 when filtering
        currentPage = 1;
        updatePagination();
    }

    function updatePagination() {
        const table = document.getElementById('routesTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        const totalRecords = filteredRows.length;
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;

        // Hide all rows
        for (let i = 0; i < rows.length; i++) {
            rows[i].style.display = 'none';
        }

        // Show paginated rows
        let visibleCount = 0;
        for (let i = startIndex; i < endIndex && i < filteredRows.length; i++) {
            filteredRows[i].style.display = '';
            visibleCount++;
        }

        // Update counts
        document.getElementById('visible-count').textContent = visibleCount;
        document.getElementById('total-count').textContent = totalRecords;
        document.getElementById('total-records').textContent = totalRecords;
        document.getElementById('page-number').textContent = `Page ${currentPage}`;
        document.getElementById('page-range').textContent = `${startIndex + 1}-${Math.min(endIndex, totalRecords)}`;

        // Update empty state
        const emptyRow = table.querySelector('.empty-row');
        if (emptyRow) {
            emptyRow.style.display = totalRecords === 0 ? '' : 'none';
        }

        // Update pagination buttons
        document.getElementById('prevPageBtn').disabled = currentPage === 1;
        document.getElementById('nextPageBtn').disabled = endIndex >= totalRecords;
    }

    function changePageSize() {
        pageSize = parseInt(document.getElementById('pageSizeSelector').value);
        currentPage = 1;
        updatePagination();
    }

    function previousPage() {
        if (currentPage > 1) {
            currentPage--;
            updatePagination();
        }
    }

    function nextPage() {
        const totalRecords = filteredRows.length;
        const totalPages = Math.ceil(totalRecords / pageSize);
        if (currentPage < totalPages) {
            currentPage++;
            updatePagination();
        }
    }

    function updateFilterIndicators() {
        const filters = ['methodFilter', 'routeTypeFilter', 'statusFilter', 'accessTypeFilter'];

        filters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter.value !== '') {
                filter.classList.add('filter-active');
            } else {
                filter.classList.remove('filter-active');
            }
        });
    }

    function clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('methodFilter').value = '';
        document.getElementById('routeTypeFilter').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('accessTypeFilter').value = '';

        // Remove filter indicators
        document.querySelectorAll('.filter-active').forEach(el => {
            el.classList.remove('filter-active');
        });

        filterTable();
    }

    function exportData() {
        const table = document.getElementById('routesTable');
        const rows = table.querySelectorAll('tbody tr[data-method]');

        let csv = 'Method,Route Path,Description,Route Type,Access Type,Permissions,OR Logic,Status,Created\n';

        rows.forEach(row => {
            if (row.style.display !== 'none') {
                const cells = row.querySelectorAll('td');
                const rowData = [
                    row.getAttribute('data-method'),
                    row.getAttribute('data-route-path'),
                    row.getAttribute('data-description') || '',
                    row.getAttribute('data-route-type'),
                    row.getAttribute('data-access-type'),
                    cells[5].textContent.trim().replace(/\s+/g, ' '),
                    cells[6].textContent.includes('OR') ? 'OR' : 'AND',
                    row.getAttribute('data-status') === 'true' ? 'Active' : 'Inactive',
                    cells[8].textContent.trim()
                ];
                csv += rowData.map(field => `"${field.replace(/"/g, '""')}"`).join(',') + '\n';
            }
        });

        // Download CSV
        const blob = new Blob([csv], {type: 'text/csv'});
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'api-routes-' + new Date().toISOString().split('T')[0] + '.csv';
        a.click();
        window.URL.revokeObjectURL(url);
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function () {
        updateFilterIndicators();
        filterTable();
    });
</script>
</body>
</html>