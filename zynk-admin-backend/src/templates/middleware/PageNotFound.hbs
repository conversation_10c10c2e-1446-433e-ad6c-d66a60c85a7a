<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Route Not Found - {{appName}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #2d3748;
        }

        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 48px 40px;
            max-width: 580px;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .error-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
        }

        .logo {
            max-width: 120px;
            margin-bottom: 24px;
            opacity: 0.9;
        }

        .brand-name {
            font-size: 28px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .brand-name span {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .error-code {
            font-size: 72px;
            font-weight: 800;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 20px 0;
            line-height: 1;
        }

        .error-title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 12px;
        }

        .error-message {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 32px;
            line-height: 1.6;
        }

        .request-info {
            background: #f8fafc;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
            text-align: left;
            border-left: 4px solid #f5576c;
        }

        .request-info h3 {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .request-info h3::before {
            content: '⚠️';
            font-size: 18px;
        }

        .info-grid {
            display: grid;
            gap: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #4a5568;
            font-size: 14px;
        }

        .info-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            color: #2d3748;
            background: #edf2f7;
            padding: 4px 8px;
            border-radius: 6px;
            word-break: break-all;
        }

        .method-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .method-get {
            background: #dcfce7;
            color: #166534;
        }

        .method-post {
            background: #dbeafe;
            color: #1e40af;
        }

        .method-put {
            background: #fef3c7;
            color: #92400e;
        }

        .method-delete {
            background: #fecaca;
            color: #dc2626;
        }

        .method-patch {
            background: #e0e7ff;
            color: #5b21b6;
        }

        .method-default {
            background: #f1f5f9;
            color: #475569;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 32px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8fafc;
            color: #475569;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }

        .branding {
            margin-top: 40px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
            font-size: 14px;
            color: #64748b;
        }

        .branding a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .branding a:hover {
            text-decoration: underline;
        }

        @media (max-width: 640px) {
            body {
                padding: 16px;
            }

            .error-container {
                padding: 32px 24px;
            }

            .error-code {
                font-size: 56px;
            }

            .error-title {
                font-size: 20px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
<div class="error-container">
    {{#if logoUrl}}
        <img src="{{logoUrl}}" alt="{{tenantName}} Logo" class="logo">
    {{else}}
        <div class="brand-name">
            <span>Easy</span>Dine
        </div>
    {{/if}}

    <div class="error-code">404</div>
    <h1 class="error-title">Route Not Found</h1>
    <p class="error-message">The requested route is not configured in the system. Please check the URL or contact your
        system administrator.</p>

    <div class="request-info">
        <h3>Request Details</h3>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Method:</span>
                <span class="method-badge method-{{method}}">{{method}}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Path:</span>
                <span class="info-value">{{path}}</span>
            </div>
            <div class="info-item">
                <span class="info-label">System:</span>
                <span class="info-value">{{appName}}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Timestamp:</span>
                <span class="info-value">{{timestamp}}</span>
            </div>
        </div>
    </div>

    <div class="action-buttons">
        <a href="javascript:history.back()" class="btn btn-secondary">
            ← Go Back
        </a>
        <a href="/" class="btn btn-primary">
            🏠 Home
        </a>
    </div>

    <div class="branding">
        Powered by <a href="https://easydine.com">EasyDine</a>
    </div>
</div>
</body>
</html>