export enum Duration {
    DAILY = "DAILY",
    WEEKLY = "WEEKLY",
    MONTHLY = "MONTHLY",
    YEARLY = "YEARLY"
}

export enum SubscriptionStatus {
    ACTIVE = "ACTIVE",
    EXPIRED = "EXPIRED",
    CANCELLED = "CANCELLED",
    BLOCKED = "BLOCKED"
}

export enum SubsOperationType {
    CANCEL_EXISTING_AND_ADD_NEW = "CANCEL_EXISTING_AND_ADD_NEW",
    EXTEND_EXISTING_DURATION_AND_ADD_NEW = "EXTEND_EXISTING_DURATION_AND_ADD_NEW",
    MINUS_BALANCE_AND_ADD_NEW = "MINUS_BALANCE_AND_ADD_NEW",
    ADD_NEW = "ADD_NEW"
}

export enum ReflectionType {
    TO_NIGHT = "TO_NIGHT",
    RIGHT_NOW = "RIGHT_NOW",
    NEXT_ACTIVATION_ONWARDS = "NEXT_ACTIVATION_ONWARDS"
}

export interface removeExSubscription {
    success: boolean;
    warnings: string[];
    error: string[];
    isRemoved: boolean;
    hasActivePlan: boolean;
}

export interface addNewSubscriptionResult {
    success: boolean;
    warnings: string[];
    error: string[];
    isAdded: boolean;
}

export interface syncFeaturePAResult {
    success: boolean;
    warnings: string[];
    error: string[];
}