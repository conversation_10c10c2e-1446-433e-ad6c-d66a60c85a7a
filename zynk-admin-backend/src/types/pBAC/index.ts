/**
 * TYPES FOR PERMISSION BASED ACCESS CONTROL (PBAC)
 */

export enum RouteType {
    PUBLIC = "PUBLIC",
    PRIVATE = "PRIVATE",
}

export enum AccessType {
    ADMIN_ONLY = "ADMIN_ONLY",
    TENANT_ADMIN_ONLY = "TENANT_ADMIN_ONLY",
    TENANT_ONLY = "TENANT_ONLY",
    STAFF_ONLY = "STAFF_ONLY",
    CUSTOMER_ONLY = "CUSTOMER_ONLY",
    ALL = "BOTH",
}

export const enum UserType {
    SUPER_ADMIN = "SUPER_ADMIN",
    TENANT_ADMIN = "TENANT_ADMIN",
    STAFF = "STAFF",
    CUSTOMER = "CUSTOMER",
}

export enum OperationType {
    PLUS = "PLUS",
    MINUS = "MINUS",
}

export enum ModelType {
    ADMIN = "ADMIN",
    STAFF = "STAFF",
    CUSTOMER = "CUSTOMER",
    TENANT = "TENANT",
}

export const getUserModelAssociation = (modelType: ModelType) => {
    const descriptions: Record<ModelType, Array<string>> = {
        [ModelType.ADMIN]: [
            "role",
            "role.rolePermissionActions",
            "role.rolePermissionActions.permissionAction",
            "role.rolePermissionActions.permissionAction.permission",
            "role.rolePermissionActions.permissionAction.action",
            "supPermissionAction",
            "supPermissionAction.permissionAction",
            "supPermissionAction.permissionAction.permission",
            "supPermissionAction.permissionAction.action"
        ],
        [ModelType.TENANT]: [
            "role",
            "role.rolePermissionActions",
            "role.rolePermissionActions.permissionAction",
            "role.rolePermissionActions.permissionAction.permission",
            "role.rolePermissionActions.permissionAction.action",
            "tenantPermissionAction",
            "tenantPermissionAction.permissionAction",
            "tenantPermissionAction.permissionAction.permission",
            "tenantPermissionAction.permissionAction.action"
        ],
        [ModelType.STAFF]: [
            "role",
            "role.rolePermissionActions",
            "role.rolePermissionActions.permissionAction",
            "role.rolePermissionActions.permissionAction.permission",
            "role.rolePermissionActions.permissionAction.action",
            "staffPermissionActions",
            "staffPermissionActions.permissionAction",
            "staffPermissionActions.permissionAction.permission",
            "staffPermissionActions.permissionAction.action"
        ],
        [ModelType.CUSTOMER]: [
            "role",
            "role.rolePermissionActions",
            "role.rolePermissionActions.permissionAction",
            "role.rolePermissionActions.permissionAction.permission",
            "role.rolePermissionActions.permissionAction.action",
            "customerPermissionActions",
            "customerPermissionActions.permissionAction",
            "customerPermissionActions.permissionAction.permission",
            "customerPermissionActions.permissionAction.action"
        ],
    };

    return descriptions[modelType] || 'Unknown model type ';
}

export interface PermActionModelType {
    actionId: string,
    permissionId: string,
    operationType: OperationType
}

export function hasSufficientAccess(parentAccessType: AccessType, childAccessType: AccessType): boolean {
    if (childAccessType === AccessType.ALL) return true;

    const compatibilityMatrix: Record<AccessType, Set<AccessType>> = {
        [AccessType.ALL]: new Set([AccessType.ALL]),
        [AccessType.ADMIN_ONLY]: new Set([
            AccessType.ADMIN_ONLY,
            AccessType.TENANT_ADMIN_ONLY,
            AccessType.TENANT_ONLY,
            AccessType.STAFF_ONLY,
            AccessType.CUSTOMER_ONLY
        ]),
        [AccessType.TENANT_ADMIN_ONLY]: new Set([
            AccessType.TENANT_ADMIN_ONLY,
            AccessType.TENANT_ONLY,
            AccessType.STAFF_ONLY
        ]),
        [AccessType.TENANT_ONLY]: new Set([
            AccessType.TENANT_ONLY,
            AccessType.STAFF_ONLY,
            AccessType.CUSTOMER_ONLY
        ]),
        [AccessType.STAFF_ONLY]: new Set([
            AccessType.STAFF_ONLY
        ]),
        [AccessType.CUSTOMER_ONLY]: new Set([
            AccessType.CUSTOMER_ONLY
        ])
    };

    return compatibilityMatrix[parentAccessType]?.has(childAccessType) ?? false;
}

export function getAccessTypeDescription(accessType: AccessType): string {
    const descriptions: Record<AccessType, string> = {
        [AccessType.ALL]: 'Accessible by all users (universal access)',
        [AccessType.ADMIN_ONLY]: 'Restricted to system administrators only',
        [AccessType.TENANT_ADMIN_ONLY]: 'Restricted to tenant administrators only',
        [AccessType.TENANT_ONLY]: 'Restricted to tenant members only',
        [AccessType.STAFF_ONLY]: 'Restricted to individual staffs only',
        [AccessType.CUSTOMER_ONLY]: 'Restricted to individual customers only',
    };

    return descriptions[accessType] || 'Unknown access type';
}

export function validateAccessCompatibility(
    parentAccessType: AccessType,
    childAccessType: AccessType,
    isNotValidation: boolean = false
): { isValid: boolean; reason: string } {
    const isValid = isNotValidation ? !hasSufficientAccess(parentAccessType, childAccessType) : hasSufficientAccess(parentAccessType, childAccessType);

    if (isValid) {
        return {
            isValid: true,
            reason: `${parentAccessType} parent can contain ${childAccessType} child`
        };
    }

    if (parentAccessType === AccessType.ALL && childAccessType !== AccessType.ALL) {
        return {
            isValid: false,
            reason: 'ALL access type can only contain other ALL access types'
        };
    }

    return {
        isValid: false,
        reason: `${parentAccessType} parent cannot contain ${childAccessType} child - insufficient access level`
    };
}