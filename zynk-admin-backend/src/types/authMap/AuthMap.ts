import {AccessType, RouteType} from "../pBAC";

export class AuthMap {
    routePath: string;
    method: string;
    description: string;
    accessType: AccessType;
    permissionActions: string[];
    isOrOperation: boolean;
    routeType: RouteType;
    isActive: boolean;

    constructor(
        routePath: string,
        method: string,
        description: string,
        accessType: AccessType,
        permissionActions: string[],
        isOrOperation: boolean,
        routeType: RouteType,
        isActive: boolean
    ) {
        this.routePath = routePath;
        this.method = method;
        this.description = description;
        this.accessType = accessType;
        this.permissionActions = permissionActions;
        this.isOrOperation = isOrOperation;
        this.routeType = routeType;
        this.isActive = isActive;
    }
}