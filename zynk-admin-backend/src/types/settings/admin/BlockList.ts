import {QueryRunner} from "typeorm";
import {LockedType} from "../../system";

export interface CheckBlockListParams {
    qRunner: QueryRunner;
    currentModifier: LockedType;
    tenantId: string;
    lockedTypes: LockedType[];
    isNotOperation?: boolean;
    unBlock?: boolean;
    throwError?: boolean;
}

export interface CheckBlockListResult {
    success: boolean;
    isTenantBlocked: boolean;
    warnings: string[];
    error: string[];
    unBlocked: boolean;
}