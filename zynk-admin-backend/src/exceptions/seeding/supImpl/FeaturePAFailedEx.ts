import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class FeaturePAFailedEx extends SeedingException {
    constructor(featureName: string) {
        super(INTERNAL_SERVER_ERROR, `failed To Create Feature Permission Action for Feature: ${featureName}`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, FeaturePAFailedEx.prototype);
    }
}