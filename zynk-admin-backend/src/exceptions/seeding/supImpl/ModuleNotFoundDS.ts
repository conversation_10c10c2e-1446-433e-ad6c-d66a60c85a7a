import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class ModuleNotFoundDS extends SeedingException {
    constructor(moduleName: string) {
        super(INTERNAL_SERVER_ERROR, `module '${moduleName}' not found in moduleMap. Please check your seeding data.`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, ModuleNotFoundDS.prototype);
    }
}