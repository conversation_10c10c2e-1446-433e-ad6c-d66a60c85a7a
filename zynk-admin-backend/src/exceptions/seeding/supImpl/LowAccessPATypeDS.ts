import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";
import {AccessType, validateAccessCompatibility} from "../../../types/pBAC";

export class LowAccessPATypeDS extends SeedingException {
    constructor(permName: string, permAccess: AccessType, actionName: string, actionAccess: AccessType) {
        super(INTERNAL_SERVER_ERROR, `failed to assign Permission "${permName}"(parent) with access type "${permAccess}" to Action "${actionName}"(child) with access type "${actionAccess}".
        Reason: ${validateAccessCompatibility(permAccess, actionAccess, true).reason}`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, LowAccessPATypeDS.prototype);
    }
}