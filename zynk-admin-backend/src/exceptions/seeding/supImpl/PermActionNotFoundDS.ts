import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class PermActionNotFoundDS extends SeedingException {
    constructor(permAction: string) {
        super(INTERNAL_SERVER_ERROR, `PermissionAction '${permAction}' not found in permActionArray. Please check your seeding data.`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, PermActionNotFoundDS.prototype);
    }
}