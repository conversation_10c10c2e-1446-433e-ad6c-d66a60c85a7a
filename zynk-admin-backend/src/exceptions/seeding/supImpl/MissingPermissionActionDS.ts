import {SeedingException} from "../SeedingException";

export class MissingPermissionActionDS extends SeedingException {
    constructor(permissionName: string, actionName: string) {
        super(500, `Permission '${permissionName}' does not have the action '${actionName}'. Please check your seeding data.`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, MissingPermissionActionDS.prototype);
    }
}