import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";
import {AccessType, validateAccessCompatibility} from "../../../types/pBAC";

export class LowAccessPMTypeDS extends SeedingException {
    constructor(permName: string, permAccess: AccessType, moduleName: string, moduleAccess: AccessType) {
        super(INTERNAL_SERVER_ERROR, `failed to assign Module "${moduleName}"(parent) with access type "${moduleAccess}" to Permission "${permName}"(child) with access type "${permAccess}".
        Reason: ${validateAccessCompatibility(moduleAccess, permAccess, true).reason}`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, LowAccessPMTypeDS.prototype);
    }
}