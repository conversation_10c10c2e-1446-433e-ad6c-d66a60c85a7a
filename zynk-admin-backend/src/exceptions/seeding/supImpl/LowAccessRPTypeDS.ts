import {SeedingException} from "../SeedingException";
import {AccessType, validateAccessCompatibility} from "../../../types/pBAC";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class LowAccessRPTypeDS extends SeedingException {
    constructor(roleName: string, roleAccess: AccessType, permName: string, permAccess: AccessType,) {
        super(INTERNAL_SERVER_ERROR, `failed to assign Role "${roleName}"(parent) with access type "${roleAccess}" to Permission "${permName}"(child) with access type "${permAccess}".
        Reason: ${validateAccessCompatibility(roleAccess, permAccess, true).reason}`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, LowAccessRPTypeDS.prototype);
    }
}