import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class PermNotFoundDS extends SeedingException {
    constructor(permission: string) {
        super(INTERNAL_SERVER_ERROR, `Permission '${permission}' not found in permission Map. Please check your seeding data.`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, PermNotFoundDS.prototype);
    }
}