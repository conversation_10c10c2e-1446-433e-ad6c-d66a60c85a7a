import {SeedingException} from "../SeedingException";
import {AccessType, validateAccessCompatibility} from "../../../types/pBAC";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class LowAccessFeatureTypeDS extends SeedingException {
    constructor(featureName: string, featureAccess: AccessType, permissionName: string, permissionAccess: AccessType) {
        super(INTERNAL_SERVER_ERROR, `failed to assign feature "${featureName}"(parent) with access type "${featureAccess}" with Permission "${permissionName}"(child) with access type "${permissionName}".
        Reason: ${validateAccessCompatibility(featureAccess, permissionAccess).reason}`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, LowAccessFeatureTypeDS.prototype);
    }
}