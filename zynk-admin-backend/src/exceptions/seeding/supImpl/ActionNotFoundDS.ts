import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class ActionNotFoundDS extends SeedingException {
    constructor(actionName: string) {
        super(INTERNAL_SERVER_ERROR, `Action '${actionName}' not found in actionMap. Please check your seeding data.`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, ActionNotFoundDS.prototype);
    }
}