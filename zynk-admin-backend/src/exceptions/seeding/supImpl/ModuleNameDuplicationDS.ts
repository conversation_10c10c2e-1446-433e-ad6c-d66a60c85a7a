import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class ModuleNameDuplicationDS extends SeedingException {
    constructor(moduleName: string) {
        super(INTERNAL_SERVER_ERROR, `moduleName: '${moduleName}' already exists in moduleMap.Please check your seeding data.`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, ModuleNameDuplicationDS.prototype);
    }
}