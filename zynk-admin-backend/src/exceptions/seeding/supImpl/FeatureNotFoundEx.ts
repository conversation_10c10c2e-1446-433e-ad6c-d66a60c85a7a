import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class FeatureNotFoundEx extends SeedingException {
    constructor(featureName: string) {
        super(INTERNAL_SERVER_ERROR, `Failed To locate feature from featureMap: ${featureName}, Please verify you seed data`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, FeatureNotFoundEx.prototype);
    }
}