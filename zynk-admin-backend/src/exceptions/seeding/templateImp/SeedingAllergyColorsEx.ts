import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class SeedingAllergyColorsEx extends SeedingException {
    constructor() {
        super(INTERNAL_SERVER_ERROR, `Error seeding allergy colors`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, SeedingAllergyColorsEx.prototype);
    }
}