import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class SeedingDishAddonsEx extends SeedingException {
    constructor() {
        super(INTERNAL_SERVER_ERROR, `Error seeding dish addons`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, SeedingDishAddonsEx.prototype);
    }
}