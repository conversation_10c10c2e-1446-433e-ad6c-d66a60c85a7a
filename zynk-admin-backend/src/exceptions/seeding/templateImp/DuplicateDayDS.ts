import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class DuplicateDayDS extends SeedingException {
    constructor(day: string) {
        super(INTERNAL_SERVER_ERROR, `Duplicate day ${day} in business hours`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, DuplicateDayDS.prototype);
    }
}