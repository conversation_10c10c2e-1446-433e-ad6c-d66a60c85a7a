import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class SeedingDishExtrasEx extends SeedingException {
    constructor() {
        super(INTERNAL_SERVER_ERROR, `Error seeding dish extras`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, SeedingDishExtrasEx.prototype);
    }
}