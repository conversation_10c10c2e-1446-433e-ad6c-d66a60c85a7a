import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class SectionIconsSeedingEx extends SeedingException {
    constructor() {
        super(INTERNAL_SERVER_ERROR, `Error seeding section icons`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, SectionIconsSeedingEx.prototype);
    }
}