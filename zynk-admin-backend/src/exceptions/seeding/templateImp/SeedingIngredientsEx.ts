import {SeedingException} from "../SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";

export class SeedingIngredientsEx extends SeedingException {
    constructor() {
        super(INTERNAL_SERVER_ERROR, `Error seeding ingredients`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, SeedingIngredientsEx.prototype);
    }
}