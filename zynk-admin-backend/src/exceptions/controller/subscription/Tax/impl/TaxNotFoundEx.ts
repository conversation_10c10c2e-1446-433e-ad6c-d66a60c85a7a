import {SubscriptionException} from "../../SubscriptionException";
import {NOT_FOUND} from "../../../../../constants/STATUS_CODES";

export class TaxNotFoundEx extends SubscriptionException {
    constructor() {
        super(
            NOT_FOUND,
            "Tax not found"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, TaxNotFoundEx.prototype);
    }
}