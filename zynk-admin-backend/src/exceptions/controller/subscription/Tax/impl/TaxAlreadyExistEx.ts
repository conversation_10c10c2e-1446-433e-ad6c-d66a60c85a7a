import {BAD_REQUEST} from "../../../../../constants/STATUS_CODES";
import {TaxException} from "../TaxException";
import {Tax} from "../../../../../models/subscription/tax/tax.model";

export class TaxAlreadyExistEx extends TaxException {
    constructor(exTax: Tax, taxData: any) {
        const uniqueFields: (keyof Tax & keyof any)[] = [
            'countryCode',
            'stateCode',
            'upperLimit',
            'lowerLimit',
            'isCentral',
            'taxGovId',
        ];
        const conflictingFields = uniqueFields
            .filter((field) => {
                const exValue = exTax[field];
                const newValue = taxData[field];

                if (field === 'stateCode' || field === 'taxGovId') {
                    return exValue === newValue || (exValue === null && newValue === undefined);
                }

                if (field === 'upperLimit' || field === 'lowerLimit') {
                    return Number(exValue) === Number(newValue);
                }

                return exValue === newValue;
            })
            .map((field) => {
                const value = taxData[field];
                return `${field}: ${value ?? 'null'}`;
            })
            .join(', ');

        const message = `Tax record already exists with conflicting unique fields: ${conflictingFields || 'unknown'}`;

        super(BAD_REQUEST, message);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, TaxAlreadyExistEx.prototype);
    }
}