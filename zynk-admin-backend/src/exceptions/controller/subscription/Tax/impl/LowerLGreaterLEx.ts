import {SubscriptionException} from "../../SubscriptionException";
import {BAD_REQUEST} from "../../../../../constants/STATUS_CODES";

export class LowerLGreaterLEx extends SubscriptionException {
    constructor() {
        super(
            BAD_REQUEST,
            "Lower limit cannot be greater than upper limit"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, LowerLGreaterLEx.prototype);
    }
}