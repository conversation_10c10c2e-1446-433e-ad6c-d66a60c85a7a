import {BAD_REQUEST} from "../../../../../constants/STATUS_CODES";
import {FeatureException} from "../FeatureException";

export class FeatureAlreadyExistEx extends FeatureException {
    constructor(name: string) {
        super(
            BAD_REQUEST,
            "Feature Already Exist with name: " + name
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, FeatureAlreadyExistEx.prototype);
    }
}