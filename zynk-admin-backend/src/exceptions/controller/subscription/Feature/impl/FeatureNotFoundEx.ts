import {FeatureException} from "../FeatureException";
import {BAD_REQUEST, NOT_FOUND} from "../../../../../constants/STATUS_CODES";

export class FeatureNotFoundEx extends FeatureException {
    constructor(message: string = "Feature not found") {
        super(
            NOT_FOUND,
            message
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, FeatureNotFoundEx.prototype);
    }
}