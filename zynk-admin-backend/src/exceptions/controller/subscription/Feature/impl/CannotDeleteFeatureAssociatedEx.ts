import {FeatureException} from "../FeatureException";
import {CONFLICT} from "../../../../../constants/STATUS_CODES";

export class CannotDeleteFeatureAssociatedEx extends FeatureException {
    constructor() {
        super(
            CONFLICT,
            "Cannot delete Feature as it is associated with Subscription Tiers"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, CannotDeleteFeatureAssociatedEx.prototype);
    }
}