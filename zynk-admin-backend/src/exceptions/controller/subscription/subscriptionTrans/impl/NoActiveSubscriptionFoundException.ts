import {NOT_FOUND} from "../../../../../constants/STATUS_CODES";
import {SubscriptionException} from "../../SubscriptionException";

export class NoActiveSubscriptionFoundException extends SubscriptionException {
    constructor() {
        super(
            NOT_FOUND,
            "No Active Subscription Found For the tenant"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, NoActiveSubscriptionFoundException.prototype);
    }
}