import {SubscriptionTierException} from "../SubscriptionTierException";
import {BAD_REQUEST} from "../../../../../constants/STATUS_CODES";

export class SubscriptionTierNotFoundEx extends SubscriptionTierException {
    constructor() {
        super(
            BAD_REQUEST,
            "Subscription Tier not found"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, SubscriptionTierNotFoundEx.prototype);
    }
}