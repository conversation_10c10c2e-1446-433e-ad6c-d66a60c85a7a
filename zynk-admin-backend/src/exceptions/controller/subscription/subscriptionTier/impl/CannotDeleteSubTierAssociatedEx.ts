import {SubscriptionTierException} from "../SubscriptionTierException";
import {BAD_REQUEST} from "../../../../../constants/STATUS_CODES";

export class CannotDeleteSubTierAssociatedEx extends SubscriptionTierException {
    constructor() {
        super(
            BAD_REQUEST,
            "Cannot delete this subscription tier as it is associated with active subscriptions."
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, CannotDeleteSubTierAssociatedEx.prototype);
    }
}