import {BAD_REQUEST} from "../../../../../constants/STATUS_CODES";
import {SubscriptionTierException} from "../SubscriptionTierException";

export class SubscriptionAlreadyExistEx extends SubscriptionTierException {
    constructor(name: string) {
        super(
            BAD_REQUEST,
            "Subscription Tier already exist with name: " + name
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, SubscriptionAlreadyExistEx.prototype);
    }
}