import {TenantAdminException} from "../TenantAdminException";
import {INTERNAL_SERVER_ERROR} from "../../../../../constants/STATUS_CODES";

export class TenantNotFoundException extends TenantAdminException {
    constructor(message: string = "No tenant found") {
        super(
            INTERNAL_SERVER_ERROR,
            message
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, TenantNotFoundException.prototype);
    }
}
