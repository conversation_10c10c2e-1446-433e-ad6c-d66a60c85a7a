import {TenantAdminException} from "../TenantAdminException";
import {INTERNAL_SERVER_ERROR} from "../../../../../constants/STATUS_CODES";
import log from "../../../../../helpers/system/logger.helper";

export class TenantAlreadyExistException extends TenantAdminException {
    constructor(name: string | null, emailAddress: string | null, subDomain: string | null) {
        let message = "Tenant Already Exists";

        const nonNullFields: string[] = [];
        if (name) nonNullFields.push(`name: ${name}`);
        if (emailAddress) nonNullFields.push(`email: ${emailAddress}`);
        if (subDomain) nonNullFields.push(`subdomain: ${subDomain}`);

        if (nonNullFields.length > 0) {
            message = `Another tenant already exists with ${nonNullFields.join(", ")}.`;
        } else {
            log.warn("Tenant already exists but all unique fields were null, Please make sure to check your controller logic");
        }

        super(
            INTERNAL_SERVER_ERROR,
            message
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, TenantAlreadyExistException.prototype);
    }
}