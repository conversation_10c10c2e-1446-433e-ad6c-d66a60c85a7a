import {INTERNAL_SERVER_ERROR} from "../../../../../constants/STATUS_CODES";
import {TenantAdminException} from "../TenantAdminException";

export class TenantRoleNotFound extends TenantAdminException {
    constructor() {
        super(
            INTERNAL_SERVER_ERROR,
            "Tenant Admin Role not found. Please ensure that the role exists in the database."
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, TenantRoleNotFound.prototype);
    }
}
