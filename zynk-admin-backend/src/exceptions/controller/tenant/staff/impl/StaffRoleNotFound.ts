import {INTERNAL_SERVER_ERROR} from "../../../../../constants/STATUS_CODES";
import {StaffException} from "../StaffException";

export class StaffRoleNotFound extends StaffException {
    constructor() {
        super(
            INTERNAL_SERVER_ERROR,
            "Staff Role not found. Please ensure that the role exists in the database."
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, StaffRoleNotFound.prototype);
    }
}
