import {AdminException} from "../AdminException";
import {INTERNAL_SERVER_ERROR} from "../../../../constants/STATUS_CODES";

export class SupRoleNotFound extends AdminException {
    constructor() {
        super(INTERNAL_SERVER_ERROR, "Super Admin Role not found. Please ensure that the role exists in the database.");
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, SupRoleNotFound.prototype);
    }
}