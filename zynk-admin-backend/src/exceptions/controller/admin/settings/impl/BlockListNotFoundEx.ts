import {SupSettingsException} from "../SupSettingsException";
import {NOT_FOUND} from "../../../../../constants/STATUS_CODES";

export class BlockListNotFoundEx extends SupSettingsException {
    constructor() {
        super(
            NOT_FOUND,
            "Tenant BlockList Not Found"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, BlockListNotFoundEx.prototype);
    }
}