import {SupSettingsException} from "../SupSettingsException";
import {NOT_FOUND} from "../../../../../constants/STATUS_CODES";

export class CannotDeleteAssociatedRecordsEx extends SupSettingsException {
    constructor() {
        super(
            NOT_FOUND,
            "Cannot Delete Associated Records"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, CannotDeleteAssociatedRecordsEx.prototype);
    }
}