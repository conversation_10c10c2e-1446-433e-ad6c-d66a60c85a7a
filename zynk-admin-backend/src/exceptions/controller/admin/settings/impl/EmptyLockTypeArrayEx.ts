import {NOT_ACCEPTABLE} from "../../../../../constants/STATUS_CODES";
import {SupSettingsException} from "../SupSettingsException";

export class EmptyLockTypeArrayEx extends SupSettingsException {
    constructor() {
        super(
            NOT_ACCEPTABLE,
            "LockType Array Cannot be Empty"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, EmptyLockTypeArrayEx.prototype);
    }
}