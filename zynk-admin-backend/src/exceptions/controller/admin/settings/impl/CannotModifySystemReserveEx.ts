import {SupSettingsException} from "../SupSettingsException";
import {NOT_FOUND} from "../../../../../constants/STATUS_CODES";

export class CannotModifySystemReserveEx extends SupSettingsException {
    constructor() {
        super(
            NOT_FOUND,
            "Cannot Modify System Reserved Data"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, CannotModifySystemReserveEx.prototype);
    }
}