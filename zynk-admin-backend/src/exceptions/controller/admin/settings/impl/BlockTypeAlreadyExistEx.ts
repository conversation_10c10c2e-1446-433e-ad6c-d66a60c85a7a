import {SupSettingsException} from "../SupSettingsException";
import {CONFLICT} from "../../../../../constants/STATUS_CODES";

export class BlockTypeAlreadyExistEx extends SupSettingsException {
    constructor(name: string) {
        super(
            CONFLICT,
            "Block type already exist with name: " + name
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, BlockTypeAlreadyExistEx.prototype);
    }
}