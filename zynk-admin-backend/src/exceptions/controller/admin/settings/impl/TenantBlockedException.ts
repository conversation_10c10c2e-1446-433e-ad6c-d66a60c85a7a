import {SupSettingsException} from "../SupSettingsException";
import {BAD_REQUEST} from "../../../../../constants/STATUS_CODES";

export class TenantBlockedException extends SupSettingsException {
    constructor(message: string = "Tenant is blocked") {
        super(BAD_REQUEST, message);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, TenantBlockedException.prototype);
    }
}