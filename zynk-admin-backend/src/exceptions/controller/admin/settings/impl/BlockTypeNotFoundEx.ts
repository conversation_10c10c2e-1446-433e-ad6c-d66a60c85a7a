import {SupSettingsException} from "../SupSettingsException";
import {NOT_FOUND} from "../../../../../constants/STATUS_CODES";

export class BlockTypeNotFoundEx extends SupSettingsException {
    constructor() {
        super(
            NOT_FOUND,
            "Block Type Not Found"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, BlockTypeNotFoundEx.prototype);
    }
}