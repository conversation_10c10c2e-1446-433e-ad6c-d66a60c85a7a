import {SupSettingsException} from "../SupSettingsException";
import {NOT_FOUND} from "../../../../../constants/STATUS_CODES";

export class NoManualBlockFoundEx extends SupSettingsException {
    constructor() {
        super(
            NOT_FOUND,
            "Tenant is not blocked by manual block (NOT_SYSTEM_MANAGER or TENANT_BLOCKER_ONLY)"
        );
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, NoManualBlockFoundEx.prototype);
    }
}