import {ModelException} from "../ModelException";
import {INTERNAL_SERVER_ERROR} from "../../../constants/STATUS_CODES";
import {getUserModelAssociation, ModelType} from "../../../types/pBAC";
import log from "../../../helpers/system/logger.helper";

export class InSufficientUserAssociation extends ModelException {
    constructor(modelType: ModelType) {
        log.error(`Insufficient user model: "${modelType}" association for accessing this function`);
        log.warn("please these associations", getUserModelAssociation(modelType))
        super(INTERNAL_SERVER_ERROR, `Insufficient user model: "${modelType}" association for accessing this function`);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, InSufficientUserAssociation.prototype);
    }
}