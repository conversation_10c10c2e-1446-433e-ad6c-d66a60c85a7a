import {UNAUTHORIZED} from "../../../../constants/STATUS_CODES";
import {RouteMWException} from "../RouteMWException";

export class UnAuthorizedUserException extends RouteMWException {
    constructor() {
        super(UNAUTHORIZED, "FORBIDDEN: InSufficient PermissionActions!");
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, UnAuthorizedUserException.prototype);
    }
}