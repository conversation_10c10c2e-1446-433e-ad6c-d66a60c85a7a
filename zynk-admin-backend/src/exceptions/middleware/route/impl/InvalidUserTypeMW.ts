import {INTERNAL_SERVER_ERROR} from "../../../../constants/STATUS_CODES";
import {RouteMWException} from "../RouteMWException";

export class InvalidUserTypeMW extends RouteMWException {
    constructor() {
        super(INTERNAL_SERVER_ERROR, "Invalid user type for route authentication");
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, InvalidUserTypeMW.prototype);
    }
}