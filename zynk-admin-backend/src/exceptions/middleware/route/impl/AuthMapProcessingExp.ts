import {RouteMWException} from "../RouteMWException";
import {INTERNAL_SERVER_ERROR} from "../../../../constants/STATUS_CODES";

export class AuthMapProcessingExp extends RouteMWException {
    constructor() {
        super(INTERNAL_SERVER_ERROR, "<PERSON>rror Occurred During Processing Route Authentication Mappings");
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, AuthMapProcessingExp.prototype);
    }
}