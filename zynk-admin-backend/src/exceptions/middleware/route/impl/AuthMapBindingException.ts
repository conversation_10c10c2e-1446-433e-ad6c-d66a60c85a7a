import {RouteMWException} from "../RouteMWException";
import {INTERNAL_SERVER_ERROR} from "../../../../constants/STATUS_CODES";

export class AuthMapBindingException extends RouteMWException {
    constructor() {
        super(INTERNAL_SERVER_ERROR, "Unable To Bind Authentication Mapping into Request ");
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, AuthMapBindingException.prototype);
    }
}