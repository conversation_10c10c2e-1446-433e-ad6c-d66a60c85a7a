import {INTERNAL_SERVER_ERROR} from "../../../../constants/STATUS_CODES";
import {RouteMWException} from "../RouteMWException";

export class PermissionActionProcessingEx extends RouteMWException {
    constructor(warnError: any) {
        super(INTERNAL_SERVER_ERROR, "unable to process permissionAction: " + warnError);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, PermissionActionProcessingEx.prototype);
    }
}