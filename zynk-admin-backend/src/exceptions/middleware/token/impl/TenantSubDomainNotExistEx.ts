import {UNAUTHORIZED} from "../../../../constants/STATUS_CODES";
import {TENANT_NOT_EXIST} from "../../../../constants/middleware/err";
import {TokenMWException} from "../TokenMWException";

export class TenantSubDomainNotExistEx extends TokenMWException {
    constructor() {
        super(UNAUTHORIZED, TENANT_NOT_EXIST);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, TenantSubDomainNotExistEx.prototype);
    }
}