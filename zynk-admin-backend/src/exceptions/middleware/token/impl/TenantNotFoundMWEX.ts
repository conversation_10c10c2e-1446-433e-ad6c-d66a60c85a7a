import {NOT_FOUND} from "../../../../constants/STATUS_CODES";
import {HOSTNAME_NOT_PROVIDED} from "../../../../constants/middleware/err";
import {TokenMWException} from "../TokenMWException";

export class TenantNotFoundMWEX extends TokenMWException {
    constructor() {
        super(NOT_FOUND, HOSTNAME_NOT_PROVIDED);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, TenantNotFoundMWEX.prototype);
    }
}