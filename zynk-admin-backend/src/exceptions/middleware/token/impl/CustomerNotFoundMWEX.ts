import {UNAUTHORIZED} from "../../../../constants/STATUS_CODES";
import {TokenMWException} from "../TokenMWException";
import { CUSTOMER_NOT_FOUND } from "../../../../constants/tenant/customer/err";

export class CustomerNotFoundMWEX extends TokenMWException {
    constructor() {
        super(UNAUTHORIZED, CUSTOMER_NOT_FOUND);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, CustomerNotFoundMWEX.prototype);
    }
}