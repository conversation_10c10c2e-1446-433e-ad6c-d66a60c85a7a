import {UNAUTHORIZED} from "../../../../constants/STATUS_CODES";
import {PLEASE_LOGIN} from "../../../../constants/middleware/err";
import {TokenMWException} from "../TokenMWException";

export class AdminNotFoundMWException extends TokenMWException {
    constructor() {
        super(UNAUTHORIZED, PLEASE_LOGIN);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, AdminNotFoundMWException.prototype);
    }
}