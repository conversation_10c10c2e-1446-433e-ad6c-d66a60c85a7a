import {UNAUTHORIZED} from "../../../../constants/STATUS_CODES";
import {TokenMWException} from "../TokenMWException";
import {STAFF_NOT_FOUND} from "../../../../constants/tenant/staff/err";

export class StaffNotFoundMWEX extends TokenMWException {
    constructor() {
        super(UNAUTHORIZED, STAFF_NOT_FOUND);
        Error.captureStackTrace(this, this.constructor);
        Object.setPrototypeOf(this, StaffNotFoundMWEX.prototype);
    }
}