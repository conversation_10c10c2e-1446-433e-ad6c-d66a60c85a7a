import { z } from "zod";
import {
  emailAddressSchema,
  phoneNumberSchema,
  TimeSchema,
  utcTimeSchema,
  UUIDSchema,
} from "../common.validation";

export const branchDetailsSchema = z.object({
  name: z.string().min(1, "Name of tenantSettings cannot be empty!"),
  isActive: z.boolean(),
  location: z.object({
    lat: z
      .number()
      .min(-90, "Latitude must be between -90 and 90")
      .max(90, "Latitude must be between -90 and 90"),
    lng: z
      .number()
      .min(-180, "Longitude must be between -180 and 180")
      .max(180, "Longitude must be between -180 and 180"),
  }),

  country: z.string().min(1, "Country of tenantSettings cannot be empty!"),

  city: z.string().min(1, "City of tenantSettings cannot be empty!"),

  state: z.string().min(1, "State of tenantSettings cannot be empty!"),

  postalCode: z.string().min(1, "Postal code of tenantSettings cannot be empty!"),

  streetName: z.string().min(1, "Street name of tenantSettings cannot be empty!"),

  houseNumber: z.string().min(1, "House number of tenantSettings cannot be empty!"),

  apartment: z.string().optional(),

  locationName: z.string().min(1, "Location of tenantSettings cannot be empty!"),
});

export const branchContactsSchema = z.array(
  z.object({
    name: z.string().min(1, "Name of person cannot be empty!"),

    emailAddress: emailAddressSchema(
      "Invalid email address for tenantSettings contacts"
    ),

    phoneNumber: phoneNumberSchema,

    faxNumber: z.string().optional(),
  })
);

export const branchHoursSchema = z.object({
  useDefault: z.boolean(),
  businessHours: z.array(
    z.object({
      days: z.record(
        z.enum(["M", "T", "W", "Th", "F", "Sa", "Su"]),
        z.boolean()
      ),
      is24Hours: z.boolean(),
      firstSeating: TimeSchema,
      isActive: z.boolean(),
      lastSeating: TimeSchema,
    })
  ),
  customHours: z
    .array(
      z.object({
        days: z.record(
          z.enum(["M", "T", "W", "Th", "F", "Sa", "Su"]),
          z.boolean()
        ),
        name: z.string().min(1, "Custom hour slot name cannot be empty!"),
        is24Hours: z.boolean(),
        firstSeating: TimeSchema,
        isActive: z.boolean(),
        lastSeating: TimeSchema,
      })
    )
    .optional(),
});

export const specialDaysSchema = z.array(
  z.object({
    eventName: z.string().min(1, "Event name cannot be empty!"),

    startTime: utcTimeSchema(
      "Timestamp must be in UTC ISO format for startTime"
    ),

    endTime: utcTimeSchema("Timestamp must be in UTC ISO format for endTime"),
    availability: z.boolean(),
  })
);

export const createBranchSchema = z.object({
  branchDetails: branchDetailsSchema,

  branchContacts: branchContactsSchema,

  branchHours: branchHoursSchema,
  specialDays: specialDaysSchema.optional(),
});

export const branchIdSchema = z.object({
  branchId: UUIDSchema("Invalid UUID for tenantSettings"),
});

export const patchBranchStatusSchema = z.object({
  isActive: z.boolean(),
});

export const patchBranchDetailsSchema = branchDetailsSchema.partial().strict();

export const patchBranchContactSchema = z.object({
  branchContacts: z.array(
    z.object({
      branchContactId: UUIDSchema("Invalid ID for tenantSettings contacts").optional(),
      name: z.string().optional(),
      emailAddress: z.string().optional(),
      phoneNumber: z.string().optional(),
      faxNumber: z.string().optional(),
    }).superRefine((data, ctx) => {
      if (!data.branchContactId) {
        if (!data.name || data.name.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Name of person cannot be empty!",
            path: ['name']
          });
        }
        
        if (!data.emailAddress) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Email address is required",
            path: ['emailAddress']
          });
        }
        
        if (!data.phoneNumber) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Phone number is required",
            path: ['phoneNumber']
          });
        }
      }
      
      if (data.emailAddress) {
        const emailResult = emailAddressSchema("Invalid email address for tenantSettings contacts")
          .safeParse(data.emailAddress);
        if (!emailResult.success) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Invalid email address for tenantSettings contacts",
            path: ['emailAddress']
          });
        }
      }
      
      if (data.phoneNumber) {
        const phoneResult = phoneNumberSchema.safeParse(data.phoneNumber);
        if (!phoneResult.success) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Invalid phone number format",
            path: ['phoneNumber']
          });
        }
      }
    })
  )
});

const DaysEnum = z.enum(["M", "T", "W", "Th", "F", "Sa", "Su"]);

const BaseSlotSchema = z.object({
  days: z.record(DaysEnum, z.boolean()),
  is24Hours: z.boolean(),
  firstSeating: TimeSchema,
  isActive: z.boolean(),
  lastSeating: TimeSchema,
});

const SlotSchema = z.object({
  slotId: UUIDSchema().optional(),
  days: z.record(DaysEnum, z.boolean()).optional(),
  is24Hours: z.boolean().optional(),
  isActive: z.boolean().optional(),
  firstSeating: TimeSchema.optional(),
  lastSeating: TimeSchema.optional(),
}).refine(
  (data) => {
    if (!data.slotId) {
      return !!data.days && data.is24Hours !== undefined && !!data.firstSeating && !!data.lastSeating;
    }
    return true;
  },
  {
    message: "When slotId is not provided, days, is24Hours, firstSeating, and lastSeating are required",
    path: ["slotId"],
  }
);

export const CustomSlotSchema = z.object({
  slotId: UUIDSchema().optional(),
  name: z.string().min(1, "Custom slot name cannot be empty").optional(),
  days: z.record(DaysEnum, z.boolean()).optional(),
  isActive: z.boolean().optional(),
  is24Hours: z.boolean().optional(),
  firstSeating: TimeSchema.optional(),
  lastSeating: TimeSchema.optional(),
}).refine(
  (data) => {
    if (!data.slotId) {
      return !!data.name && !!data.days && data.is24Hours !== undefined && !!data.firstSeating && !!data.lastSeating;
    }
    return true;
  },
  {
    message: "When slotId is not provided, name, days, is24Hours, firstSeating, and lastSeating are required",
    path: ["slotId"],
  }
);

export const SpecialDaySchema = z.object({
  specialDayId: UUIDSchema().optional(),
  eventName: z.string().min(1, "Event name cannot be empty!").optional(),
  startTime: utcTimeSchema("Timestamp must be in UTC ISO format for startTime").optional(),
  endTime: utcTimeSchema("Timestamp must be in UTC ISO format for endTime").optional(),
  availability: z.boolean().optional(),
}).refine(
  (data) => {
    if (!data.specialDayId) {
      return !!data.eventName && !!data.startTime && !!data.endTime && data.availability !== undefined;
    }
    return true;
  },
  {
    message: "When specialDayId is not provided, eventName, startTime, endTime, and availability are required",
    path: ["specialDayId"],
  }
);

export const patchBranchBusinessHoursSchema = z.object({
  slots: z.array(SlotSchema).optional(),
  useDefault: z.boolean().optional(),
  customSlots: z.array(CustomSlotSchema).optional(),
  specialDays: z.array(SpecialDaySchema).optional(),
}).refine(
  (data) => {
    return !!data.slots || !!data.customSlots || !!data.specialDays;
  },
  {
    message: "At least one of slots, customSlots, or specialDays must be provided",
    path: [],
  }
);

export type PatchBranchBusinessHoursRequest = z.infer<typeof patchBranchBusinessHoursSchema>;