import { z } from "zod";
import {
  emailAddressSchema,
  phoneNumberSchema,
  UUIDSchema,
} from "../common.validation";

export const patchCompanySchema = z
  .object({
    regNumber: z.string().min(1, "Registration number cannot be empty!"),
    name: z.string().min(1, "Name cannot be empty!"),
    description: z.string().min(1, "Description cannot be empty!"),
    businessAlias: z
      .string()
      .min(1, "Business alias/doing work as cannot be empty!"),
    regDocument: z
      .string()
      .url("Invalid URL for company registration document"),
    logoUrl: z.string().url("Invalid URL for logo"),
    addrLine1: z.string().min(1, "Address line 1 cannot be empty!"),
    addrLine2: z.string(),
    city: z.string().min(1, "City cannot be empty!"),
    state: z.string().min(1, "State cannot be empty!"),
    postalCode: z.string().min(1, "Postal code cannot be empty!"),
    country: z.string().min(1, "Country cannot be empty!"),
    taxIDNumber: z
      .string()
      .min(1, "Tax Identification number cannot be empty!"),
    businessType: z.enum(["individual", "soleproprietor", "corporation"], {
      errorMap: () => ({
        message: "Invalid business type. Should be 'individual', 'soleproprietor' or 'corporation'",
      }),
    }),
  })
  .partial()
  .strict();

export const createCompanyRepSchema = z.object({
  photoUrl: z.string().url("Invalid URL for photo"),
  firstName: z.string().min(1, "First name cannot be empty!"),
  middleName: z.string(),
  lastName: z.string().min(1, "Last name cannot be empty!"),
  dob: z.coerce.date(),
  emailAddress: emailAddressSchema(
    "Invalid email address for company representative"
  ),
  phoneNumber: phoneNumberSchema,
  country: z.string().min(1, "Country name cannot be empty!"),
  address: z.string().min(1, "Address cannot be empty!"),
  city: z.string().min(1, "City cannot be empty!"),
  state: z.string().min(1, "State cannot be empty!"),
  postalCode: z.string().min(1, "Postal code cannot be empty!"),
  jobTitle: z.string().min(1, "Job title cannot be empty!"),
  highOwnership: z.boolean(),
});

export const patchCompanyRepSchema = createCompanyRepSchema.partial().strict();

export const accountRepIdSchema = z.object({
  accountRepId: UUIDSchema("Invalid ID for account representative"),
});

export const bankDetailPatchSchema = z.object({
  bankName: z.string().min(1, "Bank name cannot be empty!"),
  sortCode: z.string().min(1, "Sort code cannot be empty!"),
  accountNumber: z.string().min(1, "Account number cannot be empty!"),
}).partial().strict();
