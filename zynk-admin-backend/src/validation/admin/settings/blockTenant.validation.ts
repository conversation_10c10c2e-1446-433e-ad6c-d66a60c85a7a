import {z} from "zod";
import {
    createUuidSchema,
    DescriptionSchema,
    LockTypeSchema,
    NameSchema,
    ResponseTypeZodEnum
} from "../../common.validation";

// TenantBlockList
export const blockTenantFilterSchema = z.object({
    responseType: ResponseTypeZodEnum().optional(),
    tenantId: createUuidSchema("tenantId").optional(),
    blockTypeId: createUuidSchema("blockTypeId").optional(),
    lockType: LockTypeSchema.optional(),
}).strict();

export const blockTenantSchema = z.object({
    tenantId: createUuidSchema("tenantId"),
    blockTypeId: createUuidSchema("blockTypeId").optional(),
    isAdvanced: z.boolean({invalid_type_error: "isAdvanced must be a boolean"}).default(false).optional(),
    blockTypeName: NameSchema.optional(),
    blockMessage: DescriptionSchema.optional()
}).strict().superRefine((data, ctx) => {
    if (!data.isAdvanced) {
        if (!data.blockTypeId) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'blockTypeId is required when isAdvanced is false',
                path: ['blockTypeId']
            });
        }
    } else {
        if (!data.blockTypeName) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'blockTypeName is required when isAdvanced is true',
                path: ['blockTypeName']
            });
        }

        if (!data.blockMessage) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'blockMessage is required when isAdvanced is true',
                path: ['blockMessage']
            });
        }
    }
});
export type blockTenantSchemaType = z.infer<typeof blockTenantSchema>;

export const unblockTenantSchema = z.object({
    tenantBlockListId: createUuidSchema("tenantBlockListId").optional(),
    isAdvanced: z
        .union([
            z.boolean(),
            z.string().transform((val) => {
                if (val === "true") return true;
                if (val === "false") return false;
                throw new Error("Invalid boolean string");
            })
        ])
        .default(false)
        .optional(),
    tenantId: createUuidSchema("tenantId").optional(),
}).strict().superRefine((data, ctx) => {
    if (!data.isAdvanced) {
        if (!data.tenantBlockListId) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'tenantBlockListId is required when isAdvanced is false',
                path: ['tenantBlockListId']
            });
        }

        if (data.tenantId) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'tenantId should not be provided when isAdvanced is false',
                path: ['tenantId']
            });
        }
    } else {
        if (!data.tenantId) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'tenantId is required when isAdvanced is true',
                path: ['tenantId']
            });
        }

        if (data.tenantBlockListId) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'tenantBlockListId should not be provided when isAdvanced is true',
                path: ['tenantBlockListId']
            });
        }
    }
});

export type unblockTenantSchemaType = z.infer<typeof unblockTenantSchema>;

// BlockType
export const blockTypeFilterSchema = z.object({
    blockTypeId: createUuidSchema("blockTypeId").optional(),
    responseType: ResponseTypeZodEnum().optional(),
    name: NameSchema.optional(),
    lockType: LockTypeSchema.optional(),
}).strict();

export const createBlockTypeSchema = z.object({
    name: NameSchema,
    blockMessage: DescriptionSchema,
    isActive: z.boolean({invalid_type_error: "isActive must be a boolean"}).default(true).optional()
}).strict();

export type createBlockTypeSchemaType = z.infer<typeof createBlockTypeSchema>;

export const patchBlockTypeSchema = createBlockTypeSchema.partial().extend({
    blockTypeId: createUuidSchema("blockTypeId")
}).strict();

export type patchBlockTypeSchemaType = z.infer<typeof patchBlockTypeSchema>;

export const deleteBlockTypeSchema = z.object({
    blockTypeId: createUuidSchema("blockTypeId")
}).strict();