import {z} from "zod";
import {createUuidSchema, emailAddressSchema, passwordSchema} from "../common.validation";
import {userPermissionActionSchema} from "../pCAB/userPermissionAction.validation";

export const createSuperAdminSchema = z.object({

    username: z.string().min(1, "Username cannot be empty!"),
    emailAddress: emailAddressSchema("Invalid email address"),
    password: passwordSchema

});

export const loginSuperAdminSchema = z.object({
    emailAddress: emailAddressSchema("Invalid email address"),

    password: passwordSchema
});

export const patchSuperAdminSchema = z.object({
    oldPassword: passwordSchema,
    newPassword: passwordSchema.optional(),
    emailAddress: emailAddressSchema("Invalid email address").optional(),
    username: z.string().min(1, "Username cannot be empty!").optional(),
    superAdminPermissionAction: userPermissionActionSchema.optional()
})

export const deleteSuperAdminSchema = z.object({
    password: passwordSchema
})

export const revokeSuperAdminSchema = z.object({
    emailAddress: emailAddressSchema("Invalid email address"),
    password: passwordSchema,
    ipAddress: z.string().nonempty("IP Address cannot be empty!"),
});

export const viewSuperSessionParams = z.object({
    superAdminId: createUuidSchema("superAdminId")
})