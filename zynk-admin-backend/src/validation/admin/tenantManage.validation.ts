import { z } from "zod";
import {
  emailAddressSchema,
  passwordSchema,
  phoneNumberSchema,
  PINSchema,
  UUIDSchema,
} from "../common.validation";

export const createTenantSchema = z.object({
  name: z.string().min(1, "Tenant name cannot be empty!"),
  subDomain: z.string().min(1, "Tenant subdomain name cannot be empty!"),
  description: z.string().min(1, "Business description cannot be empty!"),
  businessAlias: z
    .string()
    .min(1, "Business alias/doing work as cannot be empty!"),
  regDocument: z.string().url("Invalid URL for company registration document"),
  emailAddress: emailAddressSchema("Invalid email ID for tenant!"),
  phoneNumber: phoneNumberSchema,
  pin: PINSchema,
  regNo: z.string().min(1, "Registration number cannot be empty!"),
  companyName: z.string().min(1, "Company name cannot be empty!"),
  addrLine1: z.string().min(1, "Address line 1 cannot be empty!"),
  addrLine2: z.string(),
  city: z.string().min(1, "City cannot be empty!"),
  state: z.string().min(1, "State cannot be empty!"),
  postalCode: z.string().min(1, "Postal code cannot be empty!"),
  country: z.string().min(1, "Country cannot be empty!"),
  taxIdentificationNumber: z
    .string()
    .min(1, "Tax Identification number cannot be empty!"),
  password: passwordSchema,
  roles: z
    .array(z.string().min(1, "Rolename cannot be empty!"))
    .nonempty("Roles cannot be empty!"),
  subscription: z.string().min(1, "Subscription cannot be empty!"),
  status: z.enum(["draft", "active"], {
    errorMap: () => ({
      message: "Invalid status. Should be either 'draft' or 'active'",
    }),
  }),
  businessType: z.enum(["individual", "soleproprietor", "corporation"], {
    errorMap: () => ({
      message: "Invalid status. Should be either 'draft' or 'active'",
    }),
  }),
  logoUrl: z.string().url("Invalid url for logo").optional(),
});

export const tenantParamsSchema = z.object({
  tenantId: UUIDSchema("Invalid UUID for tenant!"),
});

export const patchTenantSchema = createTenantSchema.partial().strict();

export const revokeTenantSchema = z.object({
  ipAddress: z.string().nonempty("IP Address cannot be empty!"),
});
