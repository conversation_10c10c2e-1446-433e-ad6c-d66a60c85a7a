import { z } from "zod";
import { UUIDSchema } from "../common.validation";
import {
  CustomSlotSchema,
  SpecialDaySchema,
} from "../company/branch.validation";
import { dietaryInfoLabels } from "./dish.validation";

export const createFoodMenuSchema = z.object({
  pictureUrl: z.string().url("Invalid Picture URL for menu").optional(),
  name: z
    .string()
    .min(1, "Food menu name must be present")
    .max(100, "Food Menu name cannot exceed more than 100 characters!"),
  description: z.string().optional(),
  visibility: z.boolean().optional(),
});

export const foodMenuIdSchema = z.object({
  foodMenuId: UUIDSchema("Invalid UUID for foodmenu"),
});

export const patchFoodMenuStatusSchema = z.object({
  isDefault: z.boolean(),
});

export const patchFoodMenuAttrSchema = createFoodMenuSchema.partial().strict();

export const patchFoodMenuCustomHoursSchema = z.object({
  customTimes: z.boolean().optional(),
  customSlots: z.array(CustomSlotSchema).optional(),
  globalCustomSlots: z
    .array(UUIDSchema("Invalid UUID for global custom hours"))
    .optional(),
});

export const patchFoodMenuAvailabilitySchema = z.object({
  availability: z.object({
    delivery: z.boolean().optional(),
    pickup: z.boolean().optional(),
    dineIn: z.boolean().optional(),
    takeAway: z.boolean().optional(),
    contactlessDineIn: z.boolean().optional(),
    site: z.boolean().optional(),
    mobile: z.boolean().optional(),
    phoneOrder: z.boolean().optional(),
  }),
});

export const patchFoodMenuSpecialDaysSchema = z.object({
  specialDays: z.array(SpecialDaySchema).optional(),
});

export const patchUpcomingChangeSchema = z.array(
  z.object({
    changeId: UUIDSchema("Invalid UUID for upcoming change"),
    isActive: z.boolean(),
    activeFrom: z.coerce.date(),
    dishes: z.array(
      z.object({
        dishChangeId: UUIDSchema("Invalid UUID for dish change"),
        dishId: UUIDSchema("Invalid UUID for dish"),
        newName: z.string().min(1, "Dish name should exist!"),
        price: z.number().nonnegative("Price must be a positive number"),
        dietaryInfo: z
          .array(dietaryInfoLabels, {
            errorMap: () => ({
              message: "Invalid dietary info label",
            }),
          })
      }).partial().strict()
    ),
  }).partial().strict()
);

export const upcomingChangeParamsSchema = z.object({
  changeId: UUIDSchema("Invalid UUID for upcoming change")
})

export const foodMenuWithAvailabilityParams = z.object({
  branchId: UUIDSchema("Invalid UUID for branch!"),

  pos: z.string().min(1, "POS flag cannot be empty!").optional()
});

export const onlineMenuFetchSchema = z.object({
  branchId: UUIDSchema("Invalid UUID for branch!"),
}).partial().strict();
