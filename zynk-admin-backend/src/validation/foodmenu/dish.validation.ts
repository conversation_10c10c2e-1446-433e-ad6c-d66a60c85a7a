import { z } from "zod";
import { UUIDSchema } from "../common.validation";

export const dietaryInfoLabels = z.enum([
  "special",
  "vegan",
  "vegetarian",
  "gluten-free",
  "organic",
  "spicy",
  "hot",
  "extra-hot",
]);

const dishTagLabels = z.enum(["side", "beverage", "dessert"]);

export const dishDetailsSchema = z.object({
  pictureUrl: z.string().url("Invalid URL for dish picture"),

  name: z
    .string()
    .min(1, "Dish name is required")
    .max(100, "Dish name must be less than 100 characters"),

  description: z.string().optional(),

  price: z.number().nonnegative("Price must be a positive number"),

  dietaryInfo: z
    .array(dietaryInfoLabels, {
      errorMap: () => ({
        message: "Invalid dietary info label",
      }),
    })
    .optional(),

  tags: z
    .array(dishTagLabels, {
      errorMap: () => ({
        message: "Invalid dish tag label",
      }),
    })
    .optional(),

  isCustomizable: z.boolean(),
  customTimes: z.boolean().optional(),

  visibility: z.boolean(),
  preparationTime: z.number().nonnegative("Number of minutes needed to prepare dish.")
});

export const createDishSchema = z.object({
  dishDetails: dishDetailsSchema,

  auxiliaryDetails: z
    .object({
      dishIngredients: z.array(
        z.object({
          ingredientId: UUIDSchema("Invalid ingredient ID"),
          amount: z.number().nonnegative("Amount must be a positive number"),
        })
      ),

      allergies: z.array(UUIDSchema("Invalid allergy ID")),
    })
    .partial()
    .strict()
    .optional(),

  customizations: z
    .object({
      maxSelection: z
        .number()
        .nonnegative("Max selection must be a positive number")
        .default(4),

      enforceMaxSelection: z.boolean().default(true),

      dishSizes: z.array(
        z.object({
          name: z
            .string()
            .min(1, "Size name is required")
            .max(100, "Size name must be less than 100 characters"),

          price: z.number().nonnegative("Size price must be a positive number"),

          isDefault: z.boolean().default(false),
        })
      ),

      dishExclusions: z.array(
        z.object({
          name: z
            .string()
            .min(1, "Exclusion name is required")
            .max(100, "Exclusion name must be less than 100 characters"),
        })
      ),

      cookingStyles: z.array(
        z.object({
          name: z
            .string()
            .min(1, "Cooking style name is required")
            .max(100, "Cooking style name must be less than 100 characters"),
        })
      ),

      spiciness: z.array(
        z.object({
          name: z
            .string()
            .min(1, "Spiciness name is required")
            .max(100, "Spiciness name must be less than 100 characters"),
          price: z
            .number()
            .nonnegative("Spiciness price must be a positive number"),
        })
      ),

      dishAddons: z.array(
        z.object({
          addonId: UUIDSchema("Invalid addon ID"),
        })
      ),

      dishExtras: z.array(
        z.object({
          extraId: UUIDSchema("Invalid extra ID"),
        })
      ),

      dishSides: z.array(
        z.object({
          dishSideId: UUIDSchema("Invalid dish side ID"),
        })
      ),

      dishBeverages: z.array(
        z.object({
          dishBevId: UUIDSchema("Invalid dish beverage ID"),
        })
      ),

      dishDesserts: z.array(
        z.object({
          dishDessertId: UUIDSchema("Invalid dish dessert ID"),
        })
      ),
    })
    .partial()
    .strict()
    .optional(),
});

export const dishIdSchema = z.object({
  dishId: UUIDSchema("Invalid ID for dish!"),
});

export const patchDishSchema = z.object({
  dishDetails: dishDetailsSchema
    .extend({
      availability: z
        .object({
          global: z.boolean(),
          delivery: z.boolean(),
          pickup: z.boolean(),
          dineIn: z.boolean(),
          takeAway: z.boolean(),
          contactlessDineIn: z.boolean(),
          site: z.boolean(),
          mobile: z.boolean(),
          phoneOrder: z.boolean(),
        })
        .partial()
        .strict(),

      specialRequests: z.boolean()
    })
    .partial()
    .strict(),
  auxiliaryDetails: z
    .object({
      dishIngredients: z.array(
        z.object({
          ingredientId: UUIDSchema("Invalid ingredient ID"),
          amount: z.number().nonnegative("Amount must be a positive number"),
        })
      ),

      allergies: z.array(UUIDSchema("Invalid allergy ID")),
    })
    .partial()
    .strict()
    .optional(),

  customizations: z
    .object({
      maxSelection: z
        .number()
        .nonnegative("Max selection must be a positive number")
        .default(4),

      enforceMaxSelection: z.boolean().default(true),

      dishSizes: z.array(
        z
          .object({
            dishSizeId: UUIDSchema("Invalid dish size ID"),
            name: z
              .string()
              .min(1, "Size name is required")
              .max(100, "Size name must be less than 100 characters"),

            price: z
              .number()
              .nonnegative("Size price must be a positive number"),

            isDefault: z.boolean().default(false),
          })
          .partial()
          .strict()
      ),

      dishExclusions: z.array(
        z
          .object({
            dishExclusionId: UUIDSchema("Invalid dish exclusion ID"),
            name: z
              .string()
              .min(1, "Exclusion name is required")
              .max(100, "Exclusion name must be less than 100 characters"),
          })
          .partial()
          .strict()
      ),

      cookingStyles: z.array(
        z
          .object({
            cookingStyleId: UUIDSchema("Invalid cooking style ID"),
            name: z
              .string()
              .min(1, "Cooking style name is required")
              .max(100, "Cooking style name must be less than 100 characters"),
          })
          .partial()
          .strict()
      ),

      spiciness: z.array(
        z
          .object({
            spicinessId: UUIDSchema("Invalid spiciness ID"),
            name: z
              .string()
              .min(1, "Spiciness name is required")
              .max(100, "Spiciness name must be less than 100 characters"),
            price: z
              .number()
              .nonnegative("Spiciness price must be a positive number"),
          })
          .partial()
          .strict()
      ),

      dishAddons: z.array(
        z
          .object({
            addonId: UUIDSchema("Invalid addon ID"),
          })
          .partial()
          .strict()
      ),

      dishExtras: z.array(
        z
          .object({
            extraId: UUIDSchema("Invalid extra ID"),
          })
          .partial()
          .strict()
      ),

      dishSides: z.array(
        z
          .object({
            dishSideId: UUIDSchema("Invalid dish side ID"),
          })
          .partial()
          .strict()
      ),

      dishBeverages: z.array(
        z
          .object({
            dishBevId: UUIDSchema("Invalid dish beverage ID"),
          })
          .partial()
          .strict()
      ),

      dishDesserts: z.array(
        z
          .object({
            dishDessertId: UUIDSchema("Invalid dish dessert ID"),
          })
          .partial()
          .strict()
      ),
    })
    .partial()
    .strict()
    .optional(),
});