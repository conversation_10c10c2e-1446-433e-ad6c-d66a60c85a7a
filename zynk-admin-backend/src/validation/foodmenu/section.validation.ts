import { z } from "zod";
import { UUIDSchema } from "../common.validation";

export const createSectionSchema = z.object({
      pictureUrls: z.array(z.string().url("Invalid URL for section images")).nonempty("Pictures for section cannot be empty!"),
    
      name: z.string().min(1, "Name of section cannot be empty!").max(100, "Name of section cannot exceed 100 characters"),
    
      sectionIconId: UUIDSchema("Invalid UUID for section icon"),
      description: z.string().optional(),
      visibility: z.boolean().optional()
})

export const sectionIdSchema = z.object({
      sectionId: UUIDSchema("Invalid UUID for section")
})

export const patchSectionSchema = createSectionSchema.partial().strict()