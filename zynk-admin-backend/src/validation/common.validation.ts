import {z} from "zod";
import {OperationType} from "../types/pBAC";
import {LockedType, ResponseType} from "../types/system";
import {FormatType} from "../types/pBAC/formatterTypes";
import {Duration, SubsOperationType} from "../types/subscription";
import {getCountry} from "../helpers/subscription/country.helper";

export const emailAddressSchema = (errMessage: string = "Invalid email") => {
    return z.string().email(errMessage);
};
// admin

export const NameSchema = z.string({
    required_error: "Name is required",
    invalid_type_error: "Name must be a string"
}).min(3, "Name must be at least 3 characters long").max(100, "Name must be at most 100 characters long");

export const DescriptionSchema = z.string({
    required_error: "Description is required",
    invalid_type_error: "Description must be a string"
}).min(5, "Description must be at least 5 characters long").max(1000, "Description must be at most 1000 characters long");

export const createUuidSchema = (keyName: string, customMessage?: string) =>
    z.string({
        required_error: `${keyName} is required`,
    }).uuid({
        message: customMessage ?? `${keyName} must be a valid UUID`,
    });

export const countryCodeSchema = z
    .string({required_error: 'please provide a valid country code'})
    .refine(
        (code) => {
            const country = getCountry(code);
            return country !== undefined
        },
        {message: 'please provide a valid country code'}
    );

export const ResponseTypeZodEnum = () =>
    z.string({invalid_type_error: 'Invalid response type'})
        .refine(
            (val) => Object.values(ResponseType).includes(val as ResponseType),
            {message: 'Invalid response type'}
        )
        .default(ResponseType.MINIMAL)
        .optional();

export const DurationZodEnum = (message: string = "Invalid Tier Type") =>
    z.string({invalid_type_error: message})
        .refine(
            (val) => Object.values(Duration).includes(val as Duration),
            {message: message}
        )

export const FormatTypeZodEnum = () =>
    z.string({invalid_type_error: 'Invalid Tier Type'})
        .refine(
            (val) => Object.values(FormatType).includes(val as FormatType),
            {message: 'Invalid format type'}
        )
        .default(FormatType.OBJECT_ARR_OBJECT)
        .optional()
export const OperationTypeZodEnum = z.enum(Object.values(OperationType) as [string, ...string[]], {
    errorMap: (issue, ctx) => ({
        message: `operationType must be one of ${Object.values(OperationType).join(', ')}, got '${ctx.data}'`,
    }),
});

export const overRideType = z.enum(Object.values(SubsOperationType) as [string, ...string[]], {
    errorMap: (issue, ctx) => ({
        message: `overRideType must be one of ${Object.values(OperationType).join(', ')}, got '${ctx.data}'`,
    }),
});

export const LockTypeSchema = z.enum(Object.values(LockedType) as [string, ...string[]], {
    invalid_type_error: "lockType must be a type of LockedType",
    required_error: "lockType is required"
})

export const permissionActionSchema = z.array(
    z.object({
        actionId: createUuidSchema("actionId"),
        permissionId: createUuidSchema("permissionId"),
        operationType: OperationTypeZodEnum
    })
).min(1, "At least one permission action is required");

export const StringToBooleanQuerySchema = z
    .string({invalid_type_error: 'isActive must be a string'})
    .transform((val, ctx) => {
        if (val === 'true') return true;
        if (val === 'false') return false;
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'isActive must be "true" or "false"',
        });
        return z.NEVER;
    })

export const UUIDSchema = (errMessage: string = "Invalid uuid") => {
    return z.string().uuid(errMessage);
};

export const TimeSchema = z
    .string()
    .regex(
        /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/,
        "Invalid time format. Use HH:MM:SS"
    );

export const MinimalTimeSchema = z
    .string()
    .regex(
        /^([01]\d|2[0-3]):([0-5]\d)$/,
        "Invalid time format. Use HH:MM"
    );

export const phoneNumberSchema = z
    .string()
    .regex(
        /^\+?[1-9]\d{0,2}[\s.-]?(?:\(\d{1,4}\)|\d{1,4})[\s.-]?\d{1,4}[\s.-]?\d{1,9}$/,
        "Please enter a valid phone number. International format with country code is supported."
    );

export const passwordSchema = z
    .string()
    .nonempty("Password cannot be empty")
    .min(8, "Password must be atleast 8 characters long")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number")
    .regex(
        /[!@#$%^&*()\-_=+{}[\]|\\;:'",.<>?/`~]/,
        "Password must contain at least one special character"
    );

export const loginSchema = z.object({
    emailAddress: emailAddressSchema("Invalid email address for user!"),
    password: passwordSchema,
});

export const utcTimeSchema = (errMessage: string = "Timestamp must be in UTC ISO format (e.g., 2025-05-07T14:00:00Z)") => {
    return z.string().refine(
        (val) => {
            const date = new Date(val);
            return (
                !isNaN(date.getTime()) &&
                val.endsWith('Z') &&
                /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/.test(val)
            );
        },
        {
            message: errMessage,
        }
    );
}

export const DAY_AVAILABILITY = z.enum(['off', 'active', 'leave']);

export const DayOfWeek = z.enum(['Su', 'M', 'T', 'W', 'Th', 'F', 'Sa']);

export const ShiftType = z.enum(['morning', 'noon', 'night']);

export const DayAvailType = z.enum(['M', 'T', 'W', 'Th', 'F', 'Sa', 'Su']);

export const ShiftTimingSchema = z.object({
    isActive: z.boolean(),
    clockInTime: z.string().optional().refine((time) => {
        if (!time) return true;
        return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(time);
    }, {
        message: "Invalid time format. Expected HH:MM or HH:MM:SS"
    }),
    clockOutTime: z.string().optional().refine((time) => {
        if (!time) return true;
        return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(time);
    }, {
        message: "Invalid time format. Expected HH:MM or HH:MM:SS"
    })
}).refine((data) => {
    if (data.isActive && (!data.clockInTime || !data.clockOutTime)) {
        return false;
    }
    return true;
}, {
    message: "Active shifts must have both clockInTime and clockOutTime"
});

export const DailyShiftTimingsSchema = z.object({
    morning: ShiftTimingSchema,
    noon: ShiftTimingSchema,
    night: ShiftTimingSchema
});

export const PINSchema = z
    .number()
    .int("PIN must be an integer")
    .min(10000, "PIN must be a 5 digit number")
    .max(99999, "PIN must be a 5 digit number")