import { z } from "zod";
import { UUIDSchema } from "../common.validation";

export const createSectionIconSchema = z.object({
  iconName: z
    .string()
    .min(1, "Name cannot be empty!")
    .max(100, "Name cannot exceed 100 characters"),
  iconImg: z.string().url("Invalid URL for icon image"),
});

export const patchSectionIconSchema = createSectionIconSchema.partial().strict();

export const sectionIconIdSchema = z.object({
  sectionIconId: UUIDSchema(),
});