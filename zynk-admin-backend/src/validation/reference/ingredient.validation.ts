import { z } from "zod";
import { UUIDSchema } from "../common.validation";

export const createIngredientSchema = z.object({
  name: z
    .string()
    .min(1, "Name cannot be empty!")
    .max(100, "Name cannot exceed 100 characters"),
  description: z
    .string()
    .max(1000, "Description cannot exceed 1000 characters")
    .optional(),
  unit: z
    .string()
    .min(1, "Unit cannot be empty!")
    .max(50, "Unit cannot exceed 50 characters")
    .default("g"),
});

export const patchIngredientSchema = createIngredientSchema.partial().strict();

export const ingredientIdSchema = z.object({
  ingredientId: UUIDSchema(),
});