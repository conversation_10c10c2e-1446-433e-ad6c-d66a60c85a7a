import { z } from "zod";
import { UUIDSchema } from "../common.validation";

export const createDishExtraSchema = z.object({
  name: z
    .string()
    .min(1, "Name cannot be empty!")
    .max(100, "Name cannot exceed 100 characters"),
  price: z
    .number()
    .positive("Price must be positive")
    .max(10000, "Price cannot exceed 10000")
    .refine((val) => Number(val.toFixed(2)) === val, {
      message: "Price must have at most 2 decimal places",
    }),
    imgUrl: z.string().url("Invalid URL for image")
});

export const patchDishExtraSchema = createDishExtraSchema.partial().strict();

export const dishExtraIdSchema = z.object({
  extraId: UUIDSchema(),
});
