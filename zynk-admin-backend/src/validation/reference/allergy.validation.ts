import { z } from "zod";
import { UUIDSchema } from "../common.validation";

export const createAllergySchema = z.object({
  name: z
    .string()
    .min(1, "Name cannot be empty!")
    .max(100, "Name cannot exceed 100 characters"),
  description: z
    .string()
    .max(1000, "Description cannot exceed 1000 characters")
    .optional(),
  colorPreference: z
    .enum(["primary", "secondary", "tertiary"], {
      errorMap: () => ({
        message: "Color preference must be one of: primary, secondary, tertiary",
      }),
    })
    .default("primary"),
});

export const patchAllergySchema = createAllergySchema.partial().strict();

export const allergyIdSchema = z.object({
  allergyId: UUIDSchema(),
});