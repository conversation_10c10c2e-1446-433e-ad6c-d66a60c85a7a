import {z} from "zod";
import {
    createUuidSchema,
    DurationZodEnum,
    ResponseTypeZodEnum,
    StringToBooleanQuerySchema,
    UUIDSchema
} from "../common.validation";
import {Duration} from "../../types/subscription";

export const filterSubTierSchema = z.object({
    subTierId: createUuidSchema("subTierId").optional(),
    name: z.string().optional(),
    isActive: StringToBooleanQuerySchema.optional(),
    responseType: ResponseTypeZodEnum().optional(),
})

const featureIdArraySchema = z.array(createUuidSchema("featureId"))

const subTierBaseSchema = z.object({
    name: z.string().min(1, "Subscription name cannot be empty!"),
    description: z.string(),
    price: z.number({invalid_type_error: "Price should be a number!"}).min(0, "Price cannot be negative"),
    isActive: z.boolean({invalid_type_error: "isActive must be a boolean"}).optional().default(true),
    tierType: DurationZodEnum().optional().default(Duration.MONTHLY),
    validityDuration: z.number({invalid_type_error: "validityDuration should be a number!"}).min(0, "validityDuration cannot be negative"),
});

export const createSubsTierSchema = subTierBaseSchema.strict().extend({
    features: featureIdArraySchema.min(1, "At least one feature is required!"),
}).strict()

export type createSubTierType = z.infer<typeof createSubsTierSchema>;

export const subTierIdSchema = z.object({
    subTierId: createUuidSchema("subTierId")
});

export const patchSubTierSchema = subTierBaseSchema.strict().partial().extend({
    subTierId: UUIDSchema("Invalid UUID for Subscription Tier ID"),
    features: featureIdArraySchema.min(1, "At least one feature is required!").optional(),
    isReflectToNight: z.boolean().optional(),
}).strict().superRefine((data, ctx) => {
    if (data.features) {
        if (!data.isReflectToNight) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'isReflectToNight is required when feature is provided',
                path: ['isReflectToNight']
            });
        }
    }
});
