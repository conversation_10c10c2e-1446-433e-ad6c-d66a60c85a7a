import {z} from "zod";
import {getCountry} from "../../helpers/subscription/country.helper";

export const countryCodeBaseSchema = z.object({
    countryCode: z
        .string({required_error: 'please provide a valid country code'})
        .refine(
            (code) => {
                const country = getCountry(code);
                return country !== undefined
            },
            {message: 'please provide a valid country code'}
        ),
}).strict();

export const countryCodeSchemaOptional = countryCodeBaseSchema.partial();
export const stateCodeSchema = z.object({
    stateCode: z
        .string({required_error: 'please provide a valid State code'})
}).strict();

export const validateSchema = countryCodeBaseSchema.merge(stateCodeSchema);