import {z} from "zod";
import {
    countryCodeSchema,
    createUuidSchema,
    DescriptionSchema,
    NameSchema,
    ResponseTypeZodEnum,
    StringToBooleanQuerySchema
} from "../common.validation";
import {validateCountryState} from "../../helpers/subscription/country.helper";

export const baseTaxSchema = z.object({
    name: NameSchema,
    description: DescriptionSchema.optional(),
    taxGovId: z.string({
        required_error: "TaxGovId must be a string",
    }).optional(),

    isActive: z.boolean({
        required_error: "isActive must be a boolean",
    }).default(false),

    isCentral: z.boolean({
        required_error: "isCentral must be a boolean",
    }).default(false),

    countryCode: countryCodeSchema,

    stateCode: z.string({
        required_error: "State must be a string",
    }).optional(),

    taxPercentage: z
        .coerce
        .number({invalid_type_error: "Tax percentage must be a number"}),

    upperLimit: z
        .coerce
        .number({invalid_type_error: "Upper limit must be a number"}),

    lowerLimit: z
        .coerce
        .number({invalid_type_error: "Lower limit must be a number"}),
}).strict()

export const filterTaxSchema = z
    .object({
        responseType: ResponseTypeZodEnum(),
        taxId: createUuidSchema("taxId", "Invalid UUID for tax"),
        name: NameSchema,
        taxGovId: z.string({required_error: "TaxGovId must be a string"}),
        isActive: StringToBooleanQuerySchema,
        isCentral: StringToBooleanQuerySchema,
        countryCode: countryCodeSchema,
        stateCode: z.string({required_error: "StateCode must be a string"}),
        taxPercentage: z
            .coerce
            .number({invalid_type_error: "Tax percentage must be a number"})
            .min(0, "Tax percentage cannot be negative"),
        upperLimit: z
            .coerce
            .number({invalid_type_error: "Upper limit must be a number"})
            .min(0, "Upper limit cannot be negative"),
        lowerLimit: z
            .coerce
            .number({invalid_type_error: "Lower limit must be a number"})
            .min(0, "Lower limit cannot be negative"),
    })
    .strict()
    .partial().superRefine((data, ctx) => {
        if (data.countryCode && data.stateCode) {
            const isValidState = validateCountryState(data.countryCode, data.stateCode);
            if (!isValidState) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'please provide a valid state code',
                    path: ['stateCode']
                });
            }
        }
    });

export const createTaxSchema = baseTaxSchema.refine(
    (data) => data.lowerLimit <= data.upperLimit,
    {
        message: "Lower limit cannot be greater than upper limit",
        path: ["lowerLimit"],
    }
).superRefine((data, ctx) => {
    if (data.stateCode) {
        const isValidState = validateCountryState(data.countryCode, data.stateCode);
        if (!isValidState) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'please provide a valid state code',
                path: ['stateCode']
            });
        }
    }
});
export type CreateTax = z.infer<typeof createTaxSchema>;
export const patchTaxSchema = baseTaxSchema.partial().extend({
    taxId: createUuidSchema("taxId", "Invalid UUID for tax")
}).superRefine((data, ctx) => {
    if (data.countryCode && data.stateCode) {
        const isValidState = validateCountryState(data.countryCode, data.stateCode);
        if (!isValidState) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'please provide a valid state code',
                path: ['stateCode']
            });
        }
    }
});

export const deleteTaxSchema = z.object({
    taxId: createUuidSchema("taxId", "Invalid UUID for tax")
});