import {z} from "zod";
import {countryCodeSchema, createUuidSchema, DurationZodEnum, overRideType} from "../common.validation";
import {validateCountryState} from "../../helpers/subscription/country.helper";

export const manageSubscriptionSchema = z.object({
    subTierId: createUuidSchema("subTierId"),
    tenantId: createUuidSchema("tenantId"),
    allowDue: z.boolean({invalid_type_error: "allowDue must be a boolean"}).optional().default(false),
    payedAmount: z.number({
        invalid_type_error: "payedAmount must be a number",
        required_error: "payedAmount is required"
    }),
    durationType: DurationZodEnum("durationType is invalid"),
    durationValue: z.number().int({message: "durationValue must be an integer"}),
    countryCode: countryCodeSchema,
    stateCode: z.string(),
    overRideType: overRideType,
}).strict().superRefine((data, ctx) => {
    const isValidState = validateCountryState(data.countryCode, data.stateCode);
    if (!isValidState) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'please provide a valid state code',
            path: ['stateCode']
        });
    }
});
export type manageSubscriptionType = z.infer<typeof manageSubscriptionSchema>;