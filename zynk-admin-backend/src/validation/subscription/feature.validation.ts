import {z} from "zod";
import {
    createUuidSchema,
    DescriptionSchema,
    FormatTypeZodEnum,
    NameSchema, permissionActionSchema,
    ResponseTypeZodEnum, StringToBooleanQuerySchema
} from "../common.validation";

export const featureFilterSchema = z.object({
    featureId: createUuidSchema("featureId").optional(),
    name: NameSchema.optional(),
    isActive: StringToBooleanQuerySchema.optional(),
    responseType: ResponseTypeZodEnum(),
    formatType: FormatTypeZodEnum()
}).strict();

export const createFeatureSchema = z.object({
    name: NameSchema,
    description: DescriptionSchema.optional(),
    isActive: z.boolean().default(true).optional(),
    featurePermissionActions: permissionActionSchema.optional()
}).strict();

export type createFeatureType = z.infer<typeof createFeatureSchema>;

export const patchFeatureSchema = createFeatureSchema.partial().extend({
    featureId: createUuidSchema("featureId")
}).strict();

export type patchFeatureType = z.infer<typeof patchFeatureSchema>;

export const featureIdSchema = z.object({
    featureId: createUuidSchema("featureId")
}).strict();