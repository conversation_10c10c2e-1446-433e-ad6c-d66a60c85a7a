import { z } from "zod";
import { UUIDSchema } from "../../common.validation";
import { OrderTypeName } from "../../../models/tenantSettings/general/orderType.model";

export const patchOrderTypesSchema = z.array(
    z.object({
        orderTypeId: UUIDSchema("Invalid order type ID").optional(),
        name: z.string().min(1, "Order type name is required").max(100, "Order type name must be less than 100 characters").optional(),
        reservedName: z.enum(Object.values(OrderTypeName) as [string, ...string[]]).optional(),
        enabled: z.boolean().optional(),
    })
    .strict()
    .superRefine((data, ctx) => {
        // If orderTypeId is not present, all other fields are required
        if (!data.orderTypeId) {
            if (!data.name) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: "Order type name is required when orderTypeId is not present",
                    path: ["name"],
                });
            }
            if (!data.reservedName) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: "Reserved name is required when orderTypeId is not present",
                    path: ["reservedName"],
                });
            }
            if (typeof data.enabled !== "boolean") {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: "Enabled is required when orderTypeId is not present",
                    path: ["enabled"],
                });
            }
        }
    })
).refine(data => {
    const names = data.map(item => item.name).filter(Boolean);
    const uniqueNames = new Set(names);
    return names.length === uniqueNames.size;
}, {
    message: "Order type names must be unique",
});