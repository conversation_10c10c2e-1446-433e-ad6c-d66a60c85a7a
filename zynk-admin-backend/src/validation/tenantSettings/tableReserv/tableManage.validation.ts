import { z } from "zod";
import { UUIDSchema } from "../../common.validation";

export const patchTablesAndCombinationsSchema = z.object({
  tables: z.array(
    z
      .object({
        tableId: UUIDSchema("Invalid UUID for table"),
        name: z
          .string()
          .min(1, "Table name cannot be empty!")
          .max(10, "Table name cannot exceed more than 10 characters"),
        minSeats: z
          .number()
          .int("minSeats must be an integer")
          .min(0, "minSeats cannot be below zero"),
        maxSeats: z
          .number()
          .int("maxSeats must be an integer")
          .min(0, "maxSeats cannot be below zero"),
        enabled: z.boolean(),
        status: z.enum(['available','occupied','reserved']),
        cleaning: z.enum(["clean", "needscleaning", "dirty"]),
        location: z.string(),
        floor: UUIDSchema("Invalid UUID for floor"),
        availableOnline: z.boolean(),
      })
      .partial()
      .strict()
      .refine((data) => {
        if (!data.tableId) {
          return (
            typeof data.name === "string" &&
            data.name.length > 0 &&
            typeof data.minSeats === "number" &&
            typeof data.maxSeats === "number" &&
            typeof data.enabled === "boolean" &&
            typeof data.availableOnline === "boolean" &&
            typeof data.status === "string" &&
            typeof data.cleaning === "string" &&
            typeof data.location === "string"
          );
        }
        return true;
      })
  ),

  tableCombinations: z.array(
    z
      .object({
        tableCombinationId: UUIDSchema("Invalid UUID for table"),
        minSeats: z
          .number()
          .int("minSeats must be an integer")
          .min(0, "minSeats cannot be below zero"),
        maxSeats: z
          .number()
          .int("maxSeats must be an integer")
          .min(0, "maxSeats cannot be below zero"),
        enabled: z.boolean(),
        availableOnline: z.boolean(),
        tables: z.array(
          UUIDSchema("Invalid UUID for table in table combination")
        ),
      })
      .partial()
      .strict()
      .refine((data) => {
        if (!data.tableCombinationId) {
          return (
            typeof data.minSeats === "number" &&
            typeof data.maxSeats === "number" &&
            typeof data.enabled === "boolean" &&
            typeof data.availableOnline === "boolean" &&
            Array.isArray(data.tables)
          );
        }
        return true;
      })
  ),
});
