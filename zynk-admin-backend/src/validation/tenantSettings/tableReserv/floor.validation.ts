import { z } from "zod";
import { UUIDSchema } from "../../common.validation";

export const patchFloorsSchema = z.array(
    z.object({
        floorId: UUIDSchema("Invalid UUID for floor"),
        name: z.string().min(1, "Floor name cannot be empty!")
    }).partial().strict().refine(
        (data) => {
            if (!data.floorId && !data.name) {
                return false;
            }
            return true;
        },
        {
            message: "At least one of floorId or name must be provided"
        }
    )
).superRefine((floors, ctx) => {
    const names = new Set<string>();
    for (let i = 0; i < floors.length; i++) {
        const floor = floors[i];
        if (floor.name !== undefined) {
            if (names.has(floor.name)) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: "Floor names must be unique",
                    path: [i, "name"]
                });
            } else {
                names.add(floor.name);
            }
        }
    }
});