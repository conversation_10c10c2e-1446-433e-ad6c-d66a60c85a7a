import { z } from "zod";
import { UUIDSchema } from "../../common.validation";

export const patchOnlineReservConfigSchema = z.object({
    bufferTime: z.number().nonnegative("Buffer time cannot be negative"),
    bufferTimeType: z.enum(['minutes', 'hours']),
    enabled: z.boolean().default(true)
}).partial().strict().refine((data) => {
    if(data.bufferTime){
        if (!data.bufferTimeType || !['minutes', 'hour'].includes(data.bufferTimeType)) {
            return false;
        }
    }
})

export const onlineResConfigIdSchema = z.object({
    onlineResConfigId: UUIDSchema("Invalid ID for online reservation config!")
})