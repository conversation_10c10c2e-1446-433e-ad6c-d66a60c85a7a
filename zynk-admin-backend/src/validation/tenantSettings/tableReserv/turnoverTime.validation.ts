import { number, z } from "zod";
import { DefaultTurnoverDurations } from "../../../models/tenantSettings/tableReservation/turnoverTime/turnover.def";
import { UUIDSchema } from "../../common.validation";

export const patchTurnoverTimeSchema = z.object({
  defaultDuration: z
    .nativeEnum(DefaultTurnoverDurations)
    .default(DefaultTurnoverDurations.ONEHOUR),
  turnoverRules: z.array(
    z.object({
      turnoverRuleId: UUIDSchema("Invalid turnover rule ID!"),
      minGuests: z.number(),

      maxGuests: z.number(),

      duration: z
        .nativeEnum(DefaultTurnoverDurations)
        .default(DefaultTurnoverDurations.ONEHOUR),
    }).partial().strict().refine((data) => {
        if(!data.turnoverRuleId){
            if (!data.minGuests && typeof(data.minGuests) !== "number") {
                throw new Error("minGuests is required when turnoverRuleId is not provided");
            }

            if (!data.maxGuests && typeof(data.maxGuests) !== "number") {
                throw new Error("maxGuests is required when turnoverRuleId is not provided");
            }

            if (!data.duration && typeof(data.duration) !== "number") {
                throw new Error("duration is required when turnoverRuleId is not provided");
            }

            return true
        }
        return true
    })
  ),
});
