import { z } from "zod";
import { MinimalTimeSchema, UUIDSchema } from "../../common.validation";

export const timeSlotPatchSchema = z.array(
  z.object({
    timeSlotPaceId: UUIDSchema("Invalid UUID for time slot pace").optional(),
    time: MinimalTimeSchema.optional(),
    maxGuests: z.number().nonnegative("Max guests cannot be negative").optional(),
  }).refine(
    (data) => {
      if (data.timeSlotPaceId) return true;
      return data.time !== undefined && data.maxGuests !== undefined;
    },
    {
      message: "For new time slots, both time and maxGuests are required",
    }
  )
).refine(
  (data) => {
    const times = data.map(slot => slot.time).filter(Boolean);
    const uniqueTimes = new Set(times);
    return times.length === uniqueTimes.size;
  },
  {
    message: "Duplicate time slots are not allowed. Each time slot must have a unique time.",
  }
);
