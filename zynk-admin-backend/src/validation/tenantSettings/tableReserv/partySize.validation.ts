import { z } from "zod";

export const patchPartySizeConfigSchema = z
  .object({
    minGuests: z.number().nonnegative("Min guests cannot be negative"),

    maxGuests: z.number().nonnegative("Max guests cannot be negative"),

    displayPhone: z.boolean(),

    reqManualApproval: z.boolean(),

    reqManualGuests: z
      .number()
      .nonnegative("Manual guests for approval cannot be negative"),
  })
  .partial()
  .strict();
