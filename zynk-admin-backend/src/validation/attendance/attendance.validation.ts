import { z } from "zod";
import { MinimalTimeSchema, PINSchema } from "../common.validation";

export const createCheckInRecordSchema = z.object({
  checkInTime: MinimalTimeSchema,
  pin: PINSchema,
  date: z.coerce.date(),
});

export const createCheckOutRecordSchema = z.object({
  checkOutTime: MinimalTimeSchema,
  pin: PINSchema,
  date: z.coerce.date(),
});

export const createClockInRecordSchema = z.object({
  clockInTime: MinimalTimeSchema,
  pin: PINSchema,
  date: z.coerce.date(),
});

export const createClockOutRecordSchema = z.object({
  clockOutTime: MinimalTimeSchema,
  pin: PINSchema,
  date: z.coerce.date(),
});