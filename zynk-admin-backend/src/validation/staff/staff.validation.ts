import { z } from "zod";
import {
  DailyShiftTimingsSchema,
  DAY_AVAILABILITY,
  DayAvailType,
  emailAddressSchema,
  passwordSchema,
  phoneNumberSchema,
  PINSchema,
  UUIDSchema,
} from "../common.validation";

export const WeeklyShiftTimingsSchema = z.object({
  sunday: DailyShiftTimingsSchema,
  monday: DailyShiftTimingsSchema,
  tuesday: DailyShiftTimingsSchema,
  wednesday: DailyShiftTimingsSchema,
  thursday: DailyShiftTimingsSchema,
  friday: DailyShiftTimingsSchema,
  saturday: DailyShiftTimingsSchema,
});

export const DaysAvailableSchema = z.object({
  days: z.record(DayAvailType, DAY_AVAILABILITY).optional().default({
    M: "active",
    T: "active",
    W: "active",
    Th: "active",
    F: "active",
    Sa: "off",
    Su: "off",
  }),
});

export const DefaultShiftTimingSchema = z.object({
  weeklyTimings: WeeklyShiftTimingsSchema.optional().default({
    sunday: {
      morning: { isActive: false },
      noon: { isActive: false },
      night: { isActive: false },
    },
    monday: {
      morning: { isActive: false },
      noon: { isActive: false },
      night: { isActive: false },
    },
    tuesday: {
      morning: { isActive: false },
      noon: { isActive: false },
      night: { isActive: false },
    },
    wednesday: {
      morning: { isActive: false },
      noon: { isActive: false },
      night: { isActive: false },
    },
    thursday: {
      morning: { isActive: false },
      noon: { isActive: false },
      night: { isActive: false },
    },
    friday: {
      morning: { isActive: false },
      noon: { isActive: false },
      night: { isActive: false },
    },
    saturday: {
      morning: { isActive: false },
      noon: { isActive: false },
      night: { isActive: false },
    },
  }),
});

export const StaffCertificationSchema = z.object({
  certId: z.string().min(1, "Certification ID cannot be empty!"),
  certName: z.string().min(1, "Certification name cannot be empty!"),
  attachmentUrl: z.string().url("Invalid attachment URL!"),
  expiryDate: z.coerce.date().optional().nullable(),
});

export const StaffCertificationsArraySchema = z
  .array(StaffCertificationSchema)
  .default([]);

export const staffBasicDetailsSchema = z.object({
  firstName: z.string().min(1, "First name cannot be empty!"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name cannot be empty!"),
  emailAddress: emailAddressSchema("Invalid email address for staff!"),
  phoneNumber: phoneNumberSchema,
  address: z.string().min(1, "Address cannot be empty!"),
  password: passwordSchema,
  profileUrl: z.string().url("Invalid profile picture URL!"),
  role: z.string(),
  pin: PINSchema,
  branches: z
    .array(UUIDSchema("Invalid UUID for branch"))
    .nonempty("Reserved branches cannot be empty!"),
});

export const createStaffSchema = z.object({
  basicDetails: staffBasicDetailsSchema,
  staffDaysAvailable: DaysAvailableSchema,
  defaultShiftTiming: DefaultShiftTimingSchema,
  staffCertifications: StaffCertificationsArraySchema,
});

export const staffIdSchema = z.object({
  staffId: UUIDSchema("Invalid UUID for staff ID"),
});

export const patchStaffBasicDetailSchema = staffBasicDetailsSchema
  .partial()
  .strict();

export const patchStaffAvailabilitySchema = z
  .object({
    staffDaysAvailable: DaysAvailableSchema.partial().strict(),
    defaultShiftTiming: z
      .object({
        weeklyTimings: WeeklyShiftTimingsSchema.partial().optional(),
      })
      .strict(),
  })
  .partial()
  .strict();

export const patchStaffCertificationItemSchema = z
  .object({
    staffCertificationId: UUIDSchema("Certification UUID cannot be empty!"),
    certId: z.string().min(1, "Certification ID cannot be empty!"),
    certName: z.string().min(1, "Certification name cannot be empty!"),
    attachmentUrl: z.string().url("Invalid attachment URL!"),
    expiryDate: z.coerce.date().optional().nullable(),
  })
  .strict();

export const patchStaffCertificationSchema = z
  .object({
    staffCertifications: z.array(patchStaffCertificationItemSchema.partial().strict()),
  })
  .strict();
