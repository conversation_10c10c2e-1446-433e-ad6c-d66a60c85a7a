import { z } from "zod";
import { UUIDSchema } from "../common.validation";

export const tableAttrsPatchSchema = z.object({
  status: z.enum(["available", "occupied", "reserved"]),
  cleaning: z.enum(["clean", "needscleaning", "dirty"]),
  bookedSeats: z.number().nonnegative(),
}).partial().strict();

export const takeOrderFromDineInSchema = z.object({
  numOfGuests: z.number().positive("Number of guests cannot be zero or negative!"),
  confirmationCode: z.string().min(1, "Confirmation code cannot be empty!").optional()
})