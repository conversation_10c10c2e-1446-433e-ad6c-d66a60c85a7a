import { z } from "zod";
import { ReservationStatus } from "../../models/dineIn/tableReservation.model";
import { phoneNumberSchema, UUIDSchema } from "../common.validation";

export const createReservationSchema = z.object({
  customerName: z.string().trim().min(2).max(100),
  phoneNumber: phoneNumberSchema,
  numberOfGuests: z.number().int().min(1).max(20),
  reservationTime: z.coerce.date().refine((date) => date > new Date(), {
    message: "Reservation time must be in the future",
  }),
  specialNotes: z.string().trim().max(500).optional(),
  // durationMinutes: z.number().int().min(30).max(480).default(120),
});

export const updateReservationSchema = z.object({
  customerName: z.string().trim().min(2).max(100).optional(),
  phoneNumber: phoneNumberSchema,
  numberOfGuests: z.number().int().min(1).max(20).optional(),
  reservationTime: z.coerce.date().refine((date) => date > new Date(), {
    message: "Reservation time must be in the future",
  }),
  specialNotes: z.string().trim().max(500).optional(),
  status: z
    .enum(Object.values(ReservationStatus) as [string, ...string[]])
    .optional(),
  durationMinutes: z.number().int().min(30).max(480).optional(),
});

export const tableIdSchema = z.object({
  tableId: UUIDSchema("Invalid UUID for table"),
});

export const reservationIdSchema = z.object({
  reservationId: UUIDSchema("Invalid UUID for reservation"),
});

export const tableCombinationIdSchema = z.object({
  tableCombinationId: UUIDSchema("Invalid UUID for table"),
});

export const confirmationCodeSchema = z.object({
  confirmationCode: z
    .string()
    .regex(/^RES[ABCDEFGHJKLMNPQRSTUVWXYZ23456789]{6}$/, "Invalid confirmation code format"),
});

export const getTableResSchema = z.object({
  date: z.coerce.date(),
  status: z.nativeEnum(ReservationStatus), 
  tableId: UUIDSchema("Invalid UUID for table!")
}).partial().strict()

export const getTableCombinationsSchema = z.object({
  date: z.coerce.date(),
  status: z.nativeEnum(ReservationStatus), 
  tableCombinationId: UUIDSchema("Invalid UUID for table combination!")
}).partial().strict()