import {DataSource, Query<PERSON>unner, Repository} from "typeorm";
import {testDataSource} from "../setup";
import {afterAll, beforeAll, describe, expect, it,} from "@jest/globals";
import {getAllTenantModels} from "../../config/entity-config";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Branch} from "../../models/company/branch.model";
import {Tenant} from "../../models/admin/Tenant/tenant.model";
import log from "../../helpers/system/logger.helper";

describe("Primary Branch Existence Test", () => {
    let templateDS: DataSource | null = null;
    let tempQueryRunner: QueryRunner | null = null;
    let suQueryRunner: QueryRunner | null = null;

    beforeAll(async () => {
        try {
            // Ensure password is a string
            const password = process.env.POSTGRES_PASSWORD;
            if (!password) {
                throw new Error('POSTGRES_PASSWORD environment variable is required');
            }

            // Initialize template DataSource
            templateDS = new DataSource({
                type: "postgres",
                host: process.env.DB_HOSTNAME || 'localhost',
                port: parseInt(process.env.DB_PORT!) || 5434,
                username: process.env.POSTGRES_USER || 'postgres',
                password: password, // Ensure this is a string
                database: "tenant_template",
                synchronize: false,
                logging: false,
                entities: getAllTenantModels(),
                subscribers: [],
                migrations: [],
            });

            await templateDS.initialize();
            log.info('Template DataSource initialized');

            // Create QueryRunners only after successful initialization
            tempQueryRunner = templateDS.createQueryRunner();
            await tempQueryRunner.connect();

            suQueryRunner = testDataSource.createQueryRunner();
            await suQueryRunner.connect();

            log.info('QueryRunners connected successfully');
        } catch (error) {
            log.error('Error in beforeAll:', error);
            // Clean up on error
            await cleanup();
            throw error;
        }
    });

    afterAll(async () => {
        await cleanup();
    });

    async function cleanup() {
        try {
            if (tempQueryRunner) {
                await tempQueryRunner.release();
                tempQueryRunner = null;
            }
        } catch (error) {
            log.error('Error releasing tempQueryRunner:', error);
        }

        try {
            if (suQueryRunner) {
                await suQueryRunner.release();
                suQueryRunner = null;
            }
        } catch (error) {
            log.error('Error releasing suQueryRunner:', error);
        }

        try {
            if (templateDS && templateDS.isInitialized) {
                await templateDS.destroy();
                templateDS = null;
            }
        } catch (error) {
            log.error('Error destroying templateDS:', error);
        }
    }

    describe("Primary Branch Tenant Template Test", () => {
        it("primary branch should exist in tenant template and should be primary", async () => {
            if (!tempQueryRunner) {
                throw new Error('Template QueryRunner not initialized');
            }

            const {Branch} = getRepositories(tempQueryRunner) as {
                Branch: Repository<Branch>;
            };

            const primaryBranch = await Branch.findOne({
                where: {
                    isPrimary: true,
                },
            });

            // Assert
            expect(primaryBranch).toBeDefined();
            expect(primaryBranch?.isPrimary).toBeTruthy();
        });
    });

    describe("Primary Branch All Tenants Test", () => {
        it("primary branch should exist in all tenants", async () => {
            if (!suQueryRunner) {
                throw new Error('SuperAdmin QueryRunner not initialized');
            }

            const {Tenant} = getRepositories(suQueryRunner) as {
                Tenant: Repository<Tenant>;
            };

            const allTenants = await Tenant.find();
            expect(allTenants.length).toBeGreaterThan(0);

            const password = process.env.POSTGRES_PASSWORD;
            if (!password) {
                throw new Error('POSTGRES_PASSWORD environment variable is required');
            }

            // Test each tenant
            for (const tenant of allTenants) {
                const dbName = tenant.subDomain;
                let tenantDataSource: DataSource | null = null;
                let tenantQueryRunner: QueryRunner | null = null;

                try {
                    tenantDataSource = new DataSource({
                        type: "postgres",
                        host: process.env.DB_HOSTNAME || 'localhost',
                        port: parseInt(process.env.DB_PORT!) || 5434,
                        username: process.env.POSTGRES_USER || 'postgres',
                        password: password, // Ensure this is a string
                        database: dbName,
                        synchronize: false,
                        logging: false,
                        entities: getAllTenantModels(),
                        subscribers: [],
                        migrations: [],
                    });

                    await tenantDataSource.initialize();
                    tenantQueryRunner = tenantDataSource.createQueryRunner();
                    await tenantQueryRunner.connect();

                    const {Branch} = getRepositories(tenantQueryRunner) as {
                        Branch: Repository<Branch>;
                    };

                    const primaryBranch = await Branch.findOne({
                        where: {
                            isPrimary: true,
                        },
                    });

                    // Assert
                    expect(primaryBranch).toBeDefined();
                    expect(primaryBranch?.isPrimary).toBeTruthy();

                } catch (error) {
                    log.error(`Error testing tenant ${dbName}:`, error);
                    throw error;
                } finally {
                    // Clean up tenant connections
                    try {
                        if (tenantQueryRunner) {
                            await tenantQueryRunner.release();
                        }
                    } catch (error) {
                        log.error(`Error releasing QueryRunner for tenant ${dbName}:`, error);
                    }

                    try {
                        if (tenantDataSource && tenantDataSource.isInitialized) {
                            await tenantDataSource.destroy();
                        }
                    } catch (error) {
                        log.error(`Error destroying DataSource for tenant ${dbName}:`, error);
                    }
                }
            }
        });
    });
});