// tests/setup.ts - Fixed setup with proper error handling
import {DataSource} from "typeorm";
import {afterAll, beforeAll,} from "@jest/globals";
import {getAllSuperAdminModels} from "../config/entity-config";
import path from "path";
import log from "../helpers/system/logger.helper";

export let testDataSource: DataSource;

beforeAll(async () => {
    // Ensure password is a string
    const password = process.env.POSTGRES_PASSWORD;
    if (!password) {
        throw new Error('POSTGRES_PASSWORD environment variable is required');
    }

    testDataSource = new DataSource({
        type: "postgres" as const,
        host: process.env.DB_HOSTNAME || 'localhost',
        port: parseInt(process.env.DB_PORT!) || 5434,
        username: process.env.POSTGRES_USER || 'postgres',
        password: password, // Ensure this is a string
        logging: process.env.LOGGING_TOGGLE === "true",
        database: process.env.SUPERADMIN_DB,
        synchronize: process.env.MIGRATION_TOGGLE === "false",
        entities: getAllSuperAdminModels(),
        migrations: [path.join(__dirname, "migrations", "superadmin", "*.{ts,js}")],
        migrationsTableName: "migrations_superadmin",
        migrationsRun: false,
    });

    try {
        await testDataSource.initialize();
        log.info('Test DataSource initialized successfully');
    } catch (error) {
        log.error('Failed to initialize test DataSource:', error);
        throw error;
    }
});

afterAll(async () => {
    try {
        if (testDataSource && testDataSource.isInitialized) {
            await testDataSource.destroy();
            log.info('Test DataSource destroyed successfully');
        }
    } catch (error) {
        log.error('Error destroying test DataSource:', error);
    }
});