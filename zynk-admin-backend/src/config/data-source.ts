// src/db/datasource.ts
import {DataSource} from "typeorm";
import * as path from "path";
import {loadEnv} from "./envConfig";
import {getAllSuperAdminModels, getAllTenantModels} from "./entity-config";
import log from "../helpers/system/logger.helper";
import {seedDB} from "./seedDB.Config";
import {seedSupDBSeed} from "../seeds/supSeeds/seedSupDB.seed";

loadEnv();

export const baseConfig = {
    type: "postgres" as const,
    host: process.env.DB_HOSTNAME,
    port: parseInt(process.env.DB_PORT!) || 5434,
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    logging: process.env.LOGGING_TOGGLE === "true",
};

export const SuperAdminDS = new DataSource({
    ...baseConfig,
    database: process.env.SUPERADMIN_DB,
    synchronize: process.env.MIGRATION_TOGGLE === 'false',
    entities: getAllSuperAdminModels(),
    migrations: [path.join(__dirname, "migrations", "superadmin", "*.{ts,js}")],
    migrationsTableName: "migrations_superadmin",
    migrationsRun: false,
});

export async function initializeSuperAdminDS() {
    try {
        if (SuperAdminDS.isInitialized) {
            return SuperAdminDS;
        }

        const tempDataSource = new DataSource({
            ...baseConfig,
            database: "postgres",
        });

        if (!tempDataSource.isInitialized) {
            await tempDataSource.initialize();
        }

        const dbName = process.env.SUPERADMIN_DB;
        const isSupSeeding = process.env.SUPERADMIN_SEEDING === "true";
        const result = await tempDataSource.query(
            `SELECT datname
             FROM pg_catalog.pg_database
             WHERE datname = '${dbName}'`
        );

        if (result.length === 0) {
            log.info(`Database ${dbName} does not exist, creating...`);
            await tempDataSource.query(`CREATE DATABASE "${dbName}"`);
            log.info(`Database ${dbName} created successfully`);
        }

        await tempDataSource.destroy();

        if (!SuperAdminDS.isInitialized) {
            await SuperAdminDS.initialize();
            if (isSupSeeding) await seedDB(
                `${dbName}`,
                getAllSuperAdminModels,
                seedSupDBSeed
            );
        }
        return SuperAdminDS;
    } catch (error) {
        throw error;
    }
}

export async function getTenantDS(tenantName: string) {
    try {
        return new DataSource({
            ...baseConfig,
            database: tenantName,
            synchronize: process.env.MIGRATION_TOGGLE === 'false',
            entities: getAllTenantModels(),
            migrations: [path.join(__dirname, "migrations", "tenant", "*.{ts,js}")],
            migrationsTableName: "migrations_tenant",
            migrationsRun: false,
        });
    } catch (error) {
        throw error;
    }
}

export async function ensureTenantDatabaseExists(tenantName: string) {
    const tempDataSource = new DataSource({
        ...baseConfig,
        database: "postgres",
    });

    try {
        await tempDataSource.initialize();

        const result = await tempDataSource.query(
            `
                SELECT datname
                FROM pg_catalog.pg_database
                WHERE datname = '${tenantName}'`
        );

        if (result.length === 0) {
            log.warn(`Database ${tenantName}does not exist, creating...`);
            await tempDataSource.query(`CREATE DATABASE "${tenantName}"`);
            log.info(`Database ${tenantName} created successfully`);
        }
    } catch (error) {
        log.error("Error ensuring tenant database exists:", error);
        throw error;
    } finally {
        if (tempDataSource.isInitialized) {
            await tempDataSource.destroy();
        }
    }
}