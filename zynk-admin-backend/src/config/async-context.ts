import { AsyncLocalStorage } from 'async_hooks';
import { QueryRunner } from 'typeorm';

interface RequestContext {
  tenantName: string;
  queryRunner: QueryRunner;
  rootQueryRunner: QueryRunner;
}

const asyncLocalStorage = new AsyncLocalStorage<RequestContext>();

export const requestContext = {
  run: <T>(data: RequestContext, fn: () => T): T => {
    return asyncLocalStorage.run(data, fn);
  },
  
  get: (): RequestContext | undefined => {
    return asyncLocalStorage.getStore();
  },
  
  getOrThrow: (): RequestContext => {
    const store = asyncLocalStorage.getStore();
    if (!store) {
      throw new Error('Request context not available. Make sure you are within a request scope.');
    }
    return store;
  }
};