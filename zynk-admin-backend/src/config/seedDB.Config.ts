import {DataSource} from "typeorm";
import log from "../helpers/system/logger.helper";
import {SeedingException} from "../exceptions/seeding/SeedingException";

export const seedDB = async (
    dbName: string,
    models: any,
    seeds: any
): Promise<void> => {
    const DS = new DataSource({
        type: "postgres",
        host: process.env.DB_HOSTNAME,
        port: parseInt(process.env.DB_PORT!) || 5434,
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: dbName,
        synchronize: true,
        logging: false,
        entities: models(),
        subscribers: [],
        migrations: [],
    });

    try {
        await DS.initialize();

        const qRunner = DS.createQueryRunner();
        await qRunner.connect();
        await qRunner.startTransaction();

        try {
            log.info(`Seeding ${dbName} database...`);
            await seeds(qRunner);
            log.info(`Successfully seeded ${dbName} database`);
            await qRunner.commitTransaction();
        } catch (error) {
            await qRunner.rollbackTransaction();
            if (error instanceof SeedingException) throw error;
            log.error(`Error seeding ${dbName} database:`, error);
            throw error;
        } finally {
            log.debug("qRunner releasing...");
            await qRunner.release();
        }
    } catch (error) {
        if (error instanceof SeedingException) throw error;
        log.error(`Error initializing template ${dbName} structure:`, error);
        throw error;
    } finally {
        if (DS.isInitialized) {
            await DS.destroy();
        }
    }
};