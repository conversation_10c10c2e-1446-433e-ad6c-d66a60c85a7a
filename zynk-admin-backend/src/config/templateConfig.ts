import {DataSource} from "typeorm";
import {getAllTenantModels} from "./entity-config";
import log from "../helpers/system/logger.helper";
import {seedDB} from "./seedDB.Config";
import {SeedTemplateDB} from "../seeds/templateSeeds/seedTemplateDB";
import {SeedingException} from "../exceptions/seeding/SeedingException";

export const initializeTemplateDatabase = async (): Promise<void> => {
    const templateDbName = "tenant_template";

    try {
        const rootDS = new DataSource({
            type: "postgres",
            host: process.env.DB_HOSTNAME,
            port: parseInt(process.env.DB_PORT!) || 5434,
            username: process.env.POSTGRES_USER,
            password: process.env.POSTGRES_PASSWORD,
            database: "postgres",
            logging: false,
        });

        if (!rootDS.isInitialized) {
            await rootDS.initialize();
        }

        try {
            const result = await rootDS.query(
                `SELECT datname
                 FROM pg_catalog.pg_database
                 WHERE datname = '${templateDbName}'`
            );

            let seedTemplateDB = process.env.TEMPLATE_SEEDING === "true";

            if (result.length === 0) {
                await rootDS.query(`CREATE DATABASE "${templateDbName}" WITH TEMPLATE = template0`);
                seedTemplateDB = true;
            } else {
                log.info(`Template database '${templateDbName}' already exists`);
            }

            await rootDS.destroy();

            if (seedTemplateDB) await seedDB(
                `${templateDbName}`,
                getAllTenantModels,
                SeedTemplateDB
            );

            const adminDS = new DataSource({
                type: "postgres",
                host: process.env.DB_HOSTNAME,
                port: parseInt(process.env.DB_PORT!) || 5434,
                username: process.env.POSTGRES_USER,
                password: process.env.POSTGRES_PASSWORD,
                database: "postgres",
                logging: false,
            });

            if (!adminDS.isInitialized) {
                await adminDS.initialize();
            }

            try {
                const templateCheck = await adminDS.query(
                    `SELECT datistemplate
                     FROM pg_database
                     WHERE datname = '${templateDbName}'`
                );

                if (templateCheck.length > 0 && !templateCheck[0].datistemplate) {
                    await adminDS.query(`
                        SELECT pg_terminate_backend(pid)
                        FROM pg_stat_activity
                        WHERE datname = '${templateDbName}'
                          AND pid <> pg_backend_pid()
                    `);

                    await adminDS.query(`UPDATE pg_database
                                         SET datistemplate = TRUE
                                         WHERE datname = '${templateDbName}'`);
                }
            } finally {
                await adminDS.destroy();
            }

        } catch (error) {
            if (error instanceof SeedingException) throw error;
            log.error("Error during template database initialization:", error);
            throw error;
        }
    } catch (error) {
        if (error instanceof SeedingException) throw error;
        log.error("Template database initialization failed:", error);
        throw error;
    }
};

export const createTenantFromTemplate = async (subDomain: string): Promise<boolean> => {
    const templateDbName = "tenant_template";

    try {
        const rootDS = new DataSource({
            type: "postgres",
            host: process.env.DB_HOSTNAME,
            port: parseInt(process.env.DB_PORT!) || 5434,
            username: process.env.POSTGRES_USER,
            password: process.env.POSTGRES_PASSWORD,
            database: "postgres",
            logging: false,
        });

        await rootDS.initialize();

        try {
            const result = await rootDS.query(
                `SELECT datname
                 FROM pg_catalog.pg_database
                 WHERE datname = '${subDomain}'`
            );

            if (result.length > 0) {
                log.warn(`Tenant database '${subDomain}' already exists`);
                return false;
            }

            const templateExists = await rootDS.query(
                `SELECT datname
                 FROM pg_catalog.pg_database
                 WHERE datname = '${templateDbName}'`
            );

            if (templateExists.length === 0) {
                log.warn(`Template database '${templateDbName}' does not exist`);
                throw new Error(`Template database '${templateDbName}' does not exist`);
            }

            await rootDS.query(`
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = '${templateDbName}'
                  AND pid <> pg_backend_pid()
            `);

            await rootDS.query(`CREATE DATABASE "${subDomain}" TEMPLATE "${templateDbName}"`);

            return true;
        } catch (error) {
            log.error(`Error creating tenant database '${subDomain}' from template:`, error);
            throw error;
        } finally {
            await rootDS.destroy();
        }
    } catch (error) {
        log.error(`Failed to create tenant database '${subDomain}':`, error);
        throw error;
    }
};