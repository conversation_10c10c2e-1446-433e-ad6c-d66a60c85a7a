import {CompanyInformation} from "../models/admin/Tenant/companyinformation.model";
import {Subscription} from "../models/subscription/subscriptions.model";
import {SubscriptionTier} from "../models/subscription/subTier/subscriptiontier.model";
import {SuperAdmin} from "../models/admin/superadmin.model";
import {SuperSession} from "../models/admin/supersession.model";
import {AdminSettings} from "../models/admin/settings/AdminSettings.model";
import {Tenant} from "../models/admin/Tenant/tenant.model";
import {TenantAuth} from "../models/admin/Tenant/tenantAuth.model";
import {TenantSession} from "../models/admin/Tenant/tenantsession.model";
import {Dish} from "../models/foodmenu/dish.model";
import {FoodMenu} from "../models/foodmenu/foodmenu.model";
import {Section} from "../models/foodmenu/section.model";
import {CompanyInformation as CompanyInfoTenant} from "../models/company/companyinformation.model";
import {Branch} from "../models/company/branch.model";
import {BankDetail} from "../models/company/bankdetail.model";
import {AccountRep} from "../models/company/accountrep.model";
import {BranchContacts} from "../models/company/branchcontact.model";
import {BusinessHour} from "../models/common/businesshour.model";
import {BusinessHourSlot} from "../models/common/businesshourslot.model";
import {CustomHourSlot} from "../models/common/customhourslot.model";
import {SpecialDay} from "../models/common/specialday.model";
import {SectionIcon} from "../models/reference/sectionicon.model";
import {Ingredient} from "../models/reference/ingredient.model";
import {Allergy} from "../models/reference/allergy.model";
import {DishIngredient} from "../models/foodmenu/dish_ingredient.model";
import {Customization} from "../models/foodmenu/customization.model";
import {DishAddon} from "../models/foodmenu/custom/addon.model";
import {CookingStyle} from "../models/foodmenu/custom/cookingstyle.model";
import {DishBeverage} from "../models/foodmenu/custom/dish_bev.model";
import {DishDessert} from "../models/foodmenu/custom/dish_dessert.model";
import {DishSide} from "../models/foodmenu/custom/dish_side.model";
import {DishExclusion} from "../models/foodmenu/custom/dishexclusion.model";
import {DishSize} from "../models/foodmenu/custom/dishsize.model";
import {DishExtra} from "../models/foodmenu/custom/extra.model";
import {Spiciness} from "../models/foodmenu/custom/spiciness.model";
import {AllergyColor} from "../models/reference/allergyColor.model";
import {PreparationDuration} from "../models/tenantSettings/food/preparation_duration.model";
import {SpecialInstructions} from "../models/tenantSettings/food/SpecialInstructions.model";
import {WasteManagement} from "../models/tenantSettings/wasteManagement/waste_management.model";
import {ReceiveOrders} from "../models/tenantSettings/onlineOrders/ReceiveOrders.model";
import {OrderScheduling} from "../models/tenantSettings/onlineOrders/OrderScheduling.model";
import {Pickup} from "../models/tenantSettings/onlineOrders/Pickup.model";
import {Feature} from "../models/subscription/feature/feature.model";
import {TPermissions} from "../models/pBAC/tPBAC/TPermissions.model";
import {TRole} from "../models/pBAC/tRole/TRole.model";
import {TAction} from "../models/pBAC/tPBAC/TAction.entity";
import {UpcomingChange} from "../models/foodmenu/updates/upcoming_change.model";
import {DishUpcomingChange} from "../models/foodmenu/updates/dish_upcoming_change.model";
import {Staff} from "../models/staff/staff.model";
import {StaffAuth} from "../models/staff/staffAuth.model";
import {StaffSession} from "../models/staff/staffSession.model";
import {DaysAvailable} from "../models/staff/availability/days_available.model";
import {DefaultShiftTiming} from "../models/staff/availability/default_shift_timing.model";
import {StaffCertification} from "../models/staff/certs/staff_certification.model";
import {LoginAttempt} from "../models/security/loginAttempt.model";
import {PrefBranch} from "../models/common/prefbranch.model";
import {TModule} from "../models/pBAC/tPBAC/TModule.model";
import {Module} from "../models/pBAC/Module.model";
import {Role} from "../models/pBAC/role/Role.model";
import {Permission} from "../models/pBAC/Permission.model";
import {Action} from "../models/pBAC/Action.model";
import {OrderDetails} from "../models/order/orderDetail.model";
import {OrderItem} from "../models/order/orderItem.model";
import {OrderQueue} from "../models/order/orderQueue.model";
import {Customer} from "../models/customer/customer.model";
import {CustomerAddress} from "../models/customer/customerAddress.model";
import {AuthenticationMapper} from "../models/authMap/AuthenticationMapper.model";
import {PermissionAction} from "../models/pBAC/PermissionAction";
import {RolePermissionAction} from "../models/pBAC/role/RolePermissionAction";
import {SupPermissionAction} from "../models/admin/SupAdminPermissionAction.model";
import {TenantPermissionAction} from "../models/admin/Tenant/TenantPermissionAction.model";
import {AttendanceRecord} from "../models/attendance/attendance.model";
import {TimeEntry} from "../models/attendance/time_entry.model";
import {StaffPermissionAction} from "../models/staff/StaffPermissionAction";
import {CustomerPermissionAction} from "../models/customer/CustomerPermissionAction";
import {TPermissionAction} from "../models/pBAC/tPBAC/TPermissionAction";
import {TRolePermissionAction} from "../models/pBAC/tRole/TRolePermissionAction";
import {TenantBlockList} from "../models/admin/settings/tenantBlockList/TenantBlockList";
import {BlockType} from "../models/admin/settings/tenantBlockList/BlockType";
import {OrderType} from "../models/tenantSettings/general/orderType.model";
import {FeaturePermissionAction} from "../models/subscription/feature/FeaturePermissionAction.model";
import {CurrencyUnit} from "../models/tenantSettings/general/currencyUnit.model";
import {TableCombination} from "../models/tenantSettings/tableReservation/management/tableCombination.model";
import {TableModel} from "../models/tenantSettings/tableReservation/management/table.model";
import {Cart} from "../models/cart/cart.model";
import {CartItem} from "../models/cart/cartItem.model";
import {ParkingService} from "../models/tenantSettings/general/parkingService.model";
import {Floor} from "../models/tenantSettings/tableReservation/management/floor.model";
import {Tax} from "../models/subscription/tax/tax.model";
import {TableReservation} from "../models/dineIn/tableReservation.model";
import { TurnoverTime } from "../models/tenantSettings/tableReservation/turnoverTime/turnover_time.model";
import { TurnoverRule } from "../models/tenantSettings/tableReservation/turnoverTime/turnover_rules.model";
import { BufferTime } from "../models/tenantSettings/tableReservation/management/bufferTime.model";
import { OnlineReservationConfig } from "../models/tenantSettings/tableReservation/online/online_reserv.model";
import { CustomerAuth } from "../models/customer/customerAuth.model";
import { CustomerSession } from "../models/customer/customerSession.model";
import { PartySize } from "../models/tenantSettings/tableReservation/online/partysize.model";
import { TimeSlotPace } from "../models/tenantSettings/tableReservation/online/timeSlotPace.model";

export const getAllSuperAdminModels = () => {
    return [
        // Super Admin
        SuperAdmin,
        Tenant,
        TenantBlockList,
        BlockType,
        SupPermissionAction,
        TenantPermissionAction,
        TenantAuth,
        CompanyInformation,
        AdminSettings,
        SuperSession,
        TenantSession,
        Subscription,
        SubscriptionTier,
        Feature,
        FeaturePermissionAction,
        Module,
        Role,
        RolePermissionAction,
        Permission,
        PermissionAction,
        Action,
        AuthenticationMapper,
        Tax,
        LoginAttempt,
    ];
}

export const getAllTenantModels = () => {
    return [
        // Reference / Master Entries
        SectionIcon,

        // Company Info
        CompanyInfoTenant,
        Branch,
        BankDetail,
        AccountRep,
        BranchContacts,
        BusinessHour,
        BusinessHourSlot,
        CustomHourSlot,
        SpecialDay,

        TRole,
        TPermissions,
        TAction,
        TModule,
        StaffPermissionAction,
        CustomerPermissionAction,
        TPermissionAction,
        TRolePermissionAction,

        Staff,
        StaffAuth,
        PrefBranch,
        StaffSession,
        DaysAvailable,
        DefaultShiftTiming,
        StaffCertification,
        // Food menu
        FoodMenu,
        Section,

        Ingredient,
        Dish,
        DishIngredient,
        UpcomingChange,
        DishUpcomingChange,

        // Customizations
        Customization,
        DishAddon,
        CookingStyle,
        DishBeverage,
        DishDessert,
        DishSide,
        DishExclusion,
        DishSize,
        DishExtra,
        Spiciness,

        // Security
        LoginAttempt,

        // Settings
        PreparationDuration,
        WasteManagement,
        SpecialInstructions,
        OrderType,
        CurrencyUnit,
        Allergy,
        AllergyColor,
        ReceiveOrders,
        OrderScheduling,
        Pickup,
        ParkingService,
        OnlineReservationConfig,

        Floor,
        TableCombination,
        TableModel,
        TableReservation,
        TurnoverTime,
        TurnoverRule,
        BufferTime,
        PartySize,
        TimeSlotPace,

        AttendanceRecord,
        TimeEntry,

        // orders
        OrderDetails,
        OrderItem,
        OrderQueue,
        Cart,
        CartItem,

        // customer
        Customer,
        CustomerAddress,
        CustomerAuth,
        CustomerSession
    ];
};
