import compression from 'compression';
import {Request, Response} from 'express';
import zlib from 'zlib';

const shouldCompress = (req: Request, res: Response) => {
    if (req.headers['x-no-compression']) {
        return false;
    }
    return compression.filter(req, res);
};

export const compressionMiddleware = compression({
    brotli: {
        enabled: true,
        params: {
            [zlib.constants.BROTLI_PARAM_QUALITY]: 6
        }
    },
    filter: shouldCompress,
    threshold: 100
});