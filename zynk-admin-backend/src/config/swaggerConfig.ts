import swaggerJSDoc from 'swagger-jsdoc';
import { SwaggerTheme, SwaggerThemeNameEnum } from 'swagger-themes';

const theme = new SwaggerTheme();

export const swagger_options = {
  explorer: true,
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'EasyDine',
      version: '1.0.0',
    },
  },
  apis: ['./src/routes/**/*.ts'],
  customCss: theme.getBuffer(SwaggerThemeNameEnum.FLATTOP)
};

const openapiSpecification = swaggerJSDoc(swagger_options);
export default openapiSpecification;
