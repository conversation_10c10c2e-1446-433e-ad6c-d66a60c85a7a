import {Express, Request, Response} from "express";
import superAdminRoute from "../routes/admin/superAdmin.route";
import subTierRoute from "../routes/subscription/subTier.route";
import tenantManageRoute from "../routes/admin/tenantManage.route";
import tenantRoute from "../routes/tenant/tenant.route";
import companyInfoRoute from "../routes/company/company.route";
import branchRoute from "../routes/company/branch.route";
import {OK} from "../constants/STATUS_CODES";
import {swagger_options} from "./swaggerConfig";
import swaggerUi from 'swagger-ui-express';
import swaggerDocument from '../openapi/swagger.json'
import sectionIconRoute from "../routes/reference/sectionicon.route"
import dishAddonRoute from "../routes/reference/addon.route"
import dishExtraRoute from "../routes/reference/extra.route"
import ingredientRoute from "../routes/reference/ingredient.route"
import allergyRoute from "../routes/reference/allergy.route"
import tenantSettingsRoute from "../routes/tenant/tenantSettings.route";
import pBACRoute from "../routes/pBAC/pBAC.route";
import staffManageRoute from "../routes/staff/staffManage.route"
import userPrefRoute from "../routes/userPref/userPref.route"
import orderRoute from '../routes/order/order.route'
import customerRoute from '../routes/customer/customer.route'
import testRoute from "../routes/test/test.route";
import attendanceRoute from "../routes/attendance/attendance.route";
import cartRoute from "../routes/cart/cart.route";
import taxRoute from "../routes/subscription/tax.route";
import dineInRoute from "../routes/dineIn/dineIn.route"
import tableReserveRoute from "../routes/dineIn/reservation.route"
//@ts-ignore
import foodMenuRoute from "../routes/foodmenu/foodMenu.route";
//@ts-ignore
import sectionRoute from "../routes/foodmenu/section.route";
//@ts-ignore
import dishRoute from "../routes/foodmenu/dish.route";
import countryRoute from "../routes/subscription/country.route";
import subscriptionRoute from "../routes/subscription/subscription.route";
import blockTenantRoute from "../routes/admin/settings/blockTenant.route";
import featureRoute from "../routes/subscription/feature.route";
import commonRoute from "../routes/admin/common.route";
import attachmentRoute from "../routes/attachment/attachment.route"

export const loadRoutes = async (app: Express) => {
    app.get("/ping-test", (req: Request, res: Response) => {
        res.sendStatus(OK);
    });

    app.use("/test", testRoute)

    // super admin routes
    app.use("/super-admin", superAdminRoute);
    app.use("/tenant-manage", tenantManageRoute);
    app.use("/common", commonRoute)
    // admin settings
    app.use("/tenant-blocker", blockTenantRoute)

    //Subscription
    app.use("/tax", taxRoute)
    app.use("/sub-tier", subTierRoute);
    app.use("/country", countryRoute)
    app.use("/subscription", subscriptionRoute)
    app.use("/feature", featureRoute)

    // Permission Based Access Control (PBAC) routes
    app.use("/pbac", pBACRoute);

    // reference/master entries
    app.use("/section-icon", sectionIconRoute)
    app.use("/dish-addon", dishAddonRoute)
    app.use("/dish-extra", dishExtraRoute)
    app.use("/ingredient", ingredientRoute)
    app.use("/allergy", allergyRoute)

    // tenant model routes
    app.use("/tenant", tenantRoute);
    app.use("/company-info", companyInfoRoute);
    app.use("/branch", branchRoute);

    // staff
    app.use("/staff-manage", staffManageRoute)
    app.use("/user-preferences", userPrefRoute)
    app.use("/attendance", attendanceRoute)

    app.use("/food-menu", foodMenuRoute);
    app.use("/section", sectionRoute);
    app.use("/dish", dishRoute);

    // tenantSettings routes
    app.use("/tenant-settings", tenantSettingsRoute);
    app.use("/dineIn", dineInRoute)
    app.use("/table-reservation", tableReserveRoute)

    // order
    app.use("/order", orderRoute)
    app.use("/customer", customerRoute)
    app.use("/cart", cartRoute)

    // attachment
    app.use("/attachment", attachmentRoute)

    // api docs
    app.use('/reference', swaggerUi.serve, swaggerUi.setup(swaggerDocument, swagger_options));

};
