import { NextFunction, Request, Response } from "express";
import { Customer, Status } from "../../models/customer/customer.model";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { Not, Repository } from "typeorm";
import { CustomerAddress } from "../../models/customer/customerAddress.model";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import {
  BAD_REQUEST,
  CONFLICT,
  CREATED,
  NOT_FOUND,
  OK,
  UNAUTHORIZED,
} from "../../constants/STATUS_CODES";
import { errorHandler } from "../../utils/errorHandler";
import {
  generateIncrementalCustomerCode,
  generateRandomPassword,
} from "../../helpers/customer/customer.helper";
import argon2 from "argon2";
import {
  clearCookie,
  setCookie,
} from "../../helpers/system/CookieHelper.helper";
import { LoginRateLimitService } from "../../services/loginAttempt.service";
import { IP_NOT_FOUND } from "../../constants/security/err";
import { IP_BLOCKED } from "../../constants/security/msg";
import { CUSTOMER_NOT_FOUND } from "../../constants/tenant/customer/err";
import { ACCESS_DENIED_INVALID_PASSWORD } from "../../constants/common/err";
import { CustomerSession } from "../../models/customer/customerSession.model";
import { AESHelper } from "../../helpers/system/AESHelper";
import { IP_REVOKED } from "../../constants/admin/err";
import { JWTPayload } from "../../../types/jwt";
import { genNewRefreshToken } from "../../helpers/system/superadmin.helper";
import { CustomerAuth } from "../../models/customer/customerAuth.model";
import { isTokenVerified } from "../../helpers/system/token.helper";
import { PrefBranch, PrefUserType } from "../../models/common/prefbranch.model";
import { PREFBRANCH_NOT_FOUND } from "../../constants/tenant/userPref/err";
import { Branch } from "../../models/company/branch.model";
import { PRIMARY_BRANCH_NOT_FOUND } from "../../constants/tenant/company/err";

export const createCustomer = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { Customer, CustomerAddress, PrefBranch, Branch } = getRepositories(
      qRunner
    ) as {
      Customer: Repository<Customer>;
      CustomerAddress: Repository<CustomerAddress>;
      PrefBranch: Repository<PrefBranch>;
      Branch: Repository<Branch>;
    };

    const {
      firstName,
      middleName,
      lastName,
      email,
      phoneNumber,
      password,
      addresses = [],
    } = req.body;

    if (!firstName) {
      return next(errorHandler(BAD_REQUEST, "firstName is required"));
    }

    if (!email && !phoneNumber) {
      return next(
        errorHandler(BAD_REQUEST, "Either email or phoneNumber is required")
      );
    }

    // Check if customer already exists with same email or phone
    const existingCustomerQuery: any = {};
    if (email) existingCustomerQuery.email = email;
    if (phoneNumber) existingCustomerQuery.phoneNumber = phoneNumber;

    if (Object.keys(existingCustomerQuery).length > 0) {
      const existingCustomer = await Customer.findOne({
        where: [
          ...(email ? [{ email }] : []),
          ...(phoneNumber ? [{ phoneNumber }] : []),
        ],
      });

      if (existingCustomer) {
        const conflictField =
          existingCustomer.email === email ? "email" : "phoneNumber";
        return next(
          errorHandler(
            CONFLICT,
            `Customer with this ${conflictField} already exists`
          )
        );
      }
    }

    let customerPassword = password;
    let isPasswordGenerated = false;

    if (!customerPassword) {
      customerPassword = generateRandomPassword();
      isPasswordGenerated = true;
    }

    const hashedPassword = await argon2.hash(customerPassword);

    // Generate customer code
    const customerCode = await generateIncrementalCustomerCode(Customer);

    // Create customer object
    const customerData: any = {
      customerCode,
      firstName,
      middleName,
      lastName,
      customerAuth: {
        password: hashedPassword,
      },
      status: Status.ACTIVE,
    };

    // Add email and phoneNumber only if provided
    if (email) customerData.email = email;
    if (phoneNumber) customerData.phoneNumber = phoneNumber;

    const newCustomer = Customer.create(customerData as Customer);
    const savedCustomer = await Customer.save(newCustomer);

    // Handle addresses if provided
    if (addresses.length > 0) {
      // Ensure only one default address
      let hasDefault = false;
      const customerAddresses = addresses.map((address: any, index: number) => {
        const isDefault = !hasDefault && (index === 0 || address.isDefault);
        if (isDefault) hasDefault = true;

        return CustomerAddress.create({
          customerId: savedCustomer.customerId, // Use customerId instead of customer object
          addressType: address.addressType || "HOME",
          addressLine1: address.addressLine1,
          addressLine2: address.addressLine2,
          city: address.city,
          state: address.state,
          postalCode: address.postalCode,
          country: address.country || "UK",
          isDefault,
        });
      });

      await CustomerAddress.save(customerAddresses);
    }

    const primaryBranch = await Branch.findOne({
      where: {
        isPrimary: true,
      },
    });

    if (!primaryBranch)
      return next(errorHandler(NOT_FOUND, PRIMARY_BRANCH_NOT_FOUND));

    const newCustPrefBranch = PrefBranch.create({
      prefBranchId: primaryBranch.branchId,
      userId: newCustomer.customerId,
      userType: PrefUserType.CUSTOMER,
    });

    await PrefBranch.save(newCustPrefBranch);

    await qRunner.commitTransaction();

    // Fetch the complete customer data with addresses
    const result = await Customer.findOne({
      where: { customerId: savedCustomer.customerId },
      relations: ["addresses"],
      select: [
        "customerId",
        "firstName",
        "lastName",
        "email",
        "phoneNumber",
        "customerCode",
        "status",
        "createdAt",
        "addresses",
      ],
    });

    // Prepare response data
    const responseData: any = {
      customer: result,
    };

    // Include generated password in response if it was auto-generated
    // Note: In production, consider sending this via email/SMS instead
    if (isPasswordGenerated) {
      responseData.generatedPassword = customerPassword;
      responseData.message =
        "Customer created successfully. A temporary password has been generated.";
    }

    ResponseHelper.success(
      res,
      CREATED,
      isPasswordGenerated
        ? "Customer created successfully with generated password"
        : "Customer created successfully",
      responseData
    );
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchCustomer = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { Customer } = getRepositories(qRunner) as {
      Customer: Repository<Customer>;
    };

    const { customerId } = req.params;
    const updateData = req.body;

    // Remove undefined and null values from updateData
    const filteredUpdateData = Object.keys(updateData).reduce((acc, key) => {
      if (updateData[key] !== undefined && updateData[key] !== null) {
        acc[key] = updateData[key];
      }
      return acc;
    }, {} as any);

    // Check if customer exists
    const existingCustomer = await Customer.findOne({
      where: { customerId },
    });

    if (!existingCustomer) {
      return next(errorHandler(NOT_FOUND, "Customer not found"));
    }

    // Check for unique constraints if email or phoneNumber is being updated
    if (filteredUpdateData.email) {
      const emailExists = await Customer.findOne({
        where: {
          email: filteredUpdateData.email,
          customerId: Not(customerId),
        },
      });
      if (emailExists) {
        return next(errorHandler(BAD_REQUEST, "Email already exists"));
      }
    }

    if (filteredUpdateData.phoneNumber) {
      const phoneExists = await Customer.findOne({
        where: {
          phoneNumber: filteredUpdateData.phoneNumber,
          customerId: Not(customerId),
        },
      });
      if (phoneExists) {
        return next(errorHandler(BAD_REQUEST, "Phone number already exists"));
      }
    }

    // Update customer
    await Customer.update({ customerId }, filteredUpdateData);

    await qRunner.commitTransaction();

    // Fetch updated customer
    const updatedCustomer = await Customer.findOne({
      where: { customerId },
      select: [
        "customerId",
        "firstName",
        "middleName",
        "lastName",
        "email",
        "phoneNumber",
        "customerCode",
        "status",
        "createdAt",
        "updatedAt",
      ],
    });

    ResponseHelper.success(
      res,
      OK,
      "Customer updated successfully",
      updatedCustomer
    );
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Patch Customer Address Controller
export const patchCustomerAddress = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { CustomerAddress } = getRepositories(qRunner) as {
      CustomerAddress: Repository<CustomerAddress>;
    };

    const { addressId } = req.params;
    const updateData = req.body;

    // Remove undefined and null values from updateData
    const filteredUpdateData = Object.keys(updateData).reduce((acc, key) => {
      if (updateData[key] !== undefined && updateData[key] !== null) {
        acc[key] = updateData[key];
      }
      return acc;
    }, {} as any);

    // Check if address exists
    const existingAddress = await CustomerAddress.findOne({
      where: { addressId },
    });

    if (!existingAddress) {
      return next(errorHandler(NOT_FOUND, "Address not found"));
    }

    // If setting as default, unset other default addresses for this customer
    if (filteredUpdateData.isDefault === true) {
      await CustomerAddress.update(
        { customerId: existingAddress.customerId },
        { isDefault: false }
      );
    }

    // Update address
    await CustomerAddress.update({ addressId }, filteredUpdateData);

    await qRunner.commitTransaction();

    // Fetch updated address
    const updatedAddress = await CustomerAddress.findOne({
      where: { addressId },
    });

    ResponseHelper.success(
      res,
      OK,
      "Address updated successfully",
      updatedAddress
    );
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Get All Customers Controller
export const getAllCustomers = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { Customer } = getRepositories(qRunner) as {
      Customer: Repository<Customer>;
    };

    const {
      page = 1,
      limit = 10,
      status,
      search,
      sortBy = "createdAt",
      sortOrder = "DESC",
    } = req.query;

    const pageNumber = parseInt(page as string);
    const limitNumber = parseInt(limit as string);
    const offset = (pageNumber - 1) * limitNumber;

    // Build where conditions
    const whereConditions: any = {};

    if (status) {
      whereConditions.status = status;
    }

    // Build query builder for complex search
    const queryBuilder = Customer.createQueryBuilder("customer")
      .leftJoinAndSelect("customer.addresses", "addresses")
      .select([
        "customer.customerId",
        "customer.firstName",
        "customer.lastName",
        "customer.email",
        "customer.phoneNumber",
        "customer.customerCode",
        "customer.status",
        "customer.createdAt",
        "addresses.addressId",
        "addresses.addressType",
        "addresses.state",
        "addresses.country",
        "addresses.postalCode",
        "addresses.addressLine1",
        "addresses.addressLine2",
        "addresses.city",
        "addresses.isDefault",
      ]);

    // Apply where conditions
    if (Object.keys(whereConditions).length > 0) {
      Object.keys(whereConditions).forEach((key) => {
        queryBuilder.andWhere(`customer.${key} = :${key}`, {
          [key]: whereConditions[key],
        });
      });
    }

    // Apply search filter
    if (search) {
      queryBuilder.andWhere(
        "(customer.firstName ILIKE :search OR customer.lastName ILIKE :search OR customer.email ILIKE :search OR customer.phoneNumber ILIKE :search OR customer.customerCode ILIKE :search)",
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`customer.${sortBy}`, sortOrder as "ASC" | "DESC");

    // Apply pagination
    queryBuilder.skip(offset).take(limitNumber);

    // Get results and count
    const [customers, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limitNumber);

    const paginationData = {
      customers,
      pagination: {
        currentPage: pageNumber,
        totalPages,
        totalItems: total,
        itemsPerPage: limitNumber,
        hasNextPage: pageNumber < totalPages,
        hasPreviousPage: pageNumber > 1,
      },
    };

    ResponseHelper.success(
      res,
      OK,
      "Customers retrieved successfully",
      paginationData
    );
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Delete Customer Controller
export const deleteCustomer = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { Customer, CustomerAddress } = getRepositories(qRunner) as {
      Customer: Repository<Customer>;
      CustomerAddress: Repository<CustomerAddress>;
    };

    const { customerId } = req.params;

    // Check if customer exists
    const existingCustomer = await Customer.findOne({
      where: { customerId },
    });

    if (!existingCustomer) {
      return next(errorHandler(NOT_FOUND, "Customer not found"));
    }

    // Delete associated addresses first (if not using cascade)
    await CustomerAddress.delete({ customerId });

    // Delete customer
    await Customer.delete({ customerId });

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, "Customer deleted successfully", null);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Delete Customer Address Controller
export const deleteCustomerAddress = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { CustomerAddress } = getRepositories(qRunner) as {
      CustomerAddress: Repository<CustomerAddress>;
    };

    const { addressId } = req.params;

    // Check if address exists
    const existingAddress = await CustomerAddress.findOne({
      where: { addressId },
    });

    if (!existingAddress) {
      return next(errorHandler(NOT_FOUND, "Address not found"));
    }

    // Check if this is the only address for the customer
    const customerAddresses = await CustomerAddress.find({
      where: { customerId: existingAddress.customerId },
    });

    if (customerAddresses.length === 1) {
      return next(
        errorHandler(
          BAD_REQUEST,
          "Cannot delete the only address for a customer"
        )
      );
    }

    // If deleting default address, set another address as default
    if (existingAddress.isDefault && customerAddresses.length > 1) {
      const nextAddress = customerAddresses.find(
        (addr) => addr.addressId !== addressId
      );
      if (nextAddress) {
        await CustomerAddress.update(
          { addressId: nextAddress.addressId },
          { isDefault: true }
        );
      }
    }

    // Delete address
    await CustomerAddress.delete({ addressId });

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, "Address deleted successfully", null);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Add Customer Address Controller
export const addCustomerAddress = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { Customer, CustomerAddress } = getRepositories(qRunner) as {
      Customer: Repository<Customer>;
      CustomerAddress: Repository<CustomerAddress>;
    };

    const { customerId } = req.params;
    const {
      addressType = "HOME",
      addressLine1,
      addressLine2,
      city,
      state,
      postalCode,
      country = "UK",
      isDefault = false,
    } = req.body;

    // Check if customer exists
    const existingCustomer = await Customer.findOne({
      where: { customerId },
    });

    if (!existingCustomer) {
      return next(errorHandler(NOT_FOUND, "Customer not found"));
    }

    // If setting as default, unset other default addresses for this customer
    if (isDefault) {
      await CustomerAddress.update({ customerId }, { isDefault: false });
    }

    // Create new address
    const newAddress = CustomerAddress.create({
      customerId,
      addressType,
      addressLine1,
      addressLine2,
      city,
      state,
      postalCode,
      country,
      isDefault,
    });

    const savedAddress = await CustomerAddress.save(newAddress);

    await qRunner.commitTransaction();

    ResponseHelper.success(
      res,
      CREATED,
      "Address added successfully",
      savedAddress
    );
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getCustomerById = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { Customer } = getRepositories(qRunner) as {
      Customer: Repository<Customer>;
    };

    const { customerId } = req.params;

    const customer = await Customer.findOne({
      where: { customerId },
      relations: ["addresses", "orderDetails"],
      select: [
        "customerId",
        "firstName",
        "lastName",
        "email",
        "phoneNumber",
        "customerCode",
        "status",
        "createdAt",
        "updatedAt",
        "orderDetails",
        "addresses",
      ],
    });

    if (!customer) {
      return next(errorHandler(NOT_FOUND, "Customer not found"));
    }

    ResponseHelper.success(
      res,
      OK,
      "Customer retrieved successfully",
      customer
    );
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getCustomerByEmailOrPhone = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { Customer } = getRepositories(qRunner) as {
      Customer: Repository<Customer>;
    };

    const { email, phoneNumber } = req.query;

    if (!email && !phoneNumber) {
      return next(
        errorHandler(BAD_REQUEST, "Email or Phone Number is required")
      );
    }

    let customer;
    if (email) {
      customer = await Customer.findOne({
        where: { email: email as string },
        relations: ["addresses"],
        select: [
          "customerId",
          "firstName",
          "lastName",
          "email",
          "phoneNumber",
          "customerCode",
          "status",
          "createdAt",
        ],
      });
    } else if (phoneNumber) {
      // Handle the fact that + gets converted to space in URL parameters
      let searchPhone = phoneNumber as string;

      // If it contains spaces, it might be a + that got converted
      if (searchPhone.includes(" ")) {
        searchPhone = searchPhone.replace(/\s/g, "+");
      }

      // Now decode properly
      searchPhone = decodeURIComponent(searchPhone);

      // Array of phone variations to try
      const phoneVariations: any = [
        searchPhone, // Original
        searchPhone.startsWith("+")
          ? searchPhone.substring(1)
          : `+${searchPhone}`, // Toggle +
        searchPhone.replace(/\s/g, ""), // Remove spaces
        searchPhone.replace(/\s/g, "").replace(/^\+/, ""), // Remove spaces and +
        `+${searchPhone.replace(/\s/g, "").replace(/^\+/, "")}`, // Clean and add +
      ];

      // Remove duplicates
      const uniqueVariations: string[] = [];
      for (const variation of phoneVariations) {
        if (!uniqueVariations.includes(variation)) {
          uniqueVariations.push(variation);
        }
      }

      for (const variation of uniqueVariations) {
        customer = await Customer.findOne({
          where: { phoneNumber: variation },
          relations: ["addresses"],
          select: [
            "customerId",
            "firstName",
            "lastName",
            "email",
            "phoneNumber",
            "customerCode",
            "status",
            "createdAt",
          ],
        });

        if (customer) {
          break;
        }
      }
    }

    if (!customer) {
      return next(errorHandler(NOT_FOUND, "Customer not found"));
    }

    ResponseHelper.success(
      res,
      OK,
      "Customer retrieved successfully",
      customer
    );
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const loginCustomer = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { Customer, CustomerSession, CustomerAuth, PrefBranch } =
      getRepositories(qRunner) as {
        Customer: Repository<Customer>;
        CustomerSession: Repository<CustomerSession>;
        CustomerAuth: Repository<CustomerAuth>;
        PrefBranch: Repository<PrefBranch>;
      };

    const ip = req.clientIP;
    const { emailAddress, password } = req.body;
    let responseData = null;

    if (!ip) return next(errorHandler(NOT_FOUND, IP_NOT_FOUND));

    const rateLimitCheck = await LoginRateLimitService.isIpRateLimited(
      qRunner,
      ip
    );

    if (rateLimitCheck.blocked) {
      return next(
        errorHandler(
          UNAUTHORIZED,
          rateLimitCheck.message ? rateLimitCheck.message : IP_BLOCKED
        )
      );
    }

    const exCustomer = await Customer.findOne({
      where: {
        email: emailAddress,
      },
      relations: ["customerSessions", "customerAuth"],
    });

    if (!exCustomer) {
      await LoginRateLimitService.checkAndLogAttempt(
        qRunner,
        ip,
        emailAddress,
        "USER",
        req.userAgent,
        false,
        undefined,
        "USER_NOT_FOUND"
      );
      return next(errorHandler(UNAUTHORIZED, CUSTOMER_NOT_FOUND));
    }

    const verified = await argon2.verify(
      exCustomer.customerAuth.password,
      password
    );

    if (!verified) {
      await LoginRateLimitService.checkAndLogAttempt(
        qRunner,
        ip,
        emailAddress,
        "USER",
        req.userAgent,
        false,
        undefined,
        ACCESS_DENIED_INVALID_PASSWORD
      );
      return next(errorHandler(UNAUTHORIZED, ACCESS_DENIED_INVALID_PASSWORD));
    }

    const allSessions = exCustomer.customerSessions;
    const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

    let exSession: CustomerSession | null = null;

    for (const session of allSessions) {
      const decryptedIp = await AESHelper.decrypt(
        session.ipAddress,
        secret_key
      );
      if (decryptedIp === ip) {
        exSession = session;
        break;
      }
    }

    if (!exSession) {
      const newSession = CustomerSession.create({
        customer: exCustomer,
        ipAddress: await AESHelper.encrypt(ip, secret_key),
        userAgent: req.userAgent,
        lastLogin: new Date(),
      });

      await CustomerSession.save(newSession);
    } else {
      if (exSession.isRevoked) {
        await LoginRateLimitService.checkAndLogAttempt(
          qRunner,
          ip,
          emailAddress,
          "USER",
          req.userAgent,
          false,
          undefined,
          IP_REVOKED
        );
        return next(errorHandler(UNAUTHORIZED, IP_REVOKED));
      }

      if (exSession.userAgent && exSession.userAgent !== req.userAgent) {
        exSession.userAgent = req.userAgent;
      }

      exSession.lastLogin = new Date();
      await CustomerSession.save(exSession);
    }

    await LoginRateLimitService.checkAndLogAttempt(
      qRunner,
      ip,
      emailAddress,
      "USER",
      req.userAgent,
      true,
      exCustomer.customerId
    );

    const prefBranch = await PrefBranch.findOne({
      where: {
        userId: exCustomer.customerId,
        userType: "customer",
      },
    });

    if (!prefBranch) return next(errorHandler(NOT_FOUND, PREFBRANCH_NOT_FOUND));

    const payload: JWTPayload = {
      userId: exCustomer.customerId,
      branchId: prefBranch.prefBranchId!,
      isCustomer: true,
      domainName: (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
      isTenant: true,
      inSuper: false,
    };

    setCookie(res, payload);

    if (!exCustomer.customerAuth.refreshToken) {
      const newRefToken = await genNewRefreshToken(payload, secret_key);
      exCustomer.customerAuth.refreshToken = newRefToken;
      await CustomerAuth.save(exCustomer.customerAuth);
    } else {
      const decrypted_refreshToken = await AESHelper.decrypt(
        exCustomer.customerAuth.refreshToken,
        secret_key
      );
      const tokenVerif = isTokenVerified(decrypted_refreshToken, true);

      if (!tokenVerif) {
        const newRefToken = await genNewRefreshToken(payload, secret_key);
        exCustomer.customerAuth.refreshToken = newRefToken;
        await CustomerAuth.save(exCustomer.customerAuth);
      }
    }

    responseData = {
      userName: exCustomer.firstName + " " + exCustomer.lastName,
      userType: "customer",
    };

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, "Logged in successfully!", responseData);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const revokeCustomer = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {Customer, CustomerSession} = getRepositories(qRunner) as {
            Customer: Repository<Customer>;
            CustomerSession: Repository<CustomerSession>;
        };

        const customerId = req.params.customerId;

        const {ipAddress} = req.body;

        const exCustomer = await Customer.findOne({
            where: {customerId},
            relations: ["customerSessions"],
        });

        if (!exCustomer) return next(errorHandler(NOT_FOUND, CUSTOMER_NOT_FOUND));

        if (ipAddress) {
            const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

            let exSession: CustomerSession | null = null;

            for (const session of exCustomer.customerSessions) {
                const decryptedIp = await AESHelper.decrypt(
                    session.ipAddress,
                    secret_key
                );
                if (decryptedIp === ipAddress) {
                    exSession = session;
                    break;
                }
            }

            if (exSession) {
                exSession.isRevoked = true;

                await CustomerSession.save(exSession);
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Revoked customer session successfully!");
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const reinstateCustomer = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {Customer, CustomerSession} = getRepositories(qRunner) as {
            Customer: Repository<Customer>;
            CustomerSession: Repository<CustomerSession>;
        };

        const customerId = req.params.customerId;

        const {ipAddress} = req.body;

        const exCustomer = await Customer.findOne({
            where: {customerId},
            relations: ["customerSessions"],
        });

        if (!exCustomer) return next(errorHandler(NOT_FOUND, CUSTOMER_NOT_FOUND));

        if (ipAddress) {
            const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

            let exSession: CustomerSession | null = null;

            for (const session of exCustomer.customerSessions) {
                const decryptedIp = await AESHelper.decrypt(
                    session.ipAddress,
                    secret_key
                );
                if (decryptedIp === ipAddress) {
                    exSession = session;
                    break;
                }
            }

            if (exSession) {
                exSession.isRevoked = false;

                await CustomerSession.save(exSession);
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Reinstated revoked IP for customer successfully!");
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const logoutCustomer = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    clearCookie(res);

    ResponseHelper.success(res, OK, "Logged out successfully!");
  } catch (error) {
    next(error);
  }
};
