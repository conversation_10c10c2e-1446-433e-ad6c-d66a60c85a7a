import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Repository} from "typeorm";
import {Dish} from "../../models/foodmenu/dish.model";
import {
  addAddonsAndExtras,
  addFinalCustomizations,
  createDishAuxiliaryData,
  createDishDetails,
  createPreliminaryCustomizations,
  filterDishResponse,
} from "../../helpers/dish/dish.helper";
import {Section} from "../../models/foodmenu/section.model";
import {errorHandler} from "../../utils/errorHandler";
import {BAD_REQUEST, CONFLICT, CREATED, NOT_FOUND, OK,} from "../../constants/STATUS_CODES";
import {SECTION_NOT_FOUND} from "../../constants/tenant/sections/err";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {
  DISH_CLONED,
  DISH_CREATED,
  DISH_DELETED,
  DISH_FETCHED,
  DISH_MOVED_TO_SECTION,
  DISH_PATCHED,
  DISHES_FETCHED,
} from "../../constants/tenant/dish/msg";
import {Ingredient} from "../../models/reference/ingredient.model";
import {Customization} from "../../models/foodmenu/customization.model";
import {DishIngredient} from "../../models/foodmenu/dish_ingredient.model";
import {Allergy} from "../../models/reference/allergy.model";
import {DishAddon} from "../../models/foodmenu/custom/addon.model";
import {DishSize} from "../../models/foodmenu/custom/dishsize.model";
import {CookingStyle} from "../../models/foodmenu/custom/cookingstyle.model";
import {Spiciness} from "../../models/foodmenu/custom/spiciness.model";
import {DishExtra} from "../../models/foodmenu/custom/extra.model";
import {DishSide} from "../../models/foodmenu/custom/dish_side.model";
import {DishBeverage} from "../../models/foodmenu/custom/dish_bev.model";
import {DishDessert} from "../../models/foodmenu/custom/dish_dessert.model";
import {DishExclusion} from "../../models/foodmenu/custom/dishexclusion.model";
import {DISH_NOT_FOUND} from "../../constants/tenant/dish/err";
import {
  patchDishAddonsAndExtras,
  patchDishAuxiliaryDetails,
  patchDishDetails,
  patchDishFinalCustomizations,
  patchDishPreliminaryCustomization,
} from "../../helpers/dish/dish.patch.helper";
import {CustomHourSlot} from "../../models/common/customhourslot.model";
import {CUSTOM_HOUR_NOT_FOUND, DUPLICATE_DAYS_IN_CUSTOMHOURS,} from "../../constants/tenant/company/err";
import {SpecialDay} from "../../models/common/specialday.model";
import {dishDuplicator} from "../../helpers/dish/duplication.helper";
import {applyUpcomingChangesToDishes} from "../../helpers/dish/upcomingchange.helper";

export const createDish = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {
            Dish,
            Section,
            Customization,
            Ingredient,
            DishIngredient,
            Allergy,
            // Customizations
            DishSize,
            CookingStyle,
            Spiciness,
            DishExclusion,

            // Addons and Extras
            DishAddon,
            DishExtra,

            // Sides, Beverages, Desserts
            DishSide,
            DishBeverage,
            DishDessert,
        } = getRepositories(qRunner) as {
            Dish: Repository<Dish>;
            Section: Repository<Section>;
            Customization: Repository<Customization>;
            Ingredient: Repository<Ingredient>;
            DishIngredient: Repository<DishIngredient>;
            Allergy: Repository<Allergy>;

            // Customizations
            DishSize: Repository<DishSize>;
            CookingStyle: Repository<CookingStyle>;
            Spiciness: Repository<Spiciness>;
            DishExclusion: Repository<DishExclusion>;

            // Addons and Extras
            DishAddon: Repository<DishAddon>;
            DishExtra: Repository<DishExtra>;

            // Sides, Beverages, Desserts
            DishSide: Repository<DishSide>;
            DishBeverage: Repository<DishBeverage>;
            DishDessert: Repository<DishDessert>;
        };

        const sectionId = req.params.sectionId;

        const exSection = await Section.findOneBy({
            menuSectionId: sectionId,
        });

        if (!exSection) {
            return next(errorHandler(NOT_FOUND, SECTION_NOT_FOUND));
        }

        const newDish = await createDishDetails(
            req,
            Dish,
            exSection,
            DishSide,
            DishBeverage,
            DishDessert
        );
        await createDishAuxiliaryData(
            req,
            newDish,
            Dish,
            Ingredient,
            DishIngredient,
            Allergy
        );
        const newCustomization = await createPreliminaryCustomizations(
            req,
            newDish,
            Customization,
            DishSize,
            CookingStyle,
            Spiciness,
            DishExclusion
        );

        // Addons and Extras
        await addAddonsAndExtras(
            req,
            newDish,
            newCustomization,
            DishAddon,
            DishExtra,
            Customization
        );
        // Sides, Beverages, Desserts
        await addFinalCustomizations(
            req,
            newDish,
            newCustomization,
            DishSide,
            DishBeverage,
            DishDessert,
            Customization
        );

        await qRunner.commitTransaction();

        const filteredDish = filterDishResponse(newDish);
        ResponseHelper.success(res, CREATED, DISH_CREATED, filteredDish);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteDish = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {Dish} = getRepositories(qRunner) as {
            Dish: Repository<Dish>;
        };

        const dishId = req.params.dishId;

        const exDish = await Dish.findOne({
            where: {
                dishId,
            },
        });

        if (!exDish) {
            return next(errorHandler(NOT_FOUND, DISH_NOT_FOUND));
        }

        await Dish.remove(exDish);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, DISH_DELETED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const viewAllDishesBySection = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const queryRunner = req.queryRunner;

    await queryRunner.connect();

    try {
        const {Section, Dish} = getRepositories(queryRunner) as {
            Section: Repository<Section>;
            Dish: Repository<Dish>;
        };

        const sectionId = req.params.sectionId;

        const exSection = await Section.findOne({
            where: {menuSectionId: sectionId},
            relations: ["foodMenu"],
        });

        if (!exSection) return next(errorHandler(NOT_FOUND, SECTION_NOT_FOUND));

        const allDishes = await Dish.find({
            where: {
                section: {
                    menuSectionId: exSection.menuSectionId,
                },
            },
            relations: ["customSlots", "globalCustomSlots", "specialDays"],
            order: {
                createdAt: "ASC",
            },
        });

        const foodMenuId = exSection.foodMenu?.foodMenuId;
        const modifiedDishes = (await applyUpcomingChangesToDishes(
            allDishes,
            queryRunner,
            foodMenuId
        )) as Dish[];

        ResponseHelper.success(res, OK, DISHES_FETCHED, modifiedDishes);
    } catch (error) {
        next(error);
    } finally {
        await queryRunner.release();
    }
};

export const viewDishById = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const queryRunner = req.queryRunner;

    await queryRunner.connect();

    try {
        const {Dish} = getRepositories(queryRunner) as {
            Section: Repository<Section>;
            Dish: Repository<Dish>;
        };

        const dishId = req.params.dishId;

        const exDish = await Dish.findOne({
            where: {
                dishId,
            },
            relations: [
                "dishIngredients",
                "allergies",
                "customization",
                "customization.dishSizes",
                "customization.dishExclusions",
                "customization.cookingStyles",
                "customization.spiciness",
                "customization.dishAddons",
                "customization.dishExtras",
                "customization.dishSides",
                "customization.dishBeverages",
                "customization.dishDesserts",
                "customSlots",
                "globalCustomSlots",
                "specialDays",

                "section",
                "section.foodMenu",
            ],
        });

        if (!exDish) return next(errorHandler(NOT_FOUND, DISH_NOT_FOUND));

        const foodMenuId = exDish.section?.foodMenu?.foodMenuId;
        const modifiedDish = (await applyUpcomingChangesToDishes(
            exDish,
            queryRunner,
            foodMenuId
        )) as Dish;

        ResponseHelper.success(res, OK, DISH_FETCHED, modifiedDish);
    } catch (error) {
        next(error);
    } finally {
        await queryRunner.release();
    }
};

export const patchDish = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {
            Dish,
            Customization,
            Ingredient,
            DishIngredient,
            Allergy,
            // Customizations
            DishSize,
            CookingStyle,
            Spiciness,
            DishExclusion,

            // Addons and Extras
            DishAddon,
            DishExtra,

            // Sides, Beverages, Desserts
            DishSide,
            DishBeverage,
            DishDessert,
        } = getRepositories(qRunner) as {
            Dish: Repository<Dish>;
            Customization: Repository<Customization>;
            Ingredient: Repository<Ingredient>;
            DishIngredient: Repository<DishIngredient>;
            Allergy: Repository<Allergy>;

            // Customizations
            DishSize: Repository<DishSize>;
            CookingStyle: Repository<CookingStyle>;
            Spiciness: Repository<Spiciness>;
            DishExclusion: Repository<DishExclusion>;

            // Addons and Extras
            DishAddon: Repository<DishAddon>;
            DishExtra: Repository<DishExtra>;

            // Sides, Beverages, Desserts
            DishSide: Repository<DishSide>;
            DishBeverage: Repository<DishBeverage>;
            DishDessert: Repository<DishDessert>;
        };

        const dishId = req.params.dishId;

        const exDish = await Dish.findOne({
            where: {
                dishId,
            },
            relations: [
                "allergies",
                "dishIngredients",
                "customization",
                "customization.dishSizes",
                "customization.dishExclusions",
                "customization.cookingStyles",
                "customization.spiciness",
                "customization.dishAddons",
                "customization.dishExtras",
                "customization.dishSides",
                "customization.dishBeverages",
                "customization.dishDesserts",
            ],
        });

        if (!exDish) return next(errorHandler(NOT_FOUND, DISH_NOT_FOUND));

        await patchDishDetails(
            req,
            exDish,
            Dish,
            DishSide,
            DishBeverage,
            DishDessert
        );
        await patchDishAuxiliaryDetails(
            req,
            exDish,
            Dish,
            Ingredient,
            DishIngredient,
            Allergy
        );

        // Customizations
        const customization = await patchDishPreliminaryCustomization(
            req,
            exDish,
            Customization,
            DishSize,
            CookingStyle,
            Spiciness,
            DishExclusion
        );
        
        await patchDishAddonsAndExtras(
            req,
            exDish,
            customization!,
            DishAddon,
            DishExtra,
            Customization
        );
        await patchDishFinalCustomizations(
            req,
            customization!,
            DishSide,
            DishBeverage,
            DishDessert,
            Customization
        );

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, DISH_PATCHED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchDishCustomHours = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();
    try {
        const {Dish, CustomHourSlot} = getRepositories(qRunner) as {
            Dish: Repository<Dish>;
            CustomHourSlot: Repository<CustomHourSlot>;
        };

        const dishId = req.params.dishId;
        const {customTimes, customSlots, globalCustomSlots} = req.body;

        const exDish = await Dish.findOne({
            where: {
                dishId,
            },
            relations: [
                "customSlots",
                "globalCustomSlots",
                "section.foodMenu.branch.businessHour.customSlots",
            ],
        });

        if (!exDish) return next(errorHandler(NOT_FOUND, DISH_NOT_FOUND));

        if (customTimes !== undefined) {
            exDish.customTimes = customTimes;
            await Dish.save(exDish);
        }

        if (exDish.globalCustomSlots && globalCustomSlots) {
            const branchCustomSlotIds =
                exDish.section.foodMenu.branch.businessHour.customSlots.map(
                    (slot) => slot.slotId
                );

            for (const slotId of globalCustomSlots) {
                if (!branchCustomSlotIds.includes(slotId)) {
                    return next(errorHandler(BAD_REQUEST, CUSTOM_HOUR_NOT_FOUND));
                }
            }

            exDish.globalCustomSlots = exDish.globalCustomSlots.filter((slot) =>
                globalCustomSlots.includes(slot.slotId)
            );

            // Add new slots
            for (const slotId of globalCustomSlots) {
                if (!exDish.globalCustomSlots.find((slot) => slot.slotId === slotId)) {
                    const customSlot = await CustomHourSlot.findOneBy({slotId});
                    if (customSlot) {
                        exDish.globalCustomSlots.push(customSlot);
                    }
                }
            }

            await Dish.save(exDish);
        }

        if (exDish.customTimes && customSlots) {
            // Check for duplicate active days across both existing and incoming custom slots
            const customHourDays = new Map<string, boolean>();

            exDish.customSlots.forEach((slot: any) => {
                if (slot.days && slot.isActive) {
                    Object.entries(slot.days).forEach(([day, isActive]) => {
                        if (isActive && slot.isActive) {
                            customHourDays.set(day, true);
                        }
                    });
                }
            });

            customSlots.forEach((slot: any) => {
                if (slot.days && slot.isActive) {
                    Object.entries(slot.days).forEach(([day, isActive]) => {
                        if (isActive && customHourDays.get(day)) {
                            return next(errorHandler(CONFLICT, DUPLICATE_DAYS_IN_CUSTOMHOURS));
                        }
                        if (isActive && slot.isActive) {
                            customHourDays.set(day, true);
                        }
                    });
                }
            });

            const existingCustomSlotIds = exDish.customSlots.map(
                (slot) => slot.slotId
            );
            const incomingCustomSlotIds = customSlots
                .filter((slot: any) => slot.slotId)
                .map((slot: any) => slot.slotId);

            const customSlotsToDelete = exDish.customSlots.filter(
                (slot) => !incomingCustomSlotIds.includes(slot.slotId)
            );

            if (customSlotsToDelete.length > 0) {
                await CustomHourSlot.remove(customSlotsToDelete);
            }

            for (const slotData of customSlots) {
                if (slotData.slotId) {
                    const updateData: Partial<CustomHourSlot> = {};

                    if ("name" in slotData) updateData.name = slotData.name;
                    if ("days" in slotData) updateData.days = slotData.days;
                    if ("is24Hours" in slotData)
                        updateData.is24Hours = slotData.is24Hours;
                    if ("firstSeating" in slotData)
                        updateData.firstSeating = slotData.firstSeating;
                    if ("lastSeating" in slotData)
                        updateData.lastSeating = slotData.lastSeating;
                    if ("isActive" in slotData) updateData.isActive = slotData.isActive;

                    await CustomHourSlot.update({slotId: slotData.slotId}, updateData);
                } else {
                    const newCustomSlot = CustomHourSlot.create({
                        dish: exDish,
                        name: slotData.name,
                        days: slotData.days,
                        is24Hours: slotData.is24Hours,
                        firstSeating: slotData.firstSeating,
                        lastSeating: slotData.lastSeating,
                        isActive: slotData.isActive,
                    });

                    await CustomHourSlot.save(newCustomSlot);
                }
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, DISH_PATCHED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchDishAvailability = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();
    try {
        const {Dish} = getRepositories(qRunner) as {
            Dish: Repository<Dish>;
        };

        const dishId = req.params.dishId;
        const {availability} = req.body;

        const exDish = await Dish.findOne({
            where: {
                dishId,
            },
        });

        if (!exDish) return next(errorHandler(NOT_FOUND, DISH_NOT_FOUND));

        if (availability) {
            exDish.availability = {
                ...exDish.availability,
                ...availability,
            };
        }

        await Dish.save(exDish);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, DISH_PATCHED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchDishSpecialDays = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();
    try {
        const {Dish, SpecialDay} = getRepositories(qRunner) as {
            Dish: Repository<Dish>;
            SpecialDay: Repository<SpecialDay>;
        };

        const dishId = req.params.dishId;
        const {specialDays} = req.body;

        const exDish = await Dish.findOne({
            where: {
                dishId,
            },
            relations: ["specialDays"],
        });

        if (!exDish) return next(errorHandler(NOT_FOUND, DISH_NOT_FOUND));

        if (specialDays && specialDays.length > 0) {
            const existingSpecialDayIds = exDish.specialDays.map(
                (day) => day.specialDayId
            );
            const incomingSpecialDayIds = specialDays
                .filter((day: any) => day.specialDayId)
                .map((day: any) => day.specialDayId);

            const specialDaysToDelete = exDish.specialDays.filter(
                (day) => !incomingSpecialDayIds.includes(day.specialDayId)
            );

            if (specialDaysToDelete.length > 0) {
                await SpecialDay.remove(specialDaysToDelete);
            }

            for (const dayData of specialDays) {
                if (dayData.specialDayId) {
                    const updateData = dayData;

                    const existingSpecialDay = await SpecialDay.findOne({
                        where: {specialDayId: dayData.specialDayId},
                    });

                    if (existingSpecialDay) {
                        existingSpecialDay.update(updateData);
                        await SpecialDay.save(existingSpecialDay);
                    }
                } else {
                    const newSpecialDay = SpecialDay.create({
                        dish: exDish,
                        eventName: dayData.eventName,
                        startTime: dayData.startTime,
                        endTime: dayData.endTime,
                        availability: dayData.availability,
                    });

                    await SpecialDay.save(newSpecialDay);
                }
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, DISH_PATCHED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const duplicateDish = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();
    try {
        const dishId = req.params.dishId;

        await dishDuplicator(dishId, qRunner);

        await qRunner.commitTransaction();
        ResponseHelper.success(res, CREATED, DISH_CLONED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const moveToSection = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const dishId = req.params.dishId;

        const {sectionId} = req.body;

        const {Dish, Section} = getRepositories(qRunner) as {
            Dish: Repository<Dish>;
            Section: Repository<Section>;
        };

        const exDish = await Dish.findOne({
            where: {
                dishId,
            },
            relations: ["section"],
        });

        if (!exDish) return next(errorHandler(NOT_FOUND, DISH_NOT_FOUND));

        const exSection = await Section.findOneBy({
            menuSectionId: sectionId,
        });

        if (!exSection) return next(errorHandler(NOT_FOUND, SECTION_NOT_FOUND));

        exDish.section = exSection;

        await Dish.save(exDish);

        await qRunner.commitTransaction();
        ResponseHelper.success(res, OK, DISH_MOVED_TO_SECTION);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};
