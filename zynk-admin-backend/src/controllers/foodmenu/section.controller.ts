import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { Repository } from "typeorm";
import { FoodMenu } from "../../models/foodmenu/foodmenu.model";
import { Section } from "../../models/foodmenu/section.model";
import { errorHandler } from "../../utils/errorHandler";
import {
  BAD_REQUEST,
  CONFLICT,
  NOT_FOUND,
  OK,
} from "../../constants/STATUS_CODES";
import { FOOD_MENU_NOT_FOUND } from "../../constants/tenant/foodmenu/err";
import {
  SECTION_ALREADY_EXISTS,
  SECTION_NOT_FOUND,
} from "../../constants/tenant/sections/err";
import { SectionIcon } from "../../models/reference/sectionicon.model";
import { SECTION_ICON_NOT_FOUND } from "../../constants/reference/sectionicon/err";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import {
  ALL_SECTIONS_FETCHED,
  SECTION_CLONED,
  SECTION_CREATED,
  SECTION_DELETED,
  SECTION_FETCHED,
  SECTION_UPDATED,
} from "../../constants/tenant/sections/msg";
import { CustomHourSlot } from "../../models/common/customhourslot.model";
import {
  CUSTOM_HOUR_NOT_FOUND,
  DUPLICATE_DAYS_IN_CUSTOMHOURS,
} from "../../constants/tenant/company/err";
import { SpecialDay } from "../../models/common/specialday.model";
import { sectionDuplicator } from "../../helpers/dish/duplication.helper";

export const createSection = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { FoodMenu, Section, SectionIcon } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
      Section: Repository<Section>;
      SectionIcon: Repository<SectionIcon>;
    };

    const foodMenuId = req.params.foodMenuId;

    const data = req.body;

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        foodMenuId,
      },
      relations: ["sections"],
    });

    if (!exFoodMenu) return next(errorHandler(NOT_FOUND, FOOD_MENU_NOT_FOUND));

    const exSection = await Section.findOne({
      where: {
        name: data.name,
      },
      relations: ["foodMenu"],
    });

    if (exSection?.foodMenu?.foodMenuId === exFoodMenu.foodMenuId) {
      return next(errorHandler(CONFLICT, SECTION_ALREADY_EXISTS));
    }

    const exSectionIcon = await SectionIcon.findOne({
      where: {
        sectionIconId: data.sectionIconId,
      },
    });

    if (!exSectionIcon)
      return next(errorHandler(NOT_FOUND, SECTION_ICON_NOT_FOUND));

    const newSection = Section.create(data as Section);
    newSection.sectionIcon = exSectionIcon;

    await Section.save(newSection);

    if (exFoodMenu.sections.length < 1) {
      exFoodMenu.sections = [newSection];
    } else {
      exFoodMenu.sections.push(newSection);
    }

    await FoodMenu.save(exFoodMenu);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, SECTION_CREATED, newSection);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getAllSections = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { FoodMenu } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
    };

    const foodMenuId = req.params.foodMenuId;

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        foodMenuId,
      },
      relations: ["sections", "sections.sectionIcon", "sections.dishes"],
    });

    if (!exFoodMenu) return next(errorHandler(NOT_FOUND, FOOD_MENU_NOT_FOUND));

    let result = [];

    for (const section of exFoodMenu.sections) {
      const dishesCount = Array.isArray(section.dishes)
        ? section.dishes.length
        : 0;
      const { dishes, ...rest } = section as any;

      result.push({
        ...rest,
        dishes: dishesCount,
      });
    }

    ResponseHelper.success(res, OK, ALL_SECTIONS_FETCHED, result);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getSection = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { Section } = getRepositories(qRunner) as {
      Section: Repository<Section>;
    };

    const sectionId = req.params.sectionId;

    const exSection = await Section.findOne({
      where: {
        menuSectionId: sectionId,
      },
      relations: ["sectionIcon", "customSlots", "globalCustomSlots", "specialDays", "dishes"],
    });

    if (!exSection) return next(errorHandler(NOT_FOUND, SECTION_NOT_FOUND));

    ResponseHelper.success(res, OK, SECTION_FETCHED, exSection);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchSection = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { Section, SectionIcon } = getRepositories(qRunner) as {
      Section: Repository<Section>;
      SectionIcon: Repository<SectionIcon>;
    };

    const sectionId = req.params.sectionId;

    const exSection = await Section.findOne({
      where: {
        menuSectionId: sectionId,
      },
      relations: ["sectionIcon"],
    });

    if (!exSection) return next(errorHandler(NOT_FOUND, SECTION_NOT_FOUND));

    const { sectionIconId, ...updateData } = req.body;

    if (Object.keys(updateData).length > 0) {
      exSection.update(updateData);
    }

    if (
      sectionIconId &&
      sectionIconId !== exSection.sectionIcon.sectionIconId
    ) {
      const exSectionIcon = await SectionIcon.findOne({
        where: {
          sectionIconId,
        },
      });

      if (!exSectionIcon)
        return next(errorHandler(NOT_FOUND, SECTION_ICON_NOT_FOUND));

      exSection.sectionIcon = exSectionIcon;
    }

    await Section.save(exSection);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, SECTION_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const deleteSection = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { Section } = getRepositories(qRunner) as {
      Section: Repository<Section>;
    };

    const sectionId = req.params.sectionId;

    const exSection = await Section.findOne({
      where: {
        menuSectionId: sectionId,
      },
      relations: ["sectionIcon"],
    });

    if (!exSection) return next(errorHandler(NOT_FOUND, SECTION_NOT_FOUND));

    await Section.remove(exSection);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, SECTION_DELETED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchSectionCustomHours = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { Section, CustomHourSlot } = getRepositories(qRunner) as {
      Section: Repository<Section>;
      CustomHourSlot: Repository<CustomHourSlot>;
    };

    const sectionId = req.params.sectionId;
    const { customTimes, customSlots, globalCustomSlots } = req.body;

    const exSection = await Section.findOne({
      where: {
        menuSectionId: sectionId,
      },
      relations: [
        "customSlots",
        "globalCustomSlots",
        "foodMenu.branch.businessHour.customSlots",
      ],
    });

    if (!exSection) return next(errorHandler(BAD_REQUEST, SECTION_NOT_FOUND));

    if (customTimes !== undefined) {
      exSection.customTimes = customTimes;
      await Section.save(exSection);
    }

    if (exSection.globalCustomSlots && globalCustomSlots) {
      const branchCustomSlotIds =
        exSection.foodMenu.branch.businessHour.customSlots.map(
          (slot) => slot.slotId
        );

      for (const slotId of globalCustomSlots) {
        if (!branchCustomSlotIds.includes(slotId)) {
          return next(errorHandler(BAD_REQUEST, CUSTOM_HOUR_NOT_FOUND));
        }
      }

      exSection.globalCustomSlots = exSection.globalCustomSlots.filter((slot) =>
        globalCustomSlots.includes(slot.slotId)
      );

      // Add new slots
      for (const slotId of globalCustomSlots) {
        if (
          !exSection.globalCustomSlots.find((slot) => slot.slotId === slotId)
        ) {
          const customSlot = await CustomHourSlot.findOneBy({ slotId });
          if (customSlot) {
            exSection.globalCustomSlots.push(customSlot);
          }
        }
      }

      await Section.save(exSection);
    }

    if (exSection.customTimes && customSlots) {
      // Check for duplicate active days across both existing and incoming custom slots
      const customHourDays = new Map<string, boolean>();

      exSection.customSlots.forEach((slot: any) => {
        if (slot.days && slot.isActive) {
          Object.entries(slot.days).forEach(([day, isActive]) => {
            if (isActive && slot.isActive) {
              customHourDays.set(day, true);
            }
          });
        }
      });

      customSlots.forEach((slot: any) => {
        if (slot.days && slot.isActive) {
          Object.entries(slot.days).forEach(([day, isActive]) => {
            if (isActive && customHourDays.get(day)) {
              return next(
                errorHandler(CONFLICT, DUPLICATE_DAYS_IN_CUSTOMHOURS)
              );
            }
            if (isActive && slot.isActive) {
              customHourDays.set(day, true);
            }
          });
        }
      });

      const existingCustomSlotIds = exSection.customSlots.map(
        (slot) => slot.slotId
      );
      const incomingCustomSlotIds = customSlots
        .filter((slot: any) => slot.slotId)
        .map((slot: any) => slot.slotId);

      const customSlotsToDelete = exSection.customSlots.filter(
        (slot) => !incomingCustomSlotIds.includes(slot.slotId)
      );

      if (customSlotsToDelete.length > 0) {
        await CustomHourSlot.remove(customSlotsToDelete);
      }

      for (const slotData of customSlots) {
        if (slotData.slotId) {
          const updateData: Partial<CustomHourSlot> = {};

          if ("name" in slotData) updateData.name = slotData.name;
          if ("days" in slotData) updateData.days = slotData.days;
          if ("is24Hours" in slotData)
            updateData.is24Hours = slotData.is24Hours;
          if ("firstSeating" in slotData)
            updateData.firstSeating = slotData.firstSeating;
          if ("lastSeating" in slotData)
            updateData.lastSeating = slotData.lastSeating;
          if ("isActive" in slotData) updateData.isActive = slotData.isActive;

          await CustomHourSlot.update({ slotId: slotData.slotId }, updateData);
        } else {
          const newCustomSlot = CustomHourSlot.create({
            section: exSection,
            name: slotData.name,
            days: slotData.days,
            is24Hours: slotData.is24Hours,
            firstSeating: slotData.firstSeating,
            lastSeating: slotData.lastSeating,
            isActive: slotData.isActive,
          });

          await CustomHourSlot.save(newCustomSlot);
        }
      }
    }

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, SECTION_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchSectionAvailability = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { Section } = getRepositories(qRunner) as {
      Section: Repository<Section>;
    };

    const sectionId = req.params.sectionId;

    const exSection = await Section.findOne({
      where: {
        menuSectionId: sectionId,
      },
    });

    if (!exSection) return next(errorHandler(NOT_FOUND, SECTION_NOT_FOUND));

    const { availability } = req.body;

    exSection.availability = {
      ...exSection.availability,
      ...availability,
    };

    await Section.save(exSection);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, SECTION_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};
export const patchSectionSpecialDays = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { Section, SpecialDay } = getRepositories(qRunner) as {
      Section: Repository<Section>;
      SpecialDay: Repository<SpecialDay>;
    };

    const sectionId = req.params.sectionId;
    const { specialDays } = req.body;

    const exSection = await Section.findOne({
      where: {
        menuSectionId: sectionId,
      },
      relations: ["specialDays"],
    });

    if (!exSection) return next(errorHandler(NOT_FOUND, SECTION_NOT_FOUND));

    if (specialDays && specialDays.length > 0) {
      const existingSpecialDayIds = exSection.specialDays.map(
        (day) => day.specialDayId
      );
      const incomingSpecialDayIds = specialDays
        .filter((day: any) => day.specialDayId)
        .map((day: any) => day.specialDayId);

      const specialDaysToDelete = exSection.specialDays.filter(
        (day) => !incomingSpecialDayIds.includes(day.specialDayId)
      );

      if (specialDaysToDelete.length > 0) {
        await SpecialDay.remove(specialDaysToDelete);
      }

      for (const dayData of specialDays) {
        if (dayData.specialDayId) {
          const updateData = dayData;

          const existingSpecialDay = await SpecialDay.findOne({
            where: { specialDayId: dayData.specialDayId },
          });

          if (existingSpecialDay) {
            existingSpecialDay.update(updateData);
            await SpecialDay.save(existingSpecialDay);
          }
        } else {
          const newSpecialDay = SpecialDay.create({
            section: exSection,
            eventName: dayData.eventName,
            startTime: dayData.startTime,
            endTime: dayData.endTime,
            availability: dayData.availability,
          });

          await SpecialDay.save(newSpecialDay);
        }
      }
    }

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, SECTION_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const duplicateSection = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const sectionId = req.params.sectionId;
    await sectionDuplicator(sectionId, qRunner);
    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, SECTION_CLONED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};
