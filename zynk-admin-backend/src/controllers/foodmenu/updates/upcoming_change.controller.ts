import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../../helpers/system/RepositoryHelper.helper";
import {Repository} from "typeorm";
import {UpcomingChange} from "../../../models/foodmenu/updates/upcoming_change.model";
import {ResponseHelper} from "../../../helpers/system/ResponseHelper.helper";
import {NOT_FOUND, OK} from "../../../constants/STATUS_CODES";
import {Dish} from "../../../models/foodmenu/dish.model";
import {DishUpcomingChange} from "../../../models/foodmenu/updates/dish_upcoming_change.model";
import {
  ALLUPCOMINGCHANGES_FETCHED,
  UPCOMING_CHANGE_DELETED,
  UPCOMING_CHANGE_NOTFOUND,
  UPCOMING_CHANGE_PATCHED,
} from "../../../constants/tenant/foodmenu/msg";
import {errorHandler} from "../../../utils/errorHandler";
import {arraysEqual} from "../../../helpers/dish/upcomingchange.helper";
import { FoodMenu } from "../../../models/foodmenu/foodmenu.model";
import { FOOD_MENU_NOT_FOUND } from "../../../constants/tenant/foodmenu/err";

export const getUpcomingChanges = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const {UpcomingChange, FoodMenu} = getRepositories(qRunner) as {
            UpcomingChange: Repository<UpcomingChange>;
            FoodMenu: Repository<FoodMenu>
        };

        const foodMenuId = req.params.foodMenuId;

        const exFoodMenu = await FoodMenu.findOne({
            where: {
                foodMenuId
            }
        })

        if(!exFoodMenu)
            return next(errorHandler(NOT_FOUND, FOOD_MENU_NOT_FOUND))

        const upcomingChanges = await UpcomingChange.find({
            where: {
                foodMenuId: foodMenuId
            },
            relations: ["dishChanges", "dishChanges.dish"],
            order: {
                activeFrom: "ASC",
                dishChanges: {createdAt: "ASC"},
            },
        });

        const transformedChanges = upcomingChanges.map((change) => ({
            changeId: change.changeId,
            activeFrom: change.activeFrom,
            isActive: change.isActive,
            dishes: change.dishChanges.map((dishChange) => ({
                dishChangeId: dishChange.dishChangeId,
                dishId: dishChange.dishId,
                originalName: dishChange.dish.name,
                originalPrice: parseFloat(dishChange.dish.price.toString()),
                originalDietaryInfo: dishChange.dish.dietaryInfo,
                newName: dishChange.newName,
                newPrice: dishChange.newPrice,
                newDietaryInfo: dishChange.newDietaryInfo,
            })),
        }));

        ResponseHelper.success(
            res,
            OK,
            ALLUPCOMINGCHANGES_FETCHED,
            transformedChanges
        );
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchUpcomingChange = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {Dish, UpcomingChange, DishUpcomingChange, FoodMenu} = getRepositories(
            qRunner
        ) as {
            Dish: Repository<Dish>;
            UpcomingChange: Repository<UpcomingChange>;
            DishUpcomingChange: Repository<DishUpcomingChange>;
            FoodMenu: Repository<FoodMenu>
        };

        const foodMenuId = req.params.foodMenuId;

        const exFoodMenu = await FoodMenu.findOne({
            where: {
                foodMenuId
            }
        })

        if(!exFoodMenu)
            return next(errorHandler(NOT_FOUND, FOOD_MENU_NOT_FOUND))

        const upcomingChangeGroups = req.body;

        let totalChangesProcessed = 0;

        for (const changeGroup of upcomingChangeGroups) {
            const {changeId, activeFrom, dishes, isActive} = changeGroup;

            let upcomingChange: UpcomingChange | null;

            if (changeId) {
                upcomingChange = await UpcomingChange.findOne({
                    where: {changeId: changeId, foodMenuId},
                    relations: ["dishChanges"],
                });

                if (!upcomingChange) {
                    return next(errorHandler(NOT_FOUND, UPCOMING_CHANGE_PATCHED));
                }

                if (activeFrom !== undefined) {
                    const existingDate = new Date(upcomingChange.activeFrom);
                    const newDate = new Date(activeFrom);

                    if (existingDate.getTime() !== newDate.getTime()) {
                        upcomingChange.activeFrom = newDate;
                        await UpcomingChange.save(upcomingChange);
                    }
                }

                if (upcomingChange.isActive !== isActive) {
                    upcomingChange.isActive = isActive;
                    await UpcomingChange.save(upcomingChange);
                }
            } else {
                upcomingChange = UpcomingChange.create({
                    foodMenuId,
                    activeFrom: new Date(activeFrom),
                    isActive: isActive,
                });
                upcomingChange = await UpcomingChange.save(upcomingChange);
            }

            const existingDishChanges = await DishUpcomingChange.find({
                where: {changeId: upcomingChange.changeId},
            });

            const processedDishChangeIds = new Set<string>();

            if (dishes !== undefined) {
                for (const dishChange of dishes) {
                    const {dishChangeId, dishId, newName, price, dietaryInfo} =
                        dishChange;

                    const originalDish = await Dish.findOne({
                        where: {dishId: dishId},
                    });

                    if (!originalDish) {
                        continue;
                    }

                    const nameChanged = newName !== originalDish.name;
                    const priceChanged =
                        price !== parseFloat(originalDish.price.toString());
                    const dietaryInfoChanged = !arraysEqual(
                        dietaryInfo,
                        originalDish.dietaryInfo
                    );

                    if (!nameChanged && !priceChanged && !dietaryInfoChanged) {
                        if (dishChangeId) {
                            processedDishChangeIds.add(dishChangeId);
                        }
                        continue;
                    }

                    if (dishChangeId) {
                        const existingDishChange = existingDishChanges.find(
                            (dc) => dc.dishChangeId === dishChangeId
                        );

                        if (existingDishChange) {
                            await DishUpcomingChange.update(
                                {dishChangeId: dishChangeId},
                                {
                                    newName: nameChanged ? newName : null,
                                    newPrice: priceChanged ? price : null,
                                    newDietaryInfo: dietaryInfoChanged ? dietaryInfo : null,
                                }
                            );
                            processedDishChangeIds.add(dishChangeId);
                            totalChangesProcessed++;
                        }
                    } else {
                        const newDishChange = DishUpcomingChange.create({
                            changeId: upcomingChange.changeId,
                            dishId: dishId,
                            newName: nameChanged ? newName : null,
                            newPrice: priceChanged ? price : null,
                            newDietaryInfo: dietaryInfoChanged ? dietaryInfo : null,
                        });

                        const savedDishChange = await DishUpcomingChange.save(
                            newDishChange
                        );
                        processedDishChangeIds.add(savedDishChange.dishChangeId);
                        totalChangesProcessed++;
                    }
                }

                const dishChangesToRemove = existingDishChanges.filter(
                    (dc) => !processedDishChangeIds.has(dc.dishChangeId)
                );

                if (dishChangesToRemove.length > 0) {
                    await DishUpcomingChange.remove(dishChangesToRemove);
                }

                const remainingDishChanges = await DishUpcomingChange.count({
                    where: {changeId: upcomingChange.changeId},
                });

                if (remainingDishChanges === 0) {
                    await UpcomingChange.update(
                        {changeId: upcomingChange.changeId},
                        {isActive: false}
                    );
                }
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(
            res,
            OK,
            UPCOMING_CHANGE_PATCHED,
            totalChangesProcessed
        );
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteUpcomingChange = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {UpcomingChange} = getRepositories(qRunner) as {
            UpcomingChange: Repository<UpcomingChange>;
        };

        const changeId = req.params.changeId;

        const exUpcomingChange = await UpcomingChange.findOneBy({
            changeId,
        });

        if (!exUpcomingChange)
            return next(errorHandler(NOT_FOUND, UPCOMING_CHANGE_NOTFOUND));

        await UpcomingChange.remove(exUpcomingChange);

        await qRunner.commitTransaction();
        ResponseHelper.success(res, OK, UPCOMING_CHANGE_DELETED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};
