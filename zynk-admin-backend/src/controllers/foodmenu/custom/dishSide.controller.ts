import {NextFunction, Request, Response} from "express";
import {ResponseHelper} from "../../../helpers/system/ResponseHelper.helper";
import {NOT_FOUND, OK} from "../../../constants/STATUS_CODES";
import {DISH_SIDE_FETCHED, DISH_SIDES_FETCHED} from "../../../constants/tenant/foodmenu/msg";
import {getRepositories} from "../../../helpers/system/RepositoryHelper.helper";
import {Repository} from "typeorm";
import {FoodMenu} from "../../../models/foodmenu/foodmenu.model";
import {DishSide} from "../../../models/foodmenu/custom/dish_side.model";
import {errorHandler} from "../../../utils/errorHandler";
import {DISH_SIDE_NOT_FOUND, FOOD_MENU_NOT_FOUND,} from "../../../constants/tenant/foodmenu/err";

export const viewAllDishSides = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const foodMenuId = req.params.foodMenuId;

        const {FoodMenu, DishSide} = getRepositories(qRunner) as {
            FoodMenu: Repository<FoodMenu>;
            DishSide: Repository<DishSide>;
        };

        const exFoodMenu = await FoodMenu.findOne({
            where: {foodMenuId: foodMenuId},
        });

        if (!exFoodMenu) {
            return next(errorHandler(NOT_FOUND, FOOD_MENU_NOT_FOUND));
        }

        const dishSides = await DishSide.find({
            order: {createdAt: "DESC"},
            relations: ["dish.section.foodMenu"],
        });

        const dishSidesWithFoodMenu = dishSides.filter(
            (dishSide) => dishSide.dish.section.foodMenu.foodMenuId === foodMenuId
        );

        const result = dishSidesWithFoodMenu.map((dishSide) => ({
            dishSideId: dishSide.dishSideId,
            createdAt: dishSide.createdAt,
            updatedAt: dishSide.updatedAt,
            deletedAt: dishSide.deletedAt,
            dish: {
                dishId: dishSide.dish.dishId,
                pictureUrl: dishSide.dish.pictureUrl,
                name: dishSide.dish.name,
                description: dishSide.dish.description,
                price: dishSide.dish.price,
                dietaryInfo: dishSide.dish.dietaryInfo,
                tags: dishSide.dish.tags,
                isCustomizable: dishSide.dish.isCustomizable,
                availability: dishSide.dish.availability,
                visibility: dishSide.dish.visibility,
                createdAt: dishSide.dish.createdAt,
                updatedAt: dishSide.dish.updatedAt,
                deletedAt: dishSide.dish.deletedAt,
                customTimes: dishSide.dish.customTimes,
                specialRequests: dishSide.dish.specialRequests,
            },
        }));

        ResponseHelper.success(res, OK, DISH_SIDES_FETCHED, result);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const viewDishSide = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const dishSideId = req.params.dishSideId;

        const {DishSide} = getRepositories(qRunner) as {
            DishSide: Repository<DishSide>;
        };

        const dishSide = await DishSide.findOne({
            where: {dishSideId},
            relations: ["dish"],
        });

        if (!dishSide) {
            return next(errorHandler(NOT_FOUND, DISH_SIDE_NOT_FOUND));
        }

        const result = {
            dishSideId: dishSide.dishSideId,
            createdAt: dishSide.createdAt,
            updatedAt: dishSide.updatedAt,
            deletedAt: dishSide.deletedAt,
            dish: {
                dishId: dishSide.dish.dishId,
                pictureUrl: dishSide.dish.pictureUrl,
                name: dishSide.dish.name,
                description: dishSide.dish.description,
                price: dishSide.dish.price,
                dietaryInfo: dishSide.dish.dietaryInfo,
                tags: dishSide.dish.tags,
                isCustomizable: dishSide.dish.isCustomizable,
                availability: dishSide.dish.availability,
                visibility: dishSide.dish.visibility,
                createdAt: dishSide.dish.createdAt,
                updatedAt: dishSide.dish.updatedAt,
                deletedAt: dishSide.dish.deletedAt,
                customTimes: dishSide.dish.customTimes,
                specialRequests: dishSide.dish.specialRequests,
            },
        };

        ResponseHelper.success(res, OK, DISH_SIDE_FETCHED, result);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};
