import {NextFunction, Request, Response} from "express";
import {ResponseHelper} from "../../../helpers/system/ResponseHelper.helper";
import {NOT_FOUND, OK} from "../../../constants/STATUS_CODES";
import {DISH_DESSERT_FETCHED, DISH_DESSERTS_FETCHED} from "../../../constants/tenant/foodmenu/msg";
import {getRepositories} from "../../../helpers/system/RepositoryHelper.helper";
import {Repository} from "typeorm";
import {FoodMenu} from "../../../models/foodmenu/foodmenu.model";
import {errorHandler} from "../../../utils/errorHandler";
import {DISH_DES_NOT_FOUND, FOOD_MENU_NOT_FOUND,} from "../../../constants/tenant/foodmenu/err";
import {DishDessert} from "../../../models/foodmenu/custom/dish_dessert.model";

export const viewAllDishDes = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const foodMenuId = req.params.foodMenuId;

        const {FoodMenu, DishDessert} = getRepositories(qRunner) as {
            FoodMenu: Repository<FoodMenu>;
            DishDessert: Repository<DishDessert>;
        };

        const exFoodMenu = await FoodMenu.findOne({
            where: {foodMenuId: foodMenuId},
        });

        if (!exFoodMenu) {
            return next(errorHandler(NOT_FOUND, FOOD_MENU_NOT_FOUND));
        }

        const dishDesserts = await DishDessert.find({
            order: {createdAt: "DESC"},
            relations: ["dish.section.foodMenu"],
        });

        const dishDesWithFoodMenu = dishDesserts.filter(
            (dishDes) => dishDes.dish.section.foodMenu.foodMenuId === foodMenuId
        );

        const result = dishDesWithFoodMenu.map((dishDes) => ({
            dishDessertId: dishDes.dishDessertId,
            createdAt: dishDes.createdAt,
            updatedAt: dishDes.updatedAt,
            deletedAt: dishDes.deletedAt,
            dish: {
                dishId: dishDes.dish.dishId,
                pictureUrl: dishDes.dish.pictureUrl,
                name: dishDes.dish.name,
                description: dishDes.dish.description,
                price: dishDes.dish.price,
                dietaryInfo: dishDes.dish.dietaryInfo,
                tags: dishDes.dish.tags,
                isCustomizable: dishDes.dish.isCustomizable,
                availability: dishDes.dish.availability,
                visibility: dishDes.dish.visibility,
                createdAt: dishDes.dish.createdAt,
                updatedAt: dishDes.dish.updatedAt,
                deletedAt: dishDes.dish.deletedAt,
                customTimes: dishDes.dish.customTimes,
                specialRequests: dishDes.dish.specialRequests,
            },
        }));

        ResponseHelper.success(res, OK, DISH_DESSERTS_FETCHED, result);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const viewDishDes = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const dishDessertId = req.params.dishDessertId;

        const {DishDessert} = getRepositories(qRunner) as {
            DishDessert: Repository<DishDessert>;
        };

        const dishDes = await DishDessert.findOne({
            where: {dishDessertId},
            relations: ["dish"],
        });

        if (!dishDes) {
            return next(errorHandler(NOT_FOUND, DISH_DES_NOT_FOUND));
        }

        const result = {
            dishDessertId: dishDes.dishDessertId,
            createdAt: dishDes.createdAt,
            updatedAt: dishDes.updatedAt,
            deletedAt: dishDes.deletedAt,
            dish: {
                dishId: dishDes.dish.dishId,
                pictureUrl: dishDes.dish.pictureUrl,
                name: dishDes.dish.name,
                description: dishDes.dish.description,
                price: dishDes.dish.price,
                dietaryInfo: dishDes.dish.dietaryInfo,
                tags: dishDes.dish.tags,
                isCustomizable: dishDes.dish.isCustomizable,
                availability: dishDes.dish.availability,
                visibility: dishDes.dish.visibility,
                createdAt: dishDes.dish.createdAt,
                updatedAt: dishDes.dish.updatedAt,
                deletedAt: dishDes.dish.deletedAt,
                customTimes: dishDes.dish.customTimes,
                specialRequests: dishDes.dish.specialRequests,
            },
        };

        ResponseHelper.success(res, OK, DISH_DESSERT_FETCHED, result);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};
