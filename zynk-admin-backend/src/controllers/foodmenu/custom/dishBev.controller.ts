import {NextFunction, Request, Response} from "express";
import {ResponseHelper} from "../../../helpers/system/ResponseHelper.helper";
import {NOT_FOUND, OK} from "../../../constants/STATUS_CODES";
import {DISH_BEV_FETCHED, DISH_BEVS_FETCHED} from "../../../constants/tenant/foodmenu/msg";
import {getRepositories} from "../../../helpers/system/RepositoryHelper.helper";
import {Repository} from "typeorm";
import {FoodMenu} from "../../../models/foodmenu/foodmenu.model";
import {errorHandler} from "../../../utils/errorHandler";
import {DISH_BEV_NOT_FOUND, FOOD_MENU_NOT_FOUND,} from "../../../constants/tenant/foodmenu/err";
import {DishBeverage} from "../../../models/foodmenu/custom/dish_bev.model";

export const viewAllDishBevs = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const foodMenuId = req.params.foodMenuId;

        const {FoodMenu, DishBeverage} = getRepositories(qRunner) as {
            FoodMenu: Repository<FoodMenu>;
            DishBeverage: Repository<DishBeverage>;
        };

        const exFoodMenu = await FoodMenu.findOne({
            where: {foodMenuId: foodMenuId},
        });

        if (!exFoodMenu) {
            return next(errorHandler(NOT_FOUND, FOOD_MENU_NOT_FOUND));
        }

        const dishBeverages = await DishBeverage.find({
            order: {createdAt: "DESC"},
            relations: ["dish.section.foodMenu"],
        });

        const dishBevsWithFoodMenu = dishBeverages.filter(
            (dishBev) => dishBev.dish.section.foodMenu.foodMenuId === foodMenuId
        );

        const result = dishBevsWithFoodMenu.map((dishBev) => ({
            dishBevId: dishBev.dishBevId,
            createdAt: dishBev.createdAt,
            updatedAt: dishBev.updatedAt,
            deletedAt: dishBev.deletedAt,
            dish: {
                dishId: dishBev.dish.dishId,
                pictureUrl: dishBev.dish.pictureUrl,
                name: dishBev.dish.name,
                description: dishBev.dish.description,
                price: dishBev.dish.price,
                dietaryInfo: dishBev.dish.dietaryInfo,
                tags: dishBev.dish.tags,
                isCustomizable: dishBev.dish.isCustomizable,
                availability: dishBev.dish.availability,
                visibility: dishBev.dish.visibility,
                createdAt: dishBev.dish.createdAt,
                updatedAt: dishBev.dish.updatedAt,
                deletedAt: dishBev.dish.deletedAt,
                customTimes: dishBev.dish.customTimes,
                specialRequests: dishBev.dish.specialRequests,
            },
        }));

        ResponseHelper.success(res, OK, DISH_BEVS_FETCHED, result);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const viewDishBev = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const dishBevId = req.params.dishBevId;

        const {DishBeverage} = getRepositories(qRunner) as {
            DishBeverage: Repository<DishBeverage>;
        };

        const dishBev = await DishBeverage.findOne({
            where: {dishBevId},
            relations: ["dish"],
        });

        if (!dishBev) {
            return next(errorHandler(NOT_FOUND, DISH_BEV_NOT_FOUND));
        }

        const result = {
            dishBevId: dishBev.dishBevId,
            createdAt: dishBev.createdAt,
            updatedAt: dishBev.updatedAt,
            deletedAt: dishBev.deletedAt,
            dish: {
                dishId: dishBev.dish.dishId,
                pictureUrl: dishBev.dish.pictureUrl,
                name: dishBev.dish.name,
                description: dishBev.dish.description,
                price: dishBev.dish.price,
                dietaryInfo: dishBev.dish.dietaryInfo,
                tags: dishBev.dish.tags,
                isCustomizable: dishBev.dish.isCustomizable,
                availability: dishBev.dish.availability,
                visibility: dishBev.dish.visibility,
                createdAt: dishBev.dish.createdAt,
                updatedAt: dishBev.dish.updatedAt,
                deletedAt: dishBev.dish.deletedAt,
                customTimes: dishBev.dish.customTimes,
                specialRequests: dishBev.dish.specialRequests,
            },
        };

        ResponseHelper.success(res, OK, DISH_BEV_FETCHED, result);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};
