import { NextFunction, Request, Response } from "express";
import {
  PutObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from "@aws-sdk/client-s3";
import { Readable } from "stream";
import path from "path";
import {
  BAD_REQUEST,
  CREATED,
  OK,
  NOT_FOUND,
  FORBIDDEN,
} from "../../constants/STATUS_CODES";
import s3Client from "../../config/s3.config";
import { errorHandler } from "../../utils/errorHandler";
import { ACCESS_DENIED_ATTACHMENT, FILE_NOT_FOUND, INVALID_S3_URL, S3_URLREQ } from "../../constants/common/err";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import { ATTACHMENT_DELETED, ATTACHMENT_UPLOADED } from "../../constants/common/msg";

export const attachmentUpload = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.file) {
      return next(errorHandler(BAD_REQUEST, FILE_NOT_FOUND));
    }

    const file = req.file;
    const folder = req.tenantName || "assets";
    const key = `${folder}/${Date.now()}-${file.originalname}`;

    await uploadFile(
      process.env.S3BUC_BUCKET_NAME!,
      key,
      file.buffer,
      file.mimetype
    );
    const fileUrl = getFileUrl(process.env.S3BUC_BUCKET_NAME!, key);

    ResponseHelper.success(res, CREATED, ATTACHMENT_UPLOADED, fileUrl);
  } catch (error) {
    return next(errorHandler(BAD_REQUEST, (error as Error).message));
  }
};

export const deleteAttachment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const {url} = req.validatedQuery;
    if (!url) {
      return next(errorHandler(BAD_REQUEST, S3_URLREQ));
    }

    const key = url.split(
      `https://${process.env.S3BUC_BUCKET_NAME}.s3.${process.env.S3BUCKET_REGION}.amazonaws.com/`
    )[1];
    if (!key) {
      return next(errorHandler(BAD_REQUEST, INVALID_S3_URL));
    }

    const folder = req.tenantName || "assets";
    if (!key.startsWith(`${folder}/`)) {
      return next(
        errorHandler(
          FORBIDDEN,
          ACCESS_DENIED_ATTACHMENT
        )
      );
    }

    await deleteFile(process.env.S3BUC_BUCKET_NAME!, key);
    ResponseHelper.success(res, OK, ATTACHMENT_DELETED);
  } catch (error) {
    return next(errorHandler(BAD_REQUEST, (error as Error).message));
  }
};

export const viewAttachment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const {url} = req.validatedQuery;
    if (!url) {
      return next(errorHandler(BAD_REQUEST, S3_URLREQ));
    }

    const key = url.split(
      `https://${process.env.S3BUC_BUCKET_NAME}.s3.${process.env.S3BUCKET_REGION}.amazonaws.com/`
    )[1];
    if (!key) {
      return next(errorHandler(BAD_REQUEST, INVALID_S3_URL));
    }

    const folder = req.tenantName || "assets";
    if (!key.startsWith(`${folder}/`)) {
      return next(
        errorHandler(
          FORBIDDEN,
          ACCESS_DENIED_ATTACHMENT
        )
      );
    }

    const { stream, contentType } = await getFileStream(
      process.env.S3BUC_BUCKET_NAME!,
      key
    );
    res.setHeader("Content-Type", contentType);
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="${path.basename(key)}"`
    );
    stream.pipe(res);
  } catch (error) {
    return next(errorHandler(NOT_FOUND, FILE_NOT_FOUND));
  }
};

export const getFileUrl = (bucketName: string, key: string): string => {
  return `https://${bucketName}.s3.${process.env
    .S3BUCKET_REGION!}.amazonaws.com/${key}`;
};

export const getFileStream = async (
  bucketName: string,
  key: string
): Promise<{ stream: Readable; contentType: string }> => {
  const command = new GetObjectCommand({
    Bucket: bucketName,
    Key: key,
  });
  const { Body, ContentType } = await s3Client.send(command);
  return {
    stream: Body as Readable,
    contentType: ContentType || "application/octet-stream",
  };
};

export const uploadFile = async (
  bucketName: string,
  key: string,
  fileBuffer: Buffer | Readable,
  contentType?: string
): Promise<string> => {
  const command = new PutObjectCommand({
    Bucket: bucketName,
    Key: key,
    Body: fileBuffer,
    ContentType: contentType || "application/octet-stream",
  });

  await s3Client.send(command);
  return key;
};

export const deleteFile = async (
  bucketName: string,
  key: string
): Promise<void> => {
  const command = new DeleteObjectCommand({
    Bucket: bucketName,
    Key: key,
  });

  await s3Client.send(command);
};
