import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Repository } from "typeorm";
import { BufferTime } from "../../../models/tenantSettings/tableReservation/management/bufferTime.model";
import { errorHandler } from "../../../utils/errorHandler";
import { NOT_FOUND, OK } from "../../../constants/STATUS_CODES";
import { BUFFERTIME_NOTFOUND } from "../../../constants/tenant/settings/tableReserv/err";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";
import { BUFFERTIME_FETCHED, BUFFERTIME_UPDATED } from "../../../constants/tenant/settings/tableReserv/msg";

export const viewBufferTime = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const branchId = req.branchId;

    const { BufferTime } = getRepositories(qRunner) as {
      BufferTime: Repository<BufferTime>;
    };

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId,
      },
    });

    if (!exBufferTime)
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));

    ResponseHelper.success(res, OK, BUFFERTIME_FETCHED, exBufferTime);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchBufferTime = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const branchId = req.branchId;

    const { BufferTime } = getRepositories(qRunner) as {
      BufferTime: Repository<BufferTime>;
    };

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId,
      },
    });

    if (!exBufferTime)
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));

    const { time } = req.body;

    exBufferTime.time = time;

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, BUFFERTIME_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};
