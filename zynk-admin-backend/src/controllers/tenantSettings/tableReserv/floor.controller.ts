import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Between, In, Repository } from "typeorm";
import { Floor } from "../../../models/tenantSettings/tableReservation/management/floor.model";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";
import { NOT_FOUND, OK } from "../../../constants/STATUS_CODES";
import {
  ALL_FLOORS_FETCHED,
  FLOORS_UPDATED,
} from "../../../constants/tenant/settings/tableReserv/msg";
import { patchFloorHelper } from "../../../helpers/settings/tableReserv/floor.helper";
import {
  ReservationStatus,
  TableReservation,
} from "../../../models/dineIn/tableReservation.model";
import {
  canTableTakeOrder,
  getTableDisplayStatus,
} from "../../../helpers/dineIn/tableDineIn.helper";
import {
  TableCleaningStatus,
  TableStatus,
} from "../../../models/tenantSettings/tableReservation/management/table.model";
import { BufferTime } from "../../../models/tenantSettings/tableReservation/management/bufferTime.model";
import { errorHandler } from "../../../utils/errorHandler";
import { BUFFERTIME_NOTFOUND } from "../../../constants/tenant/settings/tableReserv/err";

// export const viewAllFloors = async (
//   req: Request,
//   res: Response,
//   next: NextFunction
// ) => {
//   const qRunner = req.queryRunner;

//   await qRunner.connect();

//   try {
//     const { Floor, TableReservation, BufferTime } = getRepositories(
//       qRunner
//     ) as {
//       Floor: Repository<Floor>;
//       TableReservation: Repository<TableReservation>;
//       BufferTime: Repository<BufferTime>;
//     };

//     const branchId = req.branchId;

//     const exBufferTime = await BufferTime.findOne({
//       where: {
//         bufferTimeId: branchId,
//       },
//     });

//     if (!exBufferTime)
//       return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));

//     const allFloors = await Floor.find({
//       where: {
//         branch: {
//           branchId,
//         },
//       },
//       relations: ["tables", "tableCombinations"],
//       order: {
//         createdAt: "ASC",
//         tables: {
//           createdAt: "ASC",
//         },
//         tableCombinations: {
//           createdAt: "ASC",
//         },
//       },
//     });

//     const currentTime = new Date();
//     const endOfDay = new Date();
//     endOfDay.setHours(23, 59, 59, 999);

//     const allTableIds = allFloors.flatMap((floor) =>
//       floor.tables.map((table) => table.tableId)
//     );

//     // Get all reservations for today (both confirmed and pending)
//     const todaysReservations = await TableReservation.find({
//       where: {
//         table: {
//           tableId: In(allTableIds),
//         },
//         status: In([ReservationStatus.CONFIRMED, ReservationStatus.PENDING]),
//         reservationTime: Between(currentTime, endOfDay),
//       },
//       relations: ["table"],
//       order: {
//         reservationTime: "ASC",
//       },
//     });

//     // Also get confirmed reservations that might be currently active (within last 30 minutes)
//     const thirtyMinutesAgo = new Date(currentTime.getTime() - exBufferTime.time * 60 * 1000);
//     const activeReservations = await TableReservation.find({
//       where: {
//         table: {
//           tableId: In(allTableIds),
//         },
//         status: ReservationStatus.CONFIRMED,
//         reservationTime: Between(thirtyMinutesAgo, currentTime),
//       },
//       relations: ["table"],
//     });

//     // Combine all reservations
//     const allRelevantReservations = [
//       ...todaysReservations,
//       ...activeReservations,
//     ];

//     const tableReservationsMap = new Map<string, any[]>();
//     allRelevantReservations.forEach((reservation) => {
//       const tableId = reservation.table.tableId;
//       if (!tableReservationsMap.has(tableId)) {
//         tableReservationsMap.set(tableId, []);
//       }
//       const existingReservation = tableReservationsMap
//         .get(tableId)!
//         .find((r) => r.reservationId === reservation.reservationId);
//       if (!existingReservation) {
//         tableReservationsMap.get(tableId)!.push({
//           reservationId: reservation.reservationId,
//           customerName: reservation.customerName,
//           phoneNumber: reservation.phoneNumber,
//           numberOfGuests: reservation.numberOfGuests,
//           reservationTime: reservation.reservationTime,
//           status: reservation.status,
//           specialNotes: reservation.specialNotes,
//           durationMinutes: reservation.durationMinutes,
//         });
//       }
//     });

//     const enhancedFloors = allFloors.map((floor) => ({
//       ...floor,
//       tables: floor.tables.map((table) => {
//         const availableSeats = table.maxSeats - table.bookedSeats;
//         const reservations = tableReservationsMap.get(table.tableId) || [];

//         // Find next reservation (within next 2 hours)
//         const nextTwoHours = new Date(
//           currentTime.getTime() + 2 * 60 * 60 * 1000
//         );
//         const nextReservation = reservations.find(
//           (r) => new Date(r.reservationTime) <= nextTwoHours
//         );

//         // Check if table is currently reserved (within 30 min window)
//         const thirtyMinBuffer = exBufferTime.time * 60 * 1000; // 30 minutes in milliseconds
//         const currentReservation = reservations.find((r) => {
//           const resTime = new Date(r.reservationTime);
//           const timeDiff = Math.abs(currentTime.getTime() - resTime.getTime());
//           return (
//             timeDiff <= thirtyMinBuffer &&
//             r.status === ReservationStatus.CONFIRMED
//           );
//         });

//         // Determine dynamic status - override stored status if there's an active reservation
//         let dynamicStatus = table.status;
//         if (currentReservation) {
//           dynamicStatus = TableStatus.RESERVED;
//         } else if (table.bookedSeats === 0) {
//           dynamicStatus = TableStatus.AVAILABLE;
//         } else if (table.bookedSeats >= table.maxSeats) {
//           dynamicStatus = TableStatus.OCCUPIED;
//         } else if (table.bookedSeats > 0) {
//           dynamicStatus = TableStatus.OCCUPIED;
//         }

//         // Calculate reserved seats for the response
//         let reservedSeats = 0;
//         if (currentReservation) {
//           reservedSeats = currentReservation.numberOfGuests;
//         }

//         return {
//           ...table,
//           status: dynamicStatus, // Use dynamic status instead of stored status
//           availableSeats,
//           reservedSeats, // Add reserved seats info

//           hasReservations: reservations.length > 0,
//           reservationCount: reservations.length,
//           nextReservation,
//           currentReservation,

//           todaysReservations: reservations,
//           displayStatus: getTableDisplayStatus(
//             table,
//             currentReservation,
//             availableSeats
//           ),

//           // status indicators
//           isFullyOccupied: table.bookedSeats >= table.maxSeats,
//           isPartiallyOccupied:
//             table.bookedSeats > 0 && table.bookedSeats < table.maxSeats,
//           isEmpty: table.bookedSeats === 0,
//           needsCleaning: table.cleaning !== TableCleaningStatus.CLEAN,

//           lastUpdated: table.updatedAt,

//           // Availability summary
//           availabilitySummary: {
//             status: dynamicStatus,
//             bookedSeats: table.bookedSeats,
//             availableSeats: availableSeats - reservedSeats,
//             maxSeats: table.maxSeats,
//             cleaningStatus: table.cleaning,
//             isEnabled: table.enabled,
//           },
//         };
//       }),
//     }));

//     ResponseHelper.success(res, OK, ALL_FLOORS_FETCHED, enhancedFloors);
//   } catch (error) {
//     next(error);
//   } finally {
//     await qRunner.release();
//   }
// };

export const viewAllFloors = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { Floor, TableReservation, BufferTime } = getRepositories(
      qRunner
    ) as {
      Floor: Repository<Floor>;
      TableReservation: Repository<TableReservation>;
      BufferTime: Repository<BufferTime>;
    };

    const branchId = req.branchId;

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId,
      },
    });

    if (!exBufferTime)
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));

    const allFloors = await Floor.find({
      where: {
        branch: {
          branchId,
        },
      },
      relations: ["tables", "tableCombinations", "tableCombinations.tables"],
      order: {
        createdAt: "ASC",
        tables: {
          createdAt: "ASC",
        },
        tableCombinations: {
          createdAt: "ASC",
        },
      },
    });

    const currentTime = new Date();
    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);

    const allTableIds = allFloors.flatMap((floor) =>
      floor.tables.map((table) => table.tableId)
    );

    const todaysReservations = await TableReservation.find({
      where: {
        table: {
          tableId: In(allTableIds),
        },
        status: In([ReservationStatus.CONFIRMED, ReservationStatus.PENDING]),
        reservationTime: Between(currentTime, endOfDay),
      },
      relations: ["table"],
      order: {
        reservationTime: "ASC",
      },
    });

    const bufferTimeAgo = new Date(currentTime.getTime() - exBufferTime.time * 60 * 1000);
    const activeReservations = await TableReservation.find({
      where: {
        table: {
          tableId: In(allTableIds),
        },
        status: ReservationStatus.CONFIRMED,
        reservationTime: Between(bufferTimeAgo, currentTime),
      },
      relations: ["table"],
    });

    const allRelevantReservations = [
      ...todaysReservations,
      ...activeReservations,
    ];

    const tableReservationsMap = new Map<string, any[]>();
    const tableCombinationReservationsMap = new Map<string, any[]>();

    allRelevantReservations.forEach((reservation) => {
      const tableId = reservation.table.tableId;
      
      if (!tableReservationsMap.has(tableId)) {
        tableReservationsMap.set(tableId, []);
      }
      const existingReservation = tableReservationsMap
        .get(tableId)!
        .find((r) => r.reservationId === reservation.reservationId);
      if (!existingReservation) {
        tableReservationsMap.get(tableId)!.push({
          reservationId: reservation.reservationId,
          customerName: reservation.customerName,
          phoneNumber: reservation.phoneNumber,
          numberOfGuests: reservation.numberOfGuests,
          reservationTime: reservation.reservationTime,
          status: reservation.status,
          specialNotes: reservation.specialNotes,
          durationMinutes: reservation.durationMinutes,
          confirmationCode: reservation.confirmationCode,
        });
      }

      if (reservation.tableCombinationId) {
        if (!tableCombinationReservationsMap.has(reservation.tableCombinationId)) {
          tableCombinationReservationsMap.set(reservation.tableCombinationId, []);
        }
        const existingCombinationReservation = tableCombinationReservationsMap
          .get(reservation.tableCombinationId)!
          .find((r) => r.confirmationCode === reservation.confirmationCode);
        if (!existingCombinationReservation) {
          tableCombinationReservationsMap.get(reservation.tableCombinationId)!.push({
            reservationId: reservation.reservationId,
            customerName: reservation.customerName,
            phoneNumber: reservation.phoneNumber,
            numberOfGuests: reservation.numberOfGuests,
            reservationTime: reservation.reservationTime,
            status: reservation.status,
            specialNotes: reservation.specialNotes,
            durationMinutes: reservation.durationMinutes,
            confirmationCode: reservation.confirmationCode,
          });
        }
      }
    });

    const enhancedFloors = allFloors.map((floor) => ({
      ...floor,
      tables: floor.tables.map((table) => {
        const availableSeats = table.maxSeats - table.bookedSeats;
        const reservations = tableReservationsMap.get(table.tableId) || [];

        const nextTwoHours = new Date(
          currentTime.getTime() + 2 * 60 * 60 * 1000
        );
        const nextReservation = reservations.find(
          (r) => new Date(r.reservationTime) <= nextTwoHours
        );

        // within buffer time? then show reserved!
        const bufferTimeMs = exBufferTime.time * 60 * 1000;
        const currentReservation = reservations.find((r) => {
          const resTime = new Date(r.reservationTime);
          const timeDiff = Math.abs(currentTime.getTime() - resTime.getTime());
          return (
            timeDiff <= bufferTimeMs &&
            r.status === ReservationStatus.CONFIRMED
          );
        });

        let dynamicStatus = table.status;
        if (currentReservation) {
          dynamicStatus = TableStatus.RESERVED;
        } else if (table.bookedSeats === 0) {
          dynamicStatus = TableStatus.AVAILABLE;
        } else if (table.bookedSeats >= table.maxSeats) {
          dynamicStatus = TableStatus.OCCUPIED;
        } else if (table.bookedSeats > 0) {
          dynamicStatus = TableStatus.OCCUPIED;
        }

        let reservedSeats = 0;
        if (currentReservation) {
          reservedSeats = currentReservation.numberOfGuests;
        }

        return {
          ...table,
          status: dynamicStatus,
          availableSeats,
          reservedSeats,

          hasReservations: reservations.length > 0,
          reservationCount: reservations.length,
          nextReservation,
          currentReservation,

          todaysReservations: reservations,
          displayStatus: getTableDisplayStatus(
            table,
            currentReservation,
            availableSeats
          ),

          isFullyOccupied: table.bookedSeats >= table.maxSeats,
          isPartiallyOccupied:
            table.bookedSeats > 0 && table.bookedSeats < table.maxSeats,
          isEmpty: table.bookedSeats === 0,
          needsCleaning: table.cleaning !== TableCleaningStatus.CLEAN,

          lastUpdated: table.updatedAt,

          availabilitySummary: {
            status: dynamicStatus,
            bookedSeats: table.bookedSeats,
            availableSeats: availableSeats - reservedSeats,
            maxSeats: table.maxSeats,
            cleaningStatus: table.cleaning,
            isEnabled: table.enabled,
          },
        };
      }),
      
      tableCombinations: floor.tableCombinations.map((combination) => {
        const reservations = tableCombinationReservationsMap.get(combination.tableCombinationId) || [];
        
        const totalMaxSeats = combination.tables.reduce((sum, table) => sum + table.maxSeats, 0);
        const totalBookedSeats = combination.tables.reduce((sum, table) => sum + table.bookedSeats, 0);
        const totalAvailableSeats = totalMaxSeats - totalBookedSeats;

        const nextTwoHours = new Date(
          currentTime.getTime() + 2 * 60 * 60 * 1000
        );
        const nextReservation = reservations.find(
          (r) => new Date(r.reservationTime) <= nextTwoHours
        );

        // within buffer time? reserved!
        const bufferTimeMs = exBufferTime.time * 60 * 1000;
        const currentReservation = reservations.find((r) => {
          const resTime = new Date(r.reservationTime);
          const reservationEndTime = new Date(resTime.getTime() + (r.durationMinutes * 60000));
          return (
            ((currentTime >= new Date(resTime.getTime() - bufferTimeMs) && 
              currentTime <= resTime) ||
             (currentTime >= resTime && currentTime <= reservationEndTime)) &&
            r.status === ReservationStatus.CONFIRMED
          );
        });

        const hasDisabledTables = combination.tables.some(table => 
          !table.enabled || 
          table.cleaning === TableCleaningStatus.DIRTY ||
          table.cleaning === TableCleaningStatus.NEEDSCLEANING
        );

        let dynamicStatus: string;
        if (!combination.enabled || hasDisabledTables) {
          dynamicStatus = 'DISABLED';
        } else if (currentReservation) {
          dynamicStatus = 'RESERVED';
        } else if (totalBookedSeats === 0) {
          dynamicStatus = 'AVAILABLE';
        } else if (totalBookedSeats >= totalMaxSeats) {
          dynamicStatus = 'OCCUPIED';
        } else {
          dynamicStatus = 'PARTIALLY_OCCUPIED';
        }

        let reservedSeats = 0;
        if (currentReservation) {
          const sameCodeReservations = reservations.filter(r => r.confirmationCode === currentReservation.confirmationCode);
          reservedSeats = sameCodeReservations.reduce((sum, r) => sum + r.numberOfGuests, 0);
        }

        return {
          ...combination,
          status: dynamicStatus,
          totalMaxSeats,
          totalBookedSeats,
          totalAvailableSeats: totalAvailableSeats - reservedSeats,
          reservedSeats,

          hasReservations: reservations.length > 0,
          reservationCount: reservations.length,
          nextReservation,
          currentReservation,

          todaysReservations: reservations,

          isFullyOccupied: totalBookedSeats >= totalMaxSeats,
          isPartiallyOccupied: totalBookedSeats > 0 && totalBookedSeats < totalMaxSeats,
          isEmpty: totalBookedSeats === 0,
          hasDisabledTables,

          lastUpdated: combination.updatedAt,

          availabilitySummary: {
            status: dynamicStatus,
            totalBookedSeats,
            totalAvailableSeats: totalAvailableSeats - reservedSeats,
            totalMaxSeats,
            isEnabled: combination.enabled,
            hasDisabledTables,
            minSeats: combination.minSeats,
            maxSeats: combination.maxSeats,
          },

          tables: combination.tables.map((table) => {
            const tableReservations = tableReservationsMap.get(table.tableId) || [];
            const tableAvailableSeats = table.maxSeats - table.bookedSeats;
            
            const tableCurrentReservation = tableReservations.find((r) => {
              const resTime = new Date(r.reservationTime);
              const timeDiff = Math.abs(currentTime.getTime() - resTime.getTime());
              return (
                timeDiff <= bufferTimeMs &&
                r.status === ReservationStatus.CONFIRMED
              );
            });

            let tableStatus = table.status;
            if (tableCurrentReservation) {
              tableStatus = TableStatus.RESERVED;
            } else if (table.bookedSeats === 0) {
              tableStatus = TableStatus.AVAILABLE;
            } else if (table.bookedSeats >= table.maxSeats) {
              tableStatus = TableStatus.OCCUPIED;
            } else if (table.bookedSeats > 0) {
              tableStatus = TableStatus.OCCUPIED;
            }

            return {
              ...table,
              status: tableStatus,
              availableSeats: tableAvailableSeats,
              reservedSeats: tableCurrentReservation ? tableCurrentReservation.numberOfGuests : 0,
              currentReservation: tableCurrentReservation,
              hasReservations: tableReservations.length > 0,
            };
          }),
        };
      }),
    }));

    ResponseHelper.success(res, OK, ALL_FLOORS_FETCHED, enhancedFloors);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchFloors = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { Floor } = getRepositories(qRunner) as {
      Floor: Repository<Floor>;
    };

    const branchId = req.branchId;

    const floors = req.body;

    await patchFloorHelper(floors, Floor, branchId);

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, FLOORS_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};
