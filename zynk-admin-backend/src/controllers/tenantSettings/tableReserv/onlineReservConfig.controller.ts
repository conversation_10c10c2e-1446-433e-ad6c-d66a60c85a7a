import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Repository } from "typeorm";
import { OnlineReservationConfig } from "../../../models/tenantSettings/tableReservation/online/online_reserv.model";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";
import {
  BAD_REQUEST,
  CONFLICT,
  NOT_FOUND,
  OK,
} from "../../../constants/STATUS_CODES";
import {
  ONLINE_RESERV_CONFIG_FETCHED,
  ONLINE_RESERV_CONFIG_UPDATED,
  PARTYSIZE_CONFIG_FETCHED,
  PARTYSIZE_CONFIG_UPDATED,
  TIME_SLOTS_FETCHED,
  TIME_SLOTS_UPDATED,
} from "../../../constants/tenant/settings/tableReserv/msg";
import { BufferTime } from "../../../models/tenantSettings/tableReservation/management/bufferTime.model";
import { errorHandler } from "../../../utils/errorHandler";
import {
  BUFFERTIME_NOTFOUND,
  ONLINE_RESERV_CONFIG_NOTFOUND,
  PARTYSIZE_CONFIG_NOTFOUND,
} from "../../../constants/tenant/settings/tableReserv/err";
import { CustomHourSlot } from "../../../models/common/customhourslot.model";
import {
  CUSTOM_HOUR_NOT_FOUND,
  DUPLICATE_DAYS_IN_CUSTOMHOURS,
} from "../../../constants/tenant/company/err";
import { SpecialDay } from "../../../models/common/specialday.model";
import { FOOD_MENU_NOT_FOUND } from "../../../constants/tenant/foodmenu/err";
import { PartySize } from "../../../models/tenantSettings/tableReservation/online/partysize.model";
import { TimeSlotPace } from "../../../models/tenantSettings/tableReservation/online/timeSlotPace.model";
import { timeSlotPatchSchema } from "../../../validation/tenantSettings/tableReserv/timeSlotConfig.validation";
import { z } from "zod";
import { patchTimeSlotHelper } from "../../../helpers/settings/tableReserv/timeSlotHelper";

export const getOnlineReservConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { OnlineReservationConfig, BufferTime } = getRepositories(
      qRunner
    ) as {
      OnlineReservationConfig: Repository<OnlineReservationConfig>;
      BufferTime: Repository<BufferTime>;
    };

    const branchId = req.branchId;

    const exOnlineReservConfig = await OnlineReservationConfig.findOne({
      where: {
        branch: {
          branchId,
        },
      },
      relations: ["customSlots", "globalCustomSlots", "specialDays"],
    });

    if (!exOnlineReservConfig)
      return next(errorHandler(NOT_FOUND, ONLINE_RESERV_CONFIG_NOTFOUND));

    const exBufferTime = await BufferTime.findOne({
      where: {
        branch: {
          branchId,
        },
      },
    });

    if (!exBufferTime)
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));

    const result = {
      onlineReservConfig: exOnlineReservConfig,
      bufferTime: exBufferTime,
    };

    ResponseHelper.success(res, OK, ONLINE_RESERV_CONFIG_FETCHED, result);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchOnlineReservConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { OnlineReservationConfig, BufferTime } = getRepositories(
      qRunner
    ) as {
      OnlineReservationConfig: Repository<OnlineReservationConfig>;
      BufferTime: Repository<BufferTime>;
    };

    const branchId = req.branchId;

    const exOnlineReservConfig = await OnlineReservationConfig.findOne({
      where: {
        branch: {
          branchId,
        },
      },
      relations: ["customSlots", "globalCustomSlots", "specialDays"],
    });

    if (!exOnlineReservConfig)
      return next(errorHandler(NOT_FOUND, ONLINE_RESERV_CONFIG_NOTFOUND));

    const exBufferTime = await BufferTime.findOne({
      where: {
        branch: {
          branchId,
        },
      },
    });

    if (!exBufferTime)
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));

    const { bufferTime, bufferTimeType, enabled } = req.body;

    if (bufferTime) {
      let newBufferTime;

      switch (bufferTimeType) {
        case "minutes":
          newBufferTime = bufferTime;
          break;
        case "hours":
          newBufferTime = bufferTime * 60;
          break;
        default:
          newBufferTime = bufferTime;
      }

      exBufferTime.time = newBufferTime;
      await BufferTime.save(exBufferTime);
    }

    if (enabled !== undefined) {
      exOnlineReservConfig.enabled = enabled;
      await OnlineReservationConfig.save(exOnlineReservConfig);
    }

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, ONLINE_RESERV_CONFIG_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchOnlineReservCustomHours = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { OnlineReservationConfig, CustomHourSlot } = getRepositories(
      qRunner
    ) as {
      OnlineReservationConfig: Repository<OnlineReservationConfig>;
      CustomHourSlot: Repository<CustomHourSlot>;
    };

    const onlineResConfigId = req.params.onlineResConfigId;
    const { customTimes, customSlots, globalCustomSlots } = req.body;

    const exOnlineReservConfig = await OnlineReservationConfig.findOne({
      where: {
        onlineResConfigId,
      },
      relations: [
        "customSlots",
        "globalCustomSlots",
        "branch.businessHour.customSlots",
      ],
    });

    if (!exOnlineReservConfig)
      return next(errorHandler(BAD_REQUEST, ONLINE_RESERV_CONFIG_NOTFOUND));

    if (customTimes !== undefined) {
      exOnlineReservConfig.customTimes = customTimes;
      await OnlineReservationConfig.save(exOnlineReservConfig);
    }

    if (exOnlineReservConfig.globalCustomSlots && globalCustomSlots) {
      const branchCustomSlotIds =
        exOnlineReservConfig.branch.businessHour.customSlots.map(
          (slot) => slot.slotId
        );

      for (const slotId of globalCustomSlots) {
        if (!branchCustomSlotIds.includes(slotId)) {
          return next(errorHandler(BAD_REQUEST, CUSTOM_HOUR_NOT_FOUND));
        }
      }

      exOnlineReservConfig.globalCustomSlots =
        exOnlineReservConfig.globalCustomSlots.filter((slot) =>
          globalCustomSlots.includes(slot.slotId)
        );

      // Add new slots
      for (const slotId of globalCustomSlots) {
        if (
          !exOnlineReservConfig.globalCustomSlots.find(
            (slot) => slot.slotId === slotId
          )
        ) {
          const customSlot = await CustomHourSlot.findOneBy({ slotId });
          if (customSlot) {
            exOnlineReservConfig.globalCustomSlots.push(customSlot);
          }
        }
      }

      await OnlineReservationConfig.save(exOnlineReservConfig);
    }

    if (exOnlineReservConfig.customTimes && customSlots) {
      // Check for duplicate active days across both existing and incoming custom slots
      const customHourDays = new Map<string, boolean>();

      exOnlineReservConfig.customSlots.forEach((slot: any) => {
        if (slot.days && slot.isActive) {
          Object.entries(slot.days).forEach(([day, isActive]) => {
            if (isActive && slot.isActive) {
              customHourDays.set(day, true);
            }
          });
        }
      });

      customSlots.forEach((slot: any) => {
        if (slot.days && slot.isActive) {
          Object.entries(slot.days).forEach(([day, isActive]) => {
            if (isActive && customHourDays.get(day)) {
              return next(
                errorHandler(CONFLICT, DUPLICATE_DAYS_IN_CUSTOMHOURS)
              );
            }
            if (isActive && slot.isActive) {
              customHourDays.set(day, true);
            }
          });
        }
      });

      const existingCustomSlotIds = exOnlineReservConfig.customSlots.map(
        (slot) => slot.slotId
      );
      const incomingCustomSlotIds = customSlots
        .filter((slot: any) => slot.slotId)
        .map((slot: any) => slot.slotId);

      const customSlotsToDelete = exOnlineReservConfig.customSlots.filter(
        (slot) => !incomingCustomSlotIds.includes(slot.slotId)
      );

      if (customSlotsToDelete.length > 0) {
        await CustomHourSlot.remove(customSlotsToDelete);
      }

      for (const slotData of customSlots) {
        if (slotData.slotId) {
          const updateData: Partial<CustomHourSlot> = {};

          if ("name" in slotData) updateData.name = slotData.name;
          if ("days" in slotData) updateData.days = slotData.days;
          if ("is24Hours" in slotData)
            updateData.is24Hours = slotData.is24Hours;
          if ("firstSeating" in slotData)
            updateData.firstSeating = slotData.firstSeating;
          if ("lastSeating" in slotData)
            updateData.lastSeating = slotData.lastSeating;
          if ("isActive" in slotData) updateData.isActive = slotData.isActive;

          await CustomHourSlot.update({ slotId: slotData.slotId }, updateData);
        } else {
          const newCustomSlot = CustomHourSlot.create({
            online_reservation: exOnlineReservConfig,
            name: slotData.name,
            days: slotData.days,
            is24Hours: slotData.is24Hours,
            firstSeating: slotData.firstSeating,
            lastSeating: slotData.lastSeating,
            isActive: slotData.isActive,
          });

          await CustomHourSlot.save(newCustomSlot);
        }
      }
    }

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, ONLINE_RESERV_CONFIG_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchOnlineReservSpecialDays = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { OnlineReservationConfig, SpecialDay } = getRepositories(
      qRunner
    ) as {
      OnlineReservationConfig: Repository<OnlineReservationConfig>;
      SpecialDay: Repository<SpecialDay>;
    };

    const onlineResConfigId = req.params.onlineResConfigId;
    const { specialDays } = req.body;

    const exOnlineReservConfig = await OnlineReservationConfig.findOne({
      where: {
        onlineResConfigId,
      },
      relations: ["specialDays"],
    });

    if (!exOnlineReservConfig)
      return next(errorHandler(BAD_REQUEST, FOOD_MENU_NOT_FOUND));

    if (specialDays && specialDays.length > 0) {
      const existingSpecialDayIds = exOnlineReservConfig.specialDays.map(
        (day) => day.specialDayId
      );
      const incomingSpecialDayIds = specialDays
        .filter((day: any) => day.specialDayId)
        .map((day: any) => day.specialDayId);

      const specialDaysToDelete = exOnlineReservConfig.specialDays.filter(
        (day) => !incomingSpecialDayIds.includes(day.specialDayId)
      );

      if (specialDaysToDelete.length > 0) {
        await SpecialDay.remove(specialDaysToDelete);
      }

      for (const dayData of specialDays) {
        if (dayData.specialDayId) {
          const updateData = dayData;

          const existingSpecialDay = await SpecialDay.findOne({
            where: { specialDayId: dayData.specialDayId },
          });

          if (existingSpecialDay) {
            existingSpecialDay.update(updateData);
            await SpecialDay.save(existingSpecialDay);
          }
        } else {
          const newSpecialDay = SpecialDay.create({
            online_reservation: exOnlineReservConfig,
            eventName: dayData.eventName,
            startTime: dayData.startTime,
            endTime: dayData.endTime,
            availability: dayData.availability,
          });

          await SpecialDay.save(newSpecialDay);
        }
      }
    }

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, ONLINE_RESERV_CONFIG_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getPartySizeConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { PartySize } = getRepositories(qRunner) as {
      PartySize: Repository<PartySize>;
    };

    const branchId = req.branchId;

    const exPartySizeConfig = await PartySize.findOne({
      where: {
        partySizeId: branchId,
      },
    });

    if (!exPartySizeConfig)
      return next(errorHandler(NOT_FOUND, PARTYSIZE_CONFIG_NOTFOUND));

    ResponseHelper.success(
      res,
      OK,
      PARTYSIZE_CONFIG_FETCHED,
      exPartySizeConfig
    );
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchPartySizeConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { PartySize } = getRepositories(qRunner) as {
      PartySize: Repository<PartySize>;
    };

    const branchId = req.branchId;

    const exPartySizeConfig = await PartySize.findOne({
      where: {
        partySizeId: branchId,
      },
    });

    if (!exPartySizeConfig)
      return next(errorHandler(NOT_FOUND, PARTYSIZE_CONFIG_NOTFOUND));

    const updateData = req.body;

    exPartySizeConfig.update({
      ...updateData,
    });

    await PartySize.save(exPartySizeConfig);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, PARTYSIZE_CONFIG_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getTimeSlotConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { OnlineReservationConfig } = getRepositories(qRunner) as {
      OnlineReservationConfig: Repository<OnlineReservationConfig>;
    };

    const branchId = req.branchId;

    const exOnlineReservConfig = await OnlineReservationConfig.findOne({
      where: {
        onlineResConfigId: branchId,
      },
      relations: ["timeSlotPacings"],
    });

    if (!exOnlineReservConfig)
      return next(errorHandler(NOT_FOUND, ONLINE_RESERV_CONFIG_NOTFOUND));

    let result = exOnlineReservConfig.timeSlotPacings;

    ResponseHelper.success(res, OK, TIME_SLOTS_FETCHED, result);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchTimeSlotConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { OnlineReservationConfig, TimeSlotPace } = getRepositories(qRunner) as {
      OnlineReservationConfig: Repository<OnlineReservationConfig>;
      TimeSlotPace: Repository<TimeSlotPace>;
    };

    const branchId = req.branchId;
    const timeSlots = req.body as z.infer<typeof timeSlotPatchSchema>;

    const exOnlineReservConfig = await OnlineReservationConfig.findOne({
      where: {
        onlineResConfigId: branchId,
      },
      relations: ["timeSlotPacings"],
    });

    if (!exOnlineReservConfig) {
      return next(errorHandler(NOT_FOUND, ONLINE_RESERV_CONFIG_NOTFOUND));
    }

    const result = await patchTimeSlotHelper(
      timeSlots,
      exOnlineReservConfig,
      OnlineReservationConfig,
      TimeSlotPace
    );

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, TIME_SLOTS_UPDATED, result);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};