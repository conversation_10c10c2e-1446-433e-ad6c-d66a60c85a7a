import { error } from "console";
import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Repository } from "typeorm";
import { TurnoverTime } from "../../../models/tenantSettings/tableReservation/turnoverTime/turnover_time.model";
import { TurnoverRule } from "../../../models/tenantSettings/tableReservation/turnoverTime/turnover_rules.model";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";
import { NOT_FOUND, OK } from "../../../constants/STATUS_CODES";
import { TURNOVERTIME_FETCHED, TURNOVERTIME_UPDATED } from "../../../constants/tenant/settings/tableReserv/msg";
import { errorHandler } from "../../../utils/errorHandler";
import { TURNOVERTIME_NOTFOUND } from "../../../constants/tenant/settings/tableReserv/err";
import { patchTurnoverTimeHelper, PatchTurnoverTimeInput } from "../../../helpers/settings/tenant/tableReserv/turnoverTime.helper";

export const viewTurnoverTime = async (req: Request,
    res: Response, next: NextFunction
) => {
    const qRunner = req.queryRunner

    await qRunner.connect()

    try{
        const { TurnoverTime } = getRepositories(qRunner) as {
            TurnoverTime: Repository<TurnoverTime>,
        }

        const branchId = req.branchId

        const exTurnoverTime = await TurnoverTime.findOne({
            where: {
                branch: {
                    branchId
                }
            },
            relations: ['turnoverRules']
        })

        if(!exTurnoverTime)
            return next(errorHandler(NOT_FOUND, TURNOVERTIME_NOTFOUND))

        ResponseHelper.success(res, OK, TURNOVERTIME_FETCHED, exTurnoverTime)

    }
    catch{
        next(error)
    }
    finally{
        await qRunner.release()
    }
}

export const patchTurnoverTime = async (req: Request, res: Response, next: NextFunction) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const branchId = req.branchId;
    const patchData = req.body as PatchTurnoverTimeInput;

    const {TurnoverTime} = getRepositories(qRunner) as {
        TurnoverTime: Repository<TurnoverTime>
    }
    const existingTurnoverTime = await TurnoverTime.findOne({
      where: {
        branch: {
          branchId
        }
      }
    });

    if (!existingTurnoverTime) {
      return next(errorHandler(NOT_FOUND, TURNOVERTIME_NOTFOUND));
    }

    const updatedTurnoverTime = await patchTurnoverTimeHelper(
      qRunner,
      existingTurnoverTime.turnoverTimeId,
      patchData
    );

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, TURNOVERTIME_UPDATED, updatedTurnoverTime);

  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};