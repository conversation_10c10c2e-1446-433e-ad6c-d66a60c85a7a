import { NextFunction, Request, Response } from "express";
import { Repository } from "typeorm";
import { TableModel } from "../../../models/tenantSettings/tableReservation/management/table.model";
import { TableCombination } from "../../../models/tenantSettings/tableReservation/management/tableCombination.model";
import { getPrefBranchHelper } from "../../../helpers/settings/utils/prefBranch.helper";
import { OK } from "../../../constants/STATUS_CODES";
import {
  TABLES_FETCHED,
  TABLES_PATCHED,
} from "../../../constants/tenant/settings/tableReserv/msg";
import {
  patchTableCombinationsInBranch,
  patchTablesInBranch,
} from "../../../helpers/settings/tableReserv/tableManage.helper";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";

export const viewAllTablesAndCombinations = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { TableModel, TableCombination } = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
      TableCombination: Repository<TableCombination>;
    };

    const exPrefBranch = await getPrefBranchHelper(req, qRunner);

    const allTables = await TableModel.find({
      where: {
        branch: {
          branchId: exPrefBranch.prefBranchId!,
        },
      },
    });

    const allTableCombinations = await TableCombination.find({
      where: {
        branch: {
          branchId: exPrefBranch.prefBranchId!,
        },
      },
    });

    const result = {
      tables: allTables,
      tableCombinations: allTableCombinations,
    };

    ResponseHelper.success(res, OK, TABLES_FETCHED, result);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchTablesAndCombinations = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();
  await qRunner.startTransaction();
  try {
    const { TableModel, TableCombination } = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
      TableCombination: Repository<TableCombination>;
    };

    const exPrefBranch = await getPrefBranchHelper(req, qRunner);

    const { tables, tableCombinations } = req.body;

    await patchTablesInBranch(tables, TableModel, exPrefBranch.prefBranchId!);

    await patchTableCombinationsInBranch(
      tableCombinations,
      TableModel,
      TableCombination,
      exPrefBranch.prefBranchId!
    );

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, TABLES_PATCHED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};
