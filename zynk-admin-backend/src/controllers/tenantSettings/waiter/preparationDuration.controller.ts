import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Repository } from "typeorm";
import { PreparationDuration } from "../../../models/tenantSettings/food/preparation_duration.model";
import { getPrefBranchHelper } from "../../../helpers/settings/utils/prefBranch.helper";
import { errorHandler } from "../../../utils/errorHandler";
import { NOT_FOUND, OK } from "../../../constants/STATUS_CODES";
import { PREP_DURATION_FETCHED, PREP_DURATION_UPDATED } from "../../../constants/tenant/settings/waiter/prepDuration/msg";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";
import { PREP_DURATION_NOTFOUND } from "../../../constants/tenant/settings/waiter/prepDuration/err";

export const viewPreparationTime = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {

    const qRunner = req.queryRunner

    await qRunner.connect()

    try{
        const {PreparationDuration} = getRepositories(qRunner) as {
            PreparationDuration: Repository<PreparationDuration>
        }

        const exPrefBranch = await getPrefBranchHelper(req, qRunner)

        const exPrepDuration = await PreparationDuration.findOne({
            where: {
                preparationDurationId: exPrefBranch.prefBranchId!
            }
        })

        if(!exPrepDuration)
            return next(errorHandler(NOT_FOUND, PREP_DURATION_NOTFOUND))

        ResponseHelper.success(res, OK, PREP_DURATION_FETCHED, exPrepDuration)
    }
    catch(error){
        next(error)
    }
    finally{
        await qRunner.release()
    }
}

export const patchPreparationTime = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {

    const qRunner = req.queryRunner

    await qRunner.connect()

    await qRunner.startTransaction()

    try{
        const {PreparationDuration} = getRepositories(qRunner) as {
            PreparationDuration: Repository<PreparationDuration>
        }

        const exPrefBranch = await getPrefBranchHelper(req, qRunner)

        const exPrepDuration = await PreparationDuration.findOne({
            where: {
                preparationDurationId: exPrefBranch.prefBranchId!
            }
        })

        if(!exPrepDuration)
            return next(errorHandler(NOT_FOUND, PREP_DURATION_NOTFOUND))


        const {duration} = req.body

        if(duration){
            exPrepDuration.duration = duration

            await PreparationDuration.save(exPrepDuration)
        }

        await qRunner.commitTransaction()

        ResponseHelper.success(res, OK, PREP_DURATION_UPDATED)
    }
    catch(error){
        await qRunner.rollbackTransaction()
        next(error)
    }
    finally{
        await qRunner.release()
    }
}