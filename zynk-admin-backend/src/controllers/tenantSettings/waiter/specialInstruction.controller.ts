import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Repository } from "typeorm";
import { SpecialInstructions } from "../../../models/tenantSettings/food/SpecialInstructions.model";
import { getPrefBranchHelper } from "../../../helpers/settings/utils/prefBranch.helper";
import { errorHandler } from "../../../utils/errorHandler";
import { NOT_FOUND, OK } from "../../../constants/STATUS_CODES";
import { SPECIAL_INS_NOT_FOUND } from "../../../constants/tenant/settings/waiter/prepDuration/err";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";
import { SPECIAL_INS_FETCHED } from "../../../constants/tenant/settings/waiter/prepDuration/msg";

export const viewSpecialInstructionConfig = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner

    await qRunner.connect()

    try{
        const {SpecialInstructions} = getRepositories(qRunner) as {
            SpecialInstructions: Repository<SpecialInstructions>
        }

        const exPrefBranch = await getPrefBranchHelper(req, qRunner);

        const exSpecialIns = await SpecialInstructions.findOneBy({
            specialInstructionsId: exPrefBranch.prefBranchId!
        })

        if(!exSpecialIns)
            return next(errorHandler(NOT_FOUND, SPECIAL_INS_NOT_FOUND))

        ResponseHelper.success(res, OK, SPECIAL_INS_FETCHED, exSpecialIns)
    }
    catch(error){
        next(error)
    }
    finally{
        await qRunner.release()
    }
}

export const patchSpecialIns = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner

    await qRunner.connect()

    await qRunner.startTransaction()

    try{
        const {SpecialInstructions} = getRepositories(qRunner) as {
            SpecialInstructions: Repository<SpecialInstructions>
        }

        const exPrefBranch = await getPrefBranchHelper(req, qRunner);

        const exSpecialIns = await SpecialInstructions.findOneBy({
            specialInstructionsId: exPrefBranch.prefBranchId!
        })

        if(!exSpecialIns)
            return next(errorHandler(NOT_FOUND, SPECIAL_INS_NOT_FOUND))

        const {currentInstructions} = req.body

        if(currentInstructions){
            exSpecialIns.currentInstructions = currentInstructions
            await SpecialInstructions.save(exSpecialIns)
        }

        await qRunner.commitTransaction()
        ResponseHelper.success(res, OK, SPECIAL_INS_FETCHED, exSpecialIns)
    }
    catch(error){
        await qRunner.rollbackTransaction()
        next(error)
    }
    finally{
        await qRunner.release()
    }
}