import { NextFunction, Request, Response } from "express";
import { Repository } from "typeorm";
import { OrderType, OrderTypeName } from "../../../models/tenantSettings/general/orderType.model";
import { APIError } from "../../../utils/errorHandler";
import { BAD_REQUEST, NOT_FOUND, OK } from "../../../constants/STATUS_CODES";
import { ALL_ORDERTYPES_FETCHED, ORDER_TYPES_UPDATED } from "../../../constants/tenant/settings/general/orderType/msg";
import { ORDERTYPE_NOTFOUND } from "../../../constants/tenant/settings/general/orderType/err";
import { getPrefBranchHelper } from "../../../helpers/settings/utils/prefBranch.helper";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { validateReservedNameConflicts } from "../../../helpers/settings/tenant/general/orderType.helper";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";

export const viewAllOrderTypes = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

  try {
    const { OrderType } = getRepositories(qRunner) as {
      OrderType: Repository<OrderType>;
    };

    const exPrefBranch = await getPrefBranchHelper(req, qRunner)

    const allOrderTypes = await OrderType.find({
      where: {
        branch: {
          branchId: exPrefBranch.prefBranchId!,
        },
      },
      order: {
        createdAt: "ASC",
      }
    });

        ResponseHelper.success(res, OK, ALL_ORDERTYPES_FETCHED, allOrderTypes);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchOrderTypes = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    await qRunner.startTransaction();

  try {
    const { OrderType } = getRepositories(qRunner) as {
      OrderType: Repository<OrderType>;
    };

    const exPrefBranch = await getPrefBranchHelper(req, qRunner)

    const allOrderTypes = await OrderType.find({
        where: {
            branch: {
                branchId: exPrefBranch.prefBranchId!,
            },
        },
    });

        const incomingOrderTypes = req.body;

        const orderTypesToUpdate = incomingOrderTypes.filter((orderType: any) => orderType.orderTypeId);
        const orderTypesToCreate = incomingOrderTypes.filter((orderType: any) => !orderType.orderTypeId);

        const incomingOrderTypeIds = orderTypesToUpdate.map((ot: any) => ot.orderTypeId);
        const orderTypesToDelete = allOrderTypes.filter(
            (existingOt) => !incomingOrderTypeIds.includes(existingOt.orderTypeId)
        );

        const existingOrderTypeIds = allOrderTypes.map(ot => ot.orderTypeId);
        const invalidOrderTypeIds = orderTypesToUpdate
            .map((ot: any) => ot.orderTypeId)
            .filter((id: string) => !existingOrderTypeIds.includes(id));

        if (invalidOrderTypeIds.length > 0) {
            throw new APIError(NOT_FOUND, ORDERTYPE_NOTFOUND);
        }

        await validateReservedNameConflicts(
            allOrderTypes.filter(ot => !orderTypesToDelete.map(d => d.orderTypeId).includes(ot.orderTypeId)),
            orderTypesToUpdate,
            orderTypesToCreate,
        );

        for (const orderTypeToDelete of orderTypesToDelete) {
            await OrderType.delete(orderTypeToDelete.orderTypeId);
        }

        for (const orderTypeData of orderTypesToUpdate) {
            const updateData: Partial<{
                name: string;
                reservedName: OrderTypeName;
                enabled: boolean;
            }> = {};

            if (orderTypeData.hasOwnProperty('name')) {
                updateData.name = orderTypeData.name;
            }
            if (orderTypeData.hasOwnProperty('reservedName')) {
                updateData.reservedName = orderTypeData.reservedName;
            }
            if (orderTypeData.hasOwnProperty('enabled')) {
                updateData.enabled = orderTypeData.enabled;
            }

            if (Object.keys(updateData).length > 0) {
                await OrderType.update(orderTypeData.orderTypeId, updateData);
            }
        }

        for (const orderTypeData of orderTypesToCreate) {
            if (!orderTypeData.hasOwnProperty('name') ||
                !orderTypeData.hasOwnProperty('reservedName') ||
                !orderTypeData.hasOwnProperty('enabled')) {
                throw new APIError(BAD_REQUEST, 'New order types must have name, reservedName, and enabled fields');
            }

        const newOrderType = OrderType.create({
            name: orderTypeData.name,
            reservedName: orderTypeData.reservedName,
            enabled: orderTypeData.enabled,
            branch: {
                branchId: exPrefBranch.prefBranchId!,
            },
        });

            await OrderType.save(newOrderType);
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, ORDER_TYPES_UPDATED);

    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};
