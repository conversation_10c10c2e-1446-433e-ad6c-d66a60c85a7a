import { NextFunction, Request, Response } from "express";
import { Repository } from "typeorm";
import { CurrencyUnit } from "../../../models/tenantSettings/general/currencyUnit.model";
import { errorHandler } from "../../../utils/errorHandler";
import { NOT_FOUND, OK } from "../../../constants/STATUS_CODES";
import { CURRENCY_UNIT_NOT_FOUND } from "../../../constants/tenant/settings/general/currencyUnit/err";
import { CURRENCY_UNIT_FETCHED, CURRENCY_UNIT_UPDATED } from "../../../constants/tenant/settings/general/currencyUnit/msg";
import { getPrefBranchHelper } from "../../../helpers/settings/utils/prefBranch.helper";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";

export const viewCurrencyUnit = async (req: Request, res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner

    await qRunner.connect()

    try{
        const {CurrencyUnit, } = getRepositories(qRunner) as {
            CurrencyUnit: Repository<CurrencyUnit>,
        }

        const exPrefBranch = await getPrefBranchHelper(req, qRunner)
        
        const currencyUnit = await CurrencyUnit.findOne({
            where: {
                branch: {
                    branchId: exPrefBranch.prefBranchId!
                }
            }
        })

        if(!currencyUnit)
            return next(errorHandler(NOT_FOUND, CURRENCY_UNIT_NOT_FOUND))

        ResponseHelper.success(res, OK, CURRENCY_UNIT_FETCHED, currencyUnit)
    }
    catch(error){
        next(error)
    }
    finally{
        await qRunner.release()
    }
}

export const patchCurrencyUnit = async (req: Request, res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner

    await qRunner.connect()

    await qRunner.startTransaction()

    try{
        const {CurrencyUnit} = getRepositories(qRunner) as {
            CurrencyUnit: Repository<CurrencyUnit>,
        }

        const exPrefBranch = await getPrefBranchHelper(req, qRunner)

        const currencyUnit = await CurrencyUnit.findOne({
            where: {
                branch: {
                    branchId: exPrefBranch.prefBranchId!
                }
            }
        })

        if(!currencyUnit)
            return next(errorHandler(NOT_FOUND, CURRENCY_UNIT_NOT_FOUND))

        const {currentUnit} = req.body

        currencyUnit.update({
            currentUnit
        })

        await CurrencyUnit.save(currencyUnit)

        await qRunner.commitTransaction()
        ResponseHelper.success(res, OK, CURRENCY_UNIT_UPDATED)
    }
    catch(error){
        await qRunner.rollbackTransaction()
        next(error)
    }
    finally{
        await qRunner.release()
    }
}