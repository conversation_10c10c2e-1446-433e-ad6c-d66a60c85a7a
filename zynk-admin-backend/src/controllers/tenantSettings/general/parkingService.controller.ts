import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Repository } from "typeorm";
import { ParkingService } from "../../../models/tenantSettings/general/parkingService.model";
import { getPrefBranchHelper } from "../../../helpers/settings/utils/prefBranch.helper";
import { errorHandler } from "../../../utils/errorHandler";
import { NOT_FOUND, OK } from "../../../constants/STATUS_CODES";
import { PARKING_SERVICE_TOGGLE_NOTFOUND } from "../../../constants/tenant/settings/general/parkingService/err";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";
import { PARKING_SERVICE_FETCHED, PARKING_SERVICE_UPDATED } from "../../../constants/tenant/settings/general/parkingService/msg";

export const viewParkingService = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {

    const qRunner = req.queryRunner

    await qRunner.connect()

    try{
        const {ParkingService} = getRepositories(qRunner) as {
            ParkingService: Repository<ParkingService>
        }

        const exPrefBranch = await getPrefBranchHelper(req, qRunner);

        const exParkingService = await ParkingService.findOneBy({
            parkingServiceId: exPrefBranch.prefBranchId!
        })

        if(!exParkingService)
            return next(errorHandler(NOT_FOUND, PARKING_SERVICE_TOGGLE_NOTFOUND))

        ResponseHelper.success(res, OK, PARKING_SERVICE_FETCHED, exParkingService)
    }
    catch(error){
        next(error)
    }
    finally{
        await qRunner.release()
    }
}

export const patchParkingService = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {

    const qRunner = req.queryRunner

    await qRunner.connect()

    await qRunner.startTransaction()

    try{
        const {ParkingService} = getRepositories(qRunner) as {
            ParkingService: Repository<ParkingService>
        }

        const exPrefBranch = await getPrefBranchHelper(req, qRunner);

        const exParkingService = await ParkingService.findOneBy({
            parkingServiceId: exPrefBranch.prefBranchId!
        })

        if(!exParkingService)
            return next(errorHandler(NOT_FOUND, PARKING_SERVICE_TOGGLE_NOTFOUND))

        const {enabled} = req.body

        exParkingService.enabled = enabled

        await ParkingService.save(exParkingService)

        await qRunner.commitTransaction()

        ResponseHelper.success(res, OK, PARKING_SERVICE_UPDATED)
    }
    catch(error){
        await qRunner.rollbackTransaction()
        next(error)
    }
    finally{
        await qRunner.release()
    }
}