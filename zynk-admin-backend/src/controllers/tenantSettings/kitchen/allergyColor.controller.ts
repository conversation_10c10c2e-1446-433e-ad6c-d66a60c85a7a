import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Repository } from "typeorm";
import { AllergyColor } from "../../../models/reference/allergyColor.model";
import { getPrefBranchHelper } from "../../../helpers/settings/utils/prefBranch.helper";
import { ResponseHelper } from "../../../helpers/system/ResponseHelper.helper";
import { OK } from "../../../constants/STATUS_CODES";
import {
  ALLERGY_COLOR_FETCHED,
  ALLERGY_COLOR_UPDATED,
  ALLERGY_COLORS_FETCHED,
} from "../../../constants/tenant/settings/kitchen/allergyColors/msg";

export const viewAllAllergyColors = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { AllergyColor } = getRepositories(qRunner) as {
      AllergyColor: Repository<AllergyColor>;
    };

    const exPrefBranch = await getPrefBranchHelper(req, qRunner);

    const exAllergyColors = await AllergyColor.find({
      where: {
        branch: {
          branchId: exPrefBranch.prefBranchId!,
        },
      },
      order: {
        createdAt: "ASC"
      }
    });

    ResponseHelper.success(res, OK, ALLERGY_COLORS_FETCHED, exAllergyColors);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const viewAllergyColor = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { AllergyColor } = getRepositories(qRunner) as {
      AllergyColor: Repository<AllergyColor>;
    };

    const exPrefBranch = await getPrefBranchHelper(req, qRunner);

    const allergyName = req.params.name;

    const exAllergyColor = await AllergyColor.findOne({
      where: {
        name: allergyName,
        branch: {
          branchId: exPrefBranch.prefBranchId!,
        },
      },
    });

    ResponseHelper.success(res, OK, ALLERGY_COLOR_FETCHED, exAllergyColor);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchAllergyColor = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { AllergyColor } = getRepositories(qRunner) as {
      AllergyColor: Repository<AllergyColor>;
    };

    const exPrefBranch = await getPrefBranchHelper(req, qRunner);

    const { primary, secondary, tertiary, morethanone } = req.body;

    if (primary) {
      const primaryAllergyColor = await AllergyColor.findOne({
        where: {
            name: "primary",
            branch: {
                branchId: exPrefBranch.prefBranchId!
            }
        }
      });

      if (primaryAllergyColor) {
        primaryAllergyColor.color = primary;
        await AllergyColor.save(primaryAllergyColor);
      }
    }

    if (secondary) {
      const secondaryAllergyColor = await AllergyColor.findOne({
        where: {
            name: "secondary",
            branch: {
                branchId: exPrefBranch.prefBranchId!
            }
        }
      });

      if (secondaryAllergyColor) {
        secondaryAllergyColor!.color = secondary;
        await AllergyColor.save(secondaryAllergyColor);
      }
    }

    if (tertiary) {
      const tertiaryAlleryColor = await AllergyColor.findOne({
        where: {
            name: "tertiary",
            branch: {
                branchId: exPrefBranch.prefBranchId!
            }
        }
      });

      if (tertiaryAlleryColor) {
        tertiaryAlleryColor!.color = tertiary;
        await AllergyColor.save(tertiaryAlleryColor);
      }
    }

    if (morethanone) {
      const morethanoneAlleryColor = await AllergyColor.findOne({
        where: {
            name: "morethanone",
            branch: {
                branchId: exPrefBranch.prefBranchId!
            }
        }
      });

      if (morethanoneAlleryColor) {
        morethanoneAlleryColor!.color = morethanone;
        await AllergyColor.save(morethanoneAlleryColor);
      }
    }

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, ALLERGY_COLOR_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};
