import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {OK} from "../../constants/STATUS_CODES";
import {QueryRunner} from "typeorm";

export const patchWaiterSettings = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner: QueryRunner = req.queryRunner;
    await qRunner.connect();
    await qRunner.startTransaction();
    try {
        const {} = getRepositories(qRunner) as {}
        await qRunner.commitTransaction();
        ResponseHelper.success(res, OK, "test")
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
}