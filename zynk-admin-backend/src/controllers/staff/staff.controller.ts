import { NextFunction, Request, Response } from "express";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import {
  BAD_REQUEST,
  CREATED,
  FORBIDDEN,
  NOT_FOUND,
  OK,
} from "../../constants/STATUS_CODES";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { IsNull, Not, Repository } from "typeorm";
import { Staff } from "../../models/staff/staff.model";
import { StaffAuth } from "../../models/staff/staffAuth.model";
import {
  createStaffBasicDetails,
  createStaffCertifications,
  createStaffDaysAvailable,
  createStaffDefaultShiftTiming,
  updateStaffBasicDetails,
  updateStaffCertifications,
  updateStaffDaysAvailable,
  updateStaffDefaultShiftTiming,
  updateStaffPassword,
  updateStaffReservedBranches,
} from "../../helpers/staff/staff.helper";
import { DaysAvailable } from "../../models/staff/availability/days_available.model";
import { DefaultShiftTiming } from "../../models/staff/availability/default_shift_timing.model";
import { StaffCertification } from "../../models/staff/certs/staff_certification.model";
import { PrefBranch } from "../../models/common/prefbranch.model";
import { Branch } from "../../models/company/branch.model";
import {
  ALL_CLOCKEDIN_STAFF_FETCHED,
  ALL_STAFF_FETCHED,
  ALL_STAFF_SESSIONS_FETCHED,
  STAFF_AVAILABILITY_UPDATED,
  STAFF_BASIC_DETAILS_UPDATED,
  STAFF_CERTS_UPDATED,
  STAFF_CREATED,
  STAFF_DELETED,
  STAFF_FETCHED,
} from "../../constants/tenant/staff/msg";
import { errorHandler } from "../../utils/errorHandler";
import { BRANCH_NOT_FOUND } from "../../constants/tenant/company/err";
import {
  STAFF_NOT_FOUND,
  STAFF_NOTASSIGNED_TOBRANCH,
} from "../../constants/tenant/staff/err";
import { TRole } from "../../models/pBAC/tRole/TRole.model";
import { StaffPermissionAction } from "../../models/staff/StaffPermissionAction";
import { TimeEntry } from "../../models/attendance/time_entry.model";
import { UserType } from "../../types/pBAC";
import { AESHelper } from "../../helpers/system/AESHelper";
import { StaffSession } from "../../models/staff/staffSession.model";

export const createStaff = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const {
      Staff,
      StaffAuth,
      DaysAvailable,
      DefaultShiftTiming,
      StaffCertification,
      PrefBranch,
      Branch,
      TRole,
      StaffPermissionAction,
    } = getRepositories(qRunner) as {
      Staff: Repository<Staff>;
      StaffAuth: Repository<StaffAuth>;
      DaysAvailable: Repository<DaysAvailable>;
      DefaultShiftTiming: Repository<DefaultShiftTiming>;
      StaffCertification: Repository<StaffCertification>;
      PrefBranch: Repository<PrefBranch>;
      Branch: Repository<Branch>;
      TRole: Repository<TRole>;
      StaffPermissionAction: Repository<StaffPermissionAction>;
    };

    // const staffRole = await TRole.findOne({
    //     where: {name: "Staff"},
    //     relations: [
    //         "rolePermissionActions",
    //         "rolePermissionActions.permissionAction",
    //         "rolePermissionActions.permissionAction.permission",
    //         "rolePermissionActions.permissionAction.action",
    //     ],
    //     order: {
    //         name: "ASC",
    //     },
    // });
    // if (!staffRole) throw new StaffRoleNotFound();

    const { staffDaysAvailable, defaultShiftTiming, staffCertifications } =
      req.body;

    const newStaff = await createStaffBasicDetails(
      req,
      Staff,
      StaffAuth,
      PrefBranch,
      Branch,
      StaffPermissionAction
    );

    // const newStaff = await createStaffBasicDetails(
    //     req,
    //     Staff,
    //     StaffAuth,
    //     PrefBranch,
    //     Branch,
    //     StaffPermissionAction,
    //     staffRole
    // );

    await createStaffDaysAvailable(
      newStaff.staffId,
      staffDaysAvailable,
      DaysAvailable
    );

    await createStaffDefaultShiftTiming(
      newStaff.staffId,
      defaultShiftTiming,
      DefaultShiftTiming
    );

    await createStaffCertifications(
      newStaff,
      staffCertifications,
      StaffCertification
    );

    await qRunner.commitTransaction();

    const result = {
      staffId: newStaff.staffId,

      firstName: newStaff.firstName,

      middleName: newStaff.middleName,

      lastName: newStaff.lastName,

      emailAddress: newStaff.emailAddress,

      phoneNumber: newStaff.phoneNumber,

      address: newStaff.address,

      role: newStaff.tempRole,

      profileUrl: newStaff.profileUrl,
      createdAt: newStaff.createdAt,

      updatedAt: newStaff.updatedAt,
    };

    ResponseHelper.success(res, CREATED, STAFF_CREATED, result);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const viewAllStaffByBranch = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const branchId = req.params.branchId;

    const { Staff, Branch, TimeEntry } = getRepositories(qRunner) as {
      Staff: Repository<Staff>;
      Branch: Repository<Branch>;
      TimeEntry: Repository<TimeEntry>;
    };

    const userId = req.userId;

    const exBranch = await Branch.findOneBy({
      branchId,
    });

    if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

    if (req.USER_TYPE === UserType.STAFF) {
      const exStaff = await Staff.findOne({
        where: {
          staffId: userId,
        },
        relations: ["reservedBranches"],
      });

      const hasAccessToBranch = exStaff?.reservedBranches?.some(
        (branch) => branch.branchId === branchId
      );

      if (!hasAccessToBranch) {
        return next(errorHandler(FORBIDDEN, STAFF_NOTASSIGNED_TOBRANCH));
      }
    }

    const allStaff = await Staff.find({
      relations: ["reservedBranches", "staffDaysAvailable"],
      where: {
        reservedBranches: {
          branchId: branchId,
        },
      },
      select: {
        staffId: true,
        firstName: true,
        middleName: true,
        lastName: true,
        emailAddress: true,
        phoneNumber: true,
        address: true,
        profileUrl: true,
        createdAt: true,
        tempRole: true,
        updatedAt: true,
        reservedBranches: {
          branchId: true,
        },
        staffDaysAvailable: {
          daysAvailableId: true,
          days: true,
        },
      },
      order: {
        createdAt: "ASC",
      },
    });

    const today = new Date().toISOString().split("T")[0];

    const clockedInTimeEntries = await TimeEntry.find({
      where: {
        date: today,
        clockInTime: Not(IsNull()),
        clockOutTime: IsNull(),
      },
      relations: ["staff"],
    });

    const clockedInStaffIds = new Set(
      clockedInTimeEntries.map(entry => entry.staff.staffId)
    );

    // TODO: Staff roles should be complete
    // Map tempRole to role in each staff object and add isClockedIn status
    // Workaround until staff role is complete
    const staffWithRoleAndClockStatus = allStaff.map((staff) => ({
      ...staff,
      role: staff.tempRole,
      tempRole: undefined,
      isClockedIn: clockedInStaffIds.has(staff.staffId),
    }));

    ResponseHelper.success(res, OK, ALL_STAFF_FETCHED, staffWithRoleAndClockStatus);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const viewAllClockedInStaff = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const branchId = req.params.branchId;

    const { Branch, TimeEntry, Staff } = getRepositories(qRunner) as {
      Branch: Repository<Branch>;
      TimeEntry: Repository<TimeEntry>;
      Staff: Repository<Staff>;
    };

    const userId = req.userId;

    const exBranch = await Branch.findOneBy({
      branchId,
    });

    if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

    if (req.USER_TYPE === UserType.STAFF) {
      const exStaff = await Staff.findOne({
        where: {
          staffId: userId,
        },
        relations: ["reservedBranches"],
      });

      const hasAccessToBranch = exStaff?.reservedBranches?.some(
        (branch) => branch.branchId === branchId
      );

      if (!hasAccessToBranch) {
        return next(
          errorHandler(
            FORBIDDEN,
            "Access denied: You are not assigned to this branch"
          )
        );
      }
    }

    const today = new Date().toISOString().split("T")[0];

    const clockedInTimeEntries = await TimeEntry.find({
      where: {
        date: today,
        clockInTime: Not(IsNull()),
        clockOutTime: IsNull(),
      },
      relations: [
        "staff",
        "staff.reservedBranches",
        "staff.staffDaysAvailable",
      ],
    });

    // TODO: Staff roles should be complete
    // Map tempRole to role in each staff object
    // Workaround until staff role is complete
    const clockedInStaff = clockedInTimeEntries
      .filter((entry) =>
        entry.staff.reservedBranches.some(
          (branch: Branch) => branch.branchId === branchId
        )
      )
      .map((entry) => ({
        staffId: entry.staff.staffId,
        firstName: entry.staff.firstName,
        middleName: entry.staff.middleName,
        lastName: entry.staff.lastName,
        emailAddress: entry.staff.emailAddress,
        phoneNumber: entry.staff.phoneNumber,
        address: entry.staff.address,
        profileUrl: entry.staff.profileUrl,
        role: entry.staff.tempRole,
        tempRole: undefined,
        createdAt: entry.staff.createdAt,
        updatedAt: entry.staff.updatedAt,
        reservedBranches: entry.staff.reservedBranches
          .filter((branch: Branch) => branch.branchId === branchId)
          .map((branch: Branch) => ({
            branchId: branch.branchId
          })),
        staffDaysAvailable: {
          daysAvailableId: entry.staff.staffDaysAvailable.daysAvailableId,
          days: entry.staff.staffDaysAvailable.days,
        },
      }));

    ResponseHelper.success(
      res,
      OK,
      ALL_CLOCKEDIN_STAFF_FETCHED,
      clockedInStaff
    );
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const viewStaffById = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const staffId = req.params.staffId;

    const { Staff } = getRepositories(qRunner) as {
      Staff: Repository<Staff>;
    };

    const exStaff = await Staff.findOne({
      where: {
        staffId,
      },
      relations: [
        "staffCertifications",
        "reservedBranches",
        "staffDaysAvailable",
      ],
      select: {
        staffId: true,
        firstName: true,
        middleName: true,
        lastName: true,
        emailAddress: true,
        phoneNumber: true,
        address: true,
        profileUrl: true,
        createdAt: true,
        updatedAt: true,
        staffCertifications: {
          staffCertificationId: true,
          certId: true,
          certName: true,
          attachmentUrl: true,
          createdAt: true,
          updatedAt: true,
        },
        reservedBranches: {
          branchId: true,
        },
        staffDaysAvailable: {
          daysAvailableId: true,
          days: true,
        },
      },
    });

    if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

    ResponseHelper.success(res, OK, STAFF_FETCHED, exStaff);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchStaffDetails = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const staffId = req.params.staffId;

    const { Staff, Branch } = getRepositories(qRunner) as {
      Staff: Repository<Staff>;
      Branch: Repository<Branch>;
    };

    const exStaff = await Staff.findOne({
      where: {
        staffId,
      },
      relations: ["staffAuth", "reservedBranches"],
    });

    if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

    const { password, branches, ...rest } = req.body;

    const updatedStaff = await updateStaffBasicDetails(rest, exStaff, Staff);

    if (password) {
      await updateStaffPassword(password, updatedStaff, Staff);
    }

    if (branches) {
      await updateStaffReservedBranches(branches, updatedStaff, Staff, Branch);
    }

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, STAFF_BASIC_DETAILS_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchStaffAvailability = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { staffId } = req.params;
    const { staffDaysAvailable, defaultShiftTiming } = req.body;

    const { DaysAvailable, DefaultShiftTiming, Staff } = getRepositories(
      qRunner
    ) as {
      DaysAvailable: Repository<DaysAvailable>;
      DefaultShiftTiming: Repository<DefaultShiftTiming>;
      Staff: Repository<Staff>;
    };

    const exStaff = await Staff.findOne({ where: { staffId } });
    if (!exStaff) {
      return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));
    }

    if (staffDaysAvailable) {
      await updateStaffDaysAvailable(
        staffId,
        staffDaysAvailable,
        DaysAvailable
      );
    }

    if (defaultShiftTiming) {
      await updateStaffDefaultShiftTiming(
        staffId,
        defaultShiftTiming,
        DefaultShiftTiming
      );
    }

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, STAFF_AVAILABILITY_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchStaffCertifications = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { staffId } = req.params;
    const { staffCertifications } = req.body;

    const { Staff, StaffCertification } = getRepositories(qRunner) as {
      Staff: Repository<Staff>;
      StaffCertification: Repository<StaffCertification>;
    };

    const exStaff = await Staff.findOne({ where: { staffId } });
    if (!exStaff) {
      return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));
    }

    await updateStaffCertifications(
      exStaff,
      staffCertifications,
      StaffCertification
    );

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, STAFF_CERTS_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const deleteStaff = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();
  await qRunner.startTransaction();
  try {
    const staffId = req.params.staffId;

    const { Staff } = getRepositories(qRunner) as {
      Staff: Repository<Staff>;
    };

    const exStaff = await Staff.findOneBy({
      staffId,
    });

    if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

    await Staff.remove(exStaff);

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, STAFF_DELETED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getAllStaffSessions = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    try {
        const {Staff} = getRepositories(qRunner) as {
          Staff: Repository<Staff>
        }

        const staffId = req.params.staffId

        const exStaff = await Staff.findOne({
          where: {
            staffId
          },
          relations: ['staffSessions']
        })

        if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

        const allSessions = exStaff.staffSessions;

        const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

        let sessionData: any[] = [];

        for (const session of allSessions) {
            const newSessionData = session;
            const decryptedIP = await AESHelper.decrypt(
                newSessionData.ipAddress,
                secret_key
            );

            newSessionData.ipAddress = decryptedIP;

            sessionData.push(newSessionData);
        }

        ResponseHelper.success(res, OK, ALL_STAFF_SESSIONS_FETCHED, sessionData);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const revokeStaff = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {Staff, StaffSession} = getRepositories(qRunner) as {
            Staff: Repository<Staff>;
            StaffSession: Repository<StaffSession>;
        };

        const staffId = req.params.staffId;

        const {ipAddress} = req.body;

        const exStaff = await Staff.findOne({
            where: {staffId},
            relations: ["staffSessions"],
        });

        if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

        if (ipAddress) {
            const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

            let exSession: StaffSession | null = null;

            for (const session of exStaff.staffSessions) {
                const decryptedIp = await AESHelper.decrypt(
                    session.ipAddress,
                    secret_key
                );
                if (decryptedIp === ipAddress) {
                    exSession = session;
                    break;
                }
            }

            if (exSession) {
                exSession.isRevoked = true;

                await StaffSession.save(exSession);
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Revoked staff session successfully!");
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const reinstateStaff = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {Staff, StaffSession} = getRepositories(qRunner) as {
            Staff: Repository<Staff>;
            StaffSession: Repository<StaffSession>;
        };

        const staffId = req.params.staffId;

        const {ipAddress} = req.body;

        const exStaff = await Staff.findOne({
            where: {staffId},
            relations: ["staffSessions"],
        });

        if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

        if (ipAddress) {
            const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

            let exSession: StaffSession | null = null;

            for (const session of exStaff.staffSessions) {
                const decryptedIp = await AESHelper.decrypt(
                    session.ipAddress,
                    secret_key
                );
                if (decryptedIp === ipAddress) {
                    exSession = session;
                    break;
                }
            }

            if (exSession) {
                exSession.isRevoked = false;

                await StaffSession.save(exSession);
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Reinstated revoked IP for staff successfully!");
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};