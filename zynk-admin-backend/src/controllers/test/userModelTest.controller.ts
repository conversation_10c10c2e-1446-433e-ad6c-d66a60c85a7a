import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Repository} from "typeorm";
import {getUserModelAssociation, ModelType} from "../../types/pBAC";
import {APIError} from "../../utils/errorHandler";
import {NOT_FOUND, OK} from "../../constants/STATUS_CODES";
import {FormatType} from "../../types/pBAC/formatterTypes";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {SuperAdmin} from "../../models/admin/superadmin.model";
import {PermissionAction} from "../../models/pBAC/PermissionAction";
import log from "../../helpers/system/logger.helper";

export const userModelFunctionsTest = async (req: Request, res: Response, next: NextFunction) => {
    await req.rootQueryRunner.connect();
    try {
        const {SuperAdmin, PermissionAction} = getRepositories(req.rootQueryRunner) as {
            SuperAdmin: Repository<SuperAdmin>;
            PermissionAction: Repository<PermissionAction>;
        };

        const superAdmin = await SuperAdmin.findOne({
            where: {
                username: "DR_ATOMIC"
            },
            relations: getUserModelAssociation(ModelType.ADMIN)
        });
        if (!superAdmin) {
            throw new APIError(NOT_FOUND, "Super Admin not found");
        }
        log.info("Super Admin found", superAdmin.supPermissionAction);
        const result = await superAdmin.getPermissionAction(FormatType.STRING_STRING);
        ResponseHelper.success(res, OK, "Super Admin found", {
            result: result.formattedPermissions,
            // anotherResult: anotherResult
        });
    } catch (error) {
        return next(error);
    }
}