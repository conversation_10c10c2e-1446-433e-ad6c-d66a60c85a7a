import { NextFunction, Request, Response } from "express";
import { SuperAdmin } from "../../models/admin/superadmin.model";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import {
  BAD_REQUEST,
  CONFLICT,
  CREATED,
  OK,
  UNAUTHORIZED,
} from "../../constants/STATUS_CODES";
import { AdminSettings } from "../../models/admin/settings/AdminSettings.model";
import { errorHandler } from "../../utils/errorHandler";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { Not, Repository } from "typeorm";
import argon2 from "argon2";
import {
  clearCookie,
  setCookie,
} from "../../helpers/system/CookieHelper.helper";
import { JWTPayload } from "../../../types/jwt";
import { AESHelper } from "../../helpers/system/AESHelper";
import { isTokenVerified } from "../../helpers/system/token.helper";
import { genNewRefreshToken } from "../../helpers/system/superadmin.helper";
import { SuperSession } from "../../models/admin/supersession.model";
import {
  IP_REVOKED,
  NO_MORE_SUPERADMINS,
  SUPER_ADMIN_ALREADY_EXISTS,
  SUPER_ADMIN_INVALID_MAIL,
  SUPERADMIN_PASS_REQ,
} from "../../constants/admin/err";
import {
  SUPER_ADMIN_CREATED,
  SUPER_ADMIN_FETCHED,
  SUPER_ADMIN_SESSIONS_FETCHED,
} from "../../constants/admin/msg";
import { ACCESS_DENIED_INVALID_PASSWORD } from "../../constants/common/err";
import { Role } from "../../models/pBAC/role/Role.model";
import log from "../../helpers/system/logger.helper";
import { SupPermissionAction } from "../../models/admin/SupAdminPermissionAction.model";
import { FormatType } from "../../types/pBAC/formatterTypes";
import { getUserModelAssociation, ModelType } from "../../types/pBAC";
import { PermissionAction } from "../../models/pBAC/PermissionAction";
import { SupRoleNotFound } from "../../exceptions/controller/admin/impl/SupRoleNotFound";
import { LoginRateLimitService } from "../../services/loginAttempt.service";
import { IP_NOT_FOUND } from "../../constants/security/err";

export const createSuperAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.rootQueryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { AdminSettings, SuperAdmin, Role, SupPermissionAction } =
      getRepositories(qRunner) as {
        AdminSettings: Repository<AdminSettings>;
        SuperAdmin: Repository<SuperAdmin>;
        Role: Repository<Role>;
        SupPermissionAction: Repository<SupPermissionAction>;
      };

    const adminRole = await Role.findOne({
      where: { name: "Admin" },
      relations: [
        "rolePermissionActions",
        "rolePermissionActions.permissionAction",
        "rolePermissionActions.permissionAction.permission",
        "rolePermissionActions.permissionAction.action",
      ],
      order: {
        name: "ASC",
      },
    });
    if (!adminRole) throw new SupRoleNotFound();

    const superSetting = await AdminSettings.find();

    if (superSetting.length < 1) {
      const newSetting = AdminSettings.create({
        allowNewSuperAdmin: false,
      });
      await AdminSettings.save(newSetting);
    } else {
      const exSetting = superSetting[0];

      if (!exSetting.allowNewSuperAdmin)
        return next(errorHandler(CONFLICT, NO_MORE_SUPERADMINS));
    }

    const { password, ...data } = req.body;

    const exSuperAdmin = await SuperAdmin.findOne({
      where: [{ username: data.username }, { emailAddress: data.emailAddress }],
    });

    if (exSuperAdmin) {
      return next(errorHandler(CONFLICT, SUPER_ADMIN_ALREADY_EXISTS));
    }

    const hashedPass = await argon2.hash(password);

    const newSuperAdmin = SuperAdmin.create({
      ...data,
      password: hashedPass,
      role: adminRole,
    });

    const savedSuperAdmin = await SuperAdmin.save(newSuperAdmin);

    if (!savedSuperAdmin) {
      throw new Error("Failed to save SuperAdmin");
    }

    const newSupPermissionAction: SupPermissionAction[] = [];
    for (const rolePermAction of adminRole.rolePermissionActions) {
      const permAction = SupPermissionAction.create({
        superAdmin: savedSuperAdmin[0],
        permissionAction: rolePermAction.permissionAction,
      });
      newSupPermissionAction.push(permAction);
    }

    await SupPermissionAction.save(newSupPermissionAction);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, CREATED, SUPER_ADMIN_CREATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    log.error("Error in creating super admin:", error);
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const loginSuperAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.rootQueryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { SuperAdmin, SuperSession } = getRepositories(qRunner) as {
      SuperAdmin: Repository<SuperAdmin>;
      SuperSession: Repository<SuperSession>;
    };

    const { emailAddress, password } = req.body;

    const exSuperAdmin = await SuperAdmin.findOne({
      where: { emailAddress },
      relations: ["superSessions"],
    });

    // const exSuperAdmin = await SuperAdmin.findOne({
    //     where: {emailAddress},
    //     relations: ["superSessions", ...getUserModelAssociation(ModelType.ADMIN)],
    // });

    const ip = req.clientIP;

    if (!ip) return next(errorHandler(BAD_REQUEST, IP_NOT_FOUND));

    if (!exSuperAdmin) {
      await LoginRateLimitService.checkAndLogAttempt(
        qRunner,
        ip,
        emailAddress,
        "SUPERADMIN",
        req.userAgent,
        false,
        undefined,
        "USER_NOT_FOUND"
      );
      return next(errorHandler(BAD_REQUEST, SUPER_ADMIN_INVALID_MAIL));
    }

    const verified = await argon2.verify(exSuperAdmin.password, password);

    if (!verified) {
      await LoginRateLimitService.checkAndLogAttempt(
        qRunner,
        ip,
        emailAddress,
        "SUPERADMIN",
        req.userAgent,
        false,
        undefined,
        ACCESS_DENIED_INVALID_PASSWORD
      );
      return next(errorHandler(UNAUTHORIZED, ACCESS_DENIED_INVALID_PASSWORD));
    }

    if (ip) {
      const allSessions = exSuperAdmin.superSessions;
      const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

      let exSession: SuperSession | null = null;

      for (const session of allSessions) {
        const decryptedIp = await AESHelper.decrypt(
          session.ipAddress,
          secret_key
        );
        if (decryptedIp === ip) {
          exSession = session;
          break;
        }
      }

      if (!exSession) {
        const newSession = SuperSession.create({
          superAdmin: exSuperAdmin,
          ipAddress: await AESHelper.encrypt(ip, secret_key),
          userAgent: req.userAgent,
          lastLogin: new Date(),
        });

        await SuperSession.save(newSession);
      } else {
        if (exSession.isRevoked) {
          await LoginRateLimitService.checkAndLogAttempt(
            qRunner,
            ip,
            emailAddress,
            "SUPERADMIN",
            req.userAgent,
            false,
            undefined,
            IP_REVOKED
          );
          return next(errorHandler(UNAUTHORIZED, IP_REVOKED));
        }

        if (exSession.userAgent && exSession.userAgent !== req.userAgent) {
          exSession.userAgent = req.userAgent;
        }

        exSession.lastLogin = new Date();

        await SuperSession.save(exSession);
      }
    }

    await LoginRateLimitService.checkAndLogAttempt(
      qRunner,
      ip,
      emailAddress,
      "SUPERADMIN",
      req.userAgent,
      true,
      exSuperAdmin.superAdminId
    );

    const payload: JWTPayload = {
      userId: exSuperAdmin.superAdminId,
      domainName: (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
      isTenant: false,
      inSuper: true,
    };
    setCookie(res, payload);

    const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

    if (!exSuperAdmin.refreshToken) {
      exSuperAdmin.refreshToken = await genNewRefreshToken(payload, secret_key);

      await SuperAdmin.save(exSuperAdmin);
    } else {
      const decrypted_refreshToken = await AESHelper.decrypt(
        exSuperAdmin.refreshToken,
        secret_key
      );
      const tokenVerif = isTokenVerified(decrypted_refreshToken, true);

      if (!tokenVerif) {
        exSuperAdmin.refreshToken = await genNewRefreshToken(
          payload,
          secret_key
        );

        await SuperAdmin.save(exSuperAdmin);
      }
    }
    const responseData: any = {
      userName: exSuperAdmin.username,
      // permissionAction: (await exSuperAdmin.getPermissionAction(FormatType.STRING_ARR_STRING)).formattedPermissions
    };
    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, "Logged in successfully!", responseData);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchSuperAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.rootQueryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { SuperAdmin, PermissionAction, SupPermissionAction } =
      getRepositories(qRunner) as {
        SuperAdmin: Repository<SuperAdmin>;
        PermissionAction: Repository<PermissionAction>;
        SupPermissionAction: Repository<SupPermissionAction>;
      };

    const superAdminId = req.params.superAdminId;

    const {
      oldPassword,
      newPassword,
      emailAddress,
      username,
      superAdminPermissionAction,
    } = req.body;

    if (!oldPassword)
      return next(errorHandler(UNAUTHORIZED, SUPERADMIN_PASS_REQ));

    const exSuperAdmin = await SuperAdmin.findOne({
      where: { superAdminId },
      relations: getUserModelAssociation(ModelType.ADMIN),
    });

    if (!exSuperAdmin)
      return next(errorHandler(BAD_REQUEST, SUPER_ADMIN_INVALID_MAIL));

    const verified = await argon2.verify(exSuperAdmin.password, oldPassword);

    if (!verified)
      return next(errorHandler(UNAUTHORIZED, ACCESS_DENIED_INVALID_PASSWORD));

    if (newPassword) {
      exSuperAdmin.password = await argon2.hash(newPassword);
    }

    if (emailAddress) {
      const anotherAdmin = await SuperAdmin.findOne({
        where: {
          emailAddress,
          superAdminId: Not(exSuperAdmin.superAdminId),
        },
      });

      if (anotherAdmin)
        return next(errorHandler(CONFLICT, SUPER_ADMIN_ALREADY_EXISTS));

      exSuperAdmin.emailAddress = emailAddress;
    }

    if (username) {
      const anotherAdmin = await SuperAdmin.findOne({
        where: {
          username,
          superAdminId: Not(exSuperAdmin.superAdminId),
        },
      });

      if (anotherAdmin)
        return next(errorHandler(CONFLICT, SUPER_ADMIN_ALREADY_EXISTS));

      exSuperAdmin.username = username;
    }
    await SuperAdmin.save(exSuperAdmin);
    if (superAdminPermissionAction)
      await exSuperAdmin.updatePermissionAction(
        superAdminPermissionAction,
        PermissionAction,
        SupPermissionAction
      );
    await qRunner.commitTransaction();

    ResponseHelper.success(
      res,
      OK,
      "Super admin details updated successfully!"
    );
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const deleteSuperAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.rootQueryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { SuperAdmin } = getRepositories(qRunner) as {
      SuperAdmin: Repository<SuperAdmin>;
    };

    const superAdminId = req.params.superAdminId;

    const { password } = req.body;

    if (!password) return next(errorHandler(UNAUTHORIZED, SUPERADMIN_PASS_REQ));

    const exSuperAdmin = await SuperAdmin.findOne({
      where: { superAdminId },
    });

    if (!exSuperAdmin)
      return next(errorHandler(BAD_REQUEST, SUPER_ADMIN_INVALID_MAIL));

    const verified = await argon2.verify(exSuperAdmin.password, password);

    if (!verified)
      return next(errorHandler(UNAUTHORIZED, ACCESS_DENIED_INVALID_PASSWORD));

    await SuperAdmin.remove(exSuperAdmin);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, "Super admin deleted successfully!");
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getAllSuperAdmins = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.rootQueryRunner;

  await qRunner.connect();
  try {
    const { SuperAdmin } = getRepositories(qRunner) as {
      SuperAdmin: Repository<SuperAdmin>;
    };

    const allSuperAdmins = await SuperAdmin.find();

    let responseData = [];

    for (const superAdmin of allSuperAdmins) {
      const { password, refreshToken, ...data } = superAdmin;

      const superAdminObj = {
        ...data,
      };

      responseData.push(superAdminObj);
    }

    ResponseHelper.success(res, OK, SUPER_ADMIN_FETCHED, responseData);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getAllSessions = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.rootQueryRunner;

  await qRunner.connect();
  try {
    const { SuperAdmin } = getRepositories(qRunner) as {
      SuperAdmin: Repository<SuperAdmin>;
    };

    const superAdminId = req.params.superAdminId;

    const exSuperAdmin = await SuperAdmin.findOne({
      where: { superAdminId },
      relations: ["superSessions"],
    });

    if (!exSuperAdmin)
      return next(errorHandler(BAD_REQUEST, SUPER_ADMIN_INVALID_MAIL));

    const allSessions = exSuperAdmin.superSessions;

    const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

    let sessionData: any[] = [];

    for (const session of allSessions) {
      const newSessionData = session;
      newSessionData.ipAddress = await AESHelper.decrypt(
        newSessionData.ipAddress,
        secret_key
      );

      sessionData.push(newSessionData);
    }

    ResponseHelper.success(res, OK, SUPER_ADMIN_SESSIONS_FETCHED, sessionData);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const revokeSuperAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.rootQueryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { SuperAdmin, SuperSession } = getRepositories(qRunner) as {
      SuperAdmin: Repository<SuperAdmin>;
      SuperSession: Repository<SuperSession>;
    };

    const { emailAddress, password, ipAddress } = req.body;

    const exSuperAdmin = await SuperAdmin.findOne({
      where: { emailAddress },
      relations: ["superSessions"],
    });

    if (!exSuperAdmin)
      return next(errorHandler(BAD_REQUEST, SUPER_ADMIN_INVALID_MAIL));

    const verified = await argon2.verify(exSuperAdmin.password, password);

    if (!verified)
      return next(errorHandler(UNAUTHORIZED, ACCESS_DENIED_INVALID_PASSWORD));

    if (ipAddress) {
      const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

      let exSession: SuperSession | null = null;

      for (const session of exSuperAdmin.superSessions) {
        const decryptedIp = await AESHelper.decrypt(
          session.ipAddress,
          secret_key
        );
        if (decryptedIp === ipAddress) {
          exSession = session;
          break;
        }
      }

      if (exSession) {
        exSession.isRevoked = true;

        await SuperSession.save(exSession);
      }
    }

    await qRunner.commitTransaction();

    ResponseHelper.success(
      res,
      OK,
      "Revoked super admin session successfully!"
    );
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const reinstateSuperAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.rootQueryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { SuperAdmin, SuperSession } = getRepositories(qRunner) as {
      SuperAdmin: Repository<SuperAdmin>;
      SuperSession: Repository<SuperSession>;
    };

    const { emailAddress, password, ipAddress } = req.body;

    const exSuperAdmin = await SuperAdmin.findOne({
      where: { emailAddress },
      relations: ["superSessions"],
    });

    if (!exSuperAdmin)
      return next(errorHandler(BAD_REQUEST, SUPER_ADMIN_INVALID_MAIL));

    const verified = await argon2.verify(exSuperAdmin.password, password);

    if (!verified)
      return next(errorHandler(UNAUTHORIZED, ACCESS_DENIED_INVALID_PASSWORD));

    if (ipAddress) {
      const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

      let exSession: SuperSession | null = null;

      for (const session of exSuperAdmin.superSessions) {
        const decryptedIp = await AESHelper.decrypt(
          session.ipAddress,
          secret_key
        );
        if (decryptedIp === ipAddress) {
          exSession = session;
          break;
        }
      }

      if (exSession) {
        exSession.isRevoked = false;

        await SuperSession.save(exSession);
      }
    }

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, "Reinstated revoked IP successfully!");
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const logoutSuperAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    clearCookie(res);

    ResponseHelper.success(res, OK, "Logged out successfully!");
  } catch (error) {
    next(error);
  }
};
