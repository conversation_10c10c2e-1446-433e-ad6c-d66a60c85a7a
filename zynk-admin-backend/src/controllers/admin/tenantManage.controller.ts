import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {DataSource, Not, Repository} from "typeorm";
import {Tenant} from "../../models/admin/Tenant/tenant.model";
import {TenantAuth} from "../../models/admin/Tenant/tenantAuth.model";
import {CompanyInformation} from "../../models/admin/Tenant/companyinformation.model";
import {Subscription} from "../../models/subscription/subscriptions.model";
import {SubscriptionTier} from "../../models/subscription/subTier/subscriptiontier.model";
import {errorHandler} from "../../utils/errorHandler";
import {BAD_REQUEST, CONFLICT, CREATED, NOT_FOUND, OK,} from "../../constants/STATUS_CODES";
import {
    SUBSCRIPTION_NOT_FOUND,
    TENANT_ALREADY_EXISTS,
    TENANT_DB_EXISTS,
    TENANT_DB_NOT_EXISTS,
} from "../../constants/admin/err";
import argon from "argon2";
import {getAllTenantModels} from "../../config/entity-config";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {
    ALL_TENS_FETCHED,
    ALL_TENS_SESSIONS_FETCHED,
    TENANT_CREATED,
    TENANT_DELETED,
    TENANT_FETCHED,
    TENANT_UPDATED,
} from "../../constants/admin/msg";
import {TENANT_NOT_EXIST} from "../../constants/middleware/err";
import {AESHelper} from "../../helpers/system/AESHelper";
import {TenantSession} from "../../models/admin/Tenant/tenantsession.model";
import {createCompanyRecordsInTenant} from "../../helpers/system/tenantManage.helper";
import {createTenantFromTemplate} from "../../config/templateConfig";
import {Role} from "../../models/pBAC/role/Role.model";
import {TenantRoleNotFound} from "../../exceptions/controller/tenant/admin/impl/TenantRoleNotFound";
import {TenantPermissionAction} from "../../models/admin/Tenant/TenantPermissionAction.model";
import {TenantAlreadyExistException} from "../../exceptions/controller/tenant/admin/impl/TenantAlreadyExistException";


// #TODO: During the creation of a tenant just add add it to the blocklist with the modifier subscription.
export const createTenant = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {
            Tenant,
            TenantAuth,
            CompanyInformation,
            Subscription,
            SubscriptionTier,
            Role,
            TenantPermissionAction,
        } = getRepositories(qRunner) as {
            Tenant: Repository<Tenant>;
            TenantAuth: Repository<TenantAuth>;
            CompanyInformation: Repository<CompanyInformation>;
            Subscription: Repository<Subscription>;
            SubscriptionTier: Repository<SubscriptionTier>;
            Role: Repository<Role>;
            TenantPermissionAction: Repository<TenantPermissionAction>;
        };

        const tenantAdminRole = await Role.findOne({
            where: {name: "TenantAdmin"},
            relations: [
                "rolePermissionActions",
                "rolePermissionActions.permissionAction",
                "rolePermissionActions.permissionAction.permission",
                "rolePermissionActions.permissionAction.action",
            ],
            order: {
                name: "ASC",
            },
        });
        if (!tenantAdminRole) throw new TenantRoleNotFound();

        const {
            name,
            subDomain,
            emailAddress,
            phoneNumber,
            pin,
            regNo,
            companyName,
            addrLine1,
            addrLine2,
            city,
            state,
            postalCode,
            country,
            taxIdentificationNumber,
            password,
            roles,
            subscription,
            status,
            businessType,
            description,
            businessAlias,
            regDocument,
            logoUrl,
        } = req.body;

        const exTenant = await Tenant.findOne({
            where: [{name}, {emailAddress}, {subDomain}],
        });

        if (exTenant) return next(new TenantAlreadyExistException(
            name === exTenant.name ? name : null,
            emailAddress === exTenant.emailAddress ? emailAddress : null,
            subDomain === exTenant.subDomain ? subDomain : null,
        ))
        const exSubscriptionTier = await SubscriptionTier.findOne({
            where: {
                name: subscription,
            },
        });

        if (!exSubscriptionTier)
            return next(errorHandler(NOT_FOUND, SUBSCRIPTION_NOT_FOUND));

        const hashedPass = await argon.hash(password);
        const hashedPin = await argon.hash(pin.toString())

        const now = new Date();
        const validityDays = exSubscriptionTier.validityDuration;

        const activeTill = new Date(
            now.getTime() + validityDays * 24 * 60 * 60 * 1000
        );

        const newTenant = Tenant.create({
            name,
            emailAddress,
            subDomain,
            phoneNumber,
            status,
            activeFrom: now,
            activeTill,
            tenantAuth: TenantAuth.create({
                password: hashedPass,
                pin: hashedPin,
            }),
            tenantInfo: CompanyInformation.create({
                regNumber: regNo,
                name: companyName,
                businessType,
                addrLine1,
                addrLine2,
                city,
                state,
                postalCode,
                country,
                taxIDNumber: taxIdentificationNumber,
                description,
                businessAlias,
                regDocument,
                logoUrl,
            }),
            role: tenantAdminRole,
        });

        await Tenant.save(newTenant);

        // const newTenantPermissionAction: TenantPermissionAction[] = [];
        // for (const rolePermAction of tenantAdminRole.rolePermissionActions) {
        //     const permAction = TenantPermissionAction.create({
        //         tenant: newTenant,
        //         permissionAction: rolePermAction.permissionAction,
        //     });
        //     newTenantPermissionAction.push(permAction);
        // }
        //
        // await TenantPermissionAction.save(newTenantPermissionAction);
        //
        // const exSubscr = await Subscription.findOne({
        //     where: {
        //         subTier: {subTierId: exSubscriptionTier.subTierId},
        //     },
        //     relations: ["tenantSubscriptions"],
        // });
        //
        // if (exSubscr) {
        //     exSubscr.tenant.push(newTenant);
        //     await Subscription.save(exSubscr);
        // } else {
        //     const newSubscription = Subscription.create({
        //         subTier: exSubscriptionTier,
        //         tenant: [newTenant],
        //     });
        //     await Subscription.save(newSubscription);
        // }

        const dbCreated = await createTenantFromTemplate(subDomain);

        if (!dbCreated) {
            return next(errorHandler(CONFLICT, TENANT_DB_EXISTS));
        }

        const TenantDS = new DataSource({
            type: "postgres",
            host: process.env.DB_HOSTNAME,
            port: parseInt(process.env.DB_PORT!) || 5434,
            username: process.env.POSTGRES_USER,
            password: process.env.POSTGRES_PASSWORD,
            database: subDomain,
            synchronize: false,
            logging: false,
            entities: getAllTenantModels(),
            subscribers: [],
            migrations: [],
        });

        if (!TenantDS.isInitialized) {
            await TenantDS.initialize();
        }

        const tenantQRunner = TenantDS.createQueryRunner();

        await createCompanyRecordsInTenant(
            tenantQRunner,
            next,
            {
                ...newTenant.tenantInfo,
            },
            newTenant.tenantId
        );

        await qRunner.commitTransaction();

        ResponseHelper.success(res, CREATED, TENANT_CREATED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchTenant = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {
            Tenant,
            TenantAuth,
            CompanyInformation,
            Subscription,
            SubscriptionTier,
        } = getRepositories(qRunner) as {
            Tenant: Repository<Tenant>;
            TenantAuth: Repository<TenantAuth>;
            CompanyInformation: Repository<CompanyInformation>;
            Subscription: Repository<Subscription>;
            SubscriptionTier: Repository<SubscriptionTier>;
        };

        const {
            name,
            subDomain,
            emailAddress,
            phoneNumber,
            regNo,
            companyName,
            addrLine1,
            addrLine2,
            city,
            state,
            postalCode,
            country,
            taxIdentificationNumber,
            password,
            businessType,
            roles,
            subscription,
            status,
            activeFrom,
            activeTill,
            description,
            businessAlias,
            regDocument,
            logoUrl,
        } = req.body;

        const tenantId = req.params.tenantId;

        const tenantToPatch = await Tenant.findOne({
            where: {
                tenantId,
            },
            relations: [
                "tenantAuth",
                "tenantInfo",
                "subscription",
                "subscription.subTier",
            ],
        });

        if (!tenantToPatch) return next(errorHandler(NOT_FOUND, TENANT_NOT_EXIST));

        const exTenant = await Tenant.findOne({
            where: [
                {name, tenantId: Not(tenantId)},
                {emailAddress, tenantId: Not(tenantId)},
                {subDomain, tenantId: Not(tenantId)},
            ],
        });

        if (exTenant) return next(errorHandler(CONFLICT, TENANT_ALREADY_EXISTS));

        if (password) {
            const hashedPass = await argon.hash(password);
            tenantToPatch.tenantAuth.password = hashedPass;
        }

        // if (
        //     subscription &&
        //     subscription !== tenantToPatch.subscription.subTier.name
        // ) {
        //     const newSubTier = await SubscriptionTier.findOne({
        //         where: {
        //             name: subscription,
        //         },
        //     });
        //
        //     if (!newSubTier)
        //         return next(errorHandler(NOT_FOUND, SUBSCRIPTION_NOT_FOUND));
        //
        //     tenantToPatch.subscription.subTier = newSubTier;
        // }

        const tenantInfoFields = {
            regNumber: regNo,
            companyName: companyName,
            addrLine1,
            addrLine2,
            city,
            state,
            postalCode,
            country,
            logoUrl,
            taxIDNumber: taxIdentificationNumber,
            description,
            businessAlias,
            regDocument,
            businessType,
        };

        const tenantFields = {
            name,
            emailAddress,
            subDomain,
            phoneNumber,
            status,
            activeFrom,
            activeTill,
        };

        Object.entries(tenantInfoFields).forEach(([key, value]) => {
            if (value !== undefined) {
                (tenantToPatch.tenantInfo as any)[key] = value;
            }
        });

        Object.entries(tenantFields).forEach(([key, value]) => {
            if (value !== undefined) {
                (tenantToPatch as any)[key] = value;
            }
        });

        await Tenant.save(tenantToPatch);

        if (subDomain) {
            const tempDataSource = new DataSource({
                type: "postgres",
                host: process.env.DB_HOSTNAME,
                port: parseInt(process.env.DB_PORT!) || 5434,
                username: process.env.POSTGRES_USER,
                password: process.env.POSTGRES_PASSWORD,
                database: "postgres",
                logging: false,
            });

            await tempDataSource.initialize();

            const result = await tempDataSource.query(
                `SELECT datname
                 FROM pg_catalog.pg_database
                 WHERE datname = '${tenantToPatch.subDomain}'`
            );

            if (result.length > 0) {
                await tempDataSource.query(
                    `ALTER DATABASE "${tenantToPatch.subDomain}" RENAME TO "${subDomain}"`
                );
                await tempDataSource.destroy();
            } else {
                await tempDataSource.destroy();
                return next(errorHandler(CONFLICT, TENANT_DB_NOT_EXISTS));
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, TENANT_UPDATED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteTenant = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {
            Tenant,
            TenantAuth,
            CompanyInformation,
            Subscription,
            SubscriptionTier,
        } = getRepositories(qRunner) as {
            Tenant: Repository<Tenant>;
            TenantAuth: Repository<TenantAuth>;
            CompanyInformation: Repository<CompanyInformation>;
            Subscription: Repository<Subscription>;
            SubscriptionTier: Repository<SubscriptionTier>;
        };

        const tenantId = req.params.tenantId;

        const tenantToDelete = await Tenant.findOne({
            where: {tenantId},
            relations: [
                "tenantAuth",
                "tenantInfo",
                "subscriptions",
                "subscriptions.subTier",
            ],
        });

        if (!tenantToDelete) {
            return next(errorHandler(NOT_FOUND, TENANT_NOT_EXIST));
        }

        // Remove tenant's subscription association
        // if (tenantToDelete.subscription) {
        //     const subscription = await Subscription.findOne({
        //         where: {subscriptionId: tenantToDelete.subscription.subscriptionId},
        //         relations: ["tenantSubscriptions"],
        //     });
        //
        //     if (subscription) {
        //         subscription.tenant =
        //             subscription.tenant.filter(
        //                 (tenant) => tenant.tenantId !== tenantId
        //             );
        //         await Subscription.save(subscription);
        //     }
        // }

        // Remove tenant and related entities
        await Tenant.remove(tenantToDelete);

        // Drop tenant's database
        const tempDataSource = new DataSource({
            type: "postgres",
            host: process.env.DB_HOSTNAME,
            port: parseInt(process.env.DB_PORT!) || 5434,
            username: process.env.POSTGRES_USER,
            password: process.env.POSTGRES_PASSWORD,
            database: "postgres",
            logging: false,
        });

        await tempDataSource.initialize();

        const result = await tempDataSource.query(
            `SELECT datname
             FROM pg_catalog.pg_database
             WHERE datname = '${tenantToDelete.subDomain}'`
        );

        if (result.length > 0) {
            await tempDataSource.query(`DROP DATABASE "${tenantToDelete.subDomain}"`);
        }

        await tempDataSource.destroy();

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, TENANT_DELETED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getAllTenants = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    try {
        const {Tenant} = getRepositories(qRunner) as {
            Tenant: Repository<Tenant>;
        };

        const allTenants = await Tenant.find();

        let responseData = [];

        for (const tenant of allTenants) {
            const {...data} = tenant;

            const tenantObj = {
                ...data,
            };

            responseData.push(tenantObj);
        }

        ResponseHelper.success(res, OK, ALL_TENS_FETCHED, responseData);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getTenant = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    try {
        const {Tenant} = getRepositories(qRunner) as {
            Tenant: Repository<Tenant>;
        };

        const tenantId = req.params.tenantId;

        const exTenant = await Tenant.findOneBy({
            tenantId,
        });

        if (!exTenant) return next(errorHandler(NOT_FOUND, TENANT_NOT_EXIST));

        ResponseHelper.success(res, OK, TENANT_FETCHED, exTenant);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getAllTenantSessions = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    try {
        const {Tenant} = getRepositories(qRunner) as {
            Tenant: Repository<Tenant>;
        };

        const tenantId = req.params.tenantId;

        const exTenant = await Tenant.findOne({
            where: {tenantId},
            relations: ["tenantSessions"],
        });

        if (!exTenant) return next(errorHandler(BAD_REQUEST, TENANT_NOT_EXIST));

        const allSessions = exTenant.tenantSessions;

        const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

        let sessionData: any[] = [];

        for (const session of allSessions) {
            const newSessionData = session;
            const decryptedIP = await AESHelper.decrypt(
                newSessionData.ipAddress,
                secret_key
            );

            newSessionData.ipAddress = decryptedIP;

            sessionData.push(newSessionData);
        }

        ResponseHelper.success(res, OK, ALL_TENS_SESSIONS_FETCHED, sessionData);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const revokeTenant = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {Tenant, TenantSession} = getRepositories(qRunner) as {
            Tenant: Repository<Tenant>;
            TenantSession: Repository<TenantSession>;
        };

        const tenantId = req.params.tenantId;

        const {ipAddress} = req.body;

        const exTenant = await Tenant.findOne({
            where: {tenantId},
            relations: ["tenantSessions"],
        });

        if (!exTenant) return next(errorHandler(BAD_REQUEST, TENANT_NOT_EXIST));

        if (ipAddress) {
            const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

            let exSession: TenantSession | null = null;

            for (const session of exTenant.tenantSessions) {
                const decryptedIp = await AESHelper.decrypt(
                    session.ipAddress,
                    secret_key
                );
                if (decryptedIp === ipAddress) {
                    exSession = session;
                    break;
                }
            }

            if (exSession) {
                exSession.isRevoked = true;

                await TenantSession.save(exSession);
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Revoked tenant session successfully!");
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const reinstateTenant = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {Tenant, TenantSession} = getRepositories(qRunner) as {
            Tenant: Repository<Tenant>;
            TenantSession: Repository<TenantSession>;
        };

        const tenantId = req.params.tenantId;

        const {ipAddress} = req.body;

        const exTenant = await Tenant.findOne({
            where: {tenantId},
            relations: ["tenantSessions"],
        });

        if (!exTenant) return next(errorHandler(BAD_REQUEST, TENANT_NOT_EXIST));

        if (ipAddress) {
            const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

            let exSession: TenantSession | null = null;

            for (const session of exTenant.tenantSessions) {
                const decryptedIp = await AESHelper.decrypt(
                    session.ipAddress,
                    secret_key
                );
                if (decryptedIp === ipAddress) {
                    exSession = session;
                    break;
                }
            }

            if (exSession) {
                exSession.isRevoked = false;

                await TenantSession.save(exSession);
            }
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Reinstated revoked IP for tenant successfully!");
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};