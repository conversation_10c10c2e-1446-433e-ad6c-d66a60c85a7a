import {NextFunction, Request, Response} from "express";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {OK} from "../../constants/STATUS_CODES";
import {LockedType, ResponseType} from "../../types/system";

export const listResponseType = async (req: Request, res: Response, next: NextFunction) => {
    try {
        ResponseHelper.success(res, OK, "Response types fetched successfully", Object.values(ResponseType))
    } catch (error) {
        next(error);
    }
}

export const listLockType = async (req: Request, res: Response, next: NextFunction) => {
    try {
        ResponseHelper.success(res, OK, "Lock type fetched successfully", Object.values(LockedType))
    } catch (error) {
        next(error);
    }
}