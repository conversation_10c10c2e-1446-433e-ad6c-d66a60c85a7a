import {NextFunction, Request, Response} from "express";
import log from "../../../helpers/system/logger.helper";
import {getRepositories} from "../../../helpers/system/RepositoryHelper.helper";
import {TenantBlockList} from "../../../models/admin/settings/tenantBlockList/TenantBlockList";
import {In, Repository} from "typeorm";
import {ResponseHelper} from "../../../helpers/system/ResponseHelper.helper";
import {OK} from "../../../constants/STATUS_CODES";
import {
    blockTenantSchemaType,
    createBlockTypeSchemaType,
    patchBlockTypeSchemaType,
    unblockTenantSchemaType
} from "../../../validation/admin/settings/blockTenant.validation";
import {Tenant} from "../../../models/admin/Tenant/tenant.model";
import {TenantNotFoundException} from "../../../exceptions/controller/tenant/admin/impl/TenantNotFoundException";
import {
    blockListResTypeConfig,
    blockTypeResTypeConfig,
    createBlockType,
    createTenantBlockList
} from "../../../helpers/settings/admin/blocker/blockList.helper";
import {BlockTypeNotFoundEx} from "../../../exceptions/controller/admin/settings/impl/BlockTypeNotFoundEx";
import {BlockType} from "../../../models/admin/settings/tenantBlockList/BlockType";
import {getRecords, getRecordsAd} from "../../../utils/Common.util";
import {buildWhereClause} from "../../../utils/whereBuilder";
import {LockedType, ResponseType} from "../../../types/system";
import {NoManualBlockFoundEx} from "../../../exceptions/controller/admin/settings/impl/NoManualBlockFoundEx";
import {BlockListNotFoundEx} from "../../../exceptions/controller/admin/settings/impl/BlockListNotFoundEx";
import {
    CannotModifySystemReserveEx
} from "../../../exceptions/controller/admin/settings/impl/CannotModifySystemReserveEx";
import {
    CannotDeleteAssociatedRecordsEx
} from "../../../exceptions/controller/admin/settings/impl/CannotDeleteAssociatedRecordsEx";

//TODO: After Completing Subscription test these api's

// Tenant BlockList Controllers
export const listBlockedTenants = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[viewAllBlockedTenants] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);
    try {
        const {responseType, ...rest} = req.query as unknown as {
            responseType: ResponseType,
            [key: string]: any
        };
        ResponseHelper.success(res, OK, "Blocked tenants fetched successfully", await getRecordsAd((getRepositories(qRunner) as {
            TenantBlockList: Repository<TenantBlockList>
        }).TenantBlockList, buildWhereClause(rest), blockListResTypeConfig, responseType))
    } catch (error) {
        log.error("[viewAllBlockedTenants] Error viewing blocked tenants:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[viewAllBlockedTenants] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
}

export const blockTenant = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[blockTenant] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("[blockTenant] Transaction started.", null, process.env.SUPERADMIN_DB);
    try {
        const requestBody: blockTenantSchemaType = req.body;
        const {TenantBlockList, Tenant, BlockType} = getRepositories(qRunner) as {
            TenantBlockList: Repository<TenantBlockList>;
            Tenant: Repository<Tenant>;
            BlockType: Repository<BlockType>
        }

        const tenant = await Tenant.findOne({where: {tenantId: requestBody.tenantId}});
        if (!tenant) throw new TenantNotFoundException();

        let blockType: BlockType | undefined = undefined;
        if (
            requestBody.isAdvanced &&
            requestBody.blockTypeName &&
            requestBody.blockMessage
        ) blockType = await createBlockType(BlockType, requestBody.blockTypeName, requestBody.blockMessage);
        else {
            blockType = (await getRecords(BlockType, buildWhereClause({blockTypeId: requestBody.blockTypeId}))).result as BlockType;
        }
        if (!blockType) throw new BlockTypeNotFoundEx();

        const createBlockList = await createTenantBlockList(TenantBlockList, blockType, tenant);

        await qRunner.commitTransaction();
        log.debug("[blockTenant] Transaction committed.", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Tenant blocked successfully", await getRecordsAd((getRepositories(qRunner) as {
            TenantBlockList: Repository<TenantBlockList>
        }).TenantBlockList, buildWhereClause({tenantBlockListId: createBlockList.tenantBlockListId}), blockListResTypeConfig, ResponseType.FULL))
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[blockTenant] Transaction rolled back.", null, process.env.SUPERADMIN_DB);
        log.error("[blockTenant] Error blocking tenant:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[blockTenant] Query Runner Released.", null, process.env.SUPERADMIN_DB);

    }
}

export const unblockTenant = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[unblockTenant] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("[unblockTenant] Transaction started.", null, process.env.SUPERADMIN_DB);
    try {
        const {TenantBlockList, Tenant} = getRepositories(qRunner) as {
            TenantBlockList: Repository<TenantBlockList>;
            Tenant: Repository<Tenant>;
        }
        const requestQuery: unblockTenantSchemaType = req.query;
        const lockTypeWhere = {
            lockType: In([
                LockedType.NOT_SYSTEM_MANAGER,
                LockedType.TENANT_BLOCKER_ONLY,
            ])
        }

        if (requestQuery.isAdvanced) {
            const tenant = await Tenant.findOne({where: {tenantId: requestQuery.tenantId}})

            if (!tenant) throw new TenantNotFoundException();
            const where = {
                tenant: {tenantId: requestQuery.tenantId},
                ...lockTypeWhere
            }
            const result = await getRecords(TenantBlockList, where)

            if (result.isArray) {
                if (result.isEmpty) throw new NoManualBlockFoundEx();
                for (const blockList of result.result) await TenantBlockList.delete({tenantBlockListId: blockList.tenantBlockListId});
            } else await TenantBlockList.delete({tenantBlockListId: result.result.tenantBlockListId});

        } else {
            const where = {
                tenantBlockListId: requestQuery.tenantBlockListId,
                ...lockTypeWhere
            }
            const result = (await getRecords(TenantBlockList, where));

            if (result.isEmpty && result.isArray) throw new BlockListNotFoundEx();
            await TenantBlockList.delete({tenantBlockListId: result.result.tenantBlockListId});
        }

        await qRunner.commitTransaction();
        log.debug("[unblockTenant] Transaction committed.", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Tenant unblocked successfully (NOTE: only applied to tenant blocker which is managed by not_system_manager or tenant_blocker_only)");

    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[unblockTenant] Transaction rolled back.", null, process.env.SUPERADMIN_DB);
        log.error("[unblockTenant] Error unblocking tenant:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[unblockTenant] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
}

// BlockType Controllers
export const listAllBlockTypes = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;
    await qRunner.connect()
    log.debug("[viewAllBlockTypes] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);
    try {
        const {responseType, ...rest} = req.query as unknown as {
            responseType: ResponseType,
            [key: string]: any
        }
        ResponseHelper.success(res, OK, "Block types fetched successfully", await getRecordsAd((getRepositories(qRunner) as {
            BlockType: Repository<BlockType>
        }).BlockType, buildWhereClause(rest), blockTypeResTypeConfig, responseType))
    } catch (error) {
        log.error("[viewAllBlockTypes] Error viewing block types:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[viewAllBlockTypes] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
}

export const createBlockedType = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[createBlockType] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("[createBlockType] Transaction started.", null, process.env.SUPERADMIN_DB);
    try {
        const {BlockType} = getRepositories(qRunner) as {
            BlockType: Repository<BlockType>
        }
        const requestBody: createBlockTypeSchemaType = req.body;
        const blockType = await createBlockType(BlockType, requestBody.name, requestBody.blockMessage, requestBody.isActive);

        await qRunner.commitTransaction();
        log.debug("[createBlockType] Transaction committed.", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Block type created successfully", await getRecordsAd((getRepositories(qRunner) as {
            BlockType: Repository<BlockType>
        }).BlockType, buildWhereClause({blockTypeId: blockType.blockTypeId}), blockTypeResTypeConfig, ResponseType.FULL))
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[createBlockType] Transaction rolled back.", null, process.env.SUPERADMIN_DB);
        log.error("[createBlockType] Error creating block type:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[createBlockType] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
}

export const patchBlockType = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[patchBlockType] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("Started transaction [patchBlockType]", null, process.env.SUPERADMIN_DB);

    try {
        const {BlockType} = getRepositories(qRunner) as {
            BlockType: Repository<BlockType>
        };

        const {blockTypeId, ...updateData}: patchBlockTypeSchemaType = req.body
        const existingBlockType = await BlockType.findOne({
            where: {blockTypeId},
        });
        if (!existingBlockType) {
            throw new BlockTypeNotFoundEx();
        }
        if ((updateData.name || updateData.isActive) && existingBlockType.lockedType !== LockedType.NOT_SYSTEM_MANAGER) throw new CannotModifySystemReserveEx();
        const patchedBlockType = await BlockType.save({
            ...existingBlockType,
            ...updateData,
        });

        await qRunner.commitTransaction();
        log.debug("[patchBlockType] Transaction committed successfully", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Block type patched successfully", await getRecordsAd((getRepositories(qRunner) as {
            BlockType: Repository<BlockType>
        }).BlockType, buildWhereClause({blockTypeId: patchedBlockType.blockTypeId}), blockTypeResTypeConfig, ResponseType.FULL))
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[patchBlockType] Transaction rolled back successfully", null, process.env.SUPERADMIN_DB);
        log.error("[patchBlockType] Error patching block type:", error, process.env.SUPERADMIN_DB);
        next(error);

    } finally {
        await qRunner.release();
        log.debug("[patchBlockType] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
}

export const deleteBlockType = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[deleteBlockType] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("[deleteBlockType] Started transaction", null, process.env.SUPERADMIN_DB);
    try {
        const {BlockType} = getRepositories(qRunner) as {
            BlockType: Repository<BlockType>
        };
        const {blockTypeId} = req.params;
        const existingBlockType = await BlockType.findOne({
            where: {blockTypeId},
            relations: ['tenantBlockLists']
        });
        if (!existingBlockType) {
            throw new BlockTypeNotFoundEx();
        }
        if (existingBlockType.lockedType !== LockedType.NOT_SYSTEM_MANAGER) throw new CannotModifySystemReserveEx();
        if (existingBlockType.tenantBlockLists.length > 0) throw new CannotDeleteAssociatedRecordsEx()
        await BlockType.delete({blockTypeId});

        await qRunner.commitTransaction();
        log.debug("[deleteBlockType] Transaction committed successfully", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Block type deleted successfully")
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[deleteBlockType] Transaction rolled back successfully", null, process.env.SUPERADMIN_DB);
        log.error("[deleteBlockType] Error deleting block type:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[deleteBlockType] Query Runner Released.", null, process.env.SUPERADMIN_DB);

    }
}