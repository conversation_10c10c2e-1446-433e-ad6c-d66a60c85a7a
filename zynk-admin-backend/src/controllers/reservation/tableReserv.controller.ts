import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { Between, Repository } from "typeorm";
import { ReservationStatus, TableReservation } from "../../models/dineIn/tableReservation.model";
import { errorHandler } from "../../utils/errorHandler";
import { BAD_REQUEST, CREATED, NOT_FOUND, OK } from "../../constants/STATUS_CODES";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import { RESERVATION_ALREADY_CANCELLED, RESERVATION_NOT_CONFIRMED, RESERVATION_NOT_FOUND } from "../../constants/tenant/dineIn/err";
import { RESERVATION_CANCELLED_SUCCESSFULLY, RESERVATION_CHECKED_IN_SUCCESSFULLY, RESERVATION_CREATED_SUCCESSFULLY, RESERVATION_RETRIEVED_SUCCESSFULLY, RESERVA<PERSON>ON_UPDATED_SUCCESSFULLY, RESERVATIONS_RETRIEVED_SUCCESSFULLY } from "../../constants/tenant/dineIn/msg";
import { checkTableAvailabilityForReservation } from "../../helpers/dineIn/tableReserv.helper";
import { TableModel, TableStatus } from "../../models/tenantSettings/tableReservation/management/table.model";
import { BUFFERTIME_NOTFOUND, TABLE_NOT_FOUND, TURNOVERTIME_NOTFOUND } from "../../constants/tenant/settings/tableReserv/err";
import { TurnoverTime } from "../../models/tenantSettings/tableReservation/turnoverTime/turnover_time.model";
import { TurnoverRule } from "../../models/tenantSettings/tableReservation/turnoverTime/turnover_rules.model";
import { getDurationMinutes } from "../../helpers/settings/tableReserv/tb _turnoverTime.helper";
import { BufferTime } from "../../models/tenantSettings/tableReservation/management/bufferTime.model";

export const getReservation = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {

  const qRunner = req.queryRunner;
  await qRunner.connect();

  try {
    const { TableReservation: ReservationRepository } = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
    };

    const reservation = await ReservationRepository.findOne({
      where: {
        reservationId: req.params.reservationId,
        branch: { branchId: req.branchId }
      },
      relations: ['table']
    });

    if (!reservation) {
      return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
    }

    ResponseHelper.success(res, OK, RESERVATION_RETRIEVED_SUCCESSFULLY, reservation);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
}

export const createReservation = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { TableReservation: ReservationRepository, TableModel, TurnoverTime, TurnoverRule, BufferTime} = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
      TableModel: Repository<TableModel>,
      TurnoverTime: Repository<TurnoverTime>
      TurnoverRule: Repository<TurnoverRule>
      BufferTime: Repository<BufferTime>
    };

    const branchId = req.branchId;
    const tableId = req.params.tableId;
    const {
      customerName,
      phoneNumber,
      numberOfGuests,
      reservationTime,
      specialNotes,
      // durationMinutes
    } = req.body;

    const durationMinutes = await getDurationMinutes(numberOfGuests, TurnoverTime, branchId)

    if(!durationMinutes)
      return next(errorHandler(NOT_FOUND, TURNOVERTIME_NOTFOUND))

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId
      }
    })

    if(!exBufferTime)
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND))

    // Check table availability
    const availability = await checkTableAvailabilityForReservation(
      qRunner,
      tableId,
      new Date(reservationTime),
      durationMinutes,
      numberOfGuests,
      branchId,
      undefined,
      exBufferTime.time
    );

    if (!availability.available) {
      return next(errorHandler(BAD_REQUEST, availability.reason!));
    }

    const newConfirmCode = await TableReservation.generateConfirmationCode()

    const reservation = ReservationRepository.create({
      customerName,
      phoneNumber,
      numberOfGuests,
      confirmationCode: newConfirmCode,
      reservationTime: new Date(reservationTime),
      specialNotes,
      durationMinutes,
      status: ReservationStatus.CONFIRMED,
      table: { tableId },
      branch: { branchId }
    });

    await ReservationRepository.save(reservation);

    const { exTable } = availability;

    if(!exTable)
      return next(errorHandler(NOT_FOUND, TABLE_NOT_FOUND))

    exTable.status = TableStatus.RESERVED

    await TableModel.save(exTable)
    
    await qRunner.commitTransaction();

    const result = {
      reservationId: reservation.reservationId,
      confirmationCode: reservation.confirmationCode,
      customerName: reservation.customerName,
      reservationTime: reservation.reservationTime,
      numberOfGuests: reservation.numberOfGuests,
      status: reservation.status
    }

    ResponseHelper.success(res, CREATED, RESERVATION_CREATED_SUCCESSFULLY, result);

  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getReservations = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();

  try {
    const { TableReservation: ReservationRepository } = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
    };

    const branchId = req.branchId;
    const { date, status, tableId } = req.validatedQuery;

    let whereCondition: any = {
      branch: { branchId }
    };

    if (date) {
      const startDate = new Date(date as string);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);
      
      whereCondition.reservationTime = Between(startDate, endDate);
    }

    if (status) {
      whereCondition.status = status;
    }

    if (tableId) {
      whereCondition.table = { tableId };
    }

    const reservations = await ReservationRepository.find({
      where: whereCondition,
      relations: ['table'],
      order: { reservationTime: 'ASC' }
    });

    ResponseHelper.success(res, OK, RESERVATIONS_RETRIEVED_SUCCESSFULLY, reservations);

  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const updateReservation = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { TableReservation: ReservationRepository, BufferTime} = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
      BufferTime: Repository<BufferTime>
    };

    const branchId = req.branchId;
    const reservationId = req.params.reservationId;

    const reservation = await ReservationRepository.findOne({
      where: {
        reservationId,
        branch: { branchId }
      },
      relations: ['table']
    });

    if (!reservation) {
      return next(errorHandler(NOT_FOUND, 'RESERVATION_NOT_FOUND'));
    }

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId
      }
    })

    if(!exBufferTime)
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND))

    const {reservationTime, numberOfGuests, durationMinutes} = req.body

    // If updating time or guest count, check availability
    if (reservationTime || numberOfGuests || durationMinutes) {
      const newTime = reservationTime ? new Date(reservationTime) : reservation.reservationTime;
      const newGuests = numberOfGuests || reservation.numberOfGuests;
      const newDuration = durationMinutes || reservation.durationMinutes;

      const availability = await checkTableAvailabilityForReservation(
        qRunner,
        reservation.table.tableId,
        newTime,
        newDuration,
        newGuests,
        branchId,
        reservationId,
        exBufferTime.time
      );

      if (!availability.available) {
        return next(errorHandler(BAD_REQUEST, availability.reason!));
      }
    }

    // Update reservation
    Object.assign(reservation, req.body);
    await ReservationRepository.save(reservation);
    
    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, RESERVATION_UPDATED_SUCCESSFULLY, reservation);

  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const cancelReservation = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { TableReservation: ReservationRepository } = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
    };

    const branchId = req.branchId;
    const reservationId = req.params.reservationId;

    const reservation = await ReservationRepository.findOne({
      where: {
        reservationId,
        branch: { branchId }
      }
    });

    if (!reservation) {
      return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
    }

    if (reservation.status === ReservationStatus.CANCELLED) {
      return next(errorHandler(BAD_REQUEST, RESERVATION_ALREADY_CANCELLED));
    }

    reservation.status = ReservationStatus.CANCELLED;
    await ReservationRepository.save(reservation);
    
    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, RESERVATION_CANCELLED_SUCCESSFULLY);

  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Check-in reservation (when customer arrives)
export const checkInReservation = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { TableReservation: ReservationRepository, TableModel: TableRepository } = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
      TableModel: Repository<TableModel>;
    };

    const branchId = req.branchId;
    const reservationId = req.params.reservationId;

    const reservation = await ReservationRepository.findOne({
      where: {
        reservationId,
        branch: { branchId }
      },
      relations: ['table']
    });

    if (!reservation) {
      return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
    }

    if (reservation.status !== ReservationStatus.CONFIRMED) {
      return next(errorHandler(BAD_REQUEST, RESERVATION_NOT_CONFIRMED));
    }

    // Update table status and booked seats
    const table = reservation.table;
    const newBookedSeats = table.bookedSeats + reservation.numberOfGuests;
    const newStatus = newBookedSeats >= table.maxSeats ? TableStatus.OCCUPIED : TableStatus.RESERVED;

    await TableRepository.update(
      { tableId: table.tableId },
      {
        bookedSeats: newBookedSeats,
        status: newStatus
      }
    );

    // Update reservation
    reservation.arrivedAt = new Date();
    reservation.status = ReservationStatus.COMPLETED;
    await ReservationRepository.save(reservation);
    
    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, RESERVATION_CHECKED_IN_SUCCESSFULLY, {
      tableStatus: newStatus,
      bookedSeats: newBookedSeats,
      availableSeats: table.maxSeats - newBookedSeats
    });

  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};