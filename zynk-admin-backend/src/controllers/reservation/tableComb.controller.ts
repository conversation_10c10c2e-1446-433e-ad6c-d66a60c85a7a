import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { Between, Repository, In } from "typeorm";
import { ReservationStatus, TableReservation } from "../../models/dineIn/tableReservation.model";
import { errorHandler } from "../../utils/errorHandler";
import { BAD_REQUEST, CREATED, NOT_FOUND, OK } from "../../constants/STATUS_CODES";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import { 
  RESERVATION_ALREADY_CANCELLED, 
  RESERVATION_NOT_CONFIRMED, 
  RESERVATION_NOT_FOUND,
} from "../../constants/tenant/dineIn/err";
import { 
  RESERVATION_CANCELLED_SUCCESSFULLY, 
  RESERVATION_CHECKED_IN_SUCCESSFULLY, 
  RESERVATION_CREATED_SUCCESSFULLY, 
  RESERVATION_UPDATED_SUCCESSFULLY, 
  RESERVATIONS_RETRIEVED_SUCCESSFULLY 
} from "../../constants/tenant/dineIn/msg";
import { TableModel, TableStatus } from "../../models/tenantSettings/tableReservation/management/table.model";
import { TableCombination } from "../../models/tenantSettings/tableReservation/management/tableCombination.model";
import { BUFFERTIME_NOTFOUND, GUEST_COUNT_OUT_OF_RANGE_FOR_COMBINATION, SOME_TABLES_IN_COMBINATION_DISABLED, TABLE_COMBINATION_DISABLED, TABLE_COMBINATION_NOT_FOUND, TURNOVERTIME_NOTFOUND } from "../../constants/tenant/settings/tableReserv/err";
import { TurnoverTime } from "../../models/tenantSettings/tableReservation/turnoverTime/turnover_time.model";
import { TurnoverRule } from "../../models/tenantSettings/tableReservation/turnoverTime/turnover_rules.model";
import { getDurationMinutes } from "../../helpers/settings/tableReserv/tb _turnoverTime.helper";
import { BufferTime } from "../../models/tenantSettings/tableReservation/management/bufferTime.model";
import { checkTableCombinationAvailabilityForReservation } from "../../helpers/dineIn/tableComb.helper";

export const createTableCombinationReservation = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { 
      TableReservation: ReservationRepository, 
      TableModel, 
      TableCombination: TableCombinationRepository,
      TurnoverTime, 
      TurnoverRule, 
      BufferTime
    } = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
      TableModel: Repository<TableModel>;
      TableCombination: Repository<TableCombination>;
      TurnoverTime: Repository<TurnoverTime>;
      TurnoverRule: Repository<TurnoverRule>;
      BufferTime: Repository<BufferTime>;
    };

    const branchId = req.branchId;
    const tableCombinationId = req.params.tableCombinationId;
    const {
      customerName,
      phoneNumber,
      numberOfGuests,
      reservationTime,
      specialNotes,
    } = req.body;

    const tableCombination = await TableCombinationRepository.findOne({
      where: {
        tableCombinationId,
        branch: { branchId }
      },
      relations: ['tables']
    });

    if (!tableCombination) {
      return next(errorHandler(NOT_FOUND, TABLE_COMBINATION_NOT_FOUND));
    }

    if (!tableCombination.enabled) {
      return next(errorHandler(BAD_REQUEST, TABLE_COMBINATION_DISABLED));
    }

    if (numberOfGuests < tableCombination.minSeats || numberOfGuests > tableCombination.maxSeats) {
      return next(errorHandler(BAD_REQUEST, GUEST_COUNT_OUT_OF_RANGE_FOR_COMBINATION));
    }

    const disabledTables = tableCombination.tables.filter(table => !table.enabled);
    if (disabledTables.length > 0) {
      return next(errorHandler(BAD_REQUEST, SOME_TABLES_IN_COMBINATION_DISABLED));
    }

    const durationMinutes = await getDurationMinutes(numberOfGuests, TurnoverTime, branchId);

    if (!durationMinutes) {
      return next(errorHandler(NOT_FOUND, TURNOVERTIME_NOTFOUND));
    }

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId
      }
    });

    if (!exBufferTime) {
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));
    }

    const availability = await checkTableCombinationAvailabilityForReservation(
      qRunner,
      tableCombinationId,
      new Date(reservationTime),
      durationMinutes,
      numberOfGuests,
      branchId,
      undefined,
      exBufferTime.time
    );

    if (!availability.available) {
      return next(errorHandler(BAD_REQUEST, availability.reason!));
    }

    const newConfirmCode = await TableReservation.generateConfirmationCode();

    const reservations = [];
    for (const table of tableCombination.tables) {
      const reservation = ReservationRepository.create({
        customerName,
        phoneNumber,
        numberOfGuests: Math.ceil(numberOfGuests / tableCombination.tables.length),
        confirmationCode: newConfirmCode,
        reservationTime: new Date(reservationTime),
        specialNotes: `${specialNotes || ''} [Table Combination: ${tableCombinationId}]`,
        durationMinutes,
        status: ReservationStatus.CONFIRMED,
        table: { tableId: table.tableId },
        branch: { branchId },
        tableCombinationId
      });

      const savedReservation = await ReservationRepository.save(reservation);
      reservations.push(savedReservation);

      await TableModel.update(
        { tableId: table.tableId },
        { status: TableStatus.RESERVED }
      );
    }
    
    await qRunner.commitTransaction();

    const result = {
      reservationId: reservations[0].reservationId,
      confirmationCode: newConfirmCode,
      customerName,
      reservationTime: new Date(reservationTime),
      numberOfGuests,
      status: ReservationStatus.CONFIRMED,
      tableCombinationId,
      tables: tableCombination.tables.map(t => ({
        tableId: t.tableId,
        name: t.name
      })),
      totalReservations: reservations.length
    };

    ResponseHelper.success(res, CREATED, RESERVATION_CREATED_SUCCESSFULLY, result);

  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getTableCombinationReservations = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();

  try {
    const { TableReservation: ReservationRepository } = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
    };

    const branchId = req.branchId;
    const { date, status, tableCombinationId } = req.validatedQuery;

    let whereCondition: any = {
      branch: { branchId },
      tableCombinationId: tableCombinationId || undefined
    };

    if (date) {
      const startDate = new Date(date as string);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);
      
      whereCondition.reservationTime = Between(startDate, endDate);
    }

    if (status) {
      whereCondition.status = status;
    }

    const reservations = await ReservationRepository.find({
      where: whereCondition,
      relations: ['table'],
      order: { reservationTime: 'ASC' }
    });

    // group by code
    const groupedReservations = new Map();
    
    reservations.forEach(reservation => {
      const key = reservation.confirmationCode;
      if (!groupedReservations.has(key)) {
        groupedReservations.set(key, {
          confirmationCode: key,
          customerName: reservation.customerName,
          phoneNumber: reservation.phoneNumber,
          reservationTime: reservation.reservationTime,
          status: reservation.status,
          specialNotes: reservation.specialNotes,
          durationMinutes: reservation.durationMinutes,
          tableCombinationId: reservation.tableCombinationId,
          tables: [],
          totalGuests: 0
        });
      }
      
      const group = groupedReservations.get(key);
      group.tables.push({
        tableId: reservation.table.tableId,
        tableName: reservation.table.name,
        guests: reservation.numberOfGuests
      });
      group.totalGuests += reservation.numberOfGuests;
    });

    const result = Array.from(groupedReservations.values());

    ResponseHelper.success(res, OK, RESERVATIONS_RETRIEVED_SUCCESSFULLY, result);

  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const updateTableCombinationReservation = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { 
      TableReservation: ReservationRepository, 
      BufferTime,
      TableCombination: TableCombinationRepository
    } = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
      BufferTime: Repository<BufferTime>;
      TableCombination: Repository<TableCombination>;
    };

    const branchId = req.branchId;
    const confirmationCode = req.params.confirmationCode;

    const reservations = await ReservationRepository.find({
      where: {
        confirmationCode,
        branch: { branchId }
      },
      relations: ['table']
    });

    if (reservations.length === 0) {
      return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
    }

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId
      }
    });

    if (!exBufferTime) {
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));
    }

    const { reservationTime, numberOfGuests, durationMinutes } = req.body;

    if (reservationTime || numberOfGuests || durationMinutes) {
      const firstReservation = reservations[0];
      const tableCombinationId = firstReservation.tableCombinationId;
      
      if (tableCombinationId) {
        const newTime = reservationTime ? new Date(reservationTime) : firstReservation.reservationTime;
        const newGuests = numberOfGuests || reservations.reduce((sum, r) => sum + r.numberOfGuests, 0);
        const newDuration = durationMinutes || firstReservation.durationMinutes;

        const availability = await checkTableCombinationAvailabilityForReservation(
          qRunner,
          tableCombinationId,
          newTime,
          newDuration,
          newGuests,
          branchId,
          confirmationCode,
          exBufferTime.time
        );

        if (!availability.available) {
          return next(errorHandler(BAD_REQUEST, availability.reason!));
        }
      }
    }

    for (const reservation of reservations) {
      if (reservationTime) reservation.reservationTime = new Date(reservationTime);
      if (durationMinutes) reservation.durationMinutes = durationMinutes;
      if (req.body.specialNotes !== undefined) reservation.specialNotes = req.body.specialNotes;
      if (req.body.customerName) reservation.customerName = req.body.customerName;
      if (req.body.phoneNumber) reservation.phoneNumber = req.body.phoneNumber;
      
      if (numberOfGuests) {
        reservation.numberOfGuests = Math.ceil(numberOfGuests / reservations.length);
      }

      await ReservationRepository.save(reservation);
    }
    
    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, RESERVATION_UPDATED_SUCCESSFULLY, {
      confirmationCode,
      updatedReservations: reservations.length
    });

  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const cancelTableCombinationReservation = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { 
      TableReservation: ReservationRepository,
      TableModel 
    } = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
      TableModel: Repository<TableModel>;
    };

    const branchId = req.branchId;
    const confirmationCode = req.params.confirmationCode;

    const reservations = await ReservationRepository.find({
      where: {
        confirmationCode,
        branch: { branchId }
      },
      relations: ['table']
    });

    if (reservations.length === 0) {
      return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
    }

    const alreadyCancelled = reservations.some(r => r.status === ReservationStatus.CANCELLED);
    if (alreadyCancelled) {
      return next(errorHandler(BAD_REQUEST, RESERVATION_ALREADY_CANCELLED));
    }

    for (const reservation of reservations) {
      reservation.status = ReservationStatus.CANCELLED;
      await ReservationRepository.save(reservation);

      const table = reservation.table;
      if (table.status === TableStatus.RESERVED && table.bookedSeats === 0) {
        await TableModel.update(
          { tableId: table.tableId },
          { status: TableStatus.AVAILABLE }
        );
      }
    }
    
    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, RESERVATION_CANCELLED_SUCCESSFULLY, {
      confirmationCode,
      cancelledReservations: reservations.length
    });

  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const checkInTableCombinationReservation = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { 
      TableReservation: ReservationRepository, 
      TableModel: TableRepository 
    } = getRepositories(qRunner) as {
      TableReservation: Repository<TableReservation>;
      TableModel: Repository<TableModel>;
    };

    const branchId = req.branchId;
    const confirmationCode = req.params.confirmationCode;

    const reservations = await ReservationRepository.find({
      where: {
        confirmationCode,
        branch: { branchId }
      },
      relations: ['table']
    });

    if (reservations.length === 0) {
      return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
    }

    const notConfirmed = reservations.some(r => r.status !== ReservationStatus.CONFIRMED);
    if (notConfirmed) {
      return next(errorHandler(BAD_REQUEST, RESERVATION_NOT_CONFIRMED));
    }

    const tableUpdates = [];

    for (const reservation of reservations) {
      const table = reservation.table;
      const newBookedSeats = table.bookedSeats + reservation.numberOfGuests;
      const newStatus = newBookedSeats >= table.maxSeats ? TableStatus.OCCUPIED : TableStatus.OCCUPIED;

      await TableRepository.update(
        { tableId: table.tableId },
        {
          bookedSeats: newBookedSeats,
          status: newStatus
        }
      );

      tableUpdates.push({
        tableId: table.tableId,
        tableName: table.name,
        newStatus,
        bookedSeats: newBookedSeats,
        availableSeats: table.maxSeats - newBookedSeats
      });

      // Update reservation
      reservation.arrivedAt = new Date();
      reservation.status = ReservationStatus.COMPLETED;
      await ReservationRepository.save(reservation);
    }
    
    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, RESERVATION_CHECKED_IN_SUCCESSFULLY, {
      confirmationCode,
      tables: tableUpdates,
      totalTables: reservations.length
    });

  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};