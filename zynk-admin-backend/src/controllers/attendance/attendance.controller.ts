import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { <PERSON>Null, Not, Repository } from "typeorm";
import { AttendanceRecord } from "../../models/attendance/attendance.model";
import { Staff } from "../../models/staff/staff.model";
import { errorHandler } from "../../utils/errorHandler";
import {
  BAD_REQUEST,
  CONFLICT,
  CREATED,
  NOT_FOUND,
  OK,
} from "../../constants/STATUS_CODES";
import {
  LOGGEDINSTAFF_INVALID,
  STAFF_NOT_FOUND,
} from "../../constants/tenant/staff/err";
import {
  ATT_ALREADY_CHECKEDIN,
  ATT_RECORD_NOT_FOUND,
  INVALID_PIN_STAFF,
  STAFF_ALREADY_CLOCKIN,
  STAFF_CLOCKIN_NOT_FOUND,
} from "../../constants/tenant/attendance/err";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import {
  ATT_RECORD_CHECKED_OUT,
  ATT_RECORD_CREATED,
  STAFF_CLOCKEDIN,
  STAFF_CLOCKEDOUT,
} from "../../constants/tenant/attendance/msg";
import argon2 from "argon2";
import { TimeEntry } from "../../models/attendance/time_entry.model";
import { StaffSession } from "../../models/staff/staffSession.model";
import { checkStaffSession } from "../../helpers/staff/staff.helper";
import { IP_NOT_FOUND } from "../../constants/security/err";
import { setCookie } from "../../helpers/system/CookieHelper.helper";
import { JWTPayload } from "../../../types/jwt";
import { UserType } from "../../types/pBAC";

export const checkInStaff = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { AttendanceRecord, Staff, TimeEntry, StaffSession } =
      getRepositories(qRunner) as {
        AttendanceRecord: Repository<AttendanceRecord>;
        Staff: Repository<Staff>;
        TimeEntry: Repository<TimeEntry>;
        StaffSession: Repository<StaffSession>;
      };

    const staffId = req.params.staffId;
    const ip = req.ip;

    const exStaff = await Staff.findOne({
      where: {
        staffId,
      },
      relations: ["staffSessions"],
    });

    if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

    if (!ip) return next(errorHandler(BAD_REQUEST, IP_NOT_FOUND));

    await checkStaffSession(ip, exStaff, StaffSession, req.userAgent, qRunner);

    const { checkInTime, date, pin } = req.body;

    const verified = await argon2.verify(exStaff.pin, pin.toString());

    if (!verified) return next(errorHandler(BAD_REQUEST, INVALID_PIN_STAFF));

    const exTimeEntry = await TimeEntry.findOne({
      where: {
        staff: {
          staffId: exStaff.staffId,
        },
        clockInTime: Not(IsNull()),
        clockOutTime: IsNull(),
        date,
      },
    });

    if (!exTimeEntry)
      return next(errorHandler(NOT_FOUND, STAFF_CLOCKIN_NOT_FOUND));

    const exAttRecord = await AttendanceRecord.findOne({
      where: {
        staff: {
          staffId: exStaff.staffId,
        },
        checkInTime: Not(IsNull()),
        checkOutTime: IsNull(),
        date,
      },
    });

    let userId = req.userId;
    let checkedInStaffId = staffId;

    let payload: JWTPayload = {
      userId,
      branchId: req.branchId!,
      checkedInStaffId,
      domainName: (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
      isTenant: true,
      inSuper: true,
    };

    if (req.USER_TYPE === UserType.STAFF) {
      const loggedInStaff = await Staff.findOne({
        where: {
          staffId: userId,
        },
      });

      if (!loggedInStaff)
        return next(errorHandler(NOT_FOUND, LOGGEDINSTAFF_INVALID));

      payload.inSuper = false;
    }

    if (exAttRecord) {
      setCookie(res, payload);

      // return next(errorHandler(CONFLICT, ATT_ALREADY_CHECKEDIN));

      const staffDetails = {
        staffId: exStaff.staffId,
        firstName: exStaff.firstName,
        middleName: exStaff.middleName,
        lastName: exStaff.lastName,
        profileUrl: exStaff.profileUrl,
        role: exStaff.tempRole,
        checkInTime: exAttRecord.checkInTime,
        date: exAttRecord.date,
      };

      await qRunner.commitTransaction();

      res.status(CONFLICT).json({
        statusCode: CONFLICT,
        success: false,
        message: ATT_ALREADY_CHECKEDIN,
        staffDetails,
      });

      return;
    }

    const newAttRecord = AttendanceRecord.create({
      staff: exStaff,
      checkInTime,
      date,
    });

    await AttendanceRecord.save(newAttRecord);

    setCookie(res, payload);

    const staffDetails = {
      staffId: exStaff.staffId,
      firstName: exStaff.firstName,
      middleName: exStaff.middleName,
      lastName: exStaff.lastName,
      profileUrl: exStaff.profileUrl,
      role: exStaff.tempRole,
      checkInTime: newAttRecord.checkInTime,
      date: newAttRecord.date,
    };

    await qRunner.commitTransaction();

    ResponseHelper.success(res, CREATED, ATT_RECORD_CREATED, staffDetails);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const checkOutStaff = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { AttendanceRecord, Staff, StaffSession } = getRepositories(
      qRunner
    ) as {
      AttendanceRecord: Repository<AttendanceRecord>;
      Staff: Repository<Staff>;
      StaffSession: Repository<StaffSession>;
    };

    const staffId = req.params.staffId;
    const userId = req.userId;
    const ip = req.ip;

    const exStaff = await Staff.findOne({
      where: {
        staffId,
      },
      relations: ["staffSessions"],
    });

    if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

    if (!ip) return next(errorHandler(BAD_REQUEST, IP_NOT_FOUND));

    await checkStaffSession(ip, exStaff, StaffSession, req.userAgent, qRunner);

    const { checkOutTime, date, pin } = req.body;

    const verified = await argon2.verify(exStaff.pin, pin.toString());

    if (!verified) return next(errorHandler(BAD_REQUEST, INVALID_PIN_STAFF));

    const exAttRecord = await AttendanceRecord.findOne({
      where: {
        staff: {
          staffId: exStaff.staffId,
        },
        checkInTime: Not(IsNull()),
        checkOutTime: IsNull(),
        date,
      },
    });

    if (!exAttRecord)
      return next(errorHandler(NOT_FOUND, ATT_RECORD_NOT_FOUND));

    exAttRecord.update({
      checkOutTime,
    });

    await AttendanceRecord.save(exAttRecord);

    let payload: JWTPayload = {
      userId,
      branchId: req.branchId!,
      checkedInStaffId: undefined,
      domainName: (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
      isTenant: true,
      inSuper: true,
    };

    if (req.USER_TYPE === UserType.STAFF) {
      const loggedInStaff = await Staff.findOne({
        where: {
          staffId: userId,
        },
      });

      if (!loggedInStaff)
        return next(errorHandler(NOT_FOUND, LOGGEDINSTAFF_INVALID));

      payload.inSuper = false;
    }

    setCookie(res, payload);

    const staffDetails = {
      staffId: exStaff.staffId,
      firstName: exStaff.firstName,
      middleName: exStaff.middleName,
      lastName: exStaff.lastName,
      profileUrl: exStaff.profileUrl,
      role: exStaff.tempRole,
      checkInTime: exAttRecord.checkInTime,
      checkOutTime: exAttRecord.checkOutTime,
      date: exAttRecord.date,
    };

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, ATT_RECORD_CHECKED_OUT, staffDetails);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const clockInStaff = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { TimeEntry, Staff, StaffSession } = getRepositories(qRunner) as {
      TimeEntry: Repository<TimeEntry>;
      Staff: Repository<Staff>;
      StaffSession: Repository<StaffSession>;
    };

    const staffId = req.params.staffId;
    const ip = req.ip;

    const exStaff = await Staff.findOne({
      where: {
        staffId,
      },
      relations: ["staffSessions"],
    });

    if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

    if (!ip) return next(errorHandler(BAD_REQUEST, IP_NOT_FOUND));

    await checkStaffSession(ip, exStaff, StaffSession, req.userAgent, qRunner);

    const { clockInTime, date, pin } = req.body;

    const verified = await argon2.verify(exStaff.pin, pin.toString());

    if (!verified) return next(errorHandler(BAD_REQUEST, INVALID_PIN_STAFF));

    const exTimeRecord = await TimeEntry.findOne({
      where: {
        staff: {
          staffId: exStaff.staffId,
        },
        clockInTime: Not(IsNull()),
        clockOutTime: IsNull(),
        date,
      },
    });

    if (exTimeRecord)
      return next(errorHandler(CONFLICT, STAFF_ALREADY_CLOCKIN));

    const newTimeRecord = TimeEntry.create({
      clockInTime,
      staff: exStaff,
      date,
    });

    await TimeEntry.save(newTimeRecord);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, CREATED, STAFF_CLOCKEDIN);
    await qRunner.commitTransaction();
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const clockOutStaff = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { TimeEntry, Staff, StaffSession } = getRepositories(qRunner) as {
      TimeEntry: Repository<TimeEntry>;
      Staff: Repository<Staff>;
      StaffSession: Repository<StaffSession>;
    };

    const staffId = req.params.staffId;
    const ip = req.ip;

    const exStaff = await Staff.findOne({
      where: {
        staffId,
      },
      relations: ["staffSessions"],
    });

    if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

    if (!ip) return next(errorHandler(BAD_REQUEST, IP_NOT_FOUND));

    await checkStaffSession(ip, exStaff, StaffSession, req.userAgent, qRunner);

    const { clockOutTime, date, pin } = req.body;

    const verified = await argon2.verify(exStaff.pin, pin.toString());

    if (!verified) return next(errorHandler(BAD_REQUEST, INVALID_PIN_STAFF));

    const exTimeRecord = await TimeEntry.findOne({
      where: {
        staff: {
          staffId: exStaff.staffId,
        },
        clockInTime: Not(IsNull()),
        clockOutTime: IsNull(),
        date,
      },
    });

    if (!exTimeRecord)
      return next(errorHandler(NOT_FOUND, STAFF_CLOCKIN_NOT_FOUND));

    exTimeRecord.update({
      clockOutTime,
    });

    await TimeEntry.save(exTimeRecord);

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, STAFF_CLOCKEDOUT);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};
