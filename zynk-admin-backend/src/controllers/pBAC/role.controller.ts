import {NextFunction, Request, Response} from "express";
import {QueryRunner, Repository} from "typeorm";
import log from "../../helpers/system/logger.helper";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {OK} from "../../constants/STATUS_CODES";
import {TRole} from "../../models/pBAC/tRole/TRole.model";
import {Role} from "../../models/pBAC/role/Role.model";
import {formatUNKNOWNPermissionsActionsList} from "../../helpers/pBAC/formatUNKNOWNPermsActions.helper";
import {FormatType} from "../../types/pBAC/formatterTypes";

export const getAllRoles = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const isTenantSpecific = req.tenantName !== null;
    const permissionQueryRunner: QueryRunner = isTenantSpecific ? req.queryRunner : req.rootQueryRunner;
    await permissionQueryRunner.connect();
    log.debug("connected to query runner");
    try {
        const key = isTenantSpecific ? "TRole" : "Role";
        const repositories = getRepositories(permissionQueryRunner) as {
            [key: string]: Repository<InstanceType<typeof TRole> | InstanceType<typeof Role>>;
        };
        const formatedRoles = formatUNKNOWNPermissionsActionsList(
            await repositories[key].find({
                relations: [
                    "rolePermissionActions",
                    "rolePermissionActions.permissionAction",
                    "rolePermissionActions.permissionAction.permission",
                    "rolePermissionActions.permissionAction.action"
                ],
                order: {
                    name: "ASC"
                }
            }),
            "rolePermissionActions",
            FormatType.OBJECT_ARR_OBJECT,
            true
        );
        ResponseHelper.success(res, OK, "All roles fetched successfully", formatedRoles);
    } catch (error) {
        log.error("Error while fetching all roles", error);
        next(error);
    } finally {
        await permissionQueryRunner.release();
        log.debug("Released query runner");
    }
}