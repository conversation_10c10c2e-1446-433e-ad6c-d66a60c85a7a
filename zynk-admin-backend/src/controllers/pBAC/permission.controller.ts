import {NextFunction, Request, Response} from "express";
import {QueryRunner, Repository} from "typeorm";
import log from "../../helpers/system/logger.helper";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {OK} from "../../constants/STATUS_CODES";
import {TPermissions} from "../../models/pBAC/tPBAC/TPermissions.model";
import {Permission} from "../../models/pBAC/Permission.model";

//#TODO: filter the response based on tenant(only active) and super admin(show inactive)(both permission and actions)
export const getAllPermission = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const isTenantSpecific = req.tenantName !== null;
    const permissionQueryRunner: QueryRunner = isTenantSpecific ? req.queryRunner : req.rootQueryRunner;
    await permissionQueryRunner.connect();
    log.debug("connected to query runner");
    try {
        const key = isTenantSpecific ? "TPermission" : "Permission";
        const repositories = getRepositories(permissionQueryRunner) as {
            [key: string]: Repository<InstanceType<typeof TPermissions> | InstanceType<typeof Permission>>;
        };
        const repository = repositories[key];
        const permissions = await repository.find({
            relations: ["module", "actions"],
            order: {
                name: "ASC",
            },
        });
        ResponseHelper.success(res, OK, "All permissions fetched successfully", permissions);
    } catch (error) {
        log.error("Error while fetching all permissions", error);
        next(error);
    } finally {
        await permissionQueryRunner.release();
        log.debug("Released query runner");
    }
}