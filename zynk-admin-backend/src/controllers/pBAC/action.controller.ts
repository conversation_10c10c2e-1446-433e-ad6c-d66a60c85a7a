import {NextFunction, Request, Response} from "express";
import log from "../../helpers/system/logger.helper"
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {QueryRunner, Repository} from "typeorm";
import {Action} from "../../models/pBAC/Action.model";
import {TAction} from "../../models/pBAC/tPBAC/TAction.entity";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {OK} from "../../constants/STATUS_CODES";


//#TODO: filter the response based on tenant(only active) and super admin(show inactive)
export const getAllActions = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const isTenantSpecific = req.tenantName !== null;
    const actionQueryRunner: QueryRunner = isTenantSpecific ? req.queryRunner : req.rootQueryRunner;
    await actionQueryRunner.connect();
    log.debug("connected to query runner");
    // await actionQueryRunner.startTransaction();
    // log.debug("Started transaction");
    try {
        const key = isTenantSpecific ? "TAction" : "Action";
        const repositories = getRepositories(actionQueryRunner) as {
            [key: string]: Repository<InstanceType<typeof Action> | InstanceType<typeof TAction>>;
        };
        const repository = repositories[key];
        // await actionQueryRunner.commitTransaction();
        // log.debug("Committed transaction");
        const actions = await repository.find({
            order: {
                name: "ASC",
            },
        });
        ResponseHelper.success(res, OK, "All actions fetched successfully", actions);
    } catch (error) {
        // await actionQueryRunner.rollbackTransaction();
        log.error("Error while fetching all actions", error);
        next(error);
    } finally {
        await actionQueryRunner.release();
        log.debug("Released query runner");
    }
}