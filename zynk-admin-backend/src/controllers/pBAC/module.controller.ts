import {NextFunction, Request, Response} from "express";
import log from "../../helpers/system/logger.helper"
import {Query<PERSON>un<PERSON>, TreeRepository} from "typeorm";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {OK} from "../../constants/STATUS_CODES";
import {Module} from "../../models/pBAC/Module.model";
import {TModule} from "../../models/pBAC/tPBAC/TModule.model";

export const getAllModule = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const isTenantSpecific = req.tenantName !== null;
    const moduleQueryRunner: QueryRunner = isTenantSpecific ? req.queryRunner : req.rootQueryRunner;
    await moduleQueryRunner.connect();
    log.debug("connected to query runner");
    try {
        const treeRepository = moduleQueryRunner.manager.getTreeRepository(
            isTenantSpecific ? TModule : Module
        ) as TreeRepository<TModule | Module>;

        const modules = await treeRepository.findTrees({
            relations: ["subModules", "permissions"],
        });
        ResponseHelper.success(res, OK, "All modules fetched successfully", modules);
    } catch (error) {
        log.error("Error while fetching all modules", error);
        next(error);
    } finally {
        await moduleQueryRunner.release();
        log.debug("Released query runner");
    }
}