import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Repository} from "typeorm";
import {Branch} from "../../models/company/branch.model";
import {errorHandler} from "../../utils/errorHandler";
import {BAD_REQUEST, CONFLICT, CREATED, NOT_FOUND, OK,} from "../../constants/STATUS_CODES";
import {
  ALL_BRANCH_CONTACTS_FETCHED,
  ALL_BRANCH_FETCHED,
  BRANCH_CREATED,
  <PERSON><PERSON>CH_DELETED,
  <PERSON><PERSON><PERSON>_DETAILS_FETCHED,
  BRANCH_HOURS_FETCHED,
  <PERSON><PERSON><PERSON>_UPDATED,
} from "../../constants/tenant/company/msg";
import {BranchContacts} from "../../models/company/branchcontact.model";
import {BusinessHour} from "../../models/common/businesshour.model";
import {BusinessHourSlot} from "../../models/common/businesshourslot.model";
import {CustomHourSlot} from "../../models/common/customhourslot.model";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {
  BRANCH_ALREADY_EXISTS,
  BRANCH_CONTACT_ALREADY_EXISTS,
  BRANCH_NOT_FOUND,
  BUSINESS_HOUR_NOT_FOUND,
  COMPANY_INFO_NOT_FOUND,
  DUPLICATE_DAYS_IN_CUSTOMHOURS,
  DUPLICATE_DAYS_IN_HOURS,
  NO_UPDATES_PROVIDED,
  PRIMARY_BRANCH_CANNOT_BE_DELETED,
} from "../../constants/tenant/company/err";
import {SpecialDay} from "../../models/common/specialday.model";
import {CompanyInformation} from "../../models/company/companyinformation.model";
import {branchSettingsHelper} from "../../helpers/settings/tenant/branchSettings.helper";
import {Staff} from "../../models/staff/staff.model";

export const createBranch = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();
    await qRunner.startTransaction();
    try {
        const {
            Branch,
            BranchContacts,
            BusinessHour,
            BusinessHourSlot,
            CustomHourSlot,
            SpecialDay,
            CompanyInformation,
        } = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
            BranchContacts: Repository<BranchContacts>;
            BusinessHour: Repository<BusinessHour>;
            BusinessHourSlot: Repository<BusinessHourSlot>;
            CustomHourSlot: Repository<CustomHourSlot>;
            SpecialDay: Repository<SpecialDay>;
            CompanyInformation: Repository<CompanyInformation>;
        };

        const data = req.body;
        const branchDetails = data.branchDetails as Branch;

        const exBranch = await Branch.findOne({
            where: [
                {
                    name: branchDetails.name,
                },
                {
                    location: {
                        lat: branchDetails.location.lat,
                        lng: branchDetails.location.lng,
                    },
                },
            ],
        });

        if (exBranch) return next(errorHandler(CONFLICT, BRANCH_ALREADY_EXISTS));

        const newBranch = Branch.create(branchDetails);
        await Branch.save(newBranch);

        const branchContacts = data.branchContacts as BranchContacts[];

        const existingContact = await BranchContacts.findOne({
            where: branchContacts
                .map((contact) => [
                    {emailAddress: contact.emailAddress},
                    {phoneNumber: contact.phoneNumber},
                ])
                .flat(),
        });

        if (existingContact) {
            return next(errorHandler(CONFLICT, BRANCH_CONTACT_ALREADY_EXISTS));
        }

        const newBranchContacts = branchContacts.map((contact) =>
            BranchContacts.create({...contact})
        );

        await BranchContacts.save(newBranchContacts);

        newBranch.branchContacts = newBranchContacts;

        await Branch.save(newBranch);

        const branchHours = data.branchHours;

        const newBusinessHour = BusinessHour.create();
        await BusinessHour.save(newBusinessHour);

        const businessHourSlots = branchHours.businessHours.map((bHours: any) => {
            return BusinessHourSlot.create({
                businessHour: newBusinessHour,
                days: bHours.days,
                is24Hours: bHours.is24Hours,
                firstSeating: bHours.firstSeating,
                lastSeating: bHours.lastSeating,
                isActive: bHours.isActive,
            });
        });
        const businessHourDays = new Map<string, boolean>();
        branchHours.businessHours.forEach((bHours: any) => {
            Object.entries(bHours.days).forEach(([day, isActive]) => {
                if (isActive && businessHourDays.get(day) && bHours.isActive) {
                    throw errorHandler(CONFLICT, DUPLICATE_DAYS_IN_HOURS);
                }
                if (isActive && bHours.isActive) {
                    businessHourDays.set(day, true);
                }
            });
        });

        await BusinessHourSlot.save(businessHourSlots);

        if (branchHours.customHours && branchHours.customHours.length > 0) {
            const customHourDays = new Set<string>();

            branchHours.customHours.forEach((cHours: any) => {
                Object.entries(cHours.days).forEach(([day, isActive]) => {
                    if (isActive && customHourDays.has(day) && cHours.isActive) {
                        throw errorHandler(CONFLICT, DUPLICATE_DAYS_IN_CUSTOMHOURS);
                    }
                    if (isActive && cHours.isActive) {
                        customHourDays.add(day);
                    }
                });
            });

            const customHourSlots = branchHours.customHours.map((cHours: any) => {
                return CustomHourSlot.create({
                    days: cHours.days,
                    name: cHours.name,
                    is24Hours: cHours.is24Hours,
                    firstSeating: cHours.firstSeating,
                    lastSeating: cHours.lastSeating,
                    isActive: cHours.isActive,
                });
            });

            await CustomHourSlot.save(customHourSlots);
            newBusinessHour.customSlots = customHourSlots;
        }

        newBusinessHour.slots = businessHourSlots;

        const specialDays = data.specialDays;

        if (specialDays && specialDays.length > 0) {
            const newSpecialDays = specialDays.map((sDay: any) => {
                return SpecialDay.create({
                    eventName: sDay.eventName,
                    startTime: sDay.startTime,
                    endTime: sDay.endTime,
                    availability: sDay.availability,
                });
            });

            await SpecialDay.save(newSpecialDays);
            newBusinessHour.specialDays = newSpecialDays;
        }

        await BusinessHour.save(newBusinessHour);

        newBranch.businessHour = newBusinessHour;

        await Branch.save(newBranch);

        const allCompanyInfo = await CompanyInformation.find({
            relations: ["branches"],
        });

        if (!allCompanyInfo)
            return next(errorHandler(NOT_FOUND, COMPANY_INFO_NOT_FOUND));

        const companyInfo = allCompanyInfo[0];

        companyInfo.branches = companyInfo.branches || [];
        companyInfo.branches.push(newBranch);
        await CompanyInformation.save(companyInfo);

        await branchSettingsHelper(newBranch, qRunner);
        await qRunner.commitTransaction();

        ResponseHelper.success(res, CREATED, BRANCH_CREATED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getAllBranches = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    try {
        const {Branch} = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
        };

        const allBranches = await Branch.find();

        ResponseHelper.success(res, OK, ALL_BRANCH_FETCHED, allBranches);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchBranchStatus = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {Branch} = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
        };

        const branchId = req.params.branchId;

        const exBranch = await Branch.findOneBy({
            branchId,
        });

        if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

        const {isActive} = req.body;

        exBranch.isActive = isActive;

        await Branch.save(exBranch);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, BRANCH_UPDATED, exBranch);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchBranchDetails = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {Branch} = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
        };

        const branchId = req.params.branchId;

        const exBranch = await Branch.findOneBy({
            branchId,
        });

        if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

        const data = req.body;

        Object.entries(data).forEach(([key, value]) => {
            (exBranch as any)[key] = value;
        });

        await Branch.save(exBranch);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, BRANCH_UPDATED, exBranch);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getBranchDetails = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    try {
        const branchId = req.params.branchId;

        const {Branch} = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
        };

        const exBranch = await Branch.findOneBy({
            branchId,
        });

        if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

        ResponseHelper.success(res, OK, BRANCH_DETAILS_FETCHED, exBranch);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getBranchContacts = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    try {
        const branchId = req.params.branchId;

        const {Branch} = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
        };

        const exBranch = await Branch.findOne({
            where: {
                branchId,
            },
            relations: ["branchContacts"],
        });

        if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

        ResponseHelper.success(
            res,
            OK,
            ALL_BRANCH_CONTACTS_FETCHED,
            exBranch.branchContacts
        );
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getBranchBusinessHours = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    try {
        const branchId = req.params.branchId;

        const {Branch} = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
        };

        const exBranch = await Branch.findOne({
            where: {
                branchId,
            },
            relations: ["businessHour"],
        });

        if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

        ResponseHelper.success(
            res,
            OK,
            BRANCH_HOURS_FETCHED,
            exBranch.businessHour
        );
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchBranchContacts = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {Branch, BranchContacts} = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
            BranchContacts: Repository<BranchContacts>;
        };

        const branchId = req.params.branchId;

        const exBranch = await Branch.findOne({
            where: {
                branchId,
            },
            relations: ["branchContacts"],
        });

        if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

        const data = req.body.branchContacts;

        const allBranchContacts = exBranch.branchContacts;

        const updatedContacts = new Map<string, BranchContacts>();

        for (const contact of data) {
            if (contact.branchContactId) {
                const existingContact = allBranchContacts.find(
                    (bc) => bc.branchContactId === contact.branchContactId
                );

                if (existingContact) {
                    if (contact.name !== undefined) {
                        existingContact.name = contact.name;
                    }
                    if (contact.emailAddress !== undefined) {
                        existingContact.emailAddress = contact.emailAddress;
                    }
                    if (contact.phoneNumber !== undefined) {
                        existingContact.phoneNumber = contact.phoneNumber;
                    }
                    if (contact.faxNumber !== undefined) {
                        existingContact.faxNumber = contact.faxNumber;
                    }

                    updatedContacts.set(contact.branchContactId, existingContact);
                }
            }
        }

        const contactIdsToKeep = data
            .filter((contact: any) => contact.branchContactId)
            .map((contact: any) => contact.branchContactId);

        const contactsToRemove = allBranchContacts.filter(
            (bc) => !contactIdsToKeep.includes(bc.branchContactId)
        );

        if (contactsToRemove.length > 0) {
            await BranchContacts.remove(contactsToRemove);
        }

        if (updatedContacts.size > 0) {
            await BranchContacts.save(Array.from(updatedContacts.values()));
        }

        const newContacts = data
            .filter((contact: any) => !contact.branchContactId)
            .map((contact: any) => {
                const newContact = BranchContacts.create({
                    name: contact.name,
                    emailAddress: contact.emailAddress,
                    phoneNumber: contact.phoneNumber,
                    faxNumber: contact.faxNumber,
                    branch: exBranch,
                });
                return newContact;
            });

        if (newContacts.length > 0) {
            const savedNewContacts = await BranchContacts.save(newContacts);
            exBranch.branchContacts = [
                ...exBranch.branchContacts.filter(
                    (bc) => !contactsToRemove.includes(bc)
                ),
                ...savedNewContacts,
            ];
        }

        await Branch.save(exBranch);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, BRANCH_UPDATED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchBranchBusinessHours = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {
            Branch,
            BusinessHour,
            BusinessHourSlot,
            CustomHourSlot,
            SpecialDay,
        } = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
            BusinessHour: Repository<BusinessHour>;
            BusinessHourSlot: Repository<BusinessHourSlot>;
            CustomHourSlot: Repository<CustomHourSlot>;
            SpecialDay: Repository<SpecialDay>;
        };

        const branchId = req.params.branchId;

        const exBranch = await Branch.findOne({
            where: {
                branchId,
            },
            relations: ["businessHour"],
        });

        if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

        const businessHourId = exBranch.businessHour.businessHourId;

        const existingBusinessHour = await BusinessHour.findOne({
            where: {businessHourId},
            relations: ["slots", "customSlots", "specialDays"],
        });

        if (!existingBusinessHour)
            return next(errorHandler(NOT_FOUND, BUSINESS_HOUR_NOT_FOUND));

        const data = req.body;
        let isUpdated = false;

        if (data.useDefault !== undefined) {
            existingBusinessHour.useDefault = data.useDefault;
            isUpdated = true;
            await BusinessHour.save(existingBusinessHour);
        }

        if (data.slots) {
            // Check for duplicate active days across both existing and incoming slots
            const businessHourDays = new Map<string, boolean>();

            existingBusinessHour.slots.forEach((slot: any) => {
                if (slot.days && slot.isActive) {
                    Object.entries(slot.days).forEach(([day, isActive]) => {
                        if (isActive && slot.isActive) {
                            businessHourDays.set(day, true);
                        }
                    });
                }
            });

            data.slots.forEach((slot: any) => {
                if (slot.days && slot.isActive) {
                    Object.entries(slot.days).forEach(([day, isActive]) => {
                        if (isActive && businessHourDays.get(day)) {
                            return next(errorHandler(CONFLICT, DUPLICATE_DAYS_IN_HOURS));
                        }
                        if (isActive && slot.isActive) {
                            businessHourDays.set(day, true);
                        }
                    });
                }
            });

            const existingSlotIds = existingBusinessHour.slots.map(
                (slot) => slot.slotId
            );
            const incomingSlotIds = data.slots
                .filter((slot: any) => slot.slotId)
                .map((slot: any) => slot.slotId);

            const slotsToDelete = existingBusinessHour.slots.filter(
                (slot) => !incomingSlotIds.includes(slot.slotId)
            );

            if (slotsToDelete.length > 0) {
                await BusinessHourSlot.remove(slotsToDelete);
            }

            for (const slotData of data.slots) {
                if (slotData.slotId) {
                    const updateData: Partial<BusinessHourSlot> = {};

                    if ("days" in slotData) updateData.days = slotData.days;
                    if ("is24Hours" in slotData)
                        updateData.is24Hours = slotData.is24Hours;
                    if ("firstSeating" in slotData)
                        updateData.firstSeating = slotData.firstSeating;
                    if ("lastSeating" in slotData)
                        updateData.lastSeating = slotData.lastSeating;
                    if ("isActive" in slotData) updateData.isActive = slotData.isActive;

                    await BusinessHourSlot.update(
                        {slotId: slotData.slotId},
                        updateData
                    );
                } else {
                    const newSlot = BusinessHourSlot.create({
                        businessHour: existingBusinessHour,
                        days: slotData.days,
                        is24Hours: slotData.is24Hours,
                        firstSeating: slotData.firstSeating,
                        lastSeating: slotData.lastSeating,
                        isActive: slotData.isActive,
                    });
                    await BusinessHourSlot.save(newSlot);
                }
            }
            isUpdated = true;
        }

        if (data.customSlots) {
            // Check for duplicate active days across both existing and incoming custom slots
            const customHourDays = new Map<string, boolean>();

            existingBusinessHour.customSlots.forEach((slot: any) => {
                if (slot.days && slot.isActive) {
                    Object.entries(slot.days).forEach(([day, isActive]) => {
                        if (isActive && slot.isActive) {
                            customHourDays.set(day, true);
                        }
                    });
                }
            });

            data.customSlots.forEach((slot: any) => {
                if (slot.days && slot.isActive) {
                    Object.entries(slot.days).forEach(([day, isActive]) => {
                        if (isActive && customHourDays.get(day)) {
                            return next(errorHandler(CONFLICT, DUPLICATE_DAYS_IN_HOURS));
                        }
                        if (isActive && slot.isActive) {
                            customHourDays.set(day, true);
                        }
                    });
                }
            });

            const existingCustomSlotIds = existingBusinessHour.customSlots.map(
                (slot) => slot.slotId
            );
            const incomingCustomSlotIds = data.customSlots
                .filter((slot: any) => slot.slotId)
                .map((slot: any) => slot.slotId);

            const customSlotsToDelete = existingBusinessHour.customSlots.filter(
                (slot) => !incomingCustomSlotIds.includes(slot.slotId)
            );

            if (customSlotsToDelete.length > 0) {
                await CustomHourSlot.remove(customSlotsToDelete);
            }

            for (const slotData of data.customSlots) {
                if (slotData.slotId) {
                    const updateData: Partial<CustomHourSlot> = {};

                    if ("name" in slotData) updateData.name = slotData.name;
                    if ("days" in slotData) updateData.days = slotData.days;
                    if ("is24Hours" in slotData)
                        updateData.is24Hours = slotData.is24Hours;
                    if ("firstSeating" in slotData)
                        updateData.firstSeating = slotData.firstSeating;
                    if ("lastSeating" in slotData)
                        updateData.lastSeating = slotData.lastSeating;
                    if ("isActive" in slotData) updateData.isActive = slotData.isActive;

                    await CustomHourSlot.update({slotId: slotData.slotId}, updateData);
                } else {
                    const newCustomSlot = CustomHourSlot.create({
                        businessHour: existingBusinessHour,
                        name: slotData.name,
                        days: slotData.days,
                        is24Hours: slotData.is24Hours,
                        firstSeating: slotData.firstSeating,
                        lastSeating: slotData.lastSeating,
                        isActive: slotData.isActive,
                    });
                    await CustomHourSlot.save(newCustomSlot);
                }
            }
            isUpdated = true;
        }

        if (data.specialDays && data.specialDays.length > 0) {
            const existingSpecialDayIds = existingBusinessHour.specialDays.map(
                (day) => day.specialDayId
            );
            const incomingSpecialDayIds = data.specialDays
                .filter((day: any) => day.specialDayId)
                .map((day: any) => day.specialDayId);

            const specialDaysToDelete = existingBusinessHour.specialDays.filter(
                (day) => !incomingSpecialDayIds.includes(day.specialDayId)
            );

            if (specialDaysToDelete.length > 0) {
                await SpecialDay.remove(specialDaysToDelete);
            }

            for (const dayData of data.specialDays) {
                if (dayData.specialDayId) {
                    const updateData: Partial<SpecialDay> = {};

                    if ("eventName" in dayData) updateData.eventName = dayData.eventName;
                    if ("startTime" in dayData) updateData.startTime = dayData.startTime;
                    if ("endTime" in dayData) updateData.endTime = dayData.endTime;
                    if ("availability" in dayData)
                        updateData.availability = dayData.availability;

                    await SpecialDay.update(
                        {specialDayId: dayData.specialDayId},
                        updateData
                    );
                } else {
                    const newSpecialDay = SpecialDay.create({
                        businessHour: existingBusinessHour,
                        eventName: dayData.eventName,
                        startTime: dayData.startTime,
                        endTime: dayData.endTime,
                        availability: dayData.availability,
                    });
                    await SpecialDay.save(newSpecialDay);
                }
            }
            isUpdated = true;
        }

        if (!isUpdated) {
            return next(errorHandler(BAD_REQUEST, NO_UPDATES_PROVIDED));
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, BRANCH_UPDATED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteBranch = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const branchId = req.params.branchId;

        const {Branch, Staff} = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
            Staff: Repository<Staff>;
        };

        const exBranch = await Branch.findOneBy({
            branchId,
        });

        if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

        if (exBranch.isPrimary) {
            return next(errorHandler(CONFLICT, PRIMARY_BRANCH_CANNOT_BE_DELETED));
        }

        const staffWithReservedBranches = await Staff.find({
            where: {
                reservedBranches: {
                    branchId: branchId,
                },
            },
            relations: ["reservedBranches"],
        });

        for (const staff of staffWithReservedBranches) {
            staff.reservedBranches = staff.reservedBranches.filter(
                (b) => b.branchId !== branchId
            );
            await Staff.save(staff);
        }

        const allStaff = await Staff.find({relations: ["reservedBranches"]});
        const staffToDelete = allStaff.filter(
            (staff) => !staff.reservedBranches || staff.reservedBranches.length === 0
        );

        if (staffToDelete.length > 0) {
            await Staff.remove(staffToDelete);
        }

        await Branch.remove(exBranch);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, BRANCH_DELETED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};
