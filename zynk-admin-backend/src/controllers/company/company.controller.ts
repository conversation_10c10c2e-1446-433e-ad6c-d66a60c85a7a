import {NextFunction, Request, Response} from "express";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {CONFLICT, NOT_FOUND, OK} from "../../constants/STATUS_CODES";
import {NOTHING_TO_PATCH} from "../../constants/common/msg";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Repository} from "typeorm";
import {CompanyInformation} from "../../models/company/companyinformation.model";
import {CompanyInformation as SuperCompanyInfo} from "../../models/admin/Tenant/companyinformation.model";
import {errorHandler} from "../../utils/errorHandler";
import {
    ACCOUNT_REP_ALREADY_EXIST,
    ACCOUNT_REP_NOT_EXIST,
    COMPANY_INFO_NOT_FOUND,
} from "../../constants/tenant/company/err";
import {Tenant} from "../../models/admin/Tenant/tenant.model";
import {TENANT_NOT_EXIST} from "../../constants/middleware/err";
import {
    ACCREP_UPDATED,
    ALL_ACCREPS_FETCHED,
    BANK_DETAILS_FETCHED,
    BANK_DETAILS_UPDATED,
    COMPANY_FETCHED,
    COMPANY_INFO_UPDATED,
    NEW_ACCREP_ADDED,
} from "../../constants/tenant/company/msg";
import {AccountRep} from "../../models/company/accountrep.model";
import {BankDetail} from "../../models/company/bankdetail.model";

export const patchCompanyInfo = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const rootRunner = req.rootQueryRunner;
    await rootRunner.connect();
    await rootRunner.startTransaction();

    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {CompanyInformation} = getRepositories(qRunner) as {
            CompanyInformation: Repository<CompanyInformation>;
        };

        const rootRepositories = getRepositories(rootRunner) as {
            Tenant: Repository<Tenant>;
            CompanyInformation: Repository<SuperCompanyInfo>;
        };
        const Su_CompanyInfo = rootRepositories.CompanyInformation;
        const Tenant = rootRepositories.Tenant;

        const tenantname = qRunner.connection.options.database as string;

        const dataToPatch = req.body;

        if (Object.keys(dataToPatch).length > 0) {
            const companyInfoArray = await CompanyInformation.find();

            if (!companyInfoArray)
                return next(errorHandler(NOT_FOUND, COMPANY_INFO_NOT_FOUND));

            const exCompanyInfo = companyInfoArray[0];

            const companyInfoFields = {
                regNumber: dataToPatch.regNumber,
                companyName: dataToPatch.companyName,
                addrLine1: dataToPatch.addrLine1,
                addrLine2: dataToPatch.addrLine2,
                city: dataToPatch.city,
                state: dataToPatch.state,
                postalCode: dataToPatch.postalCode,
                country: dataToPatch.country,
                logoUrl: dataToPatch.logoUrl,
                taxIDNumber: dataToPatch.taxIDNumber,
                description: dataToPatch.description,
                businessAlias: dataToPatch.businessAlias,
                regDocument: dataToPatch.regDocument,
                businessType: dataToPatch.businessType,
            };

            Object.entries(companyInfoFields).forEach(([key, value]) => {
                if (value !== undefined) {
                    (exCompanyInfo as any)[key] = value;
                }
            });

            await CompanyInformation.save(exCompanyInfo);

            const exTenant = await Tenant.findOne({
                where: {
                    subDomain: tenantname,
                },
                relations: ["tenantInfo"],
            });

            if (!exTenant) return next(errorHandler(NOT_FOUND, TENANT_NOT_EXIST));

            const tenantCompanyInfo = exTenant.tenantInfo;

            Object.entries(companyInfoFields).forEach(([key, value]) => {
                if (value !== undefined) {
                    (tenantCompanyInfo as any)[key] = value;
                }
            });

            await Su_CompanyInfo.save(tenantCompanyInfo);
        } else {
            ResponseHelper.success(res, OK, NOTHING_TO_PATCH);
        }

        await qRunner.commitTransaction();
        await rootRunner.commitTransaction();

        ResponseHelper.success(res, OK, COMPANY_INFO_UPDATED);
    } catch (error) {
        await qRunner.rollbackTransaction();

        await rootRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();

        await rootRunner.release();
    }
};

export const createCompanyRep = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {AccountRep, CompanyInformation} = getRepositories(qRunner) as {
            AccountRep: Repository<AccountRep>;
            CompanyInformation: Repository<CompanyInformation>;
        };

        const data = req.body as AccountRep;

        const exAccountRep = await AccountRep.findOne({
            where: [
                {emailAddress: data.emailAddress},
                {phoneNumber: data.phoneNumber},
            ],
        });

        if (exAccountRep)
            return next(errorHandler(CONFLICT, ACCOUNT_REP_ALREADY_EXIST));

        const newAccountRep = AccountRep.create(data);

        await AccountRep.save(newAccountRep);

        const allCompanyInfo = await CompanyInformation.find({
            relations: ["accountReps"],
        });

        if (!allCompanyInfo)
            return next(errorHandler(NOT_FOUND, COMPANY_INFO_NOT_FOUND));

        const companyInfo = allCompanyInfo[0];

        companyInfo.accountReps = companyInfo.accountReps || [];
        companyInfo.accountReps.push(newAccountRep);
        await CompanyInformation.save(companyInfo);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, NEW_ACCREP_ADDED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getAllCompanyReps = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const {CompanyInformation} = getRepositories(qRunner) as {
            AccountRep: Repository<AccountRep>;
            CompanyInformation: Repository<CompanyInformation>;
        };

        const allCompanyInfo = await CompanyInformation.find({
            relations: ["accountReps"],
        });

        if (!allCompanyInfo)
            return next(errorHandler(NOT_FOUND, COMPANY_INFO_NOT_FOUND));

        const companyInfo = allCompanyInfo[0];

        ResponseHelper.success(
            res,
            OK,
            ALL_ACCREPS_FETCHED,
            companyInfo.accountReps
        );
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchCompanyRep = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {AccountRep} = getRepositories(qRunner) as {
            AccountRep: Repository<AccountRep>;
        };

        const accountRepId = req.params.accountRepId;

        const data = req.body as AccountRep;

        const exAccountRep = await AccountRep.findOneBy({
            accountRepId,
        });

        if (!exAccountRep)
            return next(errorHandler(CONFLICT, ACCOUNT_REP_NOT_EXIST));

        Object.entries(data).forEach(([key, value]) => {
            if (value !== undefined) {
                (exAccountRep as any)[key] = value;
            }
        });

        await AccountRep.save(exAccountRep);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, ACCREP_UPDATED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteCompanyRep = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {AccountRep} = getRepositories(qRunner) as {
            AccountRep: Repository<AccountRep>;
        };

        const accountRepId = req.params.accountRepId;

        const exAccountRep = await AccountRep.findOneBy({accountRepId});

        if (!exAccountRep) {
            return next(errorHandler(NOT_FOUND, ACCOUNT_REP_NOT_EXIST));
        }

        await AccountRep.remove(exAccountRep);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Account representative deleted successfully.");
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getBankDetails = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    await qRunner.startTransaction();

    try {
        const {CompanyInformation, BankDetail} = getRepositories(qRunner) as {
            CompanyInformation: Repository<CompanyInformation>;
            BankDetail: Repository<BankDetail>;
        };

        const allCompanyInfo = await CompanyInformation.find({
            relations: ["bankdetail"],
        });

        if (!allCompanyInfo)
            return next(errorHandler(NOT_FOUND, COMPANY_INFO_NOT_FOUND));

        const companyInfo = allCompanyInfo[0];

        let result = companyInfo.bankdetail;

        if (!companyInfo.bankdetail) {
            result = BankDetail.create({
                bankName: "",
                sortCode: "",
                accountNumber: "",
            });

            companyInfo.bankdetail = result
            await CompanyInformation.save(companyInfo)
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, BANK_DETAILS_FETCHED, result);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchBankDetail = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {BankDetail, CompanyInformation} = getRepositories(qRunner) as {
            BankDetail: Repository<BankDetail>;
            CompanyInformation: Repository<CompanyInformation>;
        };

        const data = req.body as BankDetail;

        const allCompanyInfo = await CompanyInformation.find({
            relations: ["bankdetail"],
        });

        if (!allCompanyInfo)
            return next(errorHandler(NOT_FOUND, COMPANY_INFO_NOT_FOUND));

        const companyInfo = allCompanyInfo[0];

        if (!companyInfo.bankdetail) {
            const newBankDetail = BankDetail.create(data);

            companyInfo.bankdetail = newBankDetail;
            await CompanyInformation.save(companyInfo);
        } else {
            Object.entries(data).forEach(([key, value]) => {
                if (value !== undefined) {
                    (companyInfo.bankdetail as any)[key] = value;
                }
            });

            await CompanyInformation.save(companyInfo);
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, BANK_DETAILS_UPDATED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getCompanyInfo = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const {CompanyInformation} = getRepositories(qRunner) as {
            CompanyInformation: Repository<CompanyInformation>;
        };

        const allCompanyInfo = await CompanyInformation.find({
            relations: ["branches"],
        });

        if (!allCompanyInfo)
            return next(errorHandler(NOT_FOUND, COMPANY_INFO_NOT_FOUND));

        const companyInfo = allCompanyInfo[0];

        ResponseHelper.success(res, OK, COMPANY_FETCHED, companyInfo);
    } catch (error) {
        next(error);
    } finally {
        await qRunner.release();
    }
};
