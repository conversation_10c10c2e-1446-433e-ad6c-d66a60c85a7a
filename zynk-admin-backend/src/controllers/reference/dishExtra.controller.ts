import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Not, Repository} from "typeorm";
import {errorHandler} from "../../utils/errorHandler";
import {CONFLICT, CREATED, NOT_FOUND, OK,} from "../../constants/STATUS_CODES";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {DISH_EXTRA_ALREADY_EXISTS, DISH_EXTRA_NOT_FOUND,} from "../../constants/reference/dishExtra/err";
import {DISH_EXTRA_CREATED, DISH_EXTRA_DELETED, DISH_EXTRA_UPDATED,} from "../../constants/reference/dishExtra/msg";
import {DishExtra} from "../../models/foodmenu/custom/extra.model";

export const createDishExtra = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {DishExtra} = getRepositories(qRunner) as {
            DishExtra: Repository<DishExtra>;
        };

        const data = req.body;

        const exDishExtra = await DishExtra.findOne({
            where: {
                name: data.name,
            },
        });

        if (exDishExtra)
            return next(errorHandler(CONFLICT, DISH_EXTRA_ALREADY_EXISTS));

        const newDishExtra = DishExtra.create(data);
        await DishExtra.save(newDishExtra);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, CREATED, DISH_EXTRA_CREATED, newDishExtra);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getAllDishExtras = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect()

    try {
        const {DishExtra} = getRepositories(qRunner) as {
            DishExtra: Repository<DishExtra>;
        };

        const dishExtras = await DishExtra.find();

        ResponseHelper.success(
            res,
            OK,
            "Dish extras retrieved successfully",
            dishExtras
        );
    } catch (error) {
        next(error);
    }
    finally {
        await qRunner.release();
    }
};

export const getDishExtraById = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect()

    try {
        const {DishExtra} = getRepositories(qRunner) as {
            DishExtra: Repository<DishExtra>;
        };

        const {extraId} = req.params;

        const dishExtra = await DishExtra.findOne({
            where: {extraId},
        });

        if (!dishExtra) {
            return next(errorHandler(NOT_FOUND, DISH_EXTRA_NOT_FOUND));
        }

        ResponseHelper.success(
            res,
            OK,
            "Dish extra retrieved successfully",
            dishExtra
        );
    } catch (error) {
        next(error);
    }
    finally {
        await qRunner.release();
    }
};

export const updateDishExtra = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {DishExtra} = getRepositories(qRunner) as {
            DishExtra: Repository<DishExtra>;
        };

        const {extraId} = req.params;
        const data = req.body;

        const dishExtra = await DishExtra.findOne({
            where: {extraId},
        });

        if (!dishExtra) {
            return next(errorHandler(NOT_FOUND, DISH_EXTRA_NOT_FOUND));
        }

        if (data.name) {
            const exDishExtra = await DishExtra.findOne({
                where: {
                    name: data.name,
                    extraId: Not(extraId),
                },
            });

            if (exDishExtra) {
                return next(errorHandler(CONFLICT, DISH_EXTRA_ALREADY_EXISTS));
            }
        }

        await DishExtra.update(extraId, data);

        const updatedDishExtra = await DishExtra.findOne({
            where: {extraId},
        });

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, DISH_EXTRA_UPDATED, updatedDishExtra);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteDishExtra = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {DishExtra} = getRepositories(qRunner) as {
            DishExtra: Repository<DishExtra>;
        };

        const {extraId} = req.params;

        const dishExtra = await DishExtra.findOne({
            where: {extraId},
        });

        if (!dishExtra) {
            return next(errorHandler(NOT_FOUND, DISH_EXTRA_NOT_FOUND));
        }

        await DishExtra.delete(extraId);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, DISH_EXTRA_DELETED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};