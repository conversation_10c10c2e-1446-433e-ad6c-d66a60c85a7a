import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Not, Repository} from "typeorm";
import {SectionIcon} from "../../models/reference/sectionicon.model";
import {errorHandler} from "../../utils/errorHandler";
import {CONFLICT, CREATED, NOT_FOUND, OK,} from "../../constants/STATUS_CODES";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {SECTION_ICON_ALREADY_EXISTS, SECTION_ICON_NOT_FOUND,} from "../../constants/reference/sectionicon/err";
import {
  SECTION_ICON_CREATED,
  SECTION_ICON_DELETED,
  SECTION_ICON_UPDATED,
} from "../../constants/reference/sectionicon/msg";

export const createSectionIcon = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {SectionIcon} = getRepositories(qRunner) as {
            SectionIcon: Repository<SectionIcon>;
        };

        const data = req.body;

        const exSectionIcon = await SectionIcon.findOne({
            where: {
                iconName: data.iconName,
            },
        });

        if (exSectionIcon)
            return next(errorHandler(CONFLICT, SECTION_ICON_ALREADY_EXISTS));

        const newSectionIcon = SectionIcon.create(data);
        await SectionIcon.save(newSectionIcon);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, CREATED, SECTION_ICON_CREATED, newSectionIcon);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getAllSectionIcons = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect()

    try {
        const {SectionIcon} = getRepositories(qRunner) as {
            SectionIcon: Repository<SectionIcon>;
        };

        const sectionIcons = await SectionIcon.find();

        ResponseHelper.success(
            res,
            OK,
            "Section icons retrieved successfully",
            sectionIcons
        );
    } catch (error) {
        next(error);
    }
    finally{
        await qRunner.release()
    }
};

// Get section icon by ID
export const getSectionIconById = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect()

    try {
        const {SectionIcon} = getRepositories(qRunner) as {
            SectionIcon: Repository<SectionIcon>;
        };

        const {sectionIconId} = req.params;

        const sectionIcon = await SectionIcon.findOne({
            where: {sectionIconId},
        });

        if (!sectionIcon) {
            return next(errorHandler(NOT_FOUND, SECTION_ICON_NOT_FOUND));
        }

        ResponseHelper.success(
            res,
            OK,
            "Section icon retrieved successfully",
            sectionIcon
        );
    } catch (error) {
        next(error);
    }
    finally{
        await qRunner.release()
    }
};

export const updateSectionIcon = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {SectionIcon} = getRepositories(qRunner) as {
            SectionIcon: Repository<SectionIcon>;
        };

        const {sectionIconId} = req.params;
        const data = req.body;

        const sectionIcon = await SectionIcon.findOne({
            where: {sectionIconId},
        });

        if (!sectionIcon) {
            return next(errorHandler(NOT_FOUND, SECTION_ICON_NOT_FOUND));
        }

        if (data.iconName) {
            const exSectionIcon = await SectionIcon.findOne({
                where: {
                    iconName: data.iconName,
                    sectionIconId: Not(sectionIconId),
                },
            });

            if (exSectionIcon) {
                return next(errorHandler(CONFLICT, SECTION_ICON_ALREADY_EXISTS));
            }
        }

        await SectionIcon.update(sectionIconId, data);

        const updatedSectionIcon = await SectionIcon.findOne({
            where: {sectionIconId},
        });

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, SECTION_ICON_UPDATED, updatedSectionIcon);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteSectionIcon = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {SectionIcon} = getRepositories(qRunner) as {
            SectionIcon: Repository<SectionIcon>;
        };

        const {sectionIconId} = req.params;

        const sectionIcon = await SectionIcon.findOne({
            where: {sectionIconId},
        });

        if (!sectionIcon) {
            return next(errorHandler(NOT_FOUND, SECTION_ICON_NOT_FOUND));
        }

        await SectionIcon.delete(sectionIconId);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, SECTION_ICON_DELETED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};
