import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Not, Repository} from "typeorm";
import {errorHandler} from "../../utils/errorHandler";
import {CONFLICT, CREATED, NOT_FOUND, OK,} from "../../constants/STATUS_CODES";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {ALLERGY_ALREADY_EXISTS, ALLERGY_NOT_FOUND,} from "../../constants/reference/allergy/err";
import {ALLERGY_CREATED, ALLERGY_DELETED, ALLERGY_UPDATED,} from "../../constants/reference/allergy/msg";
import {Allergy} from "../../models/reference/allergy.model";

export const createAllergy = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {Allergy} = getRepositories(qRunner) as {
            Allergy: Repository<Allergy>;
        };

        const data = req.body;

        const exAllergy = await Allergy.findOne({
            where: {
                name: data.name,
            },
        });

        if (exAllergy)
            return next(errorHandler(CONFLICT, ALLERGY_ALREADY_EXISTS));

        const newAllergy = Allergy.create(data);
        await Allergy.save(newAllergy);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, CREATED, ALLERGY_CREATED, newAllergy);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getAllAllergies = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect()

    try {
        const {Allergy} = getRepositories(qRunner) as {
            Allergy: Repository<Allergy>;
        };

        const allergies = await Allergy.find();

        ResponseHelper.success(
            res,
            OK,
            "Allergies retrieved successfully",
            allergies
        );
    } catch (error) {
        next(error);
    }
    finally {
        await qRunner.release();
    }
};

export const getAllergyById = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect()

    try {
        const {Allergy} = getRepositories(qRunner) as {
            Allergy: Repository<Allergy>;
        };

        const {allergyId} = req.params;

        const allergy = await Allergy.findOne({
            where: {allergyId},
        });

        if (!allergy) {
            return next(errorHandler(NOT_FOUND, ALLERGY_NOT_FOUND));
        }

        ResponseHelper.success(
            res,
            OK,
            "Allergy retrieved successfully",
            allergy
        );
    } catch (error) {
        next(error);
    }
    finally {
        await qRunner.release();
    }
};

export const updateAllergy = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {Allergy} = getRepositories(qRunner) as {
            Allergy: Repository<Allergy>;
        };

        const {allergyId} = req.params;
        const data = req.body;

        const allergy = await Allergy.findOne({
            where: {allergyId},
        });

        if (!allergy) {
            return next(errorHandler(NOT_FOUND, ALLERGY_NOT_FOUND));
        }

        if (data.name) {
            const exAllergy = await Allergy.findOne({
                where: {
                    name: data.name,
                    allergyId: Not(allergyId),
                },
            });

            if (exAllergy) {
                return next(errorHandler(CONFLICT, ALLERGY_ALREADY_EXISTS));
            }
        }

        await Allergy.update(allergyId, data);

        const updatedAllergy = await Allergy.findOne({
            where: {allergyId},
        });

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, ALLERGY_UPDATED, updatedAllergy);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteAllergy = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {Allergy} = getRepositories(qRunner) as {
            Allergy: Repository<Allergy>;
        };

        const {allergyId} = req.params;

        const allergy = await Allergy.findOne({
            where: {allergyId},
        });

        if (!allergy) {
            return next(errorHandler(NOT_FOUND, ALLERGY_NOT_FOUND));
        }

        await Allergy.delete(allergyId);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, ALLERGY_DELETED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};