import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Not, Repository} from "typeorm";
import {errorHandler} from "../../utils/errorHandler";
import {CONFLICT, CREATED, NOT_FOUND, OK,} from "../../constants/STATUS_CODES";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {DISH_ADDON_ALREADY_EXISTS, DISH_ADDON_NOT_FOUND,} from "../../constants/reference/dishAddon/err";
import {DISH_ADDON_CREATED, DISH_ADDON_DELETED, DISH_ADDON_UPDATED,} from "../../constants/reference/dishAddon/msg";
import {DishAddon} from "../../models/foodmenu/custom/addon.model";

export const createDishAddon = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {DishAddon} = getRepositories(qRunner) as {
            DishAddon: Repository<DishAddon>;
        };

        const data = req.body;

        const exDishAddon = await DishAddon.findOne({
            where: {
                name: data.name,
            },
        });

        if (exDishAddon)
            return next(errorHandler(CONFLICT, DISH_ADDON_ALREADY_EXISTS));

        const newDishAddon = DishAddon.create(data);
        await DishAddon.save(newDishAddon);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, CREATED, DISH_ADDON_CREATED, newDishAddon);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getAllDishAddons = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;
    await qRunner.connect()

    try {
        const {DishAddon} = getRepositories(qRunner) as {
            DishAddon: Repository<DishAddon>;
        };

        const dishAddons = await DishAddon.find();

        ResponseHelper.success(
            res,
            OK,
            "Dish addons retrieved successfully",
            dishAddons
        );
    } catch (error) {
        next(error);
    }
    finally {
        await qRunner.release();
    }
};

export const getDishAddonById = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect()

    try {
        const {DishAddon} = getRepositories(qRunner) as {
            DishAddon: Repository<DishAddon>;
        };

        const {addonId} = req.params;

        const dishAddon = await DishAddon.findOne({
            where: {addonId},
        });

        if (!dishAddon) {
            return next(errorHandler(NOT_FOUND, DISH_ADDON_NOT_FOUND));
        }

        ResponseHelper.success(
            res,
            OK,
            "Dish addon retrieved successfully",
            dishAddon
        );
    } catch (error) {
        next(error);
    }
    finally {
        await qRunner.release();
    }
};

export const updateDishAddon = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {DishAddon} = getRepositories(qRunner) as {
            DishAddon: Repository<DishAddon>;
        };

        const {addonId} = req.params;
        const data = req.body;

        const dishAddon = await DishAddon.findOne({
            where: {addonId},
        });

        if (!dishAddon) {
            return next(errorHandler(NOT_FOUND, DISH_ADDON_NOT_FOUND));
        }

        if (data.name) {
            const exDishAddon = await DishAddon.findOne({
                where: {
                    name: data.name,
                    addonId: Not(addonId),
                },
            });

            if (exDishAddon) {
                return next(errorHandler(CONFLICT, DISH_ADDON_ALREADY_EXISTS));
            }
        }

        await DishAddon.update(addonId, data);

        const updatedDishAddon = await DishAddon.findOne({
            where: {addonId},
        });

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, DISH_ADDON_UPDATED, updatedDishAddon);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteDishAddon = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {DishAddon} = getRepositories(qRunner) as {
            DishAddon: Repository<DishAddon>;
        };

        const {addonId} = req.params;

        const dishAddon = await DishAddon.findOne({
            where: {addonId},
        });

        if (!dishAddon) {
            return next(errorHandler(NOT_FOUND, DISH_ADDON_NOT_FOUND));
        }

        await DishAddon.delete(addonId);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, DISH_ADDON_DELETED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};