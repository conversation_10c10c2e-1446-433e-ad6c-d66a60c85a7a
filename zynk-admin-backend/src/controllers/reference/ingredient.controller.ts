import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Not, Repository} from "typeorm";
import {errorHandler} from "../../utils/errorHandler";
import {CONFLICT, CREATED, NOT_FOUND, OK,} from "../../constants/STATUS_CODES";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {INGREDIENT_ALREADY_EXISTS, INGREDIENT_NOT_FOUND,} from "../../constants/reference/ingredient/err";
import {INGREDIENT_CREATED, INGREDIENT_DELETED, INGREDIENT_UPDATED,} from "../../constants/reference/ingredient/msg";
import {Ingredient} from "../../models/reference/ingredient.model";

export const createIngredient = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {Ingredient} = getRepositories(qRunner) as {
            Ingredient: Repository<Ingredient>;
        };

        const data = req.body;

        const exIngredient = await Ingredient.findOne({
            where: {
                name: data.name,
            },
        });

        if (exIngredient)
            return next(errorHandler(CONFLICT, INGREDIENT_ALREADY_EXISTS));

        const newIngredient = Ingredient.create(data);
        await Ingredient.save(newIngredient);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, CREATED, INGREDIENT_CREATED, newIngredient);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getAllIngredients = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect()

    try {
        const {Ingredient} = getRepositories(qRunner) as {
            Ingredient: Repository<Ingredient>;
        };

        const ingredients = await Ingredient.find();

        ResponseHelper.success(
            res,
            OK,
            "Ingredients retrieved successfully",
            ingredients
        );
    } catch (error) {
        next(error);
    }
    finally {
        await qRunner.release();
    }
    
};

export const getIngredientById = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect()

    try {
        const {Ingredient} = getRepositories(qRunner) as {
            Ingredient: Repository<Ingredient>;
        };

        const {ingredientId} = req.params;

        const ingredient = await Ingredient.findOne({
            where: {ingredientId},
        });

        if (!ingredient) {
            return next(errorHandler(NOT_FOUND, INGREDIENT_NOT_FOUND));
        }

        ResponseHelper.success(
            res,
            OK,
            "Ingredient retrieved successfully",
            ingredient
        );
    } catch (error) {
        next(error);
    }
    finally {
        await qRunner.release();
    }
};

export const updateIngredient = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {Ingredient} = getRepositories(qRunner) as {
            Ingredient: Repository<Ingredient>;
        };

        const {ingredientId} = req.params;
        const data = req.body;

        const ingredient = await Ingredient.findOne({
            where: {ingredientId},
        });

        if (!ingredient) {
            return next(errorHandler(NOT_FOUND, INGREDIENT_NOT_FOUND));
        }

        if (data.name) {
            const exIngredient = await Ingredient.findOne({
                where: {
                    name: data.name,
                    ingredientId: Not(ingredientId),
                },
            });

            if (exIngredient) {
                return next(errorHandler(CONFLICT, INGREDIENT_ALREADY_EXISTS));
            }
        }

        await Ingredient.update(ingredientId, data);

        const updatedIngredient = await Ingredient.findOne({
            where: {ingredientId},
        });

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, INGREDIENT_UPDATED, updatedIngredient);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const deleteIngredient = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {Ingredient} = getRepositories(qRunner) as {
            Ingredient: Repository<Ingredient>;
        };

        const {ingredientId} = req.params;

        const ingredient = await Ingredient.findOne({
            where: {ingredientId},
        });

        if (!ingredient) {
            return next(errorHandler(NOT_FOUND, INGREDIENT_NOT_FOUND));
        }

        await Ingredient.delete(ingredientId);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, INGREDIENT_DELETED);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};