import {NextFunction, Request, Response} from "express";
import log from "../../helpers/system/logger.helper";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Not, Repository} from "typeorm";
import {Tax} from "../../models/subscription/tax/tax.model";
import {buildWhereClause} from "../../utils/whereBuilder";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {CREATED, OK} from "../../constants/STATUS_CODES";
import {CreateTax} from "../../validation/subscription/tax.validation";
import {TaxAlreadyExistEx} from "../../exceptions/controller/subscription/Tax/impl/TaxAlreadyExistEx";
import {TaxNotFoundEx} from "../../exceptions/controller/subscription/Tax/impl/TaxNotFoundEx";
import {LowerLGreaterLEx} from "../../exceptions/controller/subscription/Tax/impl/LowerLGreaterLEx";
import {getRecordsAd} from "../../utils/Common.util";
import {taxResTypeConfig} from "../../helpers/subscription/tax.helper";
import {ResponseType} from "../../types/system";

export const listTax = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[listTax] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    try {
        const {responseType, ...rest} = req.query as unknown as {
            responseType: ResponseType,
            [key: string]: any
        }
        ResponseHelper.success(
            res,
            OK,
            "Taxes fetched successfully",
            await getRecordsAd((getRepositories(qRunner) as {
                Tax: Repository<Tax>
            }).Tax, buildWhereClause(rest), taxResTypeConfig, responseType)
        );
    } catch (error) {
        log.error("[listTax] Error fetching taxes:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[listTax] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
};

export const createTax = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[createTax] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);
    await qRunner.startTransaction();
    log.debug("[createTax] Started transaction.", null, process.env.SUPERADMIN_DB);

    try {
        const {Tax} = getRepositories(qRunner) as {
            Tax: Repository<Tax>;
        };

        const createTaxData: CreateTax = req.body;
        const {taxGovId, name, taxPercentage, description, isActive, ...data} = createTaxData;
        const where = [];
        if (taxGovId) where.push({taxGovId: taxGovId});
        where.push(buildWhereClause(data))
        const exTax = await Tax.findOne({
            where: where
        });
        if (exTax) throw new TaxAlreadyExistEx(exTax, createTaxData);

        const newTax = Tax.create({
            ...createTaxData
        });
        const savedTax = await Tax.save(newTax);

        await qRunner.commitTransaction();
        log.debug("[createTax] Transaction committed successfully", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(
            res,
            CREATED,
            "Tax Created Successfully.",
            savedTax
        );
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[createTax] Transaction rolled back successfully", null, process.env.SUPERADMIN_DB);
        log.error("[createTax] Error creating tax:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[createTax] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
}

export const patchTax = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[patchTax] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);
    await qRunner.startTransaction();
    log.debug("[patchTax] Started transaction.", null, process.env.SUPERADMIN_DB);

    try {
        const {Tax} = getRepositories(qRunner) as {
            Tax: Repository<Tax>
        };

        const {taxId, ...updateData} = req.body;

        const existingTax = await Tax.findOne({
            where: {taxId},
        });

        if (!existingTax) {
            throw new TaxNotFoundEx();
        }

        if (
            updateData.countryCode ||
            updateData.stateCode ||
            updateData.upperLimit ||
            updateData.lowerLimit ||
            updateData.isCentral ||
            updateData.taxGovId
        ) {
            const checkData = {
                countryCode: updateData.countryCode ?? existingTax.countryCode,
                stateCode: updateData.stateCode ?? existingTax.stateCode,
                upperLimit: updateData.upperLimit ?? existingTax.upperLimit,
                lowerLimit: updateData.lowerLimit ?? existingTax.lowerLimit,
                isCentral: updateData.isCentral ?? existingTax.isCentral,
                taxGovId: updateData.taxGovId ?? existingTax.taxGovId,
            };
            if (checkData.lowerLimit > checkData.upperLimit) throw new LowerLGreaterLEx();

            const where: any[] = [];

            if (checkData.taxGovId) {
                where.push({taxGovId: checkData.taxGovId, taxId: Not(taxId)});
            }

            where.push(
                {
                    countryCode: checkData.countryCode,
                    stateCode: checkData.stateCode ?? null,
                    upperLimit: Number(checkData.upperLimit),
                    lowerLimit: Number(checkData.lowerLimit),
                    isCentral: checkData.isCentral,
                    taxId: Not(taxId)
                }
            );
            const exTax = await Tax.findOne({where});

            if (exTax) throw new TaxAlreadyExistEx(exTax, checkData);
        }

        const patchedTax = await Tax.save({
            ...existingTax,
            ...updateData,
        });

        await qRunner.commitTransaction();
        log.debug("[patchTax] Transaction committed successfully", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Tax patched successfully", patchedTax)
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[patchTax] Transaction rolled back successfully", null, process.env.SUPERADMIN_DB);
        log.error("[patchTax] Error patching tax:", error, process.env.SUPERADMIN_DB);
        next(error);

    } finally {
        await qRunner.release();
        log.debug("[patchTax] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
};

export const deleteTax = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[deleteTax] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);
    await qRunner.startTransaction();
    log.debug("[deleteTax] Started transaction.", null, process.env.SUPERADMIN_DB);

    try {
        const {Tax} = getRepositories(qRunner) as {
            Tax: Repository<Tax>;
        };

        const {taxId} = req.params

        const tax = await Tax.findOne({
            where: {taxId},
        });

        if (!tax) throw new TaxNotFoundEx();

        await Tax.delete({taxId});

        await qRunner.commitTransaction();
        log.debug("[deleteTax] Transaction committed successfully", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Tax deleted successfully")

    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[deleteTax] Transaction rolled back successfully", null, process.env.SUPERADMIN_DB);
        log.error("[deleteTax] Error deleting tax:", error, process.env.SUPERADMIN_DB);
        next(error);

    } finally {
        await qRunner.release();
        log.debug("[deleteTax] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
};