import {NextFunction, Request, Response} from "express";
import log from "../../helpers/system/logger.helper";
import {Feature} from "../../models/subscription/feature/feature.model";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {OK} from "../../constants/STATUS_CODES";
import {getRecordsForPermAction} from "../../utils/Common.util";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Not, Repository} from "typeorm";
import {buildWhereClause} from "../../utils/whereBuilder";
import {ResponseType} from "../../types/system";
import {featureResTypeConfig} from "../../helpers/subscription/feature.helper";
import {FormatType} from "../../types/pBAC/formatterTypes";
import {createFeatureType, patchFeatureType} from "../../validation/subscription/feature.validation";
import {FeaturePermissionAction} from "../../models/subscription/feature/FeaturePermissionAction.model";
import {PermissionAction} from "../../models/pBAC/PermissionAction";
import {PermActionModelType} from "../../types/pBAC";
import {FeatureAlreadyExistEx} from "../../exceptions/controller/subscription/Feature/impl/FeatureAlreadyExistEx";
import {FeatureNotFoundEx} from "../../exceptions/controller/subscription/Feature/impl/FeatureNotFoundEx";
import {
    CannotDeleteFeatureAssociatedEx
} from "../../exceptions/controller/subscription/Feature/impl/CannotDeleteFeatureAssociatedEx";


export const listFeatures = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[listFeatures] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);
    try {
        const {responseType, formatType, ...rest} = req.query as unknown as {
            responseType: ResponseType;
            formatType: FormatType;
            [key: string]: any;
        };
        ResponseHelper.success(res, OK, "Features fetched successfully", await getRecordsForPermAction((getRepositories(qRunner) as {
            Feature: Repository<Feature>
        }).Feature, buildWhereClause(rest), "featurePermissionActions", formatType, responseType, featureResTypeConfig));
    } catch (error) {
        log.error("[listFeatures] Error fetching Features:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        log.debug("[listFeatures] Query Runner Released.", null, process.env.SUPERADMIN_DB);
        await qRunner.release();
    }
}

export const createFeature = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[createFeature] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("[createFeature] Transaction Started.", null, process.env.SUPERADMIN_DB);
    try {
        const {Feature, PermissionAction, FeaturePermissionAction} = getRepositories(qRunner) as {
            Feature: Repository<Feature>;
            PermissionAction: Repository<PermissionAction>;
            FeaturePermissionAction: Repository<FeaturePermissionAction>;
        }
        const {featurePermissionActions, ...requestBody}: createFeatureType = req.body;
        const exFeature = await Feature.findOne({where: {name: requestBody.name}});
        if (exFeature) throw new FeatureAlreadyExistEx(requestBody.name);
        const feature = Feature.create(requestBody);
        const savedFeature = await Feature.save(feature);
        if (featurePermissionActions) await savedFeature.updatePermissionAction(featurePermissionActions as PermActionModelType[], PermissionAction, FeaturePermissionAction);

        await qRunner.commitTransaction()
        log.debug("[createFeature] Transaction Committed.", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Feature created successfully", await getRecordsForPermAction((getRepositories(qRunner) as {
            Feature: Repository<Feature>
        }).Feature, buildWhereClause({featureId: savedFeature.featureId}), "featurePermissionActions", FormatType.OBJECT_ARR_OBJECT, ResponseType.FULL_WITH_ASSOCIATION, featureResTypeConfig))
    } catch (error) {
        await qRunner.rollbackTransaction()
        log.debug("[createFeature] Transaction Rolled Back.", null, process.env.SUPERADMIN_DB);
        log.error("[createFeature] Error creating Feature:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        log.debug("[createFeature] Query Runner Released.", null, process.env.SUPERADMIN_DB);
        await qRunner.release();
    }
}

export const patchFeature = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[patchFeature] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("[patchFeature] Transaction Started.", null, process.env.SUPERADMIN_DB);
    try {
        const {Feature, PermissionAction, FeaturePermissionAction} = getRepositories(qRunner) as {
            Feature: Repository<Feature>;
            PermissionAction: Repository<PermissionAction>;
            FeaturePermissionAction: Repository<FeaturePermissionAction>;
        }
        const {featurePermissionActions, featureId, ...requestBody}: patchFeatureType = req.body;

        const feature = await Feature.findOne({where: {featureId: featureId}});
        if (!feature) throw new FeatureNotFoundEx();
        if (requestBody.name) {
            const exFeature = await Feature.findOne({
                where: {
                    name: requestBody.name,
                    featureId: Not(feature.featureId)
                }
            });
            if (exFeature) throw new FeatureAlreadyExistEx(requestBody.name);
        }
        await Feature.save({...feature, ...requestBody});
        if (featurePermissionActions) await feature.updatePermissionAction(featurePermissionActions as PermActionModelType[], PermissionAction, FeaturePermissionAction)

        await qRunner.commitTransaction()
        log.debug("[patchFeature] Transaction Committed.", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Feature updated successfully", await getRecordsForPermAction((getRepositories(qRunner) as {
            Feature: Repository<Feature>
        }).Feature, buildWhereClause({featureId: feature.featureId}), "featurePermissionActions", FormatType.OBJECT_ARR_OBJECT, ResponseType.FULL_WITH_ASSOCIATION, featureResTypeConfig));
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[patchFeature] Transaction Rolled Back.", null, process.env.SUPERADMIN_DB);
        log.error("[patchFeature] Error updating Feature:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        log.debug("[patchFeature] Query Runner Released.", null, process.env.SUPERADMIN_DB);
        await qRunner.release();
    }
}

export const deleteFeature = async (req: Request, res: Response, next: NextFunction) => {
    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[deleteFeature] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("[deleteFeature] Transaction Started.", null, process.env.SUPERADMIN_DB);
    try {
        const {Feature} = getRepositories(qRunner) as {
            Feature: Repository<Feature>;
        }
        const {featureId} = req.params;
        const feature = await Feature.findOne({where: {featureId: featureId}, relations: ["subscriptionTiers"]});
        if (!feature) throw new FeatureNotFoundEx();
        if (feature.subscriptionTiers.length > 0) throw new CannotDeleteFeatureAssociatedEx();
        await Feature.remove(feature);
        await qRunner.commitTransaction()
        log.debug("[deleteFeature] Transaction Committed.", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, OK, "Feature deleted successfully", null);
    } catch (error) {
        await qRunner.rollbackTransaction()
        log.debug("[deleteFeature] Transaction Rolled Back.", null, process.env.SUPERADMIN_DB);
        log.error("[deleteFeature] Error deleting Feature:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        log.debug("[deleteFeature] Query Runner Released.", null, process.env.SUPERADMIN_DB);
        await qRunner.release();
    }
}