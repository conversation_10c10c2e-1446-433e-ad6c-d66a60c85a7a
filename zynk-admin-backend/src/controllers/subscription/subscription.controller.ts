import {NextFunction, Request, Response} from "express";
import log from "../../helpers/system/logger.helper";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {OK} from "../../constants/STATUS_CODES";
import {Repository} from "typeorm";
import {Subscription} from "../../models/subscription/subscriptions.model";
import {manageSubscriptionType} from "../../validation/subscription/subscription.validation";
import {Tenant} from "../../models/admin/Tenant/tenant.model";
import {TenantNotFoundException} from "../../exceptions/controller/tenant/admin/impl/TenantNotFoundException";
import {activatePlan, SubscriptionRequestData} from "../../helpers/subscription/subscription.helper";
import {Duration, SubsOperationType} from "../../types/subscription";

export const mangeSubscription = async (req: Request, res: Response, next: NextFunction) => {

    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[manageSubscription] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB)
    await qRunner.startTransaction();
    log.debug("[manageSubscription] Transaction Started.", null, process.env.SUPERADMIN_DB)
    try {
        const {Tenant} = getRepositories(qRunner) as {
            Tenant: Repository<Tenant>,
        }
        const subscriptionData: manageSubscriptionType = req.body;

        const tenant = await Tenant.findOne({
            where: {tenantId: subscriptionData.tenantId}
        })
        if (!tenant) throw new TenantNotFoundException();
        const subscriptionActivationData: SubscriptionRequestData = {
            tenantId: tenant.tenantId,
            subscriptionTierId: subscriptionData.subTierId,
            duration: subscriptionData.durationType as Duration,
            durationValue: subscriptionData.durationValue,
            overrideType: subscriptionData.overRideType as SubsOperationType,
            paidPrice: subscriptionData.payedAmount,
            allowDue: subscriptionData.allowDue,
            countryCode: subscriptionData.countryCode,
            stateCode: subscriptionData.stateCode
        };
        await activatePlan(
            null,
            qRunner,
            subscriptionActivationData,
            tenant.subDomain
        );
        await qRunner.commitTransaction();
        log.debug("[manageSubscription] Transaction Committed.", null, process.env.SUPERADMIN_DB)
        ResponseHelper.success(res, OK, "test")
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[manageSubscription] Transaction RollBacked.", null, process.env.SUPERADMIN_DB)
        next(error);
    } finally {
        log.debug("[manageSubscription] Query Runner Released.", null, process.env.SUPERADMIN_DB)
        await qRunner.release();
    }
}
export const viewAllSubscription = async (req: Request, res: Response, next: NextFunction) => {

    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[manageSubscription] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB)
    await qRunner.startTransaction();
    log.debug("[manageSubscription] Transaction Started.", null, process.env.SUPERADMIN_DB)
    try {
        const {Subscription} = getRepositories(qRunner) as {
            Subscription: Repository<Subscription>
        }


        await qRunner.commitTransaction();
        log.debug("[manageSubscription] Transaction Committed.", null, process.env.SUPERADMIN_DB)
        ResponseHelper.success(res, OK, "test")
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[manageSubscription] Transaction RollBacked.", null, process.env.SUPERADMIN_DB)
        next(error);
    } finally {
        log.debug("[manageSubscription] Query Runner Released.", null, process.env.SUPERADMIN_DB)
        await qRunner.release();
    }
}
export const terminateSubscription = async (req: Request, res: Response, next: NextFunction) => {

    const qRunner = req.rootQueryRunner;
    await qRunner.connect();
    log.debug("[manageSubscription] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB)
    await qRunner.startTransaction();
    log.debug("[manageSubscription] Transaction Started.", null, process.env.SUPERADMIN_DB)
    try {
        const {Subscription} = getRepositories(qRunner) as {
            Subscription: Repository<Subscription>
        }


        await qRunner.commitTransaction();
        log.debug("[manageSubscription] Transaction Committed.", null, process.env.SUPERADMIN_DB)
        ResponseHelper.success(res, OK, "test")
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[manageSubscription] Transaction RollBacked.", null, process.env.SUPERADMIN_DB)
        next(error);
    } finally {
        log.debug("[manageSubscription] Query Runner Released.", null, process.env.SUPERADMIN_DB)
        await qRunner.release();
    }
}