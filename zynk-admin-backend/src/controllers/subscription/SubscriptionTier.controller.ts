import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Not, Repository} from "typeorm";
import {SubscriptionTier} from "../../models/subscription/subTier/subscriptiontier.model";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {CREATED, OK} from "../../constants/STATUS_CODES";
import {ALL_SUBS_FETCHED, SUB_UPDATED, SUBSCRIP_CREATED} from "../../constants/admin/msg";
import {ResponseType} from "../../types/system";
import {getRecordsAd} from "../../utils/Common.util";
import {buildWhereClause} from "../../utils/whereBuilder";
import {subTierResTypeConfig} from "../../helpers/subscription/subTier.helper";
import log from "../../helpers/system/logger.helper";
import {createSubTierType} from "../../validation/subscription/subTier.validation";
import {Duration, SubscriptionStatus} from "../../types/subscription";
import {Feature} from "../../models/subscription/feature/feature.model";
import {FeatureNotFoundEx} from "../../exceptions/controller/subscription/Feature/impl/FeatureNotFoundEx";
import {
    SubscriptionAlreadyExistEx
} from "../../exceptions/controller/subscription/subscriptionTier/impl/SubscriptionAlreadyExistEx";
import {
    SubscriptionTierNotFoundEx
} from "../../exceptions/controller/subscription/subscriptionTier/impl/SubscriptionTierNotFoundEx";
import {
    CannotDeleteSubTierAssociatedEx
} from "../../exceptions/controller/subscription/subscriptionTier/impl/CannotDeleteSubTierAssociatedEx";


export const listSubsTier = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[listSubsTier] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB)
    try {
        const {responseType, ...rest} = req.query as unknown as {
            responseType: ResponseType,
            [key: string]: any;
        }
        ResponseHelper.success(res, CREATED, ALL_SUBS_FETCHED, await getRecordsAd((getRepositories(qRunner) as {
            SubscriptionTier: Repository<SubscriptionTier>
        }).SubscriptionTier, buildWhereClause(rest), subTierResTypeConfig, responseType));
    } catch (error) {
        log.error("[listSubsTier] Error fetching SubsTier:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        log.debug("[listSubsTier] Query Runner Released.", null, process.env.SUPERADMIN_DB);
        await qRunner.release();
    }
};

export const createSubsTier = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[createSubsTier] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("[createSubsTier] Started transaction.", null, process.env.SUPERADMIN_DB);
    try {
        const {features, tierType, ...data}: createSubTierType = req.body;
        log.info("requestBody: ", req.body)
        const {SubscriptionTier, Feature} = getRepositories(qRunner) as {
            SubscriptionTier: Repository<SubscriptionTier>;
            Feature: Repository<Feature>;
        };

        const exSubTier = await SubscriptionTier.findOne({where: {name: data.name}});
        if (exSubTier) throw new SubscriptionAlreadyExistEx(data.name)

        const newSubTier = SubscriptionTier.create({...data, tierType: tierType as Duration});
        let featureArr: Feature[] = []
        for (const featureId of features) {
            const feature = await Feature.findOne({
                where: {featureId}
            })
            if (!feature) throw new FeatureNotFoundEx();
            featureArr.push(feature)
        }
        newSubTier.features = featureArr;
        const savedSubTier = await SubscriptionTier.save(newSubTier);

        await qRunner.rollbackTransaction();
        log.debug("[createSubsTier] Transaction committed successfully", null, process.env.SUPERADMIN_DB);

        ResponseHelper.success(res, CREATED, SUBSCRIP_CREATED, await getRecordsAd((getRepositories(qRunner) as {
            SubscriptionTier: Repository<SubscriptionTier>
        }).SubscriptionTier, buildWhereClause({subTierId: savedSubTier.subTierId}), subTierResTypeConfig, ResponseType.FULL_WITH_ASSOCIATION));
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[createSubsTier] Transaction rolled back successfully", null, process.env.SUPERADMIN_DB);
        log.error("[createSubsTier] Error creating SubsTier:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[createSubsTier] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
};

export const patchSubsTier = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[patchSubsTier] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);

    await qRunner.startTransaction();
    log.debug("[patchSubsTier] Started transaction.", null, process.env.SUPERADMIN_DB);
    try {
        const {SubscriptionTier, Feature} = getRepositories(qRunner) as {
            SubscriptionTier: Repository<SubscriptionTier>;
            Feature: Repository<Feature>;
        };
        const {subTierId, features, isReflectToNight, ...updateData} = req.body;

        const exSubTier = await SubscriptionTier.findOne({
            where: {subTierId},
            relations: ['features']
        });

        if (!exSubTier) {
            throw new SubscriptionTierNotFoundEx();
        }

        if (updateData.name) {
            const exSubTierByName = await SubscriptionTier.findOne({
                where: {
                    name: updateData.name,
                    subTierId: Not(subTierId),
                },
            });
            if (exSubTierByName) {
                throw new SubscriptionAlreadyExistEx(updateData.name);
            }
        }

        const patchedSubTier: SubscriptionTier = await SubscriptionTier.save({
            ...exSubTier,
            ...updateData,
        });

        if (features) {
            const featureArr: Feature[] = [];
            if (features && features.length > 0) {
                for (const featureId of features) {
                    const feature = await Feature.findOne({
                        where: {featureId},
                    });
                    if (!feature) {
                        throw new FeatureNotFoundEx("Feature not found for this given id: " + featureId);
                    }
                    featureArr.push(feature);
                }
            }
            patchedSubTier.features = featureArr;
            patchedSubTier.isUpdated = true;
            patchedSubTier.isReflectToNight = isReflectToNight;
            await SubscriptionTier.save(patchedSubTier);
        }

        await qRunner.commitTransaction();
        log.debug("[patchSubsTier] Transaction committed successfully", null, process.env.SUPERADMIN_DB);
        //TODO: to the Update here if the feature && !isReflectToNight
        // use the features array and do the change
        // only sync if isReflectToNight is true and if any active Subscription is present

        ResponseHelper.success(res, OK, SUB_UPDATED, await getRecordsAd((getRepositories(qRunner) as {
            SubscriptionTier: Repository<SubscriptionTier>
        }).SubscriptionTier, buildWhereClause({subTierId: patchedSubTier.subTierId}), subTierResTypeConfig, ResponseType.FULL_WITH_ASSOCIATION));
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[patchSubsTier] Transaction rolled back successfully", null, process.env.SUPERADMIN_DB);
        log.error("[patchSubsTier] Error updating SubsTier:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[patchSubsTier] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
};

export const deleteSubsTier = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.rootQueryRunner;

    await qRunner.connect();
    log.debug("[deleteSubsTier] Query Runner Connected Successfully.", null, process.env.SUPERADMIN_DB);
    await qRunner.startTransaction();
    log.debug("[deleteSubsTier] Started transaction.", null, process.env.SUPERADMIN_DB);
    try {
        const {SubscriptionTier} = getRepositories(qRunner) as {
            SubscriptionTier: Repository<SubscriptionTier>;
        };
        const subTierId = req.params.subTierId;
        const exSubTier = await SubscriptionTier.findOne({
            where: {
                subTierId,
            },
            relations: ["subscriptions"]
        });

        if (!exSubTier) throw new SubscriptionTierNotFoundEx();
        if (exSubTier.subscriptions.length) {
            for (const subscription of exSubTier.subscriptions) {
                if ([SubscriptionStatus.ACTIVE, SubscriptionStatus.BLOCKED].includes(subscription.status)) throw new CannotDeleteSubTierAssociatedEx()
            }
        }
        await SubscriptionTier.remove(exSubTier)
        await qRunner.commitTransaction();
        log.debug("[deleteSubsTier] Transaction committed successfully", null, process.env.SUPERADMIN_DB);
        ResponseHelper.success(res, OK, "Subscription Tier deleted successfully!");
    } catch (error) {
        await qRunner.rollbackTransaction();
        log.debug("[deleteSubsTier] Transaction rolled back successfully", null, process.env.SUPERADMIN_DB);
        log.error("[deleteSubsTier] Error deleting SubsTier:", error, process.env.SUPERADMIN_DB);
        next(error);
    } finally {
        await qRunner.release();
        log.debug("[deleteSubsTier] Query Runner Released.", null, process.env.SUPERADMIN_DB);
    }
};