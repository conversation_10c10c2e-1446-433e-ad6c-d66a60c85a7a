import {NextFunction, Request, Response} from "express";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {OK} from "../../constants/STATUS_CODES";
import {getCountry, getState, validateCountryState} from "../../helpers/subscription/country.helper";

export const getAllCountries = async (req: Request, res: Response, next: NextFunction) => {
    try {
        ResponseHelper.success(res, OK, "Country fetched successfully", getCountry(req.query.countryCode as string ?? undefined))
    } catch (error) {
        next(error);
    }
}

export const getAllStates = async (req: Request, res: Response, next: NextFunction) => {
    try {
        ResponseHelper.success(res, OK, "State fetched successfully", getState(req.query.countryCode as string ?? undefined))
    } catch (error) {
        next(error);
    }
}

export const validateState = async (req: Request, res: Response, next: NextFunction) => {
    try {
        ResponseHelper.success(res, OK, validateCountryState(req.query.countryCode as string, req.query.stateCode as string) ? "State is valid" : "State is invalid")
    } catch (error) {
        next(error);
    }
}
