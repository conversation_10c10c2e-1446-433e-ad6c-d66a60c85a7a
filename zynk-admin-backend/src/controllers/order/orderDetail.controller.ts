import { Request, Response, NextFunction } from "express";
import { Repository, Not, In } from "typeorm";
import {
  OrderQueue,
  QueuePriority,
  QueueStatus,
} from "../../models/order/orderQueue.model";
import { APIError, errorHandler } from "../../utils/errorHandler";
import {
  BAD_REQUEST,
  CREATED,
  FORBIDDEN,
  NOT_FOUND,
  OK,
  UNAUTHORIZED,
} from "../../constants/STATUS_CODES";
import {
  AppprovalStatus,
  OrderDetails,
  OrderStatus,
} from "../../models/order/orderDetail.model";
import {
  ItemType,
  NamedEntity,
  OrderItem,
} from "../../models/order/orderItem.model";
import {
  addToQueue,
  calculateOrderItemPrice,
  calculateTotalOrderPrice,
  validateAllergyIds,
  validateBeverageDishIds,
  validateBranchId,
  validateCookingStyleId,
  validateCustomerId,
  validateDessertDishIds,
  validateDishAddonsIds,
  validateDishExclusionIds,
  validateDishExtrasIds,
  validateDishId,
  validateDishSizeId,
  validateOrderTypeId,
  validateSideDishIds,
  validateSpicinessId,
  validateStaffId,
  validateTableId,
} from "../../helpers/order/order.helper";
import { Customer } from "../../models/customer/customer.model";
import { Staff } from "../../models/staff/staff.model";
import { Branch } from "../../models/company/branch.model";
import { Dish } from "../../models/foodmenu/dish.model";
import { Spiciness } from "../../models/foodmenu/custom/spiciness.model";
import { CookingStyle } from "../../models/foodmenu/custom/cookingstyle.model";
import { DishExclusion } from "../../models/foodmenu/custom/dishexclusion.model";
import { DishSize } from "../../models/foodmenu/custom/dishsize.model";
import { Allergy } from "../../models/reference/allergy.model";
import { DishAddon } from "../../models/foodmenu/custom/addon.model";
import { DishExtra } from "../../models/foodmenu/custom/extra.model";
import { DishSide } from "../../models/foodmenu/custom/dish_side.model";
import { DishBeverage } from "../../models/foodmenu/custom/dish_bev.model";
import { DishDessert } from "../../models/foodmenu/custom/dish_dessert.model";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import {
  OrderType,
  OrderTypeName,
} from "../../models/tenantSettings/general/orderType.model";
import { TableModel } from "../../models/tenantSettings/tableReservation/management/table.model";
import { PreparationDuration } from "../../models/tenantSettings/food/preparation_duration.model";
import { completeTableService } from "../../helpers/dineIn/tableDineIn.helper";
import { Tenant } from "../../models/admin/Tenant/tenant.model";
import argon2 from "argon2";
import { UserType } from "../../types/pBAC";
import { TENANT_NOT_EXIST } from "../../constants/middleware/err";
import {
  INVALID_PIN_ADMIN,
  INVALID_PIN_STAFF,
  PIN_VERIFICATION_FAILED,
} from "../../constants/tenant/attendance/err";
import { STAFF_NOT_FOUND } from "../../constants/tenant/staff/err";
import { PIN_VERIFIED_SUCCESS } from "../../constants/tenant/attendance/msg";
import {
  CHECKEDIN_STAFF_NOT_FOUND,
  STAFF_NOT_CHECKED_IN,
} from "../../constants/tenant/order/err";
import { ONLY_TENANT_ACCESS } from "../../constants/admin/err";

export interface AddonInput {
  id: string;
  quantity: number;
}

// Interface for order item input from frontend
export interface OrderItemInput {
  name: string;
  price: number;
  quantity: number;
  dishId: string;
  type?: ItemType;
  baseItemId?: string; // For customized items
  notes?: string;
  addedOn?: string; // ISO date string

  // Customization IDs from frontend
  allergyIds?: string[];
  dishSizeId?: string;
  dishExclusionIds?: string[];
  cookingStyleId?: string;
  spicinessId?: string;
  dishAddons?: AddonInput[];
  dishExtras?: AddonInput[];
  dishSides?: AddonInput[];
  dishBeverages?: AddonInput[];
  dishDesserts?: AddonInput[];
}

export const createOrderDetails = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const {
      OrderDetails,
      OrderItem,
      OrderQueue,
      Staff,
      Customer,
      Branch,
      OrderType,
      Table,
      Dish,
      Spiciness,
      CookingStyle,
      DishExclusion,
      DishSize,
      Allergy,
      DishAddon,
      DishExtra,
      DishSide,
      DishBeverage,
      DishDessert,
    } = getRepositories(qRunner) as {
      OrderDetails: Repository<OrderDetails>;
      OrderItem: Repository<OrderItem>;
      OrderQueue: Repository<OrderQueue>;
      Staff: Repository<Staff>;
      Customer: Repository<Customer>;
      Branch: Repository<Branch>;
      OrderType: Repository<any>;
      Table: Repository<any>;
      Dish: Repository<Dish>;
      Spiciness: Repository<Spiciness>;
      CookingStyle: Repository<CookingStyle>;
      DishExclusion: Repository<DishExclusion>;
      DishSize: Repository<DishSize>;
      Allergy: Repository<Allergy>;
      DishAddon: Repository<DishAddon>;
      DishExtra: Repository<DishExtra>;
      DishSide: Repository<DishSide>;
      DishBeverage: Repository<DishBeverage>;
      DishDessert: Repository<DishDessert>;
    };

    const {
      tableId,
      orderTypeId,
      orderItems,
      notes,
      estimatedPrepTime,
      orderedById,
      customerId,
      branchId,
      orderApproval,
      total,
      assignedWaiterId,
    } = req.body;

    // Validate required fields
    if (
      !tableId ||
      !orderTypeId ||
      !orderItems ||
      !Array.isArray(orderItems) ||
      orderItems.length === 0
    ) {
      return next(
        errorHandler(
          BAD_REQUEST,
          "tableId, orderTypeId, and orderItems are required"
        )
      );
    }
    if (typeof total !== "number" || total <= 0) {
      return next(errorHandler(BAD_REQUEST, "total must be a positive number"));
    }

    // Validate and get entity objects with names
    const [
      tableEntity,
      orderTypeEntity,
      orderedByEntity,
      customerEntity,
      branchEntity,
      assignedWaiterEntity,
    ] = await Promise.all([
      validateTableId(Table, tableId),
      validateOrderTypeId(OrderType, orderTypeId),
      validateStaffId(Staff, orderedById),
      validateCustomerId(Customer, customerId),
      validateBranchId(Branch, branchId),
      validateStaffId(Staff, assignedWaiterId),
    ]);

    // Create order details with proper type handling
    const orderDetailData: Partial<OrderDetails> = {
      table: tableEntity!,
      total,
      orderType: orderTypeEntity!,
      orderApproval,
      estimatedPrepTime,
      notes,
      orderedBy: orderedByEntity || undefined,
      customerInfo: customerEntity || undefined,
      branchId: branchEntity?.id || undefined,
      assignedWaiter: assignedWaiterEntity || undefined,
      status: OrderStatus.PENDING,
    };

    const orderDetail = OrderDetails.create(orderDetailData);
    const savedOrderDetail = await OrderDetails.save(orderDetail);

    // Process order items with customizations
    const orderItemEntities = await Promise.all(
      (orderItems as OrderItemInput[]).map(async (item) => {
        // Validate dish
        const dishEntity = await validateDishId(Dish, item.dishId);

        // Validate customizations in parallel
        const [
          allergyEntities,
          dishSizeEntity,
          dishExclusionEntities,
          cookingStyleEntity,
          spicinessEntity,
          dishAddonEntities,
          dishExtraEntities,
          dishSideEntities,
          dishBeverageEntities,
          dishDessertEntities,
        ] = await Promise.all([
          item.allergyIds
            ? validateAllergyIds(Allergy, item.allergyIds)
            : Promise.resolve([]),
          item.dishSizeId
            ? validateDishSizeId(DishSize, item.dishSizeId)
            : Promise.resolve(null),
          item.dishExclusionIds
            ? validateDishExclusionIds(DishExclusion, item.dishExclusionIds)
            : Promise.resolve([]),
          item.cookingStyleId
            ? validateCookingStyleId(CookingStyle, item.cookingStyleId)
            : Promise.resolve(null),
          item.spicinessId
            ? validateSpicinessId(Spiciness, item.spicinessId)
            : Promise.resolve(null),
          item.dishAddons
            ? validateDishAddonsIds(DishAddon, item.dishAddons)
            : Promise.resolve([]),
          item.dishExtras
            ? validateDishExtrasIds(DishExtra, item.dishExtras)
            : Promise.resolve([]),
          item.dishSides
            ? validateSideDishIds(DishSide, item.dishSides)
            : Promise.resolve([]),
          item.dishBeverages
            ? validateBeverageDishIds(DishBeverage, item.dishBeverages)
            : Promise.resolve([]),
          item.dishDesserts
            ? validateDessertDishIds(DishDessert, item.dishDesserts)
            : Promise.resolve([]),
        ]);

        const orderItemData: Partial<OrderItem> = {
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          // Fix: Create proper Dish object with correct property
          baseItem: dishEntity || undefined,
          orderDetails: savedOrderDetail,
          type: item.type || ItemType.STANDARD,

          // Store validated entities as JSONB - fix null vs empty array handling
          allergies: allergyEntities.length > 0 ? allergyEntities : undefined,
          dishSizes: dishSizeEntity || undefined,
          dishExclusions:
            dishExclusionEntities.length > 0
              ? dishExclusionEntities
              : undefined,
          cookingStyles: cookingStyleEntity || undefined,
          spiciness: spicinessEntity || undefined,
          dishAddons:
            dishAddonEntities.length > 0 ? dishAddonEntities : undefined,
          dishExtras:
            dishExtraEntities.length > 0 ? dishExtraEntities : undefined,
          dishSides: dishSideEntities.length > 0 ? dishSideEntities : undefined,
          dishBeverages:
            dishBeverageEntities.length > 0 ? dishBeverageEntities : undefined,
          dishDesserts:
            dishDessertEntities.length > 0 ? dishDessertEntities : undefined,
          notes: item.notes,
        };

        // Add baseItem for customized items
        if (item.type === ItemType.CUSTOMIZED && item.baseItemId) {
          const baseItemEntity = await validateDishId(Dish, item.baseItemId);
          orderItemData.baseItem = baseItemEntity || undefined;
        }

        // Set creation time if provided
        if (item.addedOn) {
          orderItemData.createdAt = new Date(item.addedOn);
        }

        return OrderItem.create(orderItemData);
      })
    );

    const savedOrderItems = await OrderItem.save(orderItemEntities);

    // If order is approved, add to queue
    if (orderApproval === AppprovalStatus.APPROVED) {
      if (!estimatedPrepTime || estimatedPrepTime <= 0) {
        return next(
          errorHandler(
            BAD_REQUEST,
            "estimatedPrepTime is required and must be positive for approved orders"
          )
        );
      }
      try {
        await addToQueue(
          OrderQueue,
          savedOrderDetail.orderDetailId,
          savedOrderItems,
          estimatedPrepTime,
          notes
        );
        console.log(
          `Order ${savedOrderDetail.orderDetailId} added to queue successfully`
        );
      } catch (queueError: any) {
        throw new Error(
          `Failed to add order ${savedOrderDetail.orderDetailId} to queue: ${queueError.message}`
        );
      }
    }

    await qRunner.commitTransaction();

    // Fetch complete order with items
    const completeOrder = await OrderDetails.findOne({
      where: { orderDetailId: savedOrderDetail.orderDetailId },
      relations: ["orderItems", "orderItems.dishEntity"],
    });

    ResponseHelper.success(
      res,
      CREATED,
      "Order created successfully",
      completeOrder
    );
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Update Order Details Status (including approval status)
export const updateOrderDetailsStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { OrderDetails, OrderQueue, PreparationDuration, OrderType } =
      getRepositories(qRunner) as {
        OrderDetails: Repository<OrderDetails>;
        OrderQueue: Repository<OrderQueue>;
        PreparationDuration: Repository<PreparationDuration>;
        OrderType: Repository<OrderType>;
      };

    const { orderDetailId } = req.params;
    const { status, orderApproval, assignedWaiter, estimatedPrepTimeInput } =
      req.body;

    const branchId: any = req.query.branchId || req.branchId;

    const defaultEstimatedPrepTime = await PreparationDuration.findOne({
      where: { preparationDurationId: branchId },
      relations: ["branch"],
    });

    const estimatedPrepTime = estimatedPrepTimeInput
      ? Number(estimatedPrepTimeInput)
      : defaultEstimatedPrepTime?.duration;

    // Find order details with items
    const orderDetail = await OrderDetails.findOne({
      where: { orderDetailId },
      relations: ["orderItems"],
    });

    if (!orderDetail) {
      return next(errorHandler(NOT_FOUND, "Order not found"));
    }

    // Check if order is currently in queue
    const existingQueueEntry = await OrderQueue.findOne({
      where: { orderDetailId },
      relations: ["orderDetails"],
    });

    // Validate status transitions
    if (status) {
      if (
        orderDetail.status === OrderStatus.CANCELLED ||
        orderDetail.status === OrderStatus.CHECKOUT ||
        orderDetail.status === OrderStatus.COMPLETED
      ) {
        return next(
          errorHandler(
            BAD_REQUEST,
            `Invalid status transition from ${orderDetail.status} to ${status}`
          )
        );
      }
    }

    // Build update object for OrderDetails
    const updateData: any = {};
    if (status) updateData.status = status;
    if (orderApproval !== undefined) updateData.orderApproval = orderApproval;
    if (assignedWaiter) updateData.assignedWaiter = assignedWaiter;

    // Handle status-specific updates for OrderDetails
    if (status === OrderStatus.IN_PREPARATION) {
      updateData.prepStartTime = new Date();
    } else if (status === OrderStatus.READY) {
      updateData.readyTime = new Date();
    } else if (status === OrderStatus.SERVED) {
      updateData.servedTime = new Date();
    }

    // Update order details
    await OrderDetails.update({ orderDetailId }, updateData);

    // Handle queue operations based on approval status
    if (
      orderApproval === AppprovalStatus.APPROVED &&
      orderDetail.orderApproval !== AppprovalStatus.APPROVED
    ) {
      if (estimatedPrepTime) updateData.estimatedPrepTime = estimatedPrepTime;
      // Order just got approved - add to queue with all order items
      if (!estimatedPrepTime || estimatedPrepTime <= 0) {
        return next(
          errorHandler(
            BAD_REQUEST,
            "estimatedPrepTime is required and must be positive for approved orders"
          )
        );
      }

      try {
        await addToQueue(
          OrderQueue,
          orderDetailId,
          orderDetail.orderItems,
          estimatedPrepTime || orderDetail.estimatedPrepTime
        );
        console.log(`Order ${orderDetailId} added to queue successfully`);
      } catch (queueError: any) {
        throw new Error(
          `Failed to add order ${orderDetailId} to queue: ${queueError.message}`
        );
      }
    }

    // Handle queue synchronization when status changes
    if (status && existingQueueEntry) {
      const queueStatusMap: any = {
        [OrderStatus.PENDING]: QueueStatus.PENDING,
        [OrderStatus.IN_PREPARATION]: QueueStatus.IN_PREPARATION,
        [OrderStatus.READY]: QueueStatus.READY,
        [OrderStatus.SERVED]: QueueStatus.SERVED,
        [OrderStatus.CANCELLED]: QueueStatus.CANCELLED,
        [OrderStatus.CHECKOUT]: QueueStatus.CHECKOUT,
        [OrderStatus.COMPLETED]: QueueStatus.COMPLETED,
      };

      const queueStatus = queueStatusMap[status];

      if (queueStatus) {
        // If status is CANCELLED or CHECKOUT or COMPLETED, remove from queue
        if (
          queueStatus === QueueStatus.CANCELLED ||
          queueStatus === QueueStatus.CHECKOUT ||
          queueStatus === QueueStatus.COMPLETED
        ) {
          await OrderQueue.delete({ orderDetailId });
          const orderTypeName = await OrderType.findOne({
            where: { orderTypeId: orderDetail.orderType?.id },
          });

          if (!orderTypeName) {
            return next(errorHandler(NOT_FOUND, "Order type not found"));
          }

          // Check if we need to complete table service
          // Only complete table service on first transition to a final state
          const finalStates = [
            OrderStatus.CANCELLED,
            OrderStatus.CHECKOUT,
            OrderStatus.COMPLETED,
          ];
          const isTransitionToFinalState =
            finalStates.includes(status) &&
            !finalStates.includes(orderDetail.status);

          const shouldCompleteTableService =
            Number(orderDetail.numberOfPeople) > 0 &&
            orderTypeName.reservedName === OrderTypeName.DINE_IN &&
            isTransitionToFinalState;

          if (shouldCompleteTableService) {
            await completeTableService(
              qRunner,
              orderDetail.table?.id,
              orderDetail.numberOfPeople,
              branchId
            );
            console.log(
              `Table service completed for order ${orderDetailId} on transition from ${orderDetail.status} to ${status}`
            );
          }
          console.log(
            `Order ${orderDetailId} removed from queue due to status: ${status}`
          );
        } // else {
        //     // Update queue status to match order details status
        //     const queueUpdateData: any = {status: queueStatus};

        //     // Also update queue-specific fields if provided
        //     if (notes !== undefined) queueUpdateData.notes = notes;

        //     await OrderQueue.update({orderDetailId}, queueUpdateData);
        //     console.log(`Queue status updated for order ${orderDetailId}: ${queueStatus}`);
        // }
      }
    }

    // Handle case where order is rejected after being in queue
    if (orderApproval === AppprovalStatus.REJECTED && existingQueueEntry) {
      await OrderQueue.delete({ orderDetailId });
      console.log(`Order ${orderDetailId} removed from queue due to rejection`);
    }

    await qRunner.commitTransaction();

    // Fetch updated order with queue information
    const updatedOrder = await OrderDetails.findOne({
      where: { orderDetailId },
      relations: ["orderItems"],
    });

    // Check if still in queue after updates
    const updatedQueueEntry = await OrderQueue.findOne({
      where: { orderDetailId },
    });

    const responseData = {
      ...updatedOrder,
      inQueue: !!updatedQueueEntry,
      queueStatus: updatedQueueEntry?.status || null,
    };

    ResponseHelper.success(
      res,
      OK,
      "Order status updated successfully",
      responseData
    );
  } catch (error) {
    await qRunner.rollbackTransaction();
    console.error("Error updating order status:", error);
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Patch Order Details (partial update)
export const patchOrderDetails = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { OrderDetails, Staff, Customer, OrderType, Table } = getRepositories(
      qRunner
    ) as {
      OrderDetails: Repository<OrderDetails>;
      Staff: Repository<Staff>;
      Customer: Repository<Customer>;
      OrderType: Repository<OrderType>;
      Table: Repository<TableModel>;
    };

    const { orderDetailId } = req.params;
    const updateFields = req.body;

    // Find order details
    const orderDetail = await OrderDetails.findOne({
      where: { orderDetailId },
    });

    if (!orderDetail) {
      return next(errorHandler(NOT_FOUND, "Order not found"));
    }

    // Remove fields that shouldn't be updated via patch
    const {
      orderDetailId: _,
      createdAt,
      updatedAt,
      status,
      orderApproval,
      branchId,
      orderCode,
      ...fieldsToUpdate
    } = updateFields;

    // Prepare the update object
    const updateData: any = {};

    // Process each field that might need validation
    for (const [key, value] of Object.entries(fieldsToUpdate)) {
      switch (key) {
        case "assignedChefId":
          // Use staff validation for chef and waiter
          if (value !== null && value !== undefined) {
            const staffId: any = value;
            updateData.assignedChef =
              (await validateStaffId(Staff, staffId)) || undefined;
          } else {
            updateData.assignedChef = undefined;
          }
          break;
        case "assignedWaiterId":
          // Use staff validation for chef and waiter
          if (value !== null && value !== undefined) {
            const staffId: any = value;
            updateData.assignedWaiter =
              (await validateStaffId(Staff, staffId)) || undefined;
          } else {
            updateData.assignedWaiter = undefined;
          }
          break;

        case "tableId":
          // Use table validation
          if (value !== null && value !== undefined) {
            const tableId: any = value;
            updateData.table =
              (await validateTableId(Table, tableId)) || undefined;
          } else {
            updateData.table = undefined;
          }
          break;

        case "orderTypeId":
          // Use order type validation
          if (value !== null && value !== undefined) {
            const orderTypeId: any = value;
            updateData.orderType =
              (await validateOrderTypeId(OrderType, orderTypeId)) || undefined;
          } else {
            updateData.orderType = undefined;
          }
          break;

        case "customerId":
          // Use customer validation
          if (value !== null && value !== undefined) {
            const customerId: any = value;
            const customer = await Customer.findOne({
              where: { customerId },
            });

            if (!customer) {
              throw errorHandler(
                404,
                `${Customer} with ID ${customerId} not found`
              );
            }
            const customerInfo: NamedEntity = {
              id: customer.customerId,
              name: `${customer.firstName} ${customer.lastName || ""}`.trim(),
            };
            updateData.customer = customer;
            updateData.customerInfo = customerInfo;
          } else {
            updateData.customerInfo = undefined;
          }
          break;

        case "orderedById":
          // Use staff validation for orderedBy
          if (value !== null && value !== undefined) {
            const staffId: any = value;
            updateData.orderedBy =
              (await validateStaffId(Staff, staffId)) || undefined;
          } else {
            updateData.orderedBy = undefined;
          }
          break;

        default:
          // For other fields, use them as-is
          updateData[key] = value;
          break;
      }
    }

    // Update only provided fields
    await OrderDetails.update({ orderDetailId }, updateData);

    await qRunner.commitTransaction();

    // Fetch updated order
    const updatedOrder = await OrderDetails.findOne({
      where: { orderDetailId },
      relations: ["orderItems"],
    });

    ResponseHelper.success(res, OK, "Order updated successfully", updatedOrder);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Add items to existing order (when order is IN_PREPARATION, READY, or SERVED)
export const addItemsToOrder = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const {
      OrderDetails,
      OrderItem,
      OrderQueue,
      Staff,
      Customer,
      Branch,
      OrderType,
      Table,
      Dish,
      Spiciness,
      CookingStyle,
      DishExclusion,
      DishSize,
      Allergy,
      DishAddon,
      DishExtra,
      DishSide,
      DishBeverage,
      DishDessert,
      PreparationDuration,
    } = getRepositories(qRunner) as {
      OrderDetails: Repository<OrderDetails>;
      OrderItem: Repository<OrderItem>;
      OrderQueue: Repository<OrderQueue>;
      Staff: Repository<Staff>;
      Customer: Repository<Customer>;
      Branch: Repository<Branch>;
      OrderType: Repository<any>;
      Table: Repository<any>;
      Dish: Repository<Dish>;
      Spiciness: Repository<Spiciness>;
      CookingStyle: Repository<CookingStyle>;
      DishExclusion: Repository<DishExclusion>;
      DishSize: Repository<DishSize>;
      Allergy: Repository<Allergy>;
      DishAddon: Repository<DishAddon>;
      DishExtra: Repository<DishExtra>;
      DishSide: Repository<DishSide>;
      DishBeverage: Repository<DishBeverage>;
      DishDessert: Repository<DishDessert>;
      PreparationDuration: Repository<PreparationDuration>;
    };

    const { orderDetailId } = req.params;
    const { orderItems, notes, estimatedPrepTimeInput } = req.body;

    // Find order details
    const orderDetail = await OrderDetails.findOne({
      where: { orderDetailId },
      relations: ["orderItems"],
    });

    if (!orderDetail) {
      return next(errorHandler(BAD_REQUEST, "Order not found"));
    }

    const branchId = req.query.branchId || req.body.branchId;

    // Check if order allows adding items
    const allowedStatuses = [
      OrderStatus.PENDING,
      OrderStatus.IN_PREPARATION,
      OrderStatus.READY,
      OrderStatus.SERVED,
    ];
    if (!allowedStatuses.includes(orderDetail.status)) {
      return next(
        errorHandler(BAD_REQUEST, "Cannot add items to order in current status")
      );
    }

    if (!orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
      return next(errorHandler(BAD_REQUEST, "orderItems are required"));
    }

    const defaultEstimatedPrepTime = await PreparationDuration.findOne({
      where: { preparationDurationId: branchId },
      relations: ["branch"],
    });

    const estimatedPrepTime = estimatedPrepTimeInput
      ? Number(estimatedPrepTimeInput)
      : defaultEstimatedPrepTime?.duration;

    // Process order items with customizations (similar to createOrderDetails)
    const orderItemEntities = await Promise.all(
      (orderItems as OrderItemInput[]).map(async (item) => {
        // Validate dish
        const dishEntity = await validateDishId(Dish, item.dishId);

        // Validate customizations in parallel
        const [
          allergyEntities,
          dishSizeEntity,
          dishExclusionEntities,
          cookingStyleEntity,
          spicinessEntity,
          dishAddonEntities,
          dishExtraEntities,
          dishSideEntities,
          dishBeverageEntities,
          dishDessertEntities,
        ] = await Promise.all([
          item.allergyIds
            ? validateAllergyIds(Allergy, item.allergyIds)
            : Promise.resolve([]),
          item.dishSizeId
            ? validateDishSizeId(DishSize, item.dishSizeId)
            : Promise.resolve(null),
          item.dishExclusionIds
            ? validateDishExclusionIds(DishExclusion, item.dishExclusionIds)
            : Promise.resolve([]),
          item.cookingStyleId
            ? validateCookingStyleId(CookingStyle, item.cookingStyleId)
            : Promise.resolve(null),
          item.spicinessId
            ? validateSpicinessId(Spiciness, item.spicinessId)
            : Promise.resolve(null),
          item.dishAddons
            ? validateDishAddonsIds(DishAddon, item.dishAddons)
            : Promise.resolve([]),
          item.dishExtras
            ? validateDishExtrasIds(DishExtra, item.dishExtras)
            : Promise.resolve([]),
          item.dishSides
            ? validateSideDishIds(DishSide, item.dishSides)
            : Promise.resolve([]),
          item.dishBeverages
            ? validateBeverageDishIds(DishBeverage, item.dishBeverages)
            : Promise.resolve([]),
          item.dishDesserts
            ? validateDessertDishIds(DishDessert, item.dishDesserts)
            : Promise.resolve([]),
        ]);

        const orderItemData: Partial<OrderItem> = {
          name: dishEntity?.name || undefined,
          price: 0,
          quantity: item.quantity,
          // Fix: Create proper Dish object with correct property
          baseItem: dishEntity || undefined,
          orderDetails: orderDetail,
          type: item.type || ItemType.STANDARD,

          // Store validated entities as JSONB - fix null vs empty array handling
          allergies: allergyEntities.length > 0 ? allergyEntities : undefined,
          dishSizes: dishSizeEntity || undefined,
          dishExclusions:
            dishExclusionEntities.length > 0
              ? dishExclusionEntities
              : undefined,
          cookingStyles: cookingStyleEntity || undefined,
          spiciness: spicinessEntity || undefined,
          dishAddons:
            dishAddonEntities.length > 0 ? dishAddonEntities : undefined,
          dishExtras:
            dishExtraEntities.length > 0 ? dishExtraEntities : undefined,
          dishSides: dishSideEntities.length > 0 ? dishSideEntities : undefined,
          dishBeverages:
            dishBeverageEntities.length > 0 ? dishBeverageEntities : undefined,
          dishDesserts:
            dishDessertEntities.length > 0 ? dishDessertEntities : undefined,
          notes: item.notes,
        };

        const totalPrice = await calculateOrderItemPrice(orderItemData);
        orderItemData.price = totalPrice;

        // Set creation time if provided
        if (item.addedOn) {
          orderItemData.createdAt = new Date(item.addedOn);
        }

        return OrderItem.create(orderItemData);
      })
    );

    const savedOrderItems = await OrderItem.save(orderItemEntities);

    const allOrderItems = await OrderItem.find({
      where: { orderDetails: { orderDetailId } },
    });

    await OrderDetails.update(
      { orderDetailId },
      { total: await calculateTotalOrderPrice(allOrderItems) }
    );

    // If order is approved, add to queue
    if (orderDetail.orderApproval === AppprovalStatus.APPROVED) {
      if (!estimatedPrepTime || estimatedPrepTime <= 0) {
        return next(
          errorHandler(
            BAD_REQUEST,
            "estimatedPrepTime is required and must be positive for approved orders"
          )
        );
      }
      try {
        await addToQueue(
          OrderQueue,
          orderDetailId,
          savedOrderItems,
          estimatedPrepTime,
          notes
        );
        console.log(
          `Additional items for order ${orderDetailId} added to queue successfully`
        );
      } catch (queueError: any) {
        throw new Error(
          `Failed to add additional items for order ${orderDetailId} to queue: ${queueError.message}`
        );
      }
    }

    await qRunner.commitTransaction();

    // Fetch updated order with items
    const updatedOrder = await OrderDetails.findOne({
      where: { orderDetailId },
      relations: ["orderItems"],
    });

    ResponseHelper.success(
      res,
      OK,
      "Items added to order successfully",
      updatedOrder
    );
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Get Order Details by ID
export const getOrderDetailsById = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { OrderDetails } = getRepositories(qRunner) as {
      OrderDetails: Repository<OrderDetails>;
    };

    const { orderDetailId } = req.params;

    const orderDetail = await OrderDetails.createQueryBuilder("order")
      .leftJoinAndSelect("order.orderItems", "orderItems")
      .leftJoin("order.customer", "customer")
      .select([
        // Order fields
        "order.orderDetailId",
        "order.table",
        "order.total",
        "order.status",
        "order.orderApproval",
        "order.estimatedPrepTime",
        "order.notes",
        "order.orderedBy",
        "order.createdAt",
        "order.orderType",
        // Customer fields
        "customer.customerId",
        "customer.firstName",
        "customer.lastName",
        "customer.email",
        "customer.phoneNumber",
        // Order items (if you want to select specific fields)
        "orderItems",
      ])
      .where("order.orderDetailId = :orderDetailId", { orderDetailId })
      .getOne();
    if (!orderDetail) {
      return next(errorHandler(NOT_FOUND, "Order not found"));
    }

    ResponseHelper.success(
      res,
      OK,
      "Order retrieved successfully",
      orderDetail
    );
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Get all Order Details with filters
export const getOrderDetails = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { OrderDetails } = getRepositories(qRunner) as {
      OrderDetails: Repository<OrderDetails>;
    };

    const {
      status,
      orderApproval,
      branchParamId,
      page = 1,
      limit = 10,
    } = req.query;

    const branchId = branchParamId || req.branchId;

    const queryBuilder = OrderDetails.createQueryBuilder("orderDetails")
      .leftJoinAndSelect("orderDetails.orderItems", "orderItems")
      .leftJoinAndSelect("orderDetails.customer", "customer")
      .select([
        "orderDetails", // Select all fields from OrderDetails (or specify specific fields)
        "orderItems", // Select all fields from orderItems (or specify specific fields)
        "customer.customerId",
        "customer.firstName",
        "customer.lastName",
        "customer.email",
        "customer.phoneNumber",
        "customer.createdAt",
      ]);

    // Apply filters
    if (status) {
      queryBuilder.andWhere("orderDetails.status = :status", { status });
    }
    if (orderApproval) {
      queryBuilder.andWhere("orderDetails.orderApproval = :orderApproval", {
        orderApproval,
      });
    }
    if (branchId) {
      queryBuilder.andWhere("orderDetails.branchId = :branchId", { branchId });
    }

    // Pagination
    const skip = (Number(page) - 1) * Number(limit);
    queryBuilder.skip(skip).take(Number(limit));

    // Order by creation date (newest first)
    queryBuilder.orderBy("orderDetails.createdAt", "DESC");

    const [orderDetails, total] = await queryBuilder.getManyAndCount();

    const result = {
      data: orderDetails,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        totalPages: Math.ceil(total / Number(limit)),
      },
    };

    ResponseHelper.success(res, OK, "Orders retrieved successfully", result);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const deleteOrderDetails = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { OrderDetails, OrderItem, OrderQueue } = getRepositories(
      qRunner
    ) as {
      OrderDetails: Repository<OrderDetails>;
      OrderItem: Repository<OrderItem>;
      OrderQueue: Repository<OrderQueue>;
    };

    const { orderDetailId } = req.params;

    // Find order details with items to verify it exists
    const orderDetail = await OrderDetails.findOne({
      where: { orderDetailId },
      relations: ["orderItems"],
    });

    if (!orderDetail) {
      return next(errorHandler(NOT_FOUND, "Order not found"));
    }

    // Check if order can be deleted based on status
    const nonDeletableStatuses = [
      OrderStatus.IN_PREPARATION,
      OrderStatus.READY,
    ];
    if (nonDeletableStatuses.includes(orderDetail.status)) {
      return next(
        errorHandler(
          BAD_REQUEST,
          `Cannot delete order in ${orderDetail.status} status. Only orders in PENDING, SERVED, CANCELLED, or CHECKOUT status can be deleted.`
        )
      );
    }

    // Find all queue entries for this order
    const queueEntries = await OrderQueue.find({
      where: { orderDetailId },
      relations: ["orderItems"],
    });

    // Log deletion activity
    console.log(
      `Deleting order ${orderDetailId} with ${orderDetail.orderItems.length} items and ${queueEntries.length} queue entries`
    );

    // Delete queue entries first (if any exist)
    if (queueEntries.length > 0) {
      // Remove many-to-many relationships in order_queue_items junction table
      for (const queueEntry of queueEntries) {
        if (queueEntry.orderItems && queueEntry.orderItems.length > 0) {
          // Clear the many-to-many relationship
          queueEntry.orderItems = [];
          await OrderQueue.save(queueEntry);
        }
      }

      // Delete the queue entries
      await OrderQueue.delete({ orderDetailId });
      console.log(
        `Deleted ${queueEntries.length} queue entries for order ${orderDetailId}`
      );
    }

    // Delete order items (cascade should handle this, but explicit deletion for safety)
    if (orderDetail.orderItems && orderDetail.orderItems.length > 0) {
      const orderItemIds = orderDetail.orderItems.map(
        (item) => item.orderItemId
      );
      await OrderItem.delete(orderItemIds);
      console.log(
        `Deleted ${orderDetail.orderItems.length} order items for order ${orderDetailId}`
      );
    }

    // Finally, delete the order details
    await OrderDetails.delete({ orderDetailId });
    console.log(`Successfully deleted order ${orderDetailId}`);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, "Order deleted successfully", {
      deletedOrderId: orderDetailId,
      deletedItemsCount: orderDetail.orderItems.length,
      deletedQueueEntriesCount: queueEntries.length,
    });
  } catch (error) {
    await qRunner.rollbackTransaction();
    console.error("Error deleting order:", error);
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getActiveOrderDetailsByWaiter = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { OrderDetails } = getRepositories(qRunner) as {
      OrderDetails: Repository<OrderDetails>;
    };

    // Extract query parameters
    const {
      assignedWaiter,
      status,
      overdueOnly = "false",
      includeServed = "false",
    } = req.query;

    if (!assignedWaiter) {
      return next(
        errorHandler(BAD_REQUEST, "assignedWaiter parameter is required")
      );
    }

    // Build query for active order details assigned to waiter
    // Use the repository directly instead of createQueryBuilder on the entity
    const queryBuilder = OrderDetails.createQueryBuilder("order_details") // Use actual table name
      .leftJoinAndSelect("order_details.orderItems", "orderItems")
      .leftJoinAndSelect("order_details.customer", "customer");

    // Filter for active order statuses
    const activeStatuses = [
      OrderStatus.PENDING,
      OrderStatus.IN_PREPARATION,
      OrderStatus.READY,
      OrderStatus.SERVED,
      OrderStatus.CHECKOUT,
    ];

    queryBuilder.where("order_details.status IN (:...activeStatuses)", {
      activeStatuses,
    });

    // Filter by assigned waiter using JSONB query
    // Check if assignedWaiter JSONB field contains the waiter ID or name
    if (typeof assignedWaiter === "string") {
      // If assignedWaiter is a UUID, search by ID, otherwise search by name
      const isUUID =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
          assignedWaiter
        );

      if (isUUID) {
        queryBuilder.andWhere(
          "order_details.\"assignedWaiter\"->>'id' = :waiterId",
          {
            waiterId: assignedWaiter,
          }
        );
      } else {
        queryBuilder.andWhere(
          "order_details.\"assignedWaiter\"->>'name' ILIKE :waiterName",
          {
            waiterName: `%${assignedWaiter}%`,
          }
        );
      }
    }

    // Add additional filters based on query parameters using JSONB queries
    if (status) {
      queryBuilder.andWhere("order_details.status = :status", { status });
    }

    // Order by priority fields and creation time
    queryBuilder
      .orderBy("order_details.createdAt", "ASC")
      .addOrderBy("order_details.estimatedPrepTime", "ASC");

    const orderDetails = await queryBuilder.getMany();

    // Calculate overdue status and additional metrics for each order
    const ordersWithMetrics = orderDetails.map((order) => ({
      ...order,
      isOverdueFlag: order.isOverdue(),
      actualPrepTime: order.actualPrepTime,
      totalProcessingTime: order.totalProcessingTime,
      isInQueue: order.isInQueue(),
      isCompleted: order.isCompleted(),
      // Add backward compatibility fields
      customerId: order.customerId,
      tableId: order.table.name,
      orderTypeId: order.orderType.id,
    }));

    // Filter overdue orders if requested
    let filteredOrders = ordersWithMetrics;
    if (overdueOnly === "true") {
      filteredOrders = ordersWithMetrics.filter((order) => order.isOverdueFlag);
    }

    // Group by status for better organization
    const ordersByStatus = {
      [OrderStatus.PENDING]: filteredOrders.filter(
        (order) => order.status === OrderStatus.PENDING
      ),
      [OrderStatus.IN_PREPARATION]: filteredOrders.filter(
        (order) => order.status === OrderStatus.IN_PREPARATION
      ),
      [OrderStatus.READY]: filteredOrders.filter(
        (order) => order.status === OrderStatus.READY
      ),
      ...(includeServed === "true" && {
        [OrderStatus.SERVED]: filteredOrders.filter(
          (order) => order.status === OrderStatus.SERVED
        ),
        [OrderStatus.CHECKOUT]: filteredOrders.filter(
          (order) => order.status === OrderStatus.CHECKOUT
        ),
      }),
    };

    // Calculate statistics
    const overdueCount = filteredOrders.filter(
      (order) => order.isOverdueFlag
    ).length;
    const totalValue = filteredOrders.reduce(
      (sum, order) => sum + Number(order.total),
      0
    );
    const avgPrepTime =
      filteredOrders.length > 0
        ? filteredOrders.reduce(
            (sum, order) => sum + (order.estimatedPrepTime || 0),
            0
          ) / filteredOrders.length
        : 0;

    const ordersData = {
      ordersByStatus,
      allOrders: filteredOrders,
      statistics: {
        totalActive: filteredOrders.length,
        pending: ordersByStatus[OrderStatus.PENDING].length,
        inPreparation: ordersByStatus[OrderStatus.IN_PREPARATION].length,
        ready: ordersByStatus[OrderStatus.READY].length,
        served: ordersByStatus[OrderStatus.SERVED]?.length || 0,
        checkout: ordersByStatus[OrderStatus.CHECKOUT]?.length || 0,
        overdue: overdueCount,
        totalValue: Number(totalValue.toFixed(2)),
        averageEstimatedPrepTime: Math.round(avgPrepTime),
        averageOrderValue:
          filteredOrders.length > 0
            ? Number((totalValue / filteredOrders.length).toFixed(2))
            : 0,
      },
      filters: {
        assignedWaiter,
        status: status || null,
        includeServed: includeServed === "true",
        overdueOnly: overdueOnly === "true",
      },
    };

    ResponseHelper.success(
      res,
      OK,
      "Active order details retrieved successfully",
      ordersData
    );
  } catch (error) {
    console.error("Error retrieving active order details by waiter:", error);
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const verifyOrderCancel = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const rootRunner = req.rootQueryRunner;
  await rootRunner.connect();

  let qRunner = null;

  if (req.tenantName) {
    qRunner = req.queryRunner;

    await qRunner.connect();
  }

  try {
    const userId = req.userId;
    const checkedInStaffId = req.checkedInStaffId;

    if (!checkedInStaffId)
      return next(errorHandler(NOT_FOUND, STAFF_NOT_CHECKED_IN));

    const { Staff } = getRepositories(qRunner!) as {
      Staff: Repository<Staff>;
    };

    const checkedStaff = await Staff.findOne({
      where: {
        staffId: checkedInStaffId,
      },
    });

    if (!checkedStaff)
      return next(errorHandler(NOT_FOUND, CHECKEDIN_STAFF_NOT_FOUND));

    const { pin } = req.body;

    let success = false;

    if (req.USER_TYPE === UserType.TENANT_ADMIN) {
      const { Tenant } = getRepositories(rootRunner) as {
        Tenant: Repository<Tenant>;
      };

      const exTenant = await Tenant.findOne({
        where: {
          tenantId: userId,
        },
        relations: ["tenantAuth"],
      });

      if (!exTenant) return next(errorHandler(NOT_FOUND, TENANT_NOT_EXIST));

      const verified = await argon2.verify(
        exTenant.tenantAuth.pin,
        pin.toString()
      );

      if (!verified) return next(errorHandler(UNAUTHORIZED, INVALID_PIN_ADMIN));

      success = true;
    } else {
      const exStaff = await Staff.findOneBy({
        staffId: userId,
      });

      if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

      const verified = await argon2.verify(exStaff.pin, pin.toString());

      if (!verified) return next(errorHandler(UNAUTHORIZED, INVALID_PIN_STAFF));

      success = true;
    }

    if (success) {
      ResponseHelper.success(res, OK, PIN_VERIFIED_SUCCESS);
    } else {
      throw new APIError(UNAUTHORIZED, PIN_VERIFICATION_FAILED);
    }
  } catch (error) {
    next(error);
  } finally {
    await rootRunner.release();
    if (req.tenantName && qRunner) {
      await qRunner.release();
    }
  }
};
