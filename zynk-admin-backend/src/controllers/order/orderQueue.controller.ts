import { NextFunction, Request, Response } from 'express';
import { Repository } from 'typeorm';
import { OrderQueue, QueueStatus } from '../../models/order/orderQueue.model';
import { errorHandler } from '../../utils/errorHandler';
import { BAD_REQUEST, NOT_FOUND, OK } from '../../constants/STATUS_CODES';
import { getRepositories } from '../../helpers/system/RepositoryHelper.helper';
import { ResponseHelper } from '../../helpers/system/ResponseHelper.helper';
import { OrderStatus } from '../../models/order/orderDetail.model';

export const getQueueView = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const { OrderQueue } = getRepositories(qRunner) as {
            OrderQueue: Repository<OrderQueue>;
        };

        // Extract query parameters
        const {
            branchParamId,
            status,
            priority,
            overdueOnly = 'false'
        } = req.query;

        const branchId = branchParamId || req.branchId;

        // Build query for active queue items
        const queryBuilder = OrderQueue
            .createQueryBuilder('queue')
            .leftJoinAndSelect('queue.orderDetails', 'orderDetails')
            .leftJoinAndSelect('queue.orderItems', 'orderItems')
            .leftJoinAndSelect('orderDetails.customer', 'customer');

        // Always exclude CHECKOUT , CANCELLED, and COMPLETED statuses
        const excludeStatuses = [QueueStatus.CHECKOUT, QueueStatus.CANCELLED, QueueStatus.COMPLETED];

        queryBuilder.where('queue.status NOT IN (:...excludeStatuses)', {
            excludeStatuses
        });

        // Add filters based on query parameters
        if (branchId) {
            queryBuilder.andWhere('orderDetails.branchId = :branchId', { branchId });
        }

        if (status) {
            queryBuilder.andWhere('queue.status = :status', { status });
        }

        if (priority) {
            queryBuilder.andWhere('queue.priority = :priority', { priority });
        }

        // Order by priority (higher first) then by queue position
        queryBuilder
            .orderBy('queue.priority', 'DESC')
            .addOrderBy('queue.queuePosition', 'ASC')
            .addOrderBy('queue.createdAt', 'ASC');

        const queueItems = await queryBuilder.getMany();

        // Calculate overdue status for each item
        const itemsWithOverdueStatus = queueItems.map(item => ({
            ...item,
            isOverdueFlag: item.isOverdue()
        }));

        // Filter overdue items if requested
        let filteredItems = itemsWithOverdueStatus;
        if (overdueOnly === 'true') {
            filteredItems = itemsWithOverdueStatus.filter(item => item.isOverdueFlag);
        }

        // Group by status for better queue visualization
        const queueByStatus = {
            [QueueStatus.PENDING]: filteredItems.filter(item => item.status === QueueStatus.PENDING),
            [QueueStatus.IN_PREPARATION]: filteredItems.filter(item => item.status === QueueStatus.IN_PREPARATION),
            [QueueStatus.READY]: filteredItems.filter(item => item.status === QueueStatus.READY),
            [QueueStatus.SERVED]: filteredItems.filter(item => item.status === QueueStatus.SERVED),
        };

        // Calculate statistics
        const overdueCount = filteredItems.filter(item => item.isOverdueFlag).length;
        const avgPrepTime = filteredItems.length > 0
            ? filteredItems.reduce((sum, item) => sum + (item.estimatedPrepTime || 0), 0) / filteredItems.length
            : 0;

        const queueData = {
            queueByStatus,
            allItems: filteredItems,
            statistics: {
                totalActive: filteredItems.length,
                pending: queueByStatus[QueueStatus.PENDING].length,
                inPreparation: queueByStatus[QueueStatus.IN_PREPARATION].length,
                ready: queueByStatus[QueueStatus.READY].length,
                served: queueByStatus[QueueStatus.SERVED].length,
                overdue: overdueCount,
                averageEstimatedPrepTime: Math.round(avgPrepTime),
                queuePositions: {
                    next: filteredItems.find(item => item.status === QueueStatus.PENDING)?.queuePosition || null,
                    lastInQueue: Math.max(...filteredItems.map(item => item.queuePosition || 0), 0)
                }
            },
            filters: {
                branchId: branchId || null,
                status: status || null,
                priority: priority || null,
                overdueOnly: overdueOnly === 'true'
            }
        };

        ResponseHelper.success(res, OK, "Queue retrieved successfully", queueData);
    } catch (error) {
        console.error('Error retrieving queue view:', error);
        next(error);
    } finally {
        await qRunner.release();
    }
};


// Update Queue Status
export const updateQueueStatus = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const { OrderQueue } = getRepositories(qRunner) as {
            OrderQueue: Repository<OrderQueue>;
        };

        const { orderQueueId } = req.params;
        const { status, notes, estimatedPrepTime, assignedChef, assignedWaiter } = req.body;

        // Find queue item with order details
        const queueItem = await OrderQueue.findOne({
            where: { orderQueueId },
            relations: ['orderDetails']
        });

        if (!queueItem) {
            return next(errorHandler(NOT_FOUND, "Queue item not found"));
        }

        if (queueItem.status === QueueStatus.CANCELLED || queueItem.status === QueueStatus.CHECKOUT || queueItem.status === QueueStatus.COMPLETED) {
            return next(errorHandler(BAD_REQUEST, `Invalid status transition from ${queueItem.status} to ${status}`));
        }

        // Prepare update data for queue
        const queueUpdateData: any = { status };
        if (notes !== undefined) queueUpdateData.notes = notes;
        if (estimatedPrepTime !== undefined) queueUpdateData.estimatedPrepTime = estimatedPrepTime;

        // Prepare update data for order details
        const orderUpdateData: any = {
            status: status as OrderStatus // Cast to OrderStatus enum
        };
        if (notes !== undefined) orderUpdateData.notes = notes;
        if (estimatedPrepTime !== undefined) orderUpdateData.estimatedPrepTime = estimatedPrepTime;

        // Handle status-specific updates for OrderDetails
        switch (status) {
            case QueueStatus.IN_PREPARATION:
                orderUpdateData.prepStartTime = new Date();
                if (assignedChef) {
                    orderUpdateData.assignedChef = assignedChef;
                }
                break;

            case QueueStatus.READY:
                orderUpdateData.readyTime = new Date();
                break;

            case QueueStatus.SERVED:
                orderUpdateData.servedTime = new Date();
                if (assignedWaiter) {
                    orderUpdateData.assignedWaiter = assignedWaiter;
                }
                break;
        }

        // Update both queue and order details
        await OrderQueue.update({ orderQueueId }, queueUpdateData);

        // If status is CHECKOUT or CANCELLED or COMPLETED, remove from queue
        if (status === QueueStatus.CHECKOUT || status === QueueStatus.CANCELLED || status === QueueStatus.COMPLETED) {
            await OrderQueue.delete({ orderQueueId });
            await qRunner.commitTransaction();

            ResponseHelper.success(res, OK, `Order ${status.toLowerCase()} and removed from queue`, null);
            return;
        }

        await qRunner.commitTransaction();

        // Fetch updated item with order details
        const updatedItem = await OrderQueue.findOne({
            where: { orderQueueId },
            relations: ['orderDetails']
        });

        ResponseHelper.success(res, OK, "Queue status updated successfully", updatedItem);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const getActiveQueueOrdersByWaiter = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();

    try {
        const { OrderQueue } = getRepositories(qRunner) as {
            OrderQueue: Repository<OrderQueue>;
        };

        // Extract query parameters
        const {
            assignedWaiter,
            status,
            priority,
            overdueOnly = 'false'
        } = req.query;

        if (!assignedWaiter) {
            return next(errorHandler(BAD_REQUEST, "assignedWaiter parameter is required"));
        }

        

        // Build query for active queue items assigned to waiter
        const queryBuilder = OrderQueue
            .createQueryBuilder('queue')
            .leftJoinAndSelect('queue.orderDetails', 'order_details')  // Fixed: use the actual table name
            .leftJoinAndSelect('queue.orderItems', 'orderItems')
            .leftJoinAndSelect('order_details.customer', 'customer');   // Fixed: use the alias from join

        // Filter for active queue statuses only
        const activeStatuses = [
            QueueStatus.PENDING,
            QueueStatus.IN_PREPARATION,
            QueueStatus.READY,
            QueueStatus.SERVED
        ];

        queryBuilder.where('queue.status IN (:...activeStatuses)', {
            activeStatuses
        });

        // Filter by assigned waiter using JSONB query on orderDetails
        if (typeof assignedWaiter === 'string') {
            // If assignedWaiter is a UUID, search by ID, otherwise search by name
            const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(assignedWaiter);

            if (isUUID) {
                // Fixed: use the correct alias and column reference
                queryBuilder.andWhere('order_details."assignedWaiter"->>\'id\' = :waiterId', {
                    waiterId: assignedWaiter
                });
            } else {
                queryBuilder.andWhere('order_details."assignedWaiter"->>\'name\' ILIKE :waiterName', {
                    waiterName: `%${assignedWaiter}%`
                });
            }
        }


        if (status) {
            queryBuilder.andWhere('queue.status = :status', { status });
        }

        if (priority) {
            queryBuilder.andWhere('queue.priority = :priority', { priority });
        }

        // Order by priority (higher first) then by queue position
        queryBuilder
            .orderBy('queue.priority', 'DESC')
            .addOrderBy('queue.queuePosition', 'ASC')
            .addOrderBy('queue.createdAt', 'ASC');

        const queueItems = await queryBuilder.getMany();

        // Calculate overdue status for each item
        const itemsWithOverdueStatus = queueItems.map(item => ({
            ...item,
            isOverdueFlag: item.isOverdue()
        }));

        // Filter overdue items if requested
        let filteredItems = itemsWithOverdueStatus;
        if (overdueOnly === 'true') {
            filteredItems = itemsWithOverdueStatus.filter(item => item.isOverdueFlag);
        }

        // Group by status for better queue visualization
        const queueByStatus = {
            [QueueStatus.PENDING]: filteredItems.filter(item => item.status === QueueStatus.PENDING),
            [QueueStatus.IN_PREPARATION]: filteredItems.filter(item => item.status === QueueStatus.IN_PREPARATION),
            [QueueStatus.READY]: filteredItems.filter(item => item.status === QueueStatus.READY),
            [QueueStatus.SERVED]: filteredItems.filter(item => item.status === QueueStatus.SERVED)
        };

        // Calculate statistics
        const overdueCount = filteredItems.filter(item => item.isOverdueFlag).length;
        const avgPrepTime = filteredItems.length > 0
            ? filteredItems.reduce((sum, item) => sum + (item.estimatedPrepTime || 0), 0) / filteredItems.length
            : 0;

        const queueData = {
            queueByStatus,
            allItems: filteredItems,
            statistics: {
                totalActive: filteredItems.length,
                pending: queueByStatus[QueueStatus.PENDING].length,
                inPreparation: queueByStatus[QueueStatus.IN_PREPARATION].length,
                ready: queueByStatus[QueueStatus.READY].length,
                served: queueByStatus[QueueStatus.SERVED].length,
                overdue: overdueCount,
                averageEstimatedPrepTime: Math.round(avgPrepTime),
                queuePositions: {
                    next: filteredItems.find(item => item.status === QueueStatus.PENDING)?.queuePosition || null,
                    lastInQueue: Math.max(...filteredItems.map(item => item.queuePosition || 0), 0)
                }
            },
            filters: {
                assignedWaiter,
                status: status || null,
                priority: priority || null,
                overdueOnly: overdueOnly === 'true'
            }
        };

        ResponseHelper.success(res, OK, "Active queue orders retrieved successfully", queueData);
    } catch (error) {
        console.error('Error retrieving active queue orders by waiter:', error);
        next(error);
    } finally {
        await qRunner.release();
    }
};

