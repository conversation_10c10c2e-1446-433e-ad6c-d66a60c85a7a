import {NextFunction, Request, Response} from "express";
import {AuthenticationMapper} from "../../models/authMap/AuthenticationMapper.model";
import {TemplateManager, TemplateType} from "../../utils/templateManager";
import {OK} from "../../constants/STATUS_CODES";

export const getApiDocsHtml = async (req: Request, res: Response, next: NextFunction) => {
    await req.rootQueryRunner.connect();
    try {
        const authMaps = await req.rootQueryRunner.manager.find(AuthenticationMapper);
        const templateManager = new TemplateManager({
            apiDocs: authMaps,
        });
        const template = templateManager.updateTemplateData(TemplateType.API_DOCS, {
            apiDocs: authMaps,
            appName: process.env.APPLICATION_NAME ?? "EasyDine",
        });
        return res.status(OK).render(template.path, template.data);
    } catch (error) {
        return next(error);
    }
};

export const testApiDocs = async (req: Request, res: Response, next: NextFunction) => {
    await req.rootQueryRunner.connect();
    try {
        const authMap = await req.rootQueryRunner.manager.findOne(AuthenticationMapper, {
            where: {
                authMapId: req.params.authMapId,
            },
        });
        if (!authMap) throw new Error("Authentication Map not found");
        const templateManager = new TemplateManager({
            testApiData: authMap,
        });
        const template = templateManager.updateTemplateData(TemplateType.TEST_API, {
            testApiData: authMap,
            appName: process.env.APPLICATION_NAME ?? "EasyDine",
        });
        return res.status(OK).render(template.path, template.data);
    } catch (error) {
        return next(error);
    }
}