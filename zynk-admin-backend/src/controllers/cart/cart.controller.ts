import { Request, Response, NextFunction } from 'express';
import { In, Repository } from 'typeorm';
import { ItemType, OrderItem } from '../../models/order/orderItem.model';
import { Cart, CartStatus, NamedEntity } from '../../models/cart/cart.model';
import { Staff } from '../../models/staff/staff.model';
import { calculateCartItemPrice, calculateTotalCartPrice, createOrderDetailsHelper, validateAllergyIds, validateBeverageDishIds, validateBranchId, validateCookingStyleId, validateCustomerId, validateDessertDishIds, validateDishAddonsIds, validateDishExclusionIds, validateDishExtrasIds, validateDishId, validateDishSizeId, validateOrderTypeId, validateSideDishIds, validateSpicinessId, validateStaffId, validateTableId } from '../../helpers/order/order.helper';
import { BAD_REQUEST, CREATED, INTERNAL_SERVER_ERROR, NOT_FOUND, OK } from '../../constants/STATUS_CODES';
import { errorHandler } from '../../utils/errorHandler';
import { CartItem } from '../../models/cart/cartItem.model';
import { Dish } from '../../models/foodmenu/dish.model';
import { AppprovalStatus, OrderDetails } from '../../models/order/orderDetail.model';
import { getRepositories } from '../../helpers/system/RepositoryHelper.helper';
import { ResponseHelper } from '../../helpers/system/ResponseHelper.helper';
import { OrderQueue } from '../../models/order/orderQueue.model';
import { Customer } from '../../models/customer/customer.model';
import { Branch } from '../../models/company/branch.model';
import { OrderType } from '../../models/tenantSettings/general/orderType.model';
import { TableModel } from '../../models/tenantSettings/tableReservation/management/table.model';
import { Spiciness } from '../../models/foodmenu/custom/spiciness.model';
import { CookingStyle } from '../../models/foodmenu/custom/cookingstyle.model';
import { DishExclusion } from '../../models/foodmenu/custom/dishexclusion.model';
import { DishSize } from '../../models/foodmenu/custom/dishsize.model';
import { Allergy } from '../../models/reference/allergy.model';
import { DishAddon } from '../../models/foodmenu/custom/addon.model';
import { DishExtra } from '../../models/foodmenu/custom/extra.model';
import { DishSide } from '../../models/foodmenu/custom/dish_side.model';
import { DishBeverage } from '../../models/foodmenu/custom/dish_bev.model';
import { DishDessert } from '../../models/foodmenu/custom/dish_dessert.model';
import { OrderItemInput } from '../order/orderDetail.controller';
import { PreparationDuration } from '../../models/tenantSettings/food/preparation_duration.model';
import { addAlertsToCart, addMiscItemsToCart, AlertInput, MiscItemInput, removeAlertFromCart, removeMiscItemFromCart, updateAlertInCart, updateMiscItemInCart } from '../../helpers/cart/cart.helper';

// Interface for adding items to cart
export interface CartItemInput {
    name: string;
    price: number;
    quantity: number;
    dishId: string;
    type?: ItemType;
    baseItemId?: string;
    notes?: string;

    // Customization IDs from frontend
    allergyIds?: string[];
    dishSizeId?: string;
    dishExclusionIds?: string[];
    cookingStyleId?: string;
    spicinessId?: string;
    dishAddons?: AddonInput[];
    dishExtras?: AddonInput[];
    dishSides?: AddonInput[];
    dishBeverages?: AddonInput[];
    dishDesserts?: AddonInput[];
}

export interface AddonInput {
    id: string;
    quantity: number;
}

// Get or create active cart for staff
export const getOrCreateCart = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    try {
        const { Cart, Staff, Branch } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
            Staff: Repository<Staff>;
            Branch: Repository<Branch>
        };

        const staffId = req.checkedInStaffId

        const branchId: any = req.branchId;
        console.log(branchId, "branchId from query or request");
        const branch: any = await validateBranchId(Branch, branchId);

        // Validate staff
        const staff = await validateStaffId(Staff, staffId);
        if (!staff) {
            return next(errorHandler(BAD_REQUEST, "Invalid staff ID"));
        }

        // Find existing active cart
        let cart = await Cart.findOne({
            where: {
                staffId,
                status: CartStatus.ACTIVE
            },
            relations: ['cartItems']
        });

        // Create new cart if none exists
        if (!cart) {
            cart = Cart.create({
                staffId,
                status: CartStatus.ACTIVE,
                total: 0,
                branch
            });
            cart = await Cart.save(cart);
        }

        ResponseHelper.success(res, OK, "Cart retrieved successfully", cart);
    } catch (error) {
        next(error);
    }
};

// Add item to cart
export const addItemToCart = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {
            Cart,
            CartItem,
            Staff,
            Dish,
            Spiciness,
            CookingStyle,
            DishExclusion,
            DishSize,
            Allergy,
            DishAddon,
            DishExtra,
            DishSide,
            DishBeverage,
            DishDessert,
            Branch
        } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
            CartItem: Repository<CartItem>;
            Staff: Repository<Staff>;
            Dish: Repository<Dish>;
            Spiciness: Repository<Spiciness>;
            CookingStyle: Repository<CookingStyle>;
            DishExclusion: Repository<DishExclusion>;
            DishSize: Repository<DishSize>;
            Allergy: Repository<Allergy>;
            DishAddon: Repository<DishAddon>;
            DishExtra: Repository<DishExtra>;
            DishSide: Repository<DishSide>;
            DishBeverage: Repository<DishBeverage>;
            DishDessert: Repository<DishDessert>;
            Branch: Repository<Branch>
        };

        const staffId = req.checkedInStaffId
        const itemData: CartItemInput = req.body;

        if (itemData.quantity <= 0) {
            return next(errorHandler(BAD_REQUEST, "quantity must be positive"));
        }

        if (!itemData.dishId) {
            return next(errorHandler(BAD_REQUEST, "Dish ID is required"));
        }

        // Validate staff
        const staff = await validateStaffId(Staff, staffId);
        if (!staff) {
            return next(errorHandler(BAD_REQUEST, "Invalid staff ID"));
        }

        const branchId: any = req.branchId;
        const branch: any = await validateBranchId(Branch, branchId);

        // Get or create active cart
        let cart: any = await Cart.findOne({
            where: {
                staffId,
                status: CartStatus.ACTIVE
            },
            relations: ['cartItems'] // IMPORTANT: Load cart items
        });

        if (!cart) {
            cart = Cart.create({
                staffId,
                status: CartStatus.ACTIVE,
                total: 0,
                branch
            });
            cart = await Cart.save(cart);
        }

        // Validate dish
        const dishEntity = await validateDishId(Dish, itemData.dishId);

        // Validate customizations in parallel
        const [
            allergyEntities,
            dishSizeEntity,
            dishExclusionEntities,
            cookingStyleEntity,
            spicinessEntity,
            dishAddonEntities,
            dishExtraEntities,
            dishSideEntities,
            dishBeverageEntities,
            dishDessertEntities
        ] = await Promise.all([
            itemData.allergyIds ? validateAllergyIds(Allergy, itemData.allergyIds) : Promise.resolve([]),
            itemData.dishSizeId ? validateDishSizeId(DishSize, itemData.dishSizeId) : Promise.resolve(null),
            itemData.dishExclusionIds ? validateDishExclusionIds(DishExclusion, itemData.dishExclusionIds) : Promise.resolve([]),
            itemData.cookingStyleId ? validateCookingStyleId(CookingStyle, itemData.cookingStyleId) : Promise.resolve(null),
            itemData.spicinessId ? validateSpicinessId(Spiciness, itemData.spicinessId) : Promise.resolve(null),
            itemData.dishAddons ? validateDishAddonsIds(DishAddon, itemData.dishAddons) : Promise.resolve([]),
            itemData.dishExtras ? validateDishExtrasIds(DishExtra, itemData.dishExtras) : Promise.resolve([]),
            itemData.dishSides ? validateSideDishIds(DishSide, itemData.dishSides) : Promise.resolve([]),
            itemData.dishBeverages ? validateBeverageDishIds(DishBeverage, itemData.dishBeverages) : Promise.resolve([]),
            itemData.dishDesserts ? validateDessertDishIds(DishDessert, itemData.dishDesserts) : Promise.resolve([])
        ]);

        // Create cart item
        const cartItemData: Partial<CartItem> = {
            name: dishEntity?.name,
            price: dishEntity?.price,
            quantity: itemData.quantity,
            baseItem: dishEntity || undefined,
            cart: cart,
            type: itemData.type || ItemType.STANDARD,
            notes: itemData.notes,
            totalPrice: 0,

            // Store validated entities as JSONB
            allergies: allergyEntities.length > 0 ? allergyEntities : undefined,
            dishSizes: dishSizeEntity || undefined,
            dishExclusions: dishExclusionEntities.length > 0 ? dishExclusionEntities : undefined,
            cookingStyles: cookingStyleEntity || undefined,
            spiciness: spicinessEntity || undefined,
            dishAddons: dishAddonEntities.length > 0 ? dishAddonEntities : undefined,
            dishExtras: dishExtraEntities.length > 0 ? dishExtraEntities : undefined,
            dishSides: dishSideEntities.length > 0 ? dishSideEntities : undefined,
            dishBeverages: dishBeverageEntities.length > 0 ? dishBeverageEntities : undefined,
            dishDesserts: dishDessertEntities.length > 0 ? dishDessertEntities : undefined
        };

        const totalPrice = await calculateCartItemPrice(cartItemData);

        if (totalPrice === null) {
            return next(errorHandler(BAD_REQUEST, "Failed to calculate item price"));
        }

        cartItemData.totalPrice = totalPrice;

        const cartItem = CartItem.create(cartItemData);
        cart.cartItems.push(cartItem);
        cart.total = await calculateTotalCartPrice(cart.cartItems, cart.miscItems);
        cartItem.cart = cart;
        await CartItem.save(cartItem);
        await Cart.save(cart);


        await qRunner.commitTransaction();


        ResponseHelper.success(res, CREATED, "Item added to cart successfully");
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

// Update cart item quantity
export const updateCartItem = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const { Cart, CartItem } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
            CartItem: Repository<CartItem>;
        };

        const staffId = req.checkedInStaffId

        const { itemId } = req.params;
        const { quantity } = req.body;

        if (!quantity || quantity <= 0) {
            return next(errorHandler(BAD_REQUEST, "quantity must be positive"));
        }

        // Find cart item with cart relation
        const cartItem = await CartItem.findOne({
            where: {
                cartItemId: itemId,
                cart: { staffId }
            },
            relations: ['cart', 'cart.cartItems']
        });

        if (!cartItem) {
            return next(errorHandler(NOT_FOUND, "Cart item not found"));
        }

        // Update quantity
        cartItem.quantity = quantity;
        cartItem.totalPrice = await calculateCartItemPrice(cartItem);
        cartItem.cart.total = await calculateTotalCartPrice(cartItem.cart.cartItems, cartItem.cart.miscItems);
        await CartItem.save(cartItem);
        await Cart.save(cartItem.cart);


        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Cart item updated successfully");
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};


export const addCartDetails = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const { Cart, CartItem, Customer, TableModel, OrderType } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
            CartItem: Repository<CartItem>;
            Customer: Repository<Customer>;
            TableModel: Repository<TableModel>;
            OrderType: Repository<OrderType>;
        };

        const { cartId } = req.params;
        const { note, miscItems, alerts, customerId, tableId, orderTypeId } = req.body;

        if (!cartId || typeof cartId !== 'string' || cartId.trim() === '') {
            return next(errorHandler(BAD_REQUEST, "Cart ID is required and must be a valid string"));
        }

        // Find the active cart for the staff member
        const cart = await Cart.findOne({
            where: {
                cartId
            },
            relations: ['cartItems']
        });

        if (!cart) {
            return next(errorHandler(NOT_FOUND, "Active cart not found for staff"));
        }

        // Update cart notes if provided
        if (note) {
            cart.notes = note;
        }

        if (customerId) {
            const customerInfo: any = await validateCustomerId(Customer, customerId)
            cart.customerInfo = customerInfo;
        }

        if (tableId) {
            const table: any = await validateTableId(TableModel, tableId);
            cart.table = table;

        }

        if (orderTypeId) {
            const orderType: any = await validateOrderTypeId(OrderType, orderTypeId);
            cart.orderType = orderType;
        }

        await Cart.save(cart);

        // Add misc items if provided using the helper
        let addedMiscItemIds: string[] = [];
        if (miscItems && Array.isArray(miscItems) && miscItems.length > 0) {
            // Validate miscItems structure
            const validMiscItems: MiscItemInput[] = miscItems.map((item: any) => {
                if (!item.name || typeof item.price !== 'number') {
                    throw new Error('Invalid misc item structure. Each item must have name and price');
                }
                return {
                    name: item.name,
                    price: item.price
                };
            });

            addedMiscItemIds = await addMiscItemsToCart(Cart, cart.cartId, validMiscItems);
        }

        // Add alerts if provided using the helper
        let addedAlertIds: string[] = [];
        if (alerts && Array.isArray(alerts) && alerts.length > 0) {
            // Validate alerts structure
            const validAlerts: AlertInput[] = alerts.map((alert: any) => {
                if (!alert.note) {
                    throw new Error('Invalid alert structure. Each alert must have a note');
                }
                return {
                    note: alert.note
                };
            });

            addedAlertIds = await addAlertsToCart(Cart, cart.cartId, validAlerts);
        }

        const updatedCart: any = await Cart.findOne({
            where: { cartId },
            relations: ['cartItems']
        });

        updatedCart.total = await calculateTotalCartPrice(updatedCart.cartItems, updatedCart.miscItems);
        await Cart.save(updatedCart);


        await qRunner.commitTransaction();

        const responseData = {
            cartId: cart.cartId,
            message: "Cart updated successfully",
            ...(addedMiscItemIds.length > 0 && { addedMiscItemIds }),
            ...(addedAlertIds.length > 0 && { addedAlertIds })
        };

        ResponseHelper.success(res, OK, "Cart updated successfully", responseData);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

export const patchCartMiscItem = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const { Cart } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
        };

        const { cartId } = req.params;
        const { miscItem } = req.body;

        // Verify cart belongs to staff
        const cart = await Cart.findOne({
            where: {
                cartId,
            }
        });

        if (!cart) {
            return next(errorHandler(NOT_FOUND, "Cart not found or doesn't belong to staff"));
        }

        if (!miscItem || !miscItem.miscItemId) {
            return next(errorHandler(BAD_REQUEST, "Invalid misc item structure. Must have miscItemId"));
        }

        if (!miscItem || !miscItem.name || typeof miscItem.price !== 'number') {
            return next(errorHandler(BAD_REQUEST, "Invalid misc item structure. Must have name and price"));
        }

        const updates = {
            name: miscItem.name,
            price: miscItem.price,
        };

        await updateMiscItemInCart(Cart, cartId, miscItem.miscItemId, updates);

        const updatedCart: any = await Cart.findOne({
            where: { cartId },
            relations: ['cartItems']
        });

        updatedCart.total = await calculateTotalCartPrice(updatedCart.cartItems, updatedCart.miscItems);
        await Cart.save(updatedCart);


        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Misc updated added successfully", {
            miscItems: updatedCart?.miscItems,
        });
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

// Controller for adding alerts specifically
export const patchCartAlert = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const { Cart } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
        };

        const { cartId } = req.params;
        const { alert } = req.body;

        // Verify cart belongs to staff
        const cart = await Cart.findOne({
            where: {
                cartId,
            }
        });

        if (!cart) {
            return next(errorHandler(NOT_FOUND, "Cart not found or doesn't belong to staff"));
        }

        if (!alert || !alert.alertId) {
            return next(errorHandler(BAD_REQUEST, "Invalid alert structure. Must have alertId"));
        }

        if (!alert.note) {
            return next(errorHandler(BAD_REQUEST, "Invalid alert structure. Must have a note"));
        }

        await updateAlertInCart(Cart, cartId, alert.alertId, alert.note);

        await qRunner.commitTransaction();

        const updatedCart = await Cart.findOne({
            where: { cartId },
            select: ['cartId', 'alerts'],
        });

        ResponseHelper.success(res, OK, "Alerts added successfully", updatedCart);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};


export const deleteMiscItemFromCart = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const { Cart } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
        };

        const staffId = req.checkedInStaffId;
        const { cartId, miscItemId } = req.params;

        // Verify cart belongs to staff
        const cart = await Cart.findOne({
            where: {
                cartId,
                staffId
            }
        });

        if (!cart) {
            return next(errorHandler(NOT_FOUND, "Cart not found or doesn't belong to staff"));
        }

        // Use helper to remove misc item
        await removeMiscItemFromCart(Cart, cartId, miscItemId);

        const updatedCart: any = await Cart.findOne({
            where: { cartId },
            relations: ['cartItems']
        });

        updatedCart.total = await calculateTotalCartPrice(updatedCart.cartItems, updatedCart.miscItems);
        await Cart.save(updatedCart);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Misc item deleted successfully", {
            deletedMiscItemId: miscItemId
        });
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

// Delete alert from cart
export const deleteAlertFromCart = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const { Cart } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
        };

        const staffId = req.checkedInStaffId;
        const { cartId, alertId } = req.params;

        // Verify cart belongs to staff
        const cart = await Cart.findOne({
            where: {
                cartId,
                staffId
            }
        });

        if (!cart) {
            return next(errorHandler(NOT_FOUND, "Cart not found or doesn't belong to staff"));
        }

        // Use helper to remove alert
        await removeAlertFromCart(Cart, cartId, alertId);

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Alert deleted successfully", {
            deletedAlertId: alertId
        });
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

// Remove item from cart
export const removeCartItem = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const { Cart, CartItem } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
            CartItem: Repository<CartItem>;
        };

        const staffId = req.checkedInStaffId

        const { itemId } = req.params;

        // Find cart item with cart relation
        const cartItem = await CartItem.findOne({
            where: {
                cartItemId: itemId,
                cart: { staffId }
            },
            relations: ['cart']
        });

        if (!cartItem) {
            return next(errorHandler(NOT_FOUND, "Cart item not found"));
        }

        const cartId = cartItem.cart.cartId;

        // Remove the cart item FIRST
        await CartItem.remove(cartItem);

        // Then get the updated cart with remaining items
        const updatedCart = await Cart.findOne({
            where: { cartId: cartId },
            relations: ['cartItems']
        });

        if (updatedCart) {
            // Recalculate total with remaining items
            updatedCart.total = await calculateTotalCartPrice(updatedCart.cartItems, updatedCart.miscItems);
            await Cart.save(updatedCart);
        }

        await qRunner.commitTransaction();

        ResponseHelper.success(res, OK, "Item removed from cart successfully", updatedCart);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

// Clear cart
export const clearCart = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const { Cart, CartItem } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
            CartItem: Repository<CartItem>;
        };

        const staffId = req.checkedInStaffId

        // Find cart with items
        const cart = await Cart.findOne({
            where: {
                staffId,
                status: CartStatus.ACTIVE
            },
            relations: ['cartItems']
        });

        if (!cart) {
            return next(errorHandler(NOT_FOUND, "Active cart not found"));
        }

        // FIXED: Remove all cart items properly
        if (cart.cartItems && cart.cartItems.length > 0) {
            // Delete each cart item individually to ensure proper deletion
            await CartItem.delete({
                cart: { cartId: cart.cartId }
            });
        }

        // FIXED: Reset cart totals and reload items
        cart.total = 0;
        cart.cartItems = []; // Clear the array
        cart.alerts = []; // Clear alerts
        cart.miscItems = []; // Clear misc items
        await Cart.save(cart);

        await qRunner.commitTransaction();

        // Return the cleared cart
        const clearedCart = await Cart.findOne({
            where: { cartId: cart.cartId },
            relations: ['cartItems']
        });

        ResponseHelper.success(res, OK, "Cart cleared successfully", clearedCart);
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};

// Hold cart
export const holdCart = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    try {
        const { Cart } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
        };

        const branchId: any = req.branchId;

        const staffId = req.checkedInStaffId;

        // Find active cart
        const activeCart = await Cart.findOne({
            where: {
                staffId,
                status: CartStatus.ACTIVE
            },
            relations: ['cartItems']
        });

        if (!activeCart) {
            return next(errorHandler(NOT_FOUND, "Active cart not found"));
        }

        if (activeCart.isEmpty()) {
            return next(errorHandler(BAD_REQUEST, "Cannot hold empty cart"));
        }

        // Hold the current active cart
        activeCart.holdCart();
        await Cart.save(activeCart);

        // Create a new active cart for the staff member
        const newActiveCart = Cart.create({
            staffId,
            status: CartStatus.ACTIVE,
            total: 0,
            branchId,
            miscItems: [],
            alerts: []
        });

        await Cart.save(newActiveCart);

        // Return both carts in the response for frontend convenience
        ResponseHelper.success(res, OK, "Cart held successfully and new active cart created", {
            heldCart: activeCart,
            newActiveCart
        });
    } catch (error) {
        next(error);
    }
};

// Activate held cart
export const activateCart = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    try {
        const { Cart } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
        };

        const staffId = req.checkedInStaffId;
        const { cartId } = req.params;

        // Find the cart to activate (must be in HOLD status)
        const cartToActivate = await Cart.findOne({
            where: {
                cartId,
                staffId,
                status: CartStatus.HOLD
            },
            relations: ['cartItems']
        });

        if (!cartToActivate) {
            return next(errorHandler(NOT_FOUND, "Held cart not found"));
        }

        // Find any existing active cart for this staff member
        const existingActiveCart = await Cart.findOne({
            where: {
                staffId,
                status: CartStatus.ACTIVE
            }
        });

        // If there's an existing active cart, put it on hold
        if (existingActiveCart) {
            existingActiveCart.holdCart();
            await Cart.save(existingActiveCart);
        }

        // Activate the requested cart
        cartToActivate.activateCart();
        await Cart.save(cartToActivate);

        ResponseHelper.success(res, OK, "Cart activated successfully", cartToActivate);
    } catch (error) {
        next(error);
    }
};

export const listCarts = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    try {
        const { Cart } = getRepositories(req.queryRunner) as { Cart: Repository<Cart> };
        const { status } = req.query;

        const staffId = req.checkedInStaffId;

        const where: any = { staffId };
        if (status) {
            where.status = status;
        }

        const carts = await Cart.find({
            where,
            relations: ['cartItems']
        });

        ResponseHelper.success(res, OK, "Carts retrieved successfully", carts);
    } catch (error) {
        next(error);
    }
};

export const deleteCart = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    try {
        const { Cart } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
        };

        const branchId: any = req.branchId;
        const staffId = req.checkedInStaffId;
        const { cartId } = req.params;

        // Find the cart to delete
        const cart = await Cart.findOne({
            where: {
                cartId,
                staffId
            },
            relations: ['cartItems']
        });

        if (!cart) {
            return next(errorHandler(NOT_FOUND, "Cart not found"));
        }

        const wasActiveCart = cart.isActive();

        // Delete the cart (cascade will delete cart items)
        await Cart.remove(cart);

        // If we deleted the active cart, create a new one
        if (wasActiveCart) {
            const newActiveCart = Cart.create({
                staffId,
                status: CartStatus.ACTIVE,
                total: 0,
                branchId,
                miscItems: [],
                alerts: []
            });

            await Cart.save(newActiveCart);

            ResponseHelper.success(res, OK, "Cart deleted successfully and new active cart created", {
                deletedCartId: cartId,
                newActiveCart
            });
        } else {
            ResponseHelper.success(res, OK, "Cart deleted successfully", {
                deletedCartId: cartId
            });
        }
    } catch (error) {
        next(error);
    }
};

export const confirmCart = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        // Get all required repositories
        const repositories = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
            OrderDetails: Repository<OrderDetails>;
            OrderItem: Repository<OrderItem>;
            OrderQueue: Repository<OrderQueue>;
            Staff: Repository<Staff>;
            Customer: Repository<Customer>;
            Branch: Repository<Branch>;
            OrderType: Repository<OrderType>;
            TableModel: Repository<TableModel>;
            Dish: Repository<Dish>;
            Spiciness: Repository<Spiciness>;
            CookingStyle: Repository<CookingStyle>;
            DishExclusion: Repository<DishExclusion>;
            DishSize: Repository<DishSize>;
            Allergy: Repository<Allergy>;
            DishAddon: Repository<DishAddon>;
            DishExtra: Repository<DishExtra>;
            DishSide: Repository<DishSide>;
            DishBeverage: Repository<DishBeverage>;
            DishDessert: Repository<DishDessert>;
            PreparationDuration: Repository<PreparationDuration>;
        };

        const staffId = req.checkedInStaffId
        const {
            estimatedPrepTimeInput,
            assignedWaiterId,
            notes,
            tableId,
            orderTypeId,
            numberOfPeople,
        } = req.body;

        const orderApproval = AppprovalStatus.APPROVED;

        const branchId: any = req.branchId;

        const defaultEstimatedPrepTime = await repositories.PreparationDuration.findOne({
            where: { preparationDurationId: branchId },
            relations: ['branch']
        });

        const estimatedPrepTime = estimatedPrepTimeInput ? Number(estimatedPrepTimeInput) : defaultEstimatedPrepTime?.duration;

        // Validate staffId parameter
        if (!staffId) {
            return next(errorHandler(BAD_REQUEST, 'Staff ID is required'));
        }

        // Find active cart with all necessary relations
        const cart = await repositories.Cart.findOne({
            where: {
                staffId,
                status: CartStatus.ACTIVE
            },
            relations: ['cartItems', 'staff']
        });

        if (!cart) {
            return next(errorHandler(NOT_FOUND, 'Active cart not found'));
        }

        if (cart.isEmpty()) {
            return next(errorHandler(BAD_REQUEST, 'Cannot confirm an empty cart'));
        }

        // Validate required metadata
        if (!orderTypeId) {
            return next(errorHandler(BAD_REQUEST, 'Order Type ID is required'));
        }

        // Validate estimatedPrepTime for approved orders
        if (orderApproval === AppprovalStatus.APPROVED) {
            if (!estimatedPrepTime || typeof estimatedPrepTime !== 'number' || estimatedPrepTime <= 0) {
                return next(errorHandler(BAD_REQUEST, 'estimatedPrepTime is required and must be a positive number for approved orders'));
            }
        }

        // Validate cart total
        if (typeof Number(cart.total) !== 'number' || Number(cart.total) < 0) {
            return next(errorHandler(BAD_REQUEST, 'Cart total must be a positive number'));
        }

        // Prepare order items from cart
        let orderItems: OrderItemInput[];
        try {
            orderItems = cart.toOrderItemInputs();
        } catch (error: any) {
            return next(errorHandler(BAD_REQUEST, `Invalid cart items: ${error.message}`));
        }

        // Prepare inputs for createOrderDetailsHelper
        const orderDetailsInput: any = {
            tableId: cart.table?.id || tableId || null,
            orderTypeId: cart.orderType?.id || orderTypeId,
            numberOfPeople,
            orderItems,
            total: Number(cart.total) || await calculateTotalCartPrice(cart.cartItems, cart.miscItems),
            notes: notes || cart.notes || '',
            estimatedPrepTime: orderApproval === AppprovalStatus.APPROVED ? estimatedPrepTime : undefined,
            orderedById: cart.staffId,
            customerId: cart.customerInfo?.id || null,
            branchId: cart.branch?.id || null,
            orderApproval,
            assignedWaiterId,
            miscItems: cart.miscItems || [],
            alerts: cart.alerts || [],
        };

        // Update cart status to CONFIRMED before creating order
        cart.confirmCart();
        await repositories.Cart.save(cart);

        // Create order using helper
        let order: OrderDetails;
        try {
            order = await createOrderDetailsHelper(orderDetailsInput, repositories, qRunner);
        } catch (error: any) {
            // If order creation fails, rollback cart status
            throw new Error(`Failed to create order: ${error.message}`);
        }

        await qRunner.commitTransaction();

        // Return success response with both cart and order
        ResponseHelper.success(res, CREATED, 'Cart confirmed and order created successfully', {
            cart: {
                cartId: cart.cartId,
                status: cart.status,
                total: cart.total,
                itemCount: cart.cartItems?.length || 0,
                confirmedAt: cart.updatedAt
            },
            order: {
                orderDetailId: order.orderDetailId,
                status: order.status,
                orderApproval: order.orderApproval,
                total: order.total,
                tableId: order.table.id,
                tableName: order.table.name,
                orderTypeId: order.orderType.id,
                orderTypeName: order.orderType.name,
                itemCount: order.orderItems?.length || 0,
                estimatedPrepTime: order.estimatedPrepTime,
                createdAt: order.createdAt
            }
        });

    } catch (error: any) {
        await qRunner.rollbackTransaction();
        console.error('Error confirming cart:', error);

        // Handle specific error types
        if (error.message.includes('BAD_REQUEST')) {
            return next(errorHandler(BAD_REQUEST, error.message.replace('BAD_REQUEST: ', '')));
        }

        next(errorHandler(INTERNAL_SERVER_ERROR, 'Failed to confirm cart and create order'));
    } finally {
        await qRunner.release();
    }
};



export const getCartById = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    try {
        const { Cart } = getRepositories(req.queryRunner) as { Cart: Repository<Cart> };
        const { cartId } = req.params;

        const cart = await Cart.findOne({
            where: { cartId },
            relations: ['cartItems', 'staff']
        });

        if (!cart) {
            return next(errorHandler(NOT_FOUND, "Cart not found"));
        }

        ResponseHelper.success(res, OK, "Cart retrieved successfully", cart);
    } catch (error) {
        next(error);
    }
};

export const updateCartItemCustomization = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const qRunner = req.queryRunner;

    await qRunner.connect();
    await qRunner.startTransaction();

    try {
        const {
            Cart,
            CartItem,
            Staff,
            Dish,
            Spiciness,
            CookingStyle,
            DishExclusion,
            DishSize,
            Allergy,
            DishAddon,
            DishExtra,
            DishSide,
            DishBeverage,
            DishDessert
        } = getRepositories(qRunner) as {
            Cart: Repository<Cart>;
            CartItem: Repository<CartItem>;
            Staff: Repository<Staff>;
            Dish: Repository<Dish>;
            Spiciness: Repository<Spiciness>;
            CookingStyle: Repository<CookingStyle>;
            DishExclusion: Repository<DishExclusion>;
            DishSize: Repository<DishSize>;
            Allergy: Repository<Allergy>;
            DishAddon: Repository<DishAddon>;
            DishExtra: Repository<DishExtra>;
            DishSide: Repository<DishSide>;
            DishBeverage: Repository<DishBeverage>;
            DishDessert: Repository<DishDessert>;
        };

        const staffId = req.checkedInStaffId

        const { itemId } = req.params;
        const customizationData: any = req.body;

        // Validate staff
        const staff = await validateStaffId(Staff, staffId);
        if (!staff) {
            return next(errorHandler(BAD_REQUEST, "Invalid staff ID"));
        }

        // Find cart item with cart relation
        const cartItem: any = await CartItem.findOne({
            where: {
                cartItemId: itemId,
                cart: {
                    staffId,
                    status: CartStatus.ACTIVE
                }
            },
            relations: ['cart']
        });

        if (!cartItem) {
            return next(errorHandler(NOT_FOUND, "Cart item not found or cart is not active"));
        }

        // Validate price if provided
        if (customizationData.price !== undefined && customizationData.price < 0) {
            return next(errorHandler(BAD_REQUEST, "price cannot be negative"));
        }

        // Validate customizations in parallel
        const [
            allergyEntities,
            dishSizeEntity,
            dishExclusionEntities,
            cookingStyleEntity,
            spicinessEntity,
            dishAddonEntities,
            dishExtraEntities,
            dishSideEntities,
            dishBeverageEntities,
            dishDessertEntities
        ] = await Promise.all([
            customizationData.allergyIds !== undefined ?
                validateAllergyIds(Allergy, customizationData.allergyIds) :
                Promise.resolve(undefined),
            customizationData.dishSizeId !== undefined ?
                validateDishSizeId(DishSize, customizationData.dishSizeId) :
                Promise.resolve(undefined),
            customizationData.dishExclusionIds !== undefined ?
                validateDishExclusionIds(DishExclusion, customizationData.dishExclusionIds) :
                Promise.resolve(undefined),
            customizationData.cookingStyleId !== undefined ?
                validateCookingStyleId(CookingStyle, customizationData.cookingStyleId) :
                Promise.resolve(undefined),
            customizationData.spicinessId !== undefined ?
                validateSpicinessId(Spiciness, customizationData.spicinessId) :
                Promise.resolve(undefined),
            customizationData.dishAddons !== undefined ?
                validateDishAddonsIds(DishAddon, customizationData.dishAddons) :
                Promise.resolve(undefined),
            customizationData.dishExtras !== undefined ?
                validateDishExtrasIds(DishExtra, customizationData.dishExtras) :
                Promise.resolve(undefined),
            customizationData.dishSides !== undefined ?
                validateSideDishIds(DishSide, customizationData.dishSides) :
                Promise.resolve(undefined),
            customizationData.dishBeverages !== undefined ?
                validateBeverageDishIds(DishBeverage, customizationData.dishBeverages) :
                Promise.resolve(undefined),
            customizationData.dishDesserts !== undefined ?
                validateDessertDishIds(DishDessert, customizationData.dishDesserts) :
                Promise.resolve(undefined)
        ]);

        // Update cart item fields
        if (customizationData.notes !== undefined) {
            cartItem.notes = customizationData.notes;
        }

        // Update customizations (only if provided in request)
        if (allergyEntities !== undefined) {
            cartItem.allergies = allergyEntities.length > 0 ? allergyEntities : undefined;
        }
        if (dishSizeEntity !== undefined) {
            cartItem.dishSizes = dishSizeEntity || undefined;
        }
        if (dishExclusionEntities !== undefined) {
            cartItem.dishExclusions = dishExclusionEntities.length > 0 ? dishExclusionEntities : undefined;
        }
        if (cookingStyleEntity !== undefined) {
            cartItem.cookingStyles = cookingStyleEntity || undefined;
        }
        if (spicinessEntity !== undefined) {
            cartItem.spiciness = spicinessEntity || undefined;
        }
        if (dishAddonEntities !== undefined) {
            cartItem.dishAddons = dishAddonEntities.length > 0 ? dishAddonEntities : undefined;
        }
        if (dishExtraEntities !== undefined) {
            cartItem.dishExtras = dishExtraEntities.length > 0 ? dishExtraEntities : undefined;
        }
        if (dishSideEntities !== undefined) {
            cartItem.dishSides = dishSideEntities.length > 0 ? dishSideEntities : undefined;
        }
        if (dishBeverageEntities !== undefined) {
            cartItem.dishBeverages = dishBeverageEntities.length > 0 ? dishBeverageEntities : undefined;
        }
        if (dishDessertEntities !== undefined) {
            cartItem.dishDesserts = dishDessertEntities.length > 0 ? dishDessertEntities : undefined;
        }

        // Save updated cart item
        cartItem.totalPrice = await calculateCartItemPrice(cartItem);
        if (cartItem.totalPrice === null) {
            return next(errorHandler(BAD_REQUEST, "Failed to calculate item price"));
        }
        await CartItem.save(cartItem);

        const cart = await Cart.findOne({
            where: { cartId: cartItem.cart.cartId },
            relations: ['cartItems']
        });

        if (cart) {
            cart.total = await calculateTotalCartPrice(cart.cartItems, cart.miscItems);
            await Cart.save(cart);
        }

        await qRunner.commitTransaction();


        ResponseHelper.success(res, OK, "Cart item customization updated successfully",
        );
    } catch (error) {
        await qRunner.rollbackTransaction();
        next(error);
    } finally {
        await qRunner.release();
    }
};