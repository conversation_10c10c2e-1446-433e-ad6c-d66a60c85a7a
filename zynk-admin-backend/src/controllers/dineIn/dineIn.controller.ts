import { NextFunction, Request, Response } from "express";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { Between, In, Repository } from "typeorm";
import {
  TableCleaningStatus,
  TableModel,
  TableStatus,
} from "../../models/tenantSettings/tableReservation/management/table.model";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import { BAD_REQUEST, NOT_FOUND, OK } from "../../constants/STATUS_CODES";
import {
  ORDER_CANCEL_SUCCESS,
  ORDER_TAKEN_SUCCESS,
  TABLE_ATTR_UPDATED,
} from "../../constants/tenant/dineIn/msg";
import { errorHandler } from "../../utils/errorHandler";
import { BUFFERTIME_NOTFOUND, GUEST_COUNT_OUT_OF_RANGE_FOR_COMBINATION, INVALID_NUM_GUESTS, SOME_TABLES_IN_COMBINATION_DISABLED, TABLE_COMBINATION_DISABLED, TABLE_COMBINATION_NOT_FOUND, TABLE_NOT_FOUND } from "../../constants/tenant/settings/tableReserv/err";
import {
  GUEST_COUNT_MISMATCH_WITH_RESERVATION,
  GUEST_COUNT_OUT_OF_RANGE_FOR_TABLE,
  INSUFFICIENT_AVAILABLE_SEATS,
  INVALID_TABLE_STATUS,
  RESERVATION_NOT_FOUND,
  TABLE_ALREADY_OCCUPIED,
  TABLE_DISABLED,
  TABLE_NEEDS_CLEANING,
  TABLE_RESERVED_PROVIDE_RESERVATION_ID,
} from "../../constants/tenant/dineIn/err";
import {
  ReservationStatus,
  TableReservation,
} from "../../models/dineIn/tableReservation.model";
import { determineNewTableStatus, determineNewTableStatusAfterCancellation } from "../../helpers/dineIn/tableDineIn.helper";
import { BufferTime } from "../../models/tenantSettings/tableReservation/management/bufferTime.model";
import { TableCombination } from "../../models/tenantSettings/tableReservation/management/tableCombination.model";

export const patchTableAttrs = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { TableModel } = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
    };

    const branchId = req.branchId;
    const tableId = req.params.tableId;
    const updateData = req.body;

    const exTable = await TableModel.findOne({
      where: {
        tableId,
        branch: {
          branchId,
        },
      },
    });

    if (!exTable) return next(errorHandler(NOT_FOUND, TABLE_NOT_FOUND));

    if (Object.keys(updateData).length > 0) {
      exTable.update({
        ...updateData,
      });

      await TableModel.save(exTable);
    }

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, TABLE_ATTR_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const takeOrderFromDineIn = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { TableModel, TableReservation, BufferTime} = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
      TableReservation: Repository<TableReservation>;
      BufferTime: Repository<BufferTime>
    };

    const branchId = req.branchId;
    const tableId = req.params.tableId;
    const { numOfGuests, confirmationCode } = req.body;

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId
      }
    })

    if(!exBufferTime)
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND))

    const exTable = await TableModel.findOne({
      where: {
        tableId,
        branch: {
          branchId,
        },
      },
    });

    if (!exTable) {
      return next(errorHandler(NOT_FOUND, TABLE_NOT_FOUND));
    }

    if (!exTable.enabled) {
      return next(errorHandler(BAD_REQUEST, TABLE_DISABLED));
    }

    if (
      exTable.cleaning === TableCleaningStatus.DIRTY ||
      exTable.cleaning === TableCleaningStatus.NEEDSCLEANING
    ) {
      return next(errorHandler(BAD_REQUEST, TABLE_NEEDS_CLEANING));
    }

    if (numOfGuests < exTable.minSeats || numOfGuests > exTable.maxSeats) {
      return next(
        errorHandler(BAD_REQUEST, GUEST_COUNT_OUT_OF_RANGE_FOR_TABLE)
      );
    }

    let activeReservation = null;
    let reservedSeats = 0;
    let upcomingReservation = null;
    let reservationToComplete = null;
    let expiredReservations: TableReservation[] = [];
    
    const currentTime = new Date();

    const reservations = await TableReservation.find({
      where: {
        table: { tableId },
        status: In([ReservationStatus.CONFIRMED]),
      },
    });

    if (confirmationCode && reservations.length > 0) {
      const matchingReservation = reservations.find(r => r.confirmationCode === confirmationCode);
      
      if (matchingReservation) {
        if (numOfGuests !== matchingReservation.numberOfGuests) {
          return next(
            errorHandler(BAD_REQUEST, GUEST_COUNT_MISMATCH_WITH_RESERVATION)
          );
        }
        
        reservationToComplete = matchingReservation;
        reservedSeats = 0;
        
        const reservationTime = new Date(matchingReservation.reservationTime);
        const reservationEndTime = new Date(reservationTime.getTime() + (matchingReservation.durationMinutes * 60000));
        const bufferTimeMs = exBufferTime.time * 60000;
        
        if (
          (currentTime >= new Date(reservationTime.getTime() - bufferTimeMs) && 
           currentTime <= reservationTime) ||
          (currentTime >= reservationTime && currentTime <= reservationEndTime)
        ) {
          activeReservation = matchingReservation;
        } else if (currentTime < new Date(reservationTime.getTime() - bufferTimeMs)) {
          activeReservation = matchingReservation;
        }
      } else {
        return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
      }
    } else {
      if (exTable.status === TableStatus.RESERVED) {
        for (const reservation of reservations) {
          const reservationTime = new Date(reservation.reservationTime);
          const reservationEndTime = new Date(reservationTime.getTime() + (reservation.durationMinutes * 60000));
          const bufferTimeMs = exBufferTime.time * 60000;

          if (
            (currentTime >= new Date(reservationTime.getTime() - bufferTimeMs) && 
             currentTime <= reservationTime) ||
            (currentTime >= reservationTime && currentTime <= reservationEndTime)
          ) {
            activeReservation = reservation;
            break;
          }
          else if (currentTime > reservationEndTime) {
            expiredReservations.push(reservation);
          }
        }

        if (!activeReservation) {
          const futureReservations = reservations.filter(r => {
            const reservationTime = new Date(r.reservationTime);
            const bufferTimeMs = exBufferTime.time * 60000;
            return currentTime < new Date(reservationTime.getTime() - bufferTimeMs);
          });
          
          if (futureReservations.length > 0) {
            upcomingReservation = futureReservations.sort((a, b) => 
              new Date(a.reservationTime).getTime() - new Date(b.reservationTime).getTime()
            )[0];
            reservedSeats = upcomingReservation.numberOfGuests;
          } else if (expiredReservations.length > 0) {
            reservedSeats = 0;
          }
        } else if (activeReservation) {
          reservedSeats = activeReservation.numberOfGuests;
        }
      }
    }

    const totalAvailableSeats = exTable.maxSeats - exTable.bookedSeats - reservedSeats;

    switch (exTable.status) {
      case TableStatus.OCCUPIED:
        if (numOfGuests > totalAvailableSeats) {
          if (reservedSeats > 0) {
            return next(errorHandler(BAD_REQUEST, 
              `Insufficient seats. ${totalAvailableSeats} seats available (${reservedSeats} seats reserved for upcoming reservation)`
            ));
          } else {
            return next(errorHandler(BAD_REQUEST, INSUFFICIENT_AVAILABLE_SEATS));
          }
        }
        break;

      case TableStatus.RESERVED:
        if (activeReservation) {
          if (confirmationCode && confirmationCode === activeReservation.confirmationCode) {
            break;
          } else {
            if (numOfGuests > totalAvailableSeats) {
              return next(errorHandler(BAD_REQUEST, 
                `Insufficient seats. ${totalAvailableSeats} seats available (${reservedSeats} seats reserved)`
              ));
            }
          }
        } else if (upcomingReservation) {
          if (confirmationCode && confirmationCode === upcomingReservation.confirmationCode) {
            break;
          } else {
            if (numOfGuests > totalAvailableSeats) {
              return next(errorHandler(BAD_REQUEST, 
                `Insufficient seats. ${totalAvailableSeats} seats available (${reservedSeats} seats reserved for upcoming reservation)`
              ));
            }
          }
        } else if (expiredReservations.length > 0) {
          if (numOfGuests > totalAvailableSeats) {
            return next(errorHandler(BAD_REQUEST, INSUFFICIENT_AVAILABLE_SEATS));
          }
        } else {
          return next(errorHandler(BAD_REQUEST, "Table marked as reserved but no reservation found"));
        }
        break;

      case TableStatus.AVAILABLE:
        if (numOfGuests > totalAvailableSeats) {
          return next(errorHandler(BAD_REQUEST, INSUFFICIENT_AVAILABLE_SEATS));
        }
        break;

      default:
        return next(errorHandler(BAD_REQUEST, INVALID_TABLE_STATUS));
    }

    // Update table
    const newBookedSeats = exTable.bookedSeats + numOfGuests;
    const newStatus = determineNewTableStatus(
      newBookedSeats, 
      exTable.maxSeats, 
      activeReservation, 
      confirmationCode,
      expiredReservations.length > 0
    );

    await TableModel.update(
      { tableId },
      {
        bookedSeats: newBookedSeats,
        status: newStatus,
      }
    );

    if (reservationToComplete) {
      await TableReservation.update(
        { confirmationCode: reservationToComplete.confirmationCode },
        {
          status: ReservationStatus.COMPLETED,
          arrivedAt: new Date(),
        }
      );
    }

    if (expiredReservations.length > 0) {
      for (const expiredRes of expiredReservations) {
        await TableReservation.update(
          { confirmationCode: expiredRes.confirmationCode },
          {
            status: ReservationStatus.NO_SHOW,
          }
        );
      }
    }

    await qRunner.commitTransaction();

    const result = {
      tableStatus: newStatus,
      bookedSeats: newBookedSeats,
      availableSeats: exTable.maxSeats - newBookedSeats,
      reservedSeats: reservationToComplete ? 0 : reservedSeats,
      expiredReservationsMarkedAsNoShow: expiredReservations.length
    };

    ResponseHelper.success(res, OK, ORDER_TAKEN_SUCCESS, result);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const cancelOrderFromDineIn = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { TableModel, TableReservation, BufferTime} = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
      TableReservation: Repository<TableReservation>;
      BufferTime: Repository<BufferTime>
    };

    const branchId = req.branchId;
    const tableId = req.params.tableId;
    const { numOfGuests, confirmationCode } = req.body;

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId
      }
    })

    if(!exBufferTime)
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND))

    const exTable = await TableModel.findOne({
      where: {
        tableId,
        branch: {
          branchId,
        },
      },
    });

    if (!exTable) {
      return next(errorHandler(NOT_FOUND, TABLE_NOT_FOUND));
    }

    if (numOfGuests <= 0 || numOfGuests > exTable.bookedSeats) {
      return next(errorHandler(BAD_REQUEST, INVALID_NUM_GUESTS));
    }

    let completedReservation = null;
    let shouldRevertReservation = false;

    if (confirmationCode) {
      completedReservation = await TableReservation.findOne({
        where: {
          confirmationCode,
          table: { tableId },
          status: ReservationStatus.COMPLETED,
        },
      });

      if (!completedReservation) {
        return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
      }

      if (numOfGuests === completedReservation.numberOfGuests) {
        shouldRevertReservation = true;
      }
    }

    const newBookedSeats = exTable.bookedSeats - numOfGuests;

    let activeReservation = null;
    let expiredReservations: TableReservation[] = [];
    
    if (exTable.status === TableStatus.RESERVED || shouldRevertReservation) {
      const currentTime = new Date();

      const reservations = await TableReservation.find({
        where: {
          table: { tableId },
          status: In([ReservationStatus.CONFIRMED]),
        },
      });

      for (const reservation of reservations) {
        const reservationTime = new Date(reservation.reservationTime);
        const reservationEndTime = new Date(reservationTime.getTime() + (reservation.durationMinutes * 60000));
        const bufferTimeMs = exBufferTime.time * 60000;

        if (
          (currentTime >= new Date(reservationTime.getTime() - bufferTimeMs) && 
           currentTime <= reservationTime) ||
          (currentTime >= reservationTime && currentTime <= reservationEndTime)
        ) {
          activeReservation = reservation;
          break;
        }
        else if (currentTime > reservationEndTime) {
          expiredReservations.push(reservation);
        }
      }
    }

    const newStatus = determineNewTableStatusAfterCancellation(
      newBookedSeats, 
      exTable.maxSeats, 
      activeReservation,
      shouldRevertReservation,
      expiredReservations.length > 0
    );

    await TableModel.update(
      { tableId },
      {
        bookedSeats: newBookedSeats,
        status: newStatus,
      }
    );

    if (shouldRevertReservation && completedReservation) {
      const currentTime = new Date();
      const reservationTime = new Date(completedReservation.reservationTime);
      const reservationEndTime = new Date(reservationTime.getTime() + (completedReservation.durationMinutes * 60000));
      
      if (currentTime > reservationEndTime) {
        await TableReservation.update(
          { confirmationCode },
          {
            status: ReservationStatus.NO_SHOW,
          }
        );
      } else {
        await TableReservation.update(
          { confirmationCode },
          {
            status: ReservationStatus.CONFIRMED,
          }
        );
      }
    }

    if (expiredReservations.length > 0) {
      for (const expiredRes of expiredReservations) {
        await TableReservation.update(
          { confirmationCode: expiredRes.confirmationCode },
          {
            status: ReservationStatus.NO_SHOW,
          }
        );
      }
    }

    await qRunner.commitTransaction();

    let reservedSeats = 0;
    if (activeReservation && (!shouldRevertReservation || confirmationCode !== activeReservation.confirmationCode)) {
      reservedSeats = activeReservation.numberOfGuests;
    }

    const result = {
      tableStatus: newStatus,
      bookedSeats: newBookedSeats,
      availableSeats: exTable.maxSeats - newBookedSeats,
      reservedSeats,
      cancelledGuests: numOfGuests,
      expiredReservationsMarkedAsNoShow: expiredReservations.length
    };

    ResponseHelper.success(res, OK, ORDER_CANCEL_SUCCESS, result);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

// Table Combinations

export const takeOrderFromTableCombination = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { 
      TableModel, 
      TableReservation, 
      BufferTime,
      TableCombination: TableCombinationRepository
    } = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
      TableReservation: Repository<TableReservation>;
      BufferTime: Repository<BufferTime>;
      TableCombination: Repository<TableCombination>;
    };

    const branchId = req.branchId;
    const tableCombinationId = req.params.tableCombinationId;
    const { numOfGuests, confirmationCode } = req.body;

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId
      }
    });

    if (!exBufferTime) {
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));
    }

    const tableCombination = await TableCombinationRepository.findOne({
      where: {
        tableCombinationId,
        branch: { branchId }
      },
      relations: ['tables']
    });

    if (!tableCombination) {
      return next(errorHandler(NOT_FOUND, TABLE_COMBINATION_NOT_FOUND));
    }

    if (!tableCombination.enabled) {
      return next(errorHandler(BAD_REQUEST, TABLE_COMBINATION_DISABLED));
    }

    if (numOfGuests < tableCombination.minSeats || numOfGuests > tableCombination.maxSeats) {
      return next(errorHandler(BAD_REQUEST, GUEST_COUNT_OUT_OF_RANGE_FOR_COMBINATION));
    }

    const disabledTables = tableCombination.tables.filter(table => 
      !table.enabled || 
      table.cleaning === TableCleaningStatus.DIRTY ||
      table.cleaning === TableCleaningStatus.NEEDSCLEANING
    );

    if (disabledTables.length > 0) {
      return next(errorHandler(BAD_REQUEST, SOME_TABLES_IN_COMBINATION_DISABLED));
    }

    const currentTime = new Date();
    const tableIds = tableCombination.tables.map(table => table.tableId);

    const reservations = await TableReservation.find({
      where: {
        table: { tableId: In(tableIds) },
        status: In([ReservationStatus.CONFIRMED]),
        tableCombinationId
      },
      relations: ['table']
    });

    let activeReservation = null;
    let reservationToComplete = null;
    let expiredReservations: TableReservation[] = [];

    if (confirmationCode && reservations.length > 0) {
      const matchingReservations = reservations.filter(r => r.confirmationCode === confirmationCode);
      
      if (matchingReservations.length === 0) {
        return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
      }

      const totalReservationGuests = matchingReservations.reduce((sum, r) => sum + r.numberOfGuests, 0);
      if (numOfGuests !== totalReservationGuests) {
        return next(errorHandler(BAD_REQUEST, GUEST_COUNT_MISMATCH_WITH_RESERVATION));
      }

      reservationToComplete = matchingReservations[0];
      const reservationTime = new Date(reservationToComplete.reservationTime);
      const reservationEndTime = new Date(reservationTime.getTime() + (reservationToComplete.durationMinutes * 60000));
      const bufferTimeMs = exBufferTime.time * 60000;

      if (
        (currentTime >= new Date(reservationTime.getTime() - bufferTimeMs) && 
         currentTime <= reservationTime) ||
        (currentTime >= reservationTime && currentTime <= reservationEndTime)
      ) {
        activeReservation = reservationToComplete;
      } else if (currentTime < new Date(reservationTime.getTime() - bufferTimeMs)) {
        activeReservation = reservationToComplete;
      }
    } else {
      const uniqueConfirmationCodes = Array.from(new Set(reservations.map(r => r.confirmationCode)));
      
      for (const code of uniqueConfirmationCodes) {
        const codeReservations = reservations.filter(r => r.confirmationCode === code);
        const firstReservation = codeReservations[0];
        
        const reservationTime = new Date(firstReservation.reservationTime);
        const reservationEndTime = new Date(reservationTime.getTime() + (firstReservation.durationMinutes * 60000));
        const bufferTimeMs = exBufferTime.time * 60000;

        if (
          (currentTime >= new Date(reservationTime.getTime() - bufferTimeMs) && 
           currentTime <= reservationTime) ||
          (currentTime >= reservationTime && currentTime <= reservationEndTime)
        ) {
          activeReservation = firstReservation;
          break;
        } else if (currentTime > reservationEndTime) {
          expiredReservations.push(...codeReservations);
        }
      }
    }

    let totalAvailableSeats = 0;
    let totalReservedSeats = 0;

    for (const table of tableCombination.tables) {
      const currentTable = await TableModel.findOne({
        where: { tableId: table.tableId }
      });

      if (currentTable) {
        totalAvailableSeats += (currentTable.maxSeats - currentTable.bookedSeats);
        
        if (activeReservation && !confirmationCode) {
          const tableReservation = reservations.find(r => r.table.tableId === table.tableId);
          if (tableReservation) {
            totalReservedSeats += tableReservation.numberOfGuests;
          }
        }
      }
    }

    totalAvailableSeats -= totalReservedSeats;

    if (numOfGuests > totalAvailableSeats) {
      if (totalReservedSeats > 0) {
        return next(errorHandler(BAD_REQUEST, 
          `Insufficient seats. ${totalAvailableSeats} seats available (${totalReservedSeats} seats reserved for combination)`
        ));
      } else {
        return next(errorHandler(BAD_REQUEST, INSUFFICIENT_AVAILABLE_SEATS));
      }
    }

    const guestsPerTable = Math.ceil(numOfGuests / tableCombination.tables.length);
    const tableUpdates = [];

    for (const table of tableCombination.tables) {
      const currentTable = await TableModel.findOne({
        where: { tableId: table.tableId }
      });

      if (currentTable) {
        const assignedGuests = Math.min(guestsPerTable, numOfGuests - tableUpdates.reduce((sum, t) => sum + t.assignedGuests, 0));
        const newBookedSeats = currentTable.bookedSeats + assignedGuests;
        const newStatus = newBookedSeats >= currentTable.maxSeats ? TableStatus.OCCUPIED : TableStatus.OCCUPIED;

        await TableModel.update(
          { tableId: table.tableId },
          {
            bookedSeats: newBookedSeats,
            status: newStatus
          }
        );

        tableUpdates.push({
          tableId: table.tableId,
          tableName: table.name,
          assignedGuests,
          newBookedSeats,
          newStatus,
          availableSeats: currentTable.maxSeats - newBookedSeats
        });
      }
    }

    if (reservationToComplete) {
      await TableReservation.update(
        { confirmationCode: reservationToComplete.confirmationCode },
        {
          status: ReservationStatus.COMPLETED,
          arrivedAt: new Date(),
        }
      );
    }

    if (expiredReservations.length > 0) {
      const expiredCodes = Array.from(new Set(expiredReservations.map(r => r.confirmationCode)));
      for (const code of expiredCodes) {
        await TableReservation.update(
          { confirmationCode: code },
          {
            status: ReservationStatus.NO_SHOW,
          }
        );
      }
    }

    await qRunner.commitTransaction();

    const result = {
      tableCombinationId,
      totalGuests: numOfGuests,
      tables: tableUpdates,
      completedReservation: reservationToComplete ? reservationToComplete.confirmationCode : null,
      expiredReservationsMarkedAsNoShow: expiredReservations.length > 0 ? Array.from(new Set(expiredReservations.map(r => r.confirmationCode))).length : 0
    };

    ResponseHelper.success(res, OK, ORDER_TAKEN_SUCCESS, result);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const cancelOrderFromTableCombination = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const { 
      TableModel, 
      TableReservation, 
      BufferTime,
      TableCombination: TableCombinationRepository
    } = getRepositories(qRunner) as {
      TableModel: Repository<TableModel>;
      TableReservation: Repository<TableReservation>;
      BufferTime: Repository<BufferTime>;
      TableCombination: Repository<TableCombination>;
    };

    const branchId = req.branchId;
    const tableCombinationId = req.params.tableCombinationId;
    const { numOfGuests, confirmationCode } = req.body;

    const exBufferTime = await BufferTime.findOne({
      where: {
        bufferTimeId: branchId
      }
    });

    if (!exBufferTime) {
      return next(errorHandler(NOT_FOUND, BUFFERTIME_NOTFOUND));
    }

    const tableCombination = await TableCombinationRepository.findOne({
      where: {
        tableCombinationId,
        branch: { branchId }
      },
      relations: ['tables']
    });

    if (!tableCombination) {
      return next(errorHandler(NOT_FOUND, TABLE_COMBINATION_NOT_FOUND));
    }

    if (numOfGuests <= 0) {
      return next(errorHandler(BAD_REQUEST, INVALID_NUM_GUESTS));
    }

    const currentTime = new Date();
    const tableIds = tableCombination.tables.map(table => table.tableId);

    let totalBookedSeats = 0;
    for (const table of tableCombination.tables) {
      const currentTable = await TableModel.findOne({
        where: { tableId: table.tableId }
      });
      if (currentTable) {
        totalBookedSeats += currentTable.bookedSeats;
      }
    }

    if (numOfGuests > totalBookedSeats) {
      return next(errorHandler(BAD_REQUEST, INVALID_NUM_GUESTS));
    }

    let completedReservation = null;
    let shouldRevertReservation = false;

    if (confirmationCode) {
      const completedReservations = await TableReservation.find({
        where: {
          confirmationCode,
          table: { tableId: In(tableIds) },
          status: ReservationStatus.COMPLETED,
          tableCombinationId
        },
        relations: ['table']
      });

      if (completedReservations.length === 0) {
        return next(errorHandler(NOT_FOUND, RESERVATION_NOT_FOUND));
      }

      const totalCompletedGuests = completedReservations.reduce((sum, r) => sum + r.numberOfGuests, 0);
      if (numOfGuests === totalCompletedGuests) {
        shouldRevertReservation = true;
        completedReservation = completedReservations[0];
      }
    }

    const reservations = await TableReservation.find({
      where: {
        table: { tableId: In(tableIds) },
        status: In([ReservationStatus.CONFIRMED]),
        tableCombinationId
      },
      relations: ['table']
    });

    let activeReservation = null;
    let expiredReservations: TableReservation[] = [];

    const uniqueConfirmationCodes = Array.from(new Set(reservations.map(r => r.confirmationCode)));
    
    for (const code of uniqueConfirmationCodes) {
      const codeReservations = reservations.filter(r => r.confirmationCode === code);
      const firstReservation = codeReservations[0];
      
      const reservationTime = new Date(firstReservation.reservationTime);
      const reservationEndTime = new Date(reservationTime.getTime() + (firstReservation.durationMinutes * 60000));
      const bufferTimeMs = exBufferTime.time * 60000;

      if (
        (currentTime >= new Date(reservationTime.getTime() - bufferTimeMs) && 
         currentTime <= reservationTime) ||
        (currentTime >= reservationTime && currentTime <= reservationEndTime)
      ) {
        activeReservation = firstReservation;
        break;
      } else if (currentTime > reservationEndTime) {
        expiredReservations.push(...codeReservations);
      }
    }

    let remainingGuestsToCancel = numOfGuests;
    const tableUpdates = [];

    for (const table of tableCombination.tables) {
      if (remainingGuestsToCancel <= 0) break;

      const currentTable = await TableModel.findOne({
        where: { tableId: table.tableId }
      });

      if (currentTable && currentTable.bookedSeats > 0) {
        const guestsToCancel = Math.min(remainingGuestsToCancel, currentTable.bookedSeats);
        const newBookedSeats = currentTable.bookedSeats - guestsToCancel;
        
        let newStatus: TableStatus;
        if (newBookedSeats === 0) {
          newStatus = activeReservation && !shouldRevertReservation ? TableStatus.RESERVED : TableStatus.AVAILABLE;
        } else {
          newStatus = TableStatus.OCCUPIED;
        }

        await TableModel.update(
          { tableId: table.tableId },
          {
            bookedSeats: newBookedSeats,
            status: newStatus
          }
        );

        tableUpdates.push({
          tableId: table.tableId,
          tableName: table.name,
          cancelledGuests: guestsToCancel,
          newBookedSeats,
          newStatus,
          availableSeats: currentTable.maxSeats - newBookedSeats
        });

        remainingGuestsToCancel -= guestsToCancel;
      }
    }

    if (shouldRevertReservation && completedReservation) {
      const reservationTime = new Date(completedReservation.reservationTime);
      const reservationEndTime = new Date(reservationTime.getTime() + (completedReservation.durationMinutes * 60000));
      
      if (currentTime > reservationEndTime) {
        await TableReservation.update(
          { confirmationCode },
          {
            status: ReservationStatus.NO_SHOW,
          }
        );
      } else {
        await TableReservation.update(
          { confirmationCode },
          {
            status: ReservationStatus.CONFIRMED,
          }
        );
      }
    }

    if (expiredReservations.length > 0) {
      const expiredCodes = Array.from(new Set(expiredReservations.map(r => r.confirmationCode)));
      for (const code of expiredCodes) {
        await TableReservation.update(
          { confirmationCode: code },
          {
            status: ReservationStatus.NO_SHOW,
          }
        );
      }
    }

    await qRunner.commitTransaction();

    const result = {
      tableCombinationId,
      totalCancelledGuests: numOfGuests - remainingGuestsToCancel,
      tables: tableUpdates,
      revertedReservation: shouldRevertReservation ? confirmationCode : null,
      expiredReservationsMarkedAsNoShow: expiredReservations.length > 0 ? Array.from(new Set(expiredReservations.map(r => r.confirmationCode))).length : 0
    };

    ResponseHelper.success(res, OK, ORDER_CANCEL_SUCCESS, result);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};