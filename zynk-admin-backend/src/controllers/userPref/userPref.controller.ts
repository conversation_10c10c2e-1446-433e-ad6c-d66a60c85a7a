import { NextFunction, Request, Response } from "express";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import { CONFLICT, NOT_FOUND, OK } from "../../constants/STATUS_CODES";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { Repository } from "typeorm";
import { PrefBranch, PrefUserType } from "../../models/common/prefbranch.model";
import { errorHandler } from "../../utils/errorHandler";
import {
  CANNOT_ASSIGN_UNRESERVED_BRANCH,
  PREFBRANCH_NOT_FOUND,
} from "../../constants/tenant/userPref/err";
import {
  PREFBRANCH_FOUND,
  PREFBRANCH_UPDATED,
} from "../../constants/tenant/userPref/msg";
import { Staff } from "../../models/staff/staff.model";
import { STAFF_NOT_FOUND } from "../../constants/tenant/staff/err";
import { UserType } from "../../types/pBAC";
import { JWTPayload } from "../../../types/jwt";
import { setCookie } from "../../helpers/system/CookieHelper.helper";
import { Branch } from "../../models/company/branch.model";
import { BRANCH_NOT_FOUND } from "../../constants/tenant/company/err";

export const getPrefBranch = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();

  try {
    let userType;

    if (req.USER_TYPE === UserType.TENANT_ADMIN) {
      userType = PrefUserType.TENANT;
    } else if(req.USER_TYPE === UserType.CUSTOMER){
      userType = PrefUserType.CUSTOMER;
    }
    else {
      userType = PrefUserType.STAFF;
    }
    const userId = req.userId;

    const { PrefBranch } = getRepositories(qRunner) as {
      PrefBranch: Repository<PrefBranch>;
    };

    const exPrefBranch = await PrefBranch.findOne({
      where: {
        userId,
        userType,
      },
    });

    if (!exPrefBranch)
      return next(errorHandler(NOT_FOUND, PREFBRANCH_NOT_FOUND));

    ResponseHelper.success(res, OK, PREFBRANCH_FOUND, exPrefBranch);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchPrefBranch = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    let userType;

    if (req.USER_TYPE === UserType.TENANT_ADMIN) {
      userType = PrefUserType.TENANT;
    } else if(req.USER_TYPE === UserType.CUSTOMER){
      userType = PrefUserType.CUSTOMER;
    }
    else {
      userType = PrefUserType.STAFF;
    }
    const userId = req.userId;

    const { PrefBranch, Staff, Branch} = getRepositories(qRunner) as {
      PrefBranch: Repository<PrefBranch>;
      Staff: Repository<Staff>;
      Branch: Repository<Branch>
    };

    const exPrefBranch = await PrefBranch.findOne({
      where: {
        userId,
        userType,
      },
    });

    if (!exPrefBranch)
      return next(errorHandler(NOT_FOUND, PREFBRANCH_NOT_FOUND));

    let payload: JWTPayload = {
      userId,
      branchId: exPrefBranch.prefBranchId!,
      domainName: (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
      isTenant: true,
      inSuper: true,
    };

    const prefBranch = req.body.prefBranch;

    const exBranch = await Branch.findOneBy({
      branchId: prefBranch
    })

    if(!exBranch)
      return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND))

    if (userType === PrefUserType.STAFF) {
      const exStaff = await Staff.findOne({
        where: {
          staffId: userId,
        },
        relations: ["reservedBranches"],
      });

      if (!exStaff) return next(errorHandler(NOT_FOUND, STAFF_NOT_FOUND));

      if (
        !exStaff.reservedBranches.some(
          (branch) => branch.branchId === prefBranch
        )
      ) {
        return next(errorHandler(CONFLICT, CANNOT_ASSIGN_UNRESERVED_BRANCH));
      }

      payload.inSuper = false
    }
    else if(userType === PrefUserType.CUSTOMER){
      payload.inSuper = false
      payload.isCustomer = true
    }

    exPrefBranch.prefBranchId = prefBranch;
    payload.branchId = prefBranch
    await PrefBranch.save(exPrefBranch);

    setCookie(res, payload);

    await qRunner.commitTransaction();
    ResponseHelper.success(res, OK, PREFBRANCH_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};
