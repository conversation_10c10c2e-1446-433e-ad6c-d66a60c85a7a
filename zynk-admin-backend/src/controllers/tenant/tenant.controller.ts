import {NextFunction, Request, Response} from "express";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Repository} from "typeorm";
import {Tenant} from "../../models/admin/Tenant/tenant.model";
import {TenantAuth} from "../../models/admin/Tenant/tenantAuth.model";
import {errorHandler} from "../../utils/errorHandler";
import {NOT_FOUND, OK, UNAUTHORIZED} from "../../constants/STATUS_CODES";
import {HOSTNAME_NOT_PROVIDED, USER_NOT_EXIST,} from "../../constants/middleware/err";
import argon2 from "argon2";
import {ACCESS_DENIED_INVALID_PASSWORD} from "../../constants/common/err";
import {TenantSession} from "../../models/admin/Tenant/tenantsession.model";
import {AESHelper} from "../../helpers/system/AESHelper";
import {IP_REVOKED} from "../../constants/admin/err";
import {JWTPayload} from "../../../types/jwt";
import {clearCookie, setCookie} from "../../helpers/system/CookieHelper.helper";
import {genNewRefreshToken} from "../../helpers/system/superadmin.helper";
import {isTokenVerified} from "../../helpers/system/token.helper";
import {ResponseHelper} from "../../helpers/system/ResponseHelper.helper";
import {LoginRateLimitService} from "../../services/loginAttempt.service";
import {IP_NOT_FOUND} from "../../constants/security/err";
import {IP_BLOCKED} from "../../constants/security/msg";
import {Staff} from "../../models/staff/staff.model";
import {StaffAuth} from "../../models/staff/staffAuth.model";
import {StaffSession} from "../../models/staff/staffSession.model";
import {FormatType} from "../../types/pBAC/formatterTypes";
import {getUserModelAssociation, ModelType} from "../../types/pBAC";
import { PrefBranch } from "../../models/common/prefbranch.model";
import { PREFBRANCH_NOT_FOUND } from "../../constants/tenant/userPref/err";

export const loginTenant = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const rootRunner = req.rootQueryRunner;
    const tenantRunner = req.queryRunner;

    await rootRunner.connect();
    await rootRunner.startTransaction();

    if (req.tenantName) {
        await tenantRunner.connect();
        await tenantRunner.startTransaction();
    }

    try {
        const ip = req.clientIP;
        const {emailAddress, password} = req.body;

        if (!ip) return next(errorHandler(NOT_FOUND, IP_NOT_FOUND));

        if (!req.tenantName)
            return next(errorHandler(NOT_FOUND, HOSTNAME_NOT_PROVIDED));

        const rateLimitCheck = await LoginRateLimitService.isIpRateLimited(
            tenantRunner,
            ip
        );

        if (rateLimitCheck.blocked) {
            return next(
                errorHandler(
                    UNAUTHORIZED,
                    rateLimitCheck.message ? rateLimitCheck.message : IP_BLOCKED
                )
            );
        }

        let responseData = null;

        const {Tenant, TenantAuth, TenantSession} = getRepositories(
            rootRunner
        ) as {
            Tenant: Repository<Tenant>;
            TenantAuth: Repository<TenantAuth>;
            TenantSession: Repository<TenantSession>;
        };

        const {Staff, StaffAuth, StaffSession, PrefBranch} = getRepositories(
            tenantRunner
        ) as {
            Staff: Repository<Staff>;
            StaffAuth: Repository<StaffAuth>;
            StaffSession: Repository<StaffSession>;
            PrefBranch: Repository<PrefBranch>
        };

        const exTenant = await Tenant.findOne({
            where: {
                subDomain: req.tenantName,
                emailAddress,
            },
            relations: ["tenantAuth", "tenantSessions"],
        });

        // const exTenant = await Tenant.findOne({
        //     where: {
        //         subDomain: req.tenantName,
        //         emailAddress,
        //     },
        //     relations: ["tenantAuth", "tenantSessions", ...getUserModelAssociation(ModelType.TENANT)],
        // });

        const exStaff = await Staff.findOne({
            where: {
                emailAddress,
            },
            relations: ["staffAuth", "staffSessions"],
        });

        // const exStaff = await Staff.findOne({
        //     where: {
        //         emailAddress,
        //     },
        //     relations: ["staffAuth", "staffSessions", ...getUserModelAssociation(ModelType.STAFF)],
        // });

        if (!exTenant && !exStaff) {
            await LoginRateLimitService.checkAndLogAttempt(
                tenantRunner,
                ip,
                emailAddress,
                "USER",
                req.userAgent,
                false,
                undefined,
                "USER_NOT_FOUND"
            );
            return next(errorHandler(UNAUTHORIZED, USER_NOT_EXIST));
        }

        if (exTenant) {
            const verified = await argon2.verify(
                exTenant.tenantAuth.password,
                password
            );

            if (!verified) {
                await LoginRateLimitService.checkAndLogAttempt(
                    tenantRunner,
                    ip,
                    emailAddress,
                    "TENANT",
                    req.userAgent,
                    false,
                    undefined,
                    ACCESS_DENIED_INVALID_PASSWORD
                );
                return next(errorHandler(UNAUTHORIZED, ACCESS_DENIED_INVALID_PASSWORD));
            }

            const allSessions = exTenant.tenantSessions;
            const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

            let exSession: TenantSession | null = null;

            for (const session of allSessions) {
                const decryptedIp = await AESHelper.decrypt(
                    session.ipAddress,
                    secret_key
                );
                if (decryptedIp === ip) {
                    exSession = session;
                    break;
                }
            }

            if (!exSession) {
                const newSession = TenantSession.create({
                    tenant: exTenant,
                    ipAddress: await AESHelper.encrypt(ip, secret_key),
                    userAgent: req.userAgent,
                    lastLogin: new Date(),
                });

                await TenantSession.save(newSession);
            } else {
                if (exSession.isRevoked) {
                    await LoginRateLimitService.checkAndLogAttempt(
                        tenantRunner,
                        ip,
                        emailAddress,
                        "TENANT",
                        req.userAgent,
                        false,
                        undefined,
                        IP_REVOKED
                    );
                    return next(errorHandler(UNAUTHORIZED, IP_REVOKED));
                }

                if (exSession.userAgent && exSession.userAgent !== req.userAgent) {
                    exSession.userAgent = req.userAgent;
                }

                exSession.lastLogin = new Date();
                await TenantSession.save(exSession);
            }

            await LoginRateLimitService.checkAndLogAttempt(
                tenantRunner,
                ip,
                emailAddress,
                "TENANT",
                req.userAgent,
                true,
                exTenant.tenantId
            );

            const prefBranch = await PrefBranch.findOne({
                where: {
                    userId: exTenant.tenantId,
                    userType: 'tenant'
                }
            })

            if(!prefBranch)
                return next(errorHandler(NOT_FOUND, PREFBRANCH_NOT_FOUND))

            const payload: JWTPayload = {
                userId: exTenant.tenantId,
                branchId: prefBranch.prefBranchId!,
                domainName: (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
                isTenant: true,
                inSuper: true,
            };

            setCookie(res, payload);

            if (!exTenant.tenantAuth.refreshToken) {
                const newRefToken = await genNewRefreshToken(payload, secret_key);
                exTenant.tenantAuth.refreshToken = newRefToken;
                await TenantAuth.save(exTenant.tenantAuth);
            } else {
                const decrypted_refreshToken = await AESHelper.decrypt(
                    exTenant.tenantAuth.refreshToken,
                    secret_key
                );
                const tokenVerif = isTokenVerified(decrypted_refreshToken, true);

                if (!tokenVerif) {
                    const newRefToken = await genNewRefreshToken(payload, secret_key);
                    exTenant.tenantAuth.refreshToken = newRefToken;
                    await TenantAuth.save(exTenant.tenantAuth);
                }
            }

            responseData = {
                userName: exTenant.name,
                userType: "tenant",
                // permissionAction: (
                //     await exTenant.getPermissionAction(FormatType.STRING_ARR_STRING)
                // ).formattedPermissions,
            };
        } else if (exStaff) {
            const verified = await argon2.verify(
                exStaff.staffAuth.password,
                password
            );

            if (!verified) {
                await LoginRateLimitService.checkAndLogAttempt(
                    tenantRunner,
                    ip,
                    emailAddress,
                    "STAFF",
                    req.userAgent,
                    false,
                    undefined,
                    ACCESS_DENIED_INVALID_PASSWORD
                );
                return next(errorHandler(UNAUTHORIZED, ACCESS_DENIED_INVALID_PASSWORD));
            }

            const allSessions = exStaff.staffSessions;
            const secret_key = Buffer.from(process.env.AES_KEY!, "base64");

            let exSession: StaffSession | null = null;

            for (const session of allSessions) {
                const decryptedIp = await AESHelper.decrypt(
                    session.ipAddress,
                    secret_key
                );
                if (decryptedIp === ip) {
                    exSession = session;
                    break;
                }
            }

            if (!exSession) {
                const newSession = StaffSession.create({
                    staff: exStaff,
                    ipAddress: await AESHelper.encrypt(ip, secret_key),
                    userAgent: req.userAgent,
                    lastLogin: new Date(),
                });

                await StaffSession.save(newSession);
            } else {
                if (exSession.isRevoked) {
                    await LoginRateLimitService.checkAndLogAttempt(
                        tenantRunner,
                        ip,
                        emailAddress,
                        "STAFF",
                        req.userAgent,
                        false,
                        undefined,
                        IP_REVOKED
                    );
                    return next(errorHandler(UNAUTHORIZED, IP_REVOKED));
                }

                if (exSession.userAgent && exSession.userAgent !== req.userAgent) {
                    exSession.userAgent = req.userAgent;
                }

                exSession.lastLogin = new Date();
                await StaffSession.save(exSession);
            }

            await LoginRateLimitService.checkAndLogAttempt(
                tenantRunner,
                ip,
                emailAddress,
                "STAFF",
                req.userAgent,
                true,
                exStaff.staffId
            );

            const prefBranch = await PrefBranch.findOne({
                where: {
                    userId: exStaff.staffId,
                    userType: 'staff'
                }
            })

            if(!prefBranch)
                return next(errorHandler(NOT_FOUND, PREFBRANCH_NOT_FOUND))

            const payload: JWTPayload = {
                userId: exStaff.staffId,
                branchId: prefBranch.prefBranchId!,
                domainName: (process.env.SU_DOMAIN_NAME as string) || "easydine.com",
                isTenant: true,
                inSuper: false,
            };

            setCookie(res, payload);

            if (!exStaff.staffAuth.refreshToken) {
                const newRefToken = await genNewRefreshToken(payload, secret_key);
                exStaff.staffAuth.refreshToken = newRefToken;
                await StaffAuth.save(exStaff.staffAuth);
            } else {
                const decrypted_refreshToken = await AESHelper.decrypt(
                    exStaff.staffAuth.refreshToken,
                    secret_key
                );
                const tokenVerif = isTokenVerified(decrypted_refreshToken, true);

                if (!tokenVerif) {
                    const newRefToken = await genNewRefreshToken(payload, secret_key);
                    exStaff.staffAuth.refreshToken = newRefToken;
                    await StaffAuth.save(exStaff.staffAuth);
                }
            }

            responseData = {
                userName: exStaff.firstName + " " + exStaff.lastName,
                userType: "staff",
                // permissionAction: (
                //     await exStaff.getPermissionAction(FormatType.STRING_ARR_STRING)
                // ).formattedPermissions,
            };
        }

        await rootRunner.commitTransaction();
        if (req.tenantName) {
            await tenantRunner.commitTransaction();
        }

        ResponseHelper.success(res, OK, "Logged in successfully!", responseData);
    } catch (error) {
        await rootRunner.rollbackTransaction();
        if (req.tenantName) {
            await tenantRunner.rollbackTransaction();
        }
        next(error);
    } finally {
        await rootRunner.release();
        if (req.tenantName) {
            await tenantRunner.release();
        }
    }
};

export const logoutTenant = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    try {
        clearCookie(res);

        ResponseHelper.success(res, OK, "Logged out successfully!");
    } catch (error) {
        next(error);
    }
};
