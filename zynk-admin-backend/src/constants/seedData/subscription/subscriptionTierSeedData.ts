import {Duration} from "../../../types/subscription";

export const subscriptionTierSeedData = [
    {// REQUIRED FOR CURRENT DEVELOPMENT IN EASYDINE POS,
        name: "Starter",
        description: "Basic starter plan but with days counted",
        price: 100,
        subscriptionTierType: Duration.DAILY,
        validityDuration: 60,
        features: [
            "Inventory Access",
            "Staff Management",
        ],
    },
    {
        // ENTERPRISE IS MONTHLY HOWEVER, DURING TENANT CREATION VALIDITY DURATION IS TAKEN FROM DAYS
        // THEREFORE, ENTERPRISE CANNOT BE USED AS DEFAULT UNTIL MONTHLY LOGIC IS ALSO WRITTEN IN TENANT CREATION IN `tenantManage.controller.ts`
        name: "Enterprise",
        description: "The ultimate plan for large organizations, offering unlimited access to all premium features, advanced analytics, custom integrations, dedicated account management, and priority support to drive scalability and efficiency.",
        price: 100,
        subscriptionTierType: Duration.MONTHLY,
        validityDuration: 1,
        features: [
            "Inventory Access",
            "Staff Management",
        ],
    },
    {
        name: "Business",
        description: "Designed for growing companies, this plan provides access to premium features, robust analytics, and enhanced support to streamline operations and boost productivity.",
        price: 50,
        subscriptionTierType: Duration.MONTHLY,
        validityDuration: 1,
        features: [
            "Inventory Access",
            "Staff Management",
        ],
    },
    {
        name: "Professional",
        description: "Ideal for small to mid-sized teams, this plan offers essential tools, basic analytics, and reliable support to enhance team efficiency and collaboration.",
        price: 20,
        subscriptionTierType: Duration.MONTHLY,
        validityDuration: 1,
        features: [
            "Inventory Access",
            "Staff Management",
        ],
    },
];