import {DeepPartial} from "typeorm";
import {Tax} from "../../../models/subscription/tax/tax.model";

export const taxSeedData = [
    // India - GST
    {
        name: "India GST for SaaS",
        description: "18% GST for Software as a Service subscriptions in India",
        taxGovId: "27AABCU9603R1ZZ", // Example GSTIN
        isActive: true,
        isCentral: true,
        country: "IN",
        state: "",
        taxPercentage: 18.0,
        upperLimit: 0,
        lowerLimit: 0,
    },
    // United Kingdom - VAT
    {
        name: "UK VAT for SaaS",
        description: "20% VAT for Software as a Service subscriptions in the UK",
        taxGovId: "GB123456789", // Example VAT Registration Number
        isActive: true,
        isCentral: true,
        country: "UK",
        state: "",
        taxPercentage: 20.0,
        upperLimit: 0,
        lowerLimit: 0,
    },
    // United States - Sales Tax (New York)
    {
        name: "NY Sales Tax for SaaS",
        description: "8.875% Sales Tax for SaaS subscriptions in New York, USA",
        taxGovId: "***********", // Example state tax ID
        isActive: true,
        isCentral: false,
        country: "US",
        state: "New York",
        taxPercentage: 8.875,
        upperLimit: 0,
        lowerLimit: 0,
    },
    // United States - Sales Tax (California)
    {
        name: "CA Sales Tax for SaaS",
        description: "7.25% Sales Tax for SaaS subscriptions in California, USA",
        taxGovId: "***********", // Example state tax ID
        isActive: true,
        isCentral: false,
        country: "US",
        state: "California",
        taxPercentage: 7.25,
        upperLimit: 0,
        lowerLimit: 0,
    },
    // Canada - HST (Ontario)
    {
        name: "Ontario HST for SaaS",
        description: "13% HST for SaaS subscriptions in Ontario, Canada",
        taxGovId: "123456789RT0001", // Example GST/HST number
        isActive: true,
        isCentral: false,
        country: "CA",
        state: "Ontario",
        taxPercentage: 13.0,
        upperLimit: 0,
        lowerLimit: 0,
    },
    // Canada - GST (British Columbia)
    {
        name: "BC GST for SaaS",
        description: "5% GST for SaaS subscriptions in British Columbia, Canada",
        taxGovId: "987654321RT0001", // Example GST number
        isActive: true,
        isCentral: true,
        country: "CA",
        state: "",
        taxPercentage: 5.0,
        upperLimit: 0,
        lowerLimit: 0,
    },
    // Australia - GST
    {
        name: "Australia GST for SaaS",
        description: "10% GST for Software as a Service subscriptions in Australia",
        taxGovId: "***********", // Example ABN
        isActive: true,
        isCentral: true,
        country: "AU",
        state: "",
        taxPercentage: 10.0,
        upperLimit: 0,
        lowerLimit: 0,
    },
    // Germany - VAT
    {
        name: "Germany VAT for SaaS",
        description: "19% VAT for Software as a Service subscriptions in Germany",
        taxGovId: "DE123456789", // Example VAT ID
        isActive: true,
        isCentral: true,
        country: "DE",
        state: "",
        taxPercentage: 19.0,
        upperLimit: 0,
        lowerLimit: 0,
    },
    // Singapore - GST
    {
        name: "Singapore GST for SaaS",
        description: "9% GST for Software as a Service subscriptions in Singapore",
        taxGovId: "M2-1234567-X", // Example GST registration number
        isActive: true,
        isCentral: true,
        country: "SG",
        state: "",
        taxPercentage: 9.0,
        upperLimit: 0,
        lowerLimit: 0,
    },
];