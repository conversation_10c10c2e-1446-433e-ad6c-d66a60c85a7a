import {AccessType} from "../../../types/pBAC";

export const moduleSeedData = [
    {
        moduleName: "Settings",
        moduleDescription:
            "Configure core system settings for tenant-specific operations, including business details, notifications, and service configurations.",
        accessType: AccessType.ALL,
        isActive: true,
        subModules: [
            {
                moduleName: "General",
                moduleDescription:
                    "Manage general business configurations, such as company details, notifications, and operational preferences.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Company Information",
                        moduleDescription:
                            "Handle core company details, including legal and contact information.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Company Info",
                                moduleDescription:
                                    "Maintain company profile details, such as name, address, and contact information.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Bank Details",
                                moduleDescription:
                                    "Configure banking information for payment processing and financial transactions.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Account Representatives",
                                moduleDescription:
                                    "Manage contact details for account representatives or key personnel.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                        ],
                    },
                    {
                        moduleName: "Business Information",
                        moduleDescription:
                            "Define business-specific settings, such as operating hours and service types.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Notification",
                        moduleDescription:
                            "Set up notification preferences for system alerts and communications.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Dashboard Notifications",
                                moduleDescription:
                                    "Configure alerts and updates displayed on the dashboard.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Email Notifications",
                                moduleDescription:
                                    "Manage settings for email-based alerts and communications.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                        ],
                    },
                    {
                        moduleName: "Currency Unit",
                        moduleDescription:
                            "Define the currency used for transactions and pricing.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Order Types",
                        moduleDescription:
                            "Configure available  types, such as dine-in, takeout, or delivery.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Parking Services",
                        moduleDescription:
                            "Set up parking-related services, such as valet or customer parking options.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                ],
            },
            {
                moduleName: "Waiter",
                moduleDescription:
                    "Manage waiter-specific configurations for efficient service delivery.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Preparation Time",
                        moduleDescription:
                            "Set expected preparation times for s to optimize waiter workflows.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Wastage",
                        moduleDescription:
                            "Track and manage food or resource wastage reported by waitstaff.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                ],
            },
            {
                moduleName: "Kitchen",
                moduleDescription:
                    "Configure kitchen operations, including instructions and allergen management.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Special Instructions",
                        moduleDescription:
                            "Define special instructions for food preparation to meet customer needs.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Manage Allergies",
                        moduleDescription:
                            "Maintain a list of allergens and their handling protocols.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Allergy Colors",
                        moduleDescription:
                            "Assign color codes to allergens for quick identification in the kitchen.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Manage Section Icons",
                        moduleDescription:
                            "Maintain a list of section icons and their configurations.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Manage Dish Addons",
                        moduleDescription:
                            "Maintain a list of dish addons and their configurations.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Manage Dish Extras",
                        moduleDescription:
                            "Maintain a list of dish extras and their configurations.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Manage Ingredients",
                        moduleDescription:
                            "Maintain a list of ingredients and their configurations.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                ],
            },
            {
                moduleName: "Online Order",
                moduleDescription:
                    "Configure settings for online ing processes, including scheduling and delivery options.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Receive Orders Online Order",
                        moduleDescription:
                            "Manage the receipt and processing of online customer s.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Order Scheduling",
                        moduleDescription:
                            "Set up scheduling rules for online s to manage delivery or pickup times.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Pickup Online Order",
                        moduleDescription:
                            "Configure pickup options for online s, including curbside and in-store.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Pickup",
                                moduleDescription:
                                    "Manage in-store pickup settings for online s.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Curbside Pickup",
                                moduleDescription:
                                    "Configure curbside pickup options for customer convenience.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                        ],
                    },
                    {
                        moduleName: "Delivery",
                        moduleDescription:
                            "Set up delivery options and logistics for online s.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Contactless Dine-In",
                        moduleDescription:
                            "Configure contactless dine-in options, such as QR code-based ing.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Notification Online Order",
                        moduleDescription:
                            "Manage notifications related to online s, such as confirmations and updates.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Receive Orders",
                                moduleDescription:
                                    "Configure notifications for received online s.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Unhandled Orders",
                                moduleDescription:
                                    "Set up alerts for unprocessed or pending online s.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Order Confirmation",
                                moduleDescription:
                                    "Manage confirmation notifications sent to customers for online s.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                        ],
                    },
                    {
                        moduleName: "Advanced",
                        moduleDescription:
                            "Configure advanced settings for online s, such as tips and pacing.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Tips",
                                moduleDescription: "Set up tipping options for online s.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Order Pacing",
                                moduleDescription:
                                    "Control the rate at which online s are accepted to manage kitchen capacity.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Service Fees",
                                moduleDescription:
                                    "Define service fees applied to online s.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Policies Advanced",
                                moduleDescription:
                                    "Configure advanced policies for online ing, such as cancellation rules.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                        ],
                    },
                ],
            },
            {
                moduleName: "Table Reservation Settings",
                moduleDescription:
                    "Manage settings for table reservations, including availability and forms.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Enable Online Reservation",
                        moduleDescription:
                            "Activate or deactivate online table reservation functionality.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Online Reservation",
                        moduleDescription:
                            "Configure online reservation settings, such as availability and party size.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Online Availability",
                                moduleDescription:
                                    "Set available times and dates for online reservations.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Party Size",
                                moduleDescription:
                                    "Define maximum and minimum party sizes for reservations.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Turnover Time",
                                moduleDescription:
                                    "Configure expected table turnover times for efficient scheduling.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Time Slot And Pacing",
                                moduleDescription:
                                    "Manage time slots and pacing for reservations to optimize table usage.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                        ],
                    },
                    {
                        moduleName: "Table Management",
                        moduleDescription:
                            "Configure table layouts and assignments for reservations.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Reservation Form",
                        moduleDescription:
                            "Customize the reservation form for customer input.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Reservation Requirements",
                                moduleDescription:
                                    "Define required fields for the reservation form, such as contact details.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Email Marketing Checkbox",
                                moduleDescription:
                                    "Enable or configure an email marketing opt-in checkbox on the reservation form.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Custom Fields",
                                moduleDescription:
                                    "Add custom fields to the reservation form for specific data collection.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Policies Reservation Form",
                                moduleDescription:
                                    "Set policies for reservations, such as cancellation or no-show rules.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                        ],
                    },
                    {
                        moduleName: "Prepayment",
                        moduleDescription:
                            "Configure prepayment requirements for table reservations.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Table Reservation Notification",
                        moduleDescription:
                            "Manage notifications for table reservations, such as confirmations and reminders.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Notification Preferences",
                                moduleDescription:
                                    "Set preferences for reservation-related notifications.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Outgoing Notifications",
                                moduleDescription:
                                    "Configure outgoing notifications for reservations, such as customer reminders.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                                subModules: [
                                    {
                                        moduleName: "Table Reservation",
                                        moduleDescription:
                                            "Manage notifications specific to table reservation confirmations.",
                                        accessType: AccessType.TENANT_ONLY,
                                        isActive: true,
                                    },
                                    {
                                        moduleName: "Cashier Merchant",
                                        moduleDescription:
                                            "Configure notifications for payment-related reservation activities.",
                                        accessType: AccessType.TENANT_ONLY,
                                        isActive: true,
                                    },
                                    {
                                        moduleName: "Forms",
                                        moduleDescription:
                                            "Manage notifications related to reservation form submissions.",
                                        accessType: AccessType.TENANT_ONLY,
                                        isActive: true,
                                    },
                                    {
                                        moduleName: "Checkout And Orders",
                                        moduleDescription:
                                            "Configure notifications for reservation-related checkouts and s.",
                                        accessType: AccessType.TENANT_ONLY,
                                        isActive: true,
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
            {
                moduleName: "Staff Settings",
                moduleDescription:
                    "Configure staff-related settings, including discounts and roles.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Staff Discount",
                        moduleDescription: "Set up discount policies for staff members.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Daily Due Diligence",
                        moduleDescription:
                            "Manage daily and weekly checks for staff compliance and operations.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Daily CheckSheet",
                                moduleDescription:
                                    "Configure daily checklists for staff tasks and compliance.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                            {
                                moduleName: "Weekly CheckSheet",
                                moduleDescription:
                                    "Set up weekly checklists for staff tasks and operational reviews.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                        ],
                    },
                    {
                        moduleName: "Manage Roles",
                        moduleDescription: "Define and assign roles for staff members.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Roles And Permissions",
                        moduleDescription:
                            "Configure permissions associated with staff roles.",
                        accessType: AccessType.ALL,
                        isActive: true,
                    },
                    {
                        moduleName: "ID Preference",
                        moduleDescription:
                            "Set preferences for staff identification methods, such as ID cards or badges.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                ],
            },
            {
                moduleName: "Checkout",
                moduleDescription:
                    "Configure checkout processes, including payment and policy settings.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Checkout Configuration",
                        moduleDescription:
                            "Set up general checkout settings, such as payment methods and flow.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Policies",
                        moduleDescription:
                            "Define checkout policies, such as refund or return rules.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Manage Payment",
                        moduleDescription:
                            "Configure payment processing options for checkouts.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                        subModules: [
                            {
                                moduleName: "Accept Payments",
                                moduleDescription:
                                    "Enable and configure accepted payment methods.",
                                accessType: AccessType.TENANT_ONLY,
                                isActive: true,
                            },
                        ],
                    },
                ],
            },
            {
                moduleName: "Settings Inventory",
                moduleDescription:
                    "Manage inventory-related settings, such as units and equipment.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Inventory Unit",
                        moduleDescription:
                            "Define units of measurement for inventory items.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Equipment Unit",
                        moduleDescription:
                            "Configure units for equipment inventory tracking.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                ],
            },
            {
                moduleName: "Tax",
                moduleDescription: "Configure tax rates and rules for transactions.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
            },
            {
                moduleName: "Service Charges",
                moduleDescription:
                    "Set up service charge configurations for s and transactions.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Set Service Charges",
                        moduleDescription:
                            "Define specific service charge amounts or percentages.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Fee Configuration",
                        moduleDescription:
                            "Configure additional fees applied to services or transactions.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                ],
            },
            {
                moduleName: "Promotions Settings",
                moduleDescription:
                    "Configure settings for promotional campaigns and discounts.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
                subModules: [
                    {
                        moduleName: "Coupon Type",
                        moduleDescription:
                            "Define types of coupons available for promotional campaigns.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                    {
                        moduleName: "Voucher Type",
                        moduleDescription:
                            "Configure types of vouchers offered to customers.",
                        accessType: AccessType.TENANT_ONLY,
                        isActive: true,
                    },
                ],
            },
            {
                moduleName: "Loyalty Program",
                moduleDescription:
                    "Manage settings for customer loyalty programs, such as points or rewards.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
            },
            {
                moduleName: "Authentication Map",
                moduleDescription:
                    "Configure api access for each request, decides what should a request have to access a resource.",
                accessType: AccessType.ADMIN_ONLY,
                isActive: true,
            },
        ],
    },
    {
        moduleName: "Promotions",
        moduleDescription:
            "Manage active promotional campaigns, including coupons and vouchers.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Marketing",
        moduleDescription:
            "Configure and execute marketing campaigns, such as email or social media promotions.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Analytics",
        moduleDescription:
            "Access and analyze business performance data, such as sales and customer metrics.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Costing",
        moduleDescription:
            "Manage cost analysis and pricing strategies for products and services.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Resources",
        moduleDescription:
            "Track and manage business resources, such as equipment or supplies.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Suppliers",
        moduleDescription:
            "Manage supplier information and relationships for inventory procurement.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Inventory",
        moduleDescription:
            "Manage inventory levels, including stock tracking and reing.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Reports",
        moduleDescription:
            "Generate and view reports on business operations, such as sales or staff performance.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Shifts",
        moduleDescription: "Schedule and manage staff shifts and work hours.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Staff",
        moduleDescription:
            "Manage staff profiles, including roles, permissions, and contact details.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage User Preferences",
        moduleDescription:
            "Maintain user preferences, including branch-specific settings.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage Attendance",
        moduleDescription:
            "Manage staff attendance like check in and check out.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage Food Menus",
        moduleDescription:
            "Maintain food menu records, including creation, viewing, updating, and deletion.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage Upcoming Changes",
        moduleDescription: "Maintain upcoming changes for food menus.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage Sections",
        moduleDescription:
            "Maintain food menu sections, including creation, viewing, updating, and deletion.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage Dishes",
        moduleDescription:
            "Maintain dish records, including creation, viewing, updating, deletion, and associated sides, beverages, and desserts.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Customers",
        moduleDescription:
            "Manage customer profiles and interactions, including contact details and  history.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
        subModules: [
            {
                moduleName: "Customer View",
                moduleDescription:
                    "View and manage customer details.",
                accessType: AccessType.TENANT_ONLY,
                isActive: true,
            },
        ],
    },
    {
        moduleName: "Dashboard",
        moduleDescription:
            "Access a centralized dashboard for monitoring key business metrics and activities.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage Superadmin",
        moduleDescription:
            "Manage system-wide configurations and oversee administrative tasks from a centralized dashboard.",
        accessType: AccessType.ADMIN_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage Subscription",
        moduleDescription:
            "Administer subscription plans, billing, and user access levels through a dedicated interface.",
        accessType: AccessType.ADMIN_ONLY,
        isActive: true,
        subModules: [
            {
                moduleName: "Manage Tax",
                moduleDescription:
                    "Administer tax entities, including creating, viewing, updating, deleting, and filtering tax records for administrative purposes.",
                accessType: AccessType.ADMIN_ONLY,
                isActive: true,
            },
            {
                moduleName: "Countries",
                moduleDescription: "Listing country information, including viewing, listing, filtering, and searching country records for administrative purposes.",
                accessType: AccessType.ADMIN_ONLY,
                isActive: true,
            },
            {
                moduleName: "Manage Features",
                moduleDescription:
                    "Administer Features entities, including creating, viewing, updating, deleting, and filtering Features records for administrative purposes.",
                accessType: AccessType.ADMIN_ONLY,
                isActive: true,
            },
            {
                moduleName: "Manage Subscription Tier",
                moduleDescription:
                    "Administer Subscription Tier entities, including creating, viewing, updating, deleting, and filtering subscription tier records for administrative purposes.",
                accessType: AccessType.ADMIN_ONLY,
                isActive: true,
            },
        ]
    },
    {
        moduleName: "Manage Tenant",
        moduleDescription:
            "Administer tenant management like tenant creation, session management, updation and deletion",
        accessType: AccessType.ADMIN_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage Order",
        moduleDescription:
            "Handle all order-related operations including creation, tracking, updating status, and managing order history within the tenant scope.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Manage Cart",
        moduleDescription: "Handle all cart-related operations including adding, updating, removing items, and managing the cart state within the tenant scope.",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    },
    {
        moduleName: "Attachment",
        moduleDescription: "Handle all attachment related permissions like upload, view and delete here",
        accessType: AccessType.TENANT_ONLY,
        isActive: true,
    }
];
