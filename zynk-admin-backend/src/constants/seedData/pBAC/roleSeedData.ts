import {permission} from "process";
import {AccessType} from "../../../types/pBAC";

export const adminRole = [
    {
        name: "Admin",
        description: "Administrator with full access to all role, permission, module, action, and authentication mapping management.",
        accessType: AccessType.ADMIN_ONLY,
        permissions: [
            {
                permission: "role",
                actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"]
            },
            {
                permission: "permission",
                actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"]
            },
            {
                permission: "module",
                actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"]
            },
            {
                permission: "action",
                actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"]
            },
            {
                permission: "authMap",
                actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"]
            },
            {
                permission: "superadmin",
                actions: ["create", "read", "update", "delete", "filter", "search"],
            },
            {
                permission: "subscription",
                actions: ["list", "view", "create", "delete", "filter", "search"],
            },
            {
                permission: "subscription-tier",
                actions: ["create", "view", "list", "update", "delete", "filter", "search"],
            },
            {
                permission: "tenant-manage",
                actions: ["create", "read", "update", "delete", "filter", "search"],
            },
            {
                permission: "manage-tenant-blocker",
                actions: ["list", "view", "create", "delete", "filter", "search"],
            },
            {
                permission: "manage-block-type",
                actions: ["list", "view", "create", "delete", "filter", "search", "update"],
            },
            {
                permission: "tax",
                actions: ["create", "view", "list", "update", "delete", "filter", "search"],
            },
            {
                permission: "country",
                actions: ["view", "list", "filter", "search"],
            },
            {
                permission: "features",
                actions: ["create", "view", "list", "update", "delete", "filter", "search"],
            },
        ],
    },
];

export const tenantAdminRole = [
    {
        name: "TenantAdmin",
        description: "Tenant administrator with full control over roles and authentication mappings, and read-only access to permissions, modules, and actions.",
        accessType: AccessType.TENANT_ONLY,
        permissions: [
            {
                permission: "role",
                actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"]
            },
            {
                permission: "permission",
                actions: ["list"]
            },
            {
                permission: "module",
                actions: ["list"]
            },
            {
                permission: "action", actions: ["list"]
            },
            {
                permission: "cart",
                actions: ["add", "update", "delete", "clear", "hold", "activate", "confirm", "cancel", "get", "list"]
            },
            {permission: "customer", actions: ["create", "list", "export", "delete", "search", "update"]},
            {permission: "order", actions: ["create", "list", "export", "delete", "search", "update"]},
            {permission: "order-queue", actions: ["create", "list", "export", "delete", "search", "update"]},
            {permission: "section-icon", actions: ["create", "read", "update", "delete", "filter", "search"]},
            {permission: "dish-addon", actions: ["create", "read", "update", "delete", "filter", "search"]},
            {permission: "dish-extra", actions: ["create", "read", "update", "delete", "filter", "search"]},
            {permission: "ingredient", actions: ["create", "read", "update", "delete", "filter", "search"]},
            {permission: "allergy", actions: ["create", "read", "update", "delete", "filter", "search"]},
            {permission: "company-info", actions: ["create", "read", "update", "delete", "filter", "search"]},
            {permission: "branch", actions: ["create", "read", "update", "delete", "filter", "search"]},
            {permission: "staff-manage", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "user-preferences", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "food-menu", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "upcoming-changes", actions: ["read", "update", "delete"]},
            {permission: "section", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "dish", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "attendance", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "order-types", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "table-manage", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "parking-service", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "allergy-color", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "preparation-duration", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "special-ins", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "floor", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "table-reservation", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "turnover-time", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "buffer-time", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "online-tablereserv-config", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "attachment", actions: ["create", "read", "update", "delete", "filter"]}
        ],
    },
];

export const managerRole = [
    {
        name: "Manager",
        description: "Manager with read-only access to roles, permissions, modules, and actions, excluding authentication mappings.",
        accessType: AccessType.STAFF_ONLY,
        permissions: [
            {permission: "role", actions: ["list"]},
            {permission: "permission", actions: ["list"]},
            {permission: "module", actions: ["list"]},
            {permission: "action", actions: ["list"]},
            {permission: "branch", actions: ["read"]},
            {permission: "customer", actions: ["create", "list", "export", "delete", "search", "update"]},
            {permission: "order", actions: ["create", "list", "export", "delete", "search", "update"]},
            {permission: "order-queue", actions: ["create", "list", "export", "delete", "search", "update"]},
            {permission: "user-preferences", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "food-menu", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "upcoming-changes", actions: ["read", "update", "delete"]},
            {permission: "section", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "dish", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "attendance", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "order-types", actions: ["read", "update"]},
            {permission: "table-manage", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "parking-service", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "allergy-color", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "preparation-duration", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "special-ins", actions: ["read", "update"]},
            {permission: "floor", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "table-reservation", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "turnover-time", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "buffer-time", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "online-tablereserv-config", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "attachment", actions: ["create", "read", "update", "delete", "filter"]}
        ],
    },
];

export const staffRole = [
    {
        name: "Staff",
        description: "A general staff member responsible for supporting daily operations, assisting with various tasks, and ensuring a smooth workflow across departments.",
        accessType: AccessType.STAFF_ONLY,
        permissions: [
            {permission: "role", actions: ["view"]},
            {permission: "branch", actions: ["read"]},
            {permission: "customer", actions: ["list", "export", "search", "update"]},
            {permission: "order", actions: ["create", "list", "export", "search", "update"]},
            {permission: "order-queue", actions: ["create", "list", "export", "search", "update"]},
            {permission: "user-preferences", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "food-menu", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "upcoming-changes", actions: ["read", "update", "delete"]},
            {permission: "section", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "dish", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "attendance", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "order-types", actions: ["read"]},
            {permission: "table-manage", actions: ["read", "update"]},
            {permission: "parking-service", actions: ["read"]},
            {permission: "allergy-color", actions: ["read"]},
            {permission: "preparation-duration", actions: ["read"]},
            {permission: "special-ins", actions: ["read"]},
            {permission: "floor", actions: ["read"]},
            {permission: "table-reservation", actions: ["read", "update"]},
            {permission: "turnover-time", actions: ["read", "update"]},
            {permission: "buffer-time", actions: ["read", "update"]},
            {permission: "online-tablereserv-config", actions: ["read", "update"]},
            {permission: "attachment", actions: ["create", "read"]}
        ],
    },
    {
        name: "Waiter",
        description: "Responsible for providing excellent customer service, taking orders, serving food and beverages, and ensuring a pleasant dining experience for guests.",
        accessType: AccessType.STAFF_ONLY,
        permissions: [
            {permission: "role", actions: ["view"]},
            {permission: "branch", actions: ["read"]},
            {permission: "customer", actions: ["list", "export", "search", "update"]},
            {permission: "order", actions: ["create", "list", "export", "search", "update"]},
            {permission: "order-queue", actions: ["create", "list", "export", "search", "update"]},
            {permission: "user-preferences", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "food-menu", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "upcoming-changes", actions: ["read", "update", "delete"]},
            {permission: "section", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "dish", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "attendance", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "order-types", actions: ["read"]},
            {permission: "table-manage", actions: ["read", "update"]},
            {permission: "parking-service", actions: ["read"]},
            {permission: "allergy-color", actions: ["read"]},
            {permission: "preparation-duration", actions: ["read"]},
            {permission: "special-ins", actions: ["read"]},
            {permission: "floor", actions: ["read"]},
            {permission: "table-reservation", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "turnover-time", actions: ["read"]},
            {permission: "buffer-time", actions: ["read"]},
            {permission: "online-tablereserv-config", actions: ["read"]},
            {permission: "attachment", actions: ["create", "read"]}
        ],
    },
    {
        name: "Chef",
        description: "Oversees kitchen operations, prepares high-quality dishes, manages food inventory, and ensures compliance with health and safety standards.",
        accessType: AccessType.STAFF_ONLY,
        permissions: [
            {permission: "role", actions: ["view"]},
            {permission: "branch", actions: ["read"]},
            {permission: "customer", actions: ["list", "export", "search", "update"]},
            {permission: "order", actions: ["create", "list", "export", "search", "update"]},
            {permission: "order-queue", actions: ["create", "list", "export", "search", "update"]},
            {permission: "user-preferences", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "food-menu", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "upcoming-changes", actions: ["read", "update", "delete"]},
            {permission: "section", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "dish", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "attendance", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "order-types", actions: ["read"]},
            {permission: "table-manage", actions: ["read", "update"]},
            {permission: "parking-service", actions: ["read"]},
            {permission: "allergy-color", actions: ["read"]},
            {permission: "preparation-duration", actions: ["read"]},
            {permission: "special-ins", actions: ["read"]},
            {permission: "floor", actions: ["read"]},
            {permission: "table-reservation", actions: ["read"]},
            {permission: "turnover-time", actions: ["read"]},
            {permission: "buffer-time", actions: ["read"]},
            {permission: "online-tablereserv-config", actions: ["read"]},
            {permission: "attachment", actions: ["create", "read"]}
        ],
    },
    {
        name: "Receptionist",
        description: "Manages front desk operations, greets guests, handles reservations, and provides information to ensure a welcoming and efficient guest experience.",
        accessType: AccessType.STAFF_ONLY,
        permissions: [
            {permission: "role", actions: ["view"]},
            {permission: "branch", actions: ["read"]},
            {permission: "customer", actions: ["list", "export", "search", "update"]},
            {permission: "order", actions: ["create", "list", "export", "search", "update"]},
            {permission: "order-queue", actions: ["create", "list", "export", "search", "update"]},
            {permission: "user-preferences", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "food-menu", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "upcoming-changes", actions: ["read", "update", "delete"]},
            {permission: "section", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "dish", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "attendance", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "order-types", actions: ["read"]},
            {permission: "table-manage", actions: ["read", "update"]},
            {permission: "parking-service", actions: ["read"]},
            {permission: "allergy-color", actions: ["read"]},
            {permission: "preparation-duration", actions: ["read"]},
            {permission: "special-ins", actions: ["read"]},
            {permission: "floor", actions: ["read"]},
            {permission: "table-reservation", actions: ["create", "read", "update", "delete", "filter"]},
            {permission: "turnover-time", actions: ["read"]},
            {permission: "buffer-time", actions: ["read"]},
            {permission: "online-tablereserv-config", actions: ["read"]},
            {permission: "attachment", actions: ["create", "read"]}
        ],
    },
]

export const rolesData = [
    ...adminRole,
    ...tenantAdminRole,
];

export const tenantRolesData = [
    ...managerRole,
    ...staffRole,
];