import {AccessType} from "../../../types/pBAC";

const permissionModulePermissions = [
    {
        name: "permission",
        description:
            "Administer permissions in the Roles And Permissions module, including creating, viewing, updating, and searching permission assignments.",
        moduleName: "Roles And Permissions",
        accessType: AccessType.ALL,
        actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"],
    },
    {
        name: "module",
        description:
            "Configure modules in the Roles And Permissions module, including creating, viewing, updating, and searching module definitions.",
        moduleName: "Roles And Permissions",
        accessType: AccessType.ALL,
        actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"],
    },
    {
        name: "action",
        description:
            "Manage actions in the Roles And Permissions module, including defining, viewing, updating, and searching available actions.",
        moduleName: "Roles And Permissions",
        accessType: AccessType.ALL,
        actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"],
    },
    {
        name: "role",
        description:
            "Manage roles within the Manage Roles module, including creating, viewing, updating, and deleting role definitions.",
        moduleName: "Roles And Permissions",
        accessType: AccessType.ALL,
        actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"],
    },
];

const customerPermissions = [
    {
        name: "customer",
        description: "Grants access to manage customer profiles, including creating, listing, exporting, deleting, searching, and updating customer information.",
        moduleName: "Customer View",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "list", "export", "delete", "search", "update"]
    },
    {
        name: "order-history",
        description: "Allows management of customer order history, including listing, deleting, searching, and updating past orders.",
        moduleName: "Customer View",
        accessType: AccessType.TENANT_ONLY,
        actions: ["list", "delete", "search", "update"],
    },
    {
        name: "view-invoice",
        description: "Permits viewing and exporting customer invoices for billing and record-keeping purposes.",
        moduleName: "Customer View",
        accessType: AccessType.TENANT_ONLY,
        actions: ["export", "view"],
    }
];

const orderPermissions = [
    {
        name: "order",
        description: "Grants access to manage customer orders, including creating new orders, listing all orders, exporting order data, deleting orders, searching orders, and updating order details.",
        moduleName: "Manage Order",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "list", "export", "delete", "search", "update"]
    },
    {
        name: "order-queue",
        description: "Grants access to manage customer orders, including creating new orders, listing all orders, exporting order data, deleting orders, searching orders, and updating order details.",
        moduleName: "Manage Order",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "list", "export", "delete", "search", "update"]
    },
];

const cartPermissions = [
    {
        name: "cart",
        description: "Grants access to manage customer carts, including adding items, updating quantities, removing items, clearing the cart, placing holds, activating carts, confirming carts into orders, and canceling carts.",
        moduleName: "Manage Cart",
        accessType: AccessType.TENANT_ONLY,
        actions: ["add", "update", "delete", "clear", "hold", "activate", "confirm", "cancel", "get", "list"]
    }
];

const adminSettingsPermissions = [
    {
        name: "manage-tenant-blocker",
        description: "Route permissions related to superadmin",
        moduleName: "Settings",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["list", "view", "create", "delete", "filter", "search"],
    },
    {
        name: "manage-block-type",
        description: "Route permissions related to superadmin",
        moduleName: "Settings",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["list", "view", "create", "delete", "filter", "search", "update"],
    },
]

const routePermissions = [
    {
        name: "superadmin",
        description: "Route permissions related to superadmin",
        moduleName: "Manage Superadmin",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["create", "read", "update", "delete", "filter", "search"],
    },
    {
        name: "tenant-manage",
        description: "Route permissions related to tenant management",
        moduleName: "Manage Tenant",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["create", "read", "update", "delete", "filter", "search"],
    },
    {
        name: "section-icon",
        description: "Route permissions related to section icon management",
        moduleName: "Manage Section Icons",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter", "search"],
    },
    {
        name: "dish-addon",
        description: "Route permissions related to dish addon management",
        moduleName: "Manage Dish Addons",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter", "search"],
    },
    {
        name: "dish-extra",
        description: "Route permissions related to dish extra management",
        moduleName: "Manage Dish Extras",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter", "search"],
    },
    {
        name: "ingredient",
        description: "Route permissions related to ingredient management",
        moduleName: "Manage Ingredients",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter", "search"],
    },
    {
        name: "allergy",
        description: "Route permissions related to allergy management",
        moduleName: "Manage Allergies",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter", "search"],
    },
    {
        name: "company-info",
        description:
            "Route permissions related to company information, representatives, and bank details management",
        moduleName: "Company Information",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter", "search"],
    },
    {
        name: "branch",
        description:
            "Route permissions related to branch management, including status, details, contacts, and business hours",
        moduleName: "Business Information",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter", "search"],
    },
    {
        name: "staff-manage",
        description:
            "Route permissions related to staff management, including creation, viewing, updating, and deletion",
        moduleName: "Staff",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "user-preferences",
        description:
            "Route permissions related to user preferences management, including branch preferences",
        moduleName: "Manage User Preferences",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "food-menu",
        description:
            "Route permissions related to food menu management, including creation, viewing, updating, and deletion",
        moduleName: "Manage Food Menus",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "upcoming-changes",
        description:
            "Route permissions related to managing upcoming changes for food menus",
        moduleName: "Manage Upcoming Changes",
        accessType: AccessType.TENANT_ONLY,
        actions: ["read", "update", "delete"],
    },
    {
        name: "section",
        description:
            "Route permissions related to food menu section management, including creation, viewing, updating, and deletion",
        moduleName: "Manage Sections",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "dish",
        description:
            "Route permissions related to dish management, including creation, viewing, updating, deletion, and associated sides, beverages, and desserts",
        moduleName: "Manage Dishes",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "attendance",
        description:
            "Route permissions related to attendance records like check in, check out, etc...",
        moduleName: "Manage Attendance",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "order-types",
        description:
            "Route permissions related to order types management, including creation, viewing, updating, and deletion",
        moduleName: "Order Types",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "table-manage",
        description:
            "Route permissions related to table management which includes table creation, updation, table combinations, etc...",
        moduleName: "Table Management",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "parking-service",
        description:
            "Route permissions related to parking service in a branch...",
        moduleName: "Parking Services",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "allergy-color",
        description:
            "Route permissions related to parking service in a branch...",
        moduleName: "Manage Allergies",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "preparation-duration",
        description:
            "Route permissions related to preparation duration for dishes in a branch.",
        moduleName: "Preparation Time",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "special-ins",
        description:
            "Route permissions related to special instructions for kitchen.",
        moduleName: "Special Instructions",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "floor",
        description:
            "Route permissions related to floors in a restaurant",
        moduleName: "Table Management",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "table-reservation",
        description:
            "Route permissions related to table reservations like creating, updating, checking in etc.",
        moduleName: "Table Management",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "turnover-time",
        description:
            "Route permissions related to table reservation turnover time and turnover rules",
        moduleName: "Turnover Time",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "buffer-time",
        description:
            "Route permissions related to table reservation buffer time",
        moduleName: "Table Management",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "online-tablereserv-config",
        description:
            "Route permissions related to table reservation online config",
        moduleName: "Table Management",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
    {
        name: "attachment",
        description:
            "Route permissions related to attachment upload, view and delete",
        moduleName: "Attachment",
        accessType: AccessType.TENANT_ONLY,
        actions: ["create", "read", "update", "delete", "filter"],
    },
];

const authMapPermissions = [
    {
        name: "authMap",
        description:
            "Administer authentication mappings in the Authentication Map module, including creating, viewing, and updating route-permission mappings for administrative access.",
        moduleName: "Authentication Map",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["create", "view", "list", "edit", "update", "delete", "filter", "search"],
    },
];

const subscriptionModulePermission = [
    {
        name: "subscription",
        description: "Route permissions related to subscription",
        moduleName: "Manage Subscription",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["list", "view", "create", "delete", "filter", "search"],
    },
    {
        name: "subscription-tier",
        description: "Route permissions related to subscription tiers",
        moduleName: "Manage Subscription Tier",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["create", "view", "list","update", "delete", "filter", "search"],
    },
    {
        name: "tax",
        description:
            "Manage tax entities, including creating, viewing, listing, editing, updating, deleting, filtering, and searching tax records for administrative purposes.",
        moduleName: "Manage Tax",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["create", "view", "list","update", "delete", "filter", "search"],
    },
    {
        name: "country",
        description: "Listing country information, including viewing, listing, filtering, and searching country records for administrative purposes.",
        moduleName: "Countries",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["view", "list", "filter", "search"],
    },
    {
        name: "features",
        description: "Listing feature information, including viewing, listing, filtering, and searching feature records for administrative purposes.",
        moduleName: "Manage Features",
        accessType: AccessType.ADMIN_ONLY,
        actions: ["create", "view", "list", "update", "delete", "filter", "search"],
    },
];

export const permissionSeedsData = [
    ...subscriptionModulePermission,
    ...permissionModulePermissions,
    ...adminSettingsPermissions,
    ...routePermissions,
    ...authMapPermissions,
    ...customerPermissions,
    ...orderPermissions,
    ...cartPermissions
];
