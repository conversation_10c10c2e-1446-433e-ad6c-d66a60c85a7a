import { AuthMapType } from "../authMap";
import { AccessType, RouteType } from "../../../../types/pBAC";

export const tableReservDineInAuthMap: AuthMapType[] = [
  {
    routePath: "/table-reservation/tables/:tableId/reservations",
    description: "",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:create"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "POST",
    isActive: true,
  },
  {
    routePath: "/table-reservation/reservations",
    description: "",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/table-reservation/reservations/:reservationId",
    description: "",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/table-reservation/reservations/:reservationId",
    description: "",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "PUT",
    isActive: true,
  },
  {
    routePath: "/table-reservation/reservations/:reservationId",
    description: "",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:delete"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "DELETE",
    isActive: true,
  },
  {
    routePath: "/table-reservation/reservations/:reservationId/checkin",
    description: "",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:create"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "POST",
    isActive: true,
  },
];
