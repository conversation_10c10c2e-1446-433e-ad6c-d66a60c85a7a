import { AuthMapType } from "../authMap";
import { AccessType, RouteType } from "../../../../types/pBAC";

export const tableCombReservAuthMap: AuthMapType[] = [
  {
    routePath: "/table-reservation/table-combinations/:tableCombinationId/reservations",
    description: "Reserve a table combination",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:create"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "POST",
    isActive: true,
  },
  {
    routePath: "/table-reservation/getTableCombinationReservations",
    description: "View reservations belonging to table combinations",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/table-reservation/updateTableCombinationReservation/:confirmationCode",
    description: "Update table combination by confirmation code",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "PUT",
    isActive: true,
  },
  {
    routePath: "/table-reservation/cancelTableCombinationReservation/:confirmationCode",
    description: "Cancel table combination by confirmation code",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:delete"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "DELETE",
    isActive: true,
  },
  {
    routePath: "/table-reservation/table-combinations/:confirmationCode/checkin",
    description: "Check in with confirmation code for table combination reservations",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-reservation:create"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "POST",
    isActive: true,
  },
];
