import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";
import { tableReservDineInAuthMap } from "./tableReservDineIn.authMap";

export const dineInAuthMap: AuthMapType[] = [
    ...tableReservDineInAuthMap,
    {
        routePath: "/dineIn/table-attrs/*",
        description: "Dine In table patch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["table-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/dineIn/take-order/*",
        description: "Dine In table take order route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["table-reservation:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/dineIn/cancel-order/*",
        description: "Dine In table cancel order route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["table-reservation:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
    {
        routePath: "/dineIn/take-order-combined/*",
        description: "Dine In table combination take order route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["table-reservation:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/dineIn/cancel-order-combined/*",
        description: "Dine In table combination cancel order route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["table-reservation:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
];
