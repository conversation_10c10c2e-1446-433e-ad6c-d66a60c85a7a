import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const staffManageAuthMap: AuthMapType[] = [
    {
        routePath: "/staff-manage/create",
        description: "Create staff route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/staff-manage/view_all/*",
        description: "View all staff by branch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/staff-manage/view/*",
        description: "View staff by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/staff-manage/viewAllClockedInStaff/*",
        description: "View all clocked in staff",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/staff-manage/patch/staff-details/*",
        description: "Patch staff details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/staff-manage/patch/default-availability/*",
        description: "Patch staff availability route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/staff-manage/patch/staff-certifications/*",
        description: "Patch staff certifications route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/staff-manage/view_sessions/*",
        description: "View all staff sessions",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/staff-manage/revoke/*",
        description: "Revoke staff login from an IP",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/staff-manage/reinstate/*",
        description: "Reinstate staff login from an IP",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/staff-manage/delete/*",
        description: "Delete staff route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
];