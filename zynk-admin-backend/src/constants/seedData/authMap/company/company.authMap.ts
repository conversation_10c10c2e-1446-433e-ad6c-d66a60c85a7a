import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const companyInfoAuthMap: AuthMapType[] = [
    {
        routePath: "/company-info/company-info",
        description: "Get company information route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["company-info:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/company-info/patch",
        description: "Patch company information route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["company-info:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/company-info/company-rep",
        description: "Create company representative route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["company-info:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/company-info/company-rep/view_all",
        description: "View all company representatives route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["company-info:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/company-info/company-rep/patch/*",
        description: "Patch company representative route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["company-info:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/company-info/company-rep/delete/*",
        description: "Delete company representative route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["company-info:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
    {
        routePath: "/company-info/bank-details",
        description: "Get bank details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["company-info:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/company-info/bank-details",
        description: "Patch bank details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["company-info:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    }
];
