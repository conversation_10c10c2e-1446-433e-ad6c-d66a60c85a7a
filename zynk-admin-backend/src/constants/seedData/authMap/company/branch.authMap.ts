import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const branchAuthMap: AuthMapType[] = [
    {
        routePath: "/branch/create",
        description: "Branch create route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/branch/view_all",
        description: "View all branches route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/branch/status/*",
        description: "Patch branch status route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/branch/details/*",
        description: "Get branch details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/branch/details/*",
        description: "Patch branch details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/branch/contacts/*",
        description: "Get branch contacts route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/branch/contacts/*",
        description: "Patch branch contacts route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/branch/business-hours/*",
        description: "Get branch business hours route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/branch/business-hours/*",
        description: "Patch branch business hours route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/branch/delete/*",
        description: "Delete branch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["branch:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    }
];
