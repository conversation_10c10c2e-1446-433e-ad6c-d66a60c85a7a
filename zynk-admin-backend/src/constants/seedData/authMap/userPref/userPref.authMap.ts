import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const userPreferencesAuthMap: AuthMapType[] = [
    {
        routePath: "/user-preferences/pref-branch",
        description: "Get preferred branch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["user-preferences:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/user-preferences/pref-branch",
        description: "Patch preferred branch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["user-preferences:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
];