import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const tenantAuthMap: AuthMapType[] = [
    {
        routePath: "/tenant/login",
        description: "Tenant login route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/tenant/logout",
        description: "Tenant logout route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        method: 'POST',
        isActive: true,
    },
];
