import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";
import {
    createSubsTierSchema,
    filterSubTierSchema,
    patchSubTierSchema, subTierIdSchema
} from "../../../../validation/subscription/subTier.validation";

export const subTierAuthMap: AuthMapType[] = [
    {
        routePath: "/sub-tier/list",
        method: "GET",
        description: "list subscription tier based on filters",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["subscription-tier:list", "subscription-tier:view", "subscription-tier:search", "subscription-tier:filter"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        queryParams: filterSubTierSchema,
    },
    {
        routePath: "/sub-tier/create",
        method: "POST",
        description: "Add new subscription tier",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["subscription-tier:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: createSubsTierSchema,
    },
    {
        routePath: "/sub-tier/patch",
        method: "PATCH",
        description: "sub-tier patch route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["subscription-tier:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: patchSubTierSchema,
    },
    {
        routePath: "/sub-tier/delete/*",
        method: "DELETE",
        description: "sub-tier delete route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["subscription-tier:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestParams: subTierIdSchema
    },
];
