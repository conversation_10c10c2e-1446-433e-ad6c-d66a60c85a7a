import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";
import {
    createFeatureSchema,
    featureFilterSchema, featureIdSchema,
    patchFeatureSchema
} from "../../../../validation/subscription/feature.validation";

export const featureAuthMap: AuthMapType[] = [
    {
        routePath: "/feature/list",
        method: "GET",
        description: "list of features",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["features:list", "features:view", "features:filter", "features:search"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        queryParams: featureFilterSchema,
    },
    {
        routePath: "/feature/create",
        method: "POST",
        description: "create new feature",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["features:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: createFeatureSchema
    },
    {
        routePath: "/feature/patch",
        method: "PATCH",
        description: "update existing feature",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["features:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: patchFeatureSchema
    },
    {
        routePath: "/feature/delete/*",
        method: "DELETE",
        description: "delete feature",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["features:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestParams: featureIdSchema
    }
]