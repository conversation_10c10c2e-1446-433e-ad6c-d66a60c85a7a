import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";
import {manageSubscriptionSchema} from "../../../../validation/subscription/subscription.validation";

export const subscriptionAuthMap: AuthMapType[] = [
    // subscription routes
    {
        routePath: "/subscription/list",
        method: "GET",
        description: "subscription listing route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["subscription:list", "subscription:view", "subscription:filter", "subscription:search"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/subscription/activate",
        method: "POST",
        description: "subscription activation route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["subscription:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: manageSubscriptionSchema
    },
    {
        routePath: "/terminate/*",
        method: "DELETE",
        description: "subscription terminate route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["subscription:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    }
];
