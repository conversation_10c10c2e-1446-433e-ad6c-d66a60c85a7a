import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";
import {
    createTaxSchema,
    deleteTaxSchema,
    filterTaxSchema,
    patchTaxSchema
} from "../../../../validation/subscription/tax.validation";

export const taxAuthMap: AuthMapType[] = [
    {
        routePath: "/tax/create",
        method: "POST",
        description: "Create a new tax entry",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tax:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: createTaxSchema
    },
    {
        routePath: "/tax/list",
        method: "GET",
        description: "Retrieve all tax entries with optional filters",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tax:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        queryParams: filterTaxSchema
    },
    {
        routePath: "/tax/patch",
        method: "PATCH",
        description: "Update an existing tax entry by taxId",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tax:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: patchTaxSchema
    },
    {
        routePath: "/tax/delete/*",
        method: "DELETE",
        description: "Delete a tax entry by taxId",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tax:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestParams: deleteTaxSchema
    },
];