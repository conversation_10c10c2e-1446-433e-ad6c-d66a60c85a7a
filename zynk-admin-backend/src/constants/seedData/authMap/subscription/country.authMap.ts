import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";
import {countryCodeBaseSchema} from "../../../../validation/subscription/country.validation";

export const countryAuthMap: AuthMapType[] = [
    {
        routePath: "/country/view_all",
        method: "GET",
        description: "Retrieve a list of all country information with optional filters",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["country:list", "country:search", "country:filter"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        queryParams: countryCodeBaseSchema
    },
    {
        routePath: "/country/state/view_all",
        method: "GET",
        description: "Retrieve a list of all state information with optional filters",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["country:list", "country:search", "country:filter"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/country/state/validate",
        method: "GET",
        description: "Validate a state ID by country ID ",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["country:search", "country:filter"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
];