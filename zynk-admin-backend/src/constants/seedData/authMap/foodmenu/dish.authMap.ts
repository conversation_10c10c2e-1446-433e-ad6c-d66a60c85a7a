import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const dishAuthMap: AuthMapType[] = [
    {
        routePath: "/dish/create/*",
        description: "Create dish route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/dish/view_all/*",
        description: "View all dishes by section route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish/view/*",
        description: "View dish by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish/patch/*",
        description: "Patch dish details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/dish/delete/*",
        description: "Delete dish route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
    {
        routePath: "/dish/custom-hours/*",
        description: "Patch dish custom hours route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/dish/availability/*",
        description: "Patch dish availability route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/dish/special-days/*",
        description: "Patch dish special days route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/dish/duplicate/*",
        description: "Duplicate dish route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/dish/move/*",
        description: "Move dish to another section route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/dish/side/view_all/*",
        description: "View all dish sides by food menu route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish/side/view/*",
        description: "View dish side by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish/beverage/view_all/*",
        description: "View all dish beverages by food menu route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish/beverage/view/*",
        description: "View dish beverage by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish/dessert/view_all/*",
        description: "View all dish desserts by food menu route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish/dessert/view/*",
        description: "View dish dessert by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
];