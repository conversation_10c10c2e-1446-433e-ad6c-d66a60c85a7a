import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const foodMenuAuthMap: AuthMapType[] = [
    {
        routePath: "/food-menu/create/*",
        description: "Create food menu route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/food-menu/view_all/*",
        description: "View all food menus by branch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/food-menu/view/*",
        description: "View food menu by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/food-menu/patch/status/*",
        description: "Patch food menu status route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/food-menu/patch/menu/*",
        description: "Patch food menu attributes route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/food-menu/custom-hours/*",
        description: "Patch food menu custom hours route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/food-menu/availability/*",
        description: "Patch food menu availability route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/food-menu/special-days/*",
        description: "Patch food menu special days route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/food-menu/delete/*",
        description: "Delete food menu route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
    {
        routePath: "/food-menu/active-menu-withoutAvailability/*",
        description: "Get active menu by branch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/food-menu/active-menu-withAvailability/*",
        description: "Get active menu by branch route with availability applied",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/food-menu/online-menu/*",
        description: "Get active menu for online (website, user app)",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/food-menu/duplicate/*",
        description: "Duplicate food menu route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["food-menu:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/food-menu/updates/upcoming-changes/*",
        description: "Get upcoming changes for food menu route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["upcoming-changes:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/food-menu/updates/upcoming-changes/*",
        description: "Patch upcoming changes for food menu route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["upcoming-changes:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/food-menu/updates/delete/*",
        description: "Delete upcoming change route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["upcoming-changes:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
];