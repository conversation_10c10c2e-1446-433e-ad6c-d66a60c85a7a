import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const sectionAuthMap: AuthMapType[] = [
    {
        routePath: "/section/create/*",
        description: "Create section route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["section:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/section/view_all/*",
        description: "View all sections by food menu route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["section:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/section/view/*",
        description: "View section by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["section:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/section/patch/*",
        description: "Patch section details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["section:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/section/custom-hours/*",
        description: "Patch section custom hours route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["section:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/section/availability/*",
        description: "Patch section availability route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["section:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/section/delete/*",
        description: "Delete section route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["section:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
    {
        routePath: "/section/special-days/*",
        description: "Patch section special days route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["section:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/section/duplicate/*",
        description: "Duplicate section route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["section:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
];
