import { AccessType, RouteType } from "../../../../types/pBAC";
import { createCheckInRecordSchema, createCheckOutRecordSchema, createClockInRecordSchema, createClockOutRecordSchema } from "../../../../validation/attendance/attendance.validation";

export const attendanceAuthMap = [
  {
    routePath: "/attendance/check-in/*",
    method: "POST",
    description: "Staff check in route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["attendance:create"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    isActive: true,
    requestBody: createCheckInRecordSchema,
  },
  {
    routePath: "/attendance/check-out/*",
    method: "POST",
    description: "Staff check out route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["attendance:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    isActive: true,
    requestBody: createCheckOutRecordSchema,
  },
  {
    routePath: "/attendance/clock-in/*",
    method: "POST",
    description: "Staff clock in route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["attendance:create"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    isActive: true,
    requestBody: createClockInRecordSchema,
  },
  {
    routePath: "/attendance/clock-out/*",
    method: "POST",
    description: "Staff clock out route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["attendance:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    isActive: true,
    requestBody: createClockOutRecordSchema,
  },
];
