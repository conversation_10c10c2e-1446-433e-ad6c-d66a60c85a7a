
import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const orderTypeAuthMap: AuthMapType[] = [
    {
        routePath: "/tenant-settings/general/order-types/view_all",
        description: "Get all order types",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order-types:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/tenant-settings/general/order-types/patch",
        description: "Patch order types",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order-types:update", "order-types:delete"],
        isOrOperation: true,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
];