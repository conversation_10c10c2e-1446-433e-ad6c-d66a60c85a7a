
import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const parkingSrvAuthMap: AuthMapType[] = [
    {
        routePath: "/tenant-settings/general/parking-service/view",
        description: "Get branch's parking service",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["parking-service:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/tenant-settings/general/parking-service/patch",
        description: "Patch branch's parking service",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["parking-service:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
];