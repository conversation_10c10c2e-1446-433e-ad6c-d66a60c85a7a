
import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const prepDurationAuthMap: AuthMapType[] = [
    {
        routePath: "/tenant-settings/waiter/prep-duration/view",
        description: "Get branch's global dish preparation time",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["preparation-duration:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/tenant-settings/waiter/prep-duration/patch",
        description: "Patch branch's global dish preparation time",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["preparation-duration:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
];