import { AuthMapType } from "../authMap";
import { orderTypeAuthMap } from "./general/orderType.authMap";
import { parkingSrvAuthMap } from "./general/parkingSrv.authMap";
import { allergyColorAuthMap } from "./kitchen/allergyColor.authMap";
import { specialInsAuthMap } from "./kitchen/specialIns.authMap";
import { bufferTimeAuthMap } from "./tableReserv/bufferTime.authMap";
import { floorAuthMap } from "./tableReserv/floor.authMap";
import { onlineReservConfigAuthMap } from "./tableReserv/onlineReservConfig.authMap";
import { tableManageAuthMap } from "./tableReserv/tableManage.authMap";
import { turnovertimeAuthMap } from "./tableReserv/turnoverTime.authMap";
import { prepDurationAuthMap } from "./waiter/prepDuration.authMap";

export const tenantSettingsAuthMap: AuthMapType[] = [
    ...orderTypeAuthMap,
    ...tableManageAuthMap,
    ...floorAuthMap,
    ...parkingSrvAuthMap,
    ...allergyColorAuthMap,
    ...prepDurationAuthMap,
    ...specialInsAuthMap,
    ...turnovertimeAuthMap,
    ...bufferTimeAuthMap,
    ...onlineReservConfigAuthMap
]