
import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const specialInsAuthMap: AuthMapType[] = [
    {
        routePath: "/tenant-settings/kitchen/special-ins/view",
        description: "Get special instructions config",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["special-ins:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/tenant-settings/kitchen/special-ins/patch",
        description: "Patch special instructions config",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["special-ins:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
];