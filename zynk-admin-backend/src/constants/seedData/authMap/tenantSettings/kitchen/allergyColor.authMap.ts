
import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const allergyColorAuthMap: AuthMapType[] = [
    {
        routePath: "/tenant-settings/kitchen/allergy-color/view_all",
        description: "Get all allergy colors",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["allergy-color:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/tenant-settings/kitchen/allergy-color/view/*",
        description: "Get an allergy color by name",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["allergy-color:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/tenant-settings/kitchen/allergy-color/patch",
        description: "Patch allergy colors",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["allergy-color:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
];