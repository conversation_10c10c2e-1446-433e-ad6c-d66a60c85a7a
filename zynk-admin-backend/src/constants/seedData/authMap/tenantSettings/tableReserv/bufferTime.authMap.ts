import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const bufferTimeAuthMap: AuthMapType[] = [
  {
    routePath: "/tenant-settings/table-reservation/buffer-time/view",
    description: "View buffer time settings for table reservation",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["buffer-time:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/buffer-time/patch",
    description: "Patch buffer time settings for table reservation",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["buffer-time:update"],
    isOrOperation: true,
    routeType: RouteType.PRIVATE,
    method: "PATCH",
    isActive: true,
  },
];
