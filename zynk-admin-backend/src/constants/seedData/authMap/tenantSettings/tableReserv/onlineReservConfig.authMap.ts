import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const onlineReservConfigAuthMap: AuthMapType[] = [
  {
    routePath: "/tenant-settings/table-reservation/online-tablereserv-config/view",
    description: "View table reservation online config",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["online-tablereserv-config:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/online-tablereserv-config/patch",
    description: "Patch table reservation online config",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["online-tablereserv-config:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "PATCH",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/online-tablereserv-config/custom-hours/:onlineResConfigId",
    description: "Patch custom hours for table reservation online config",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["online-tablereserv-config:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "PATCH",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/online-tablereserv-config/special-days/:onlineResConfigId",
    description: "Patch special days for table reservation online config",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["online-tablereserv-config:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "PATCH",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/online-tablereserv-config/party-size/view",
    description: "View party size config",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["online-tablereserv-config:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/online-tablereserv-config/party-size/patch",
    description: "Patch party size config",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["online-tablereserv-config:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "PATCH",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/online-tablereserv-config/time-slot/view",
    description: "View time slot config",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["online-tablereserv-config:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/online-tablereserv-config/time-slot/patch",
    description: "Patch time slot config",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["online-tablereserv-config:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "PATCH",
    isActive: true,
  },
];
