import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const tableManageAuthMap: AuthMapType[] = [
  {
    routePath: "/tenant-settings/table-reservation/table-manage/view_all",
    description: "Get all tables and table combinations",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-manage:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/table-manage/patch",
    description: "Patch tables and table combinations",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["table-manage:update", "table-manage:delete"],
    isOrOperation: true,
    routeType: RouteType.PRIVATE,
    method: "PATCH",
    isActive: true,
  },
];
