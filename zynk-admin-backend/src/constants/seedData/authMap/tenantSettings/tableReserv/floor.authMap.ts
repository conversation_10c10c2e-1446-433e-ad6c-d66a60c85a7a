import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const floorAuthMap: AuthMapType[] = [
  {
    routePath: "/tenant-settings/table-reservation/floors/view_all",
    description: "Get all floors with tables and table combinations",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["floor:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/floors/patch",
    description: "Patch floor properties",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["floor:update", "floor:delete"],
    isOrOperation: true,
    routeType: RouteType.PRIVATE,
    method: "PATCH",
    isActive: true,
  },
];
