import { AccessType, RouteType } from "../../../../../types/pBAC";
import { AuthMapType } from "../../authMap";

export const turnovertimeAuthMap: AuthMapType[] = [
  {
    routePath: "/tenant-settings/table-reservation/turnover-time/view",
    description: "View current turnover time config",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["turnover-time:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: "GET",
    isActive: true,
  },
  {
    routePath: "/tenant-settings/table-reservation/turnover-time/patch",
    description: "Patch turnover time config including turnover rules",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["turnover-time:update", "turnover-time:delete"],
    isOrOperation: true,
    routeType: RouteType.PRIVATE,
    method: "PATCH",
    isActive: true,
  },
];
