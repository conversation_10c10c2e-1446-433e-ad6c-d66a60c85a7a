import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const miscAuthMap: AuthMapType[] = [
    {
        routePath: "/",
        method: "GET",
        description: "ping pong route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        isActive: true,
    },
    {
        routePath: "/ping-test",
        method: "GET",
        description: "ping test route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        isActive: true,
    },
    {
        routePath: "/reference",
        method: "GET",
        description: "api documentation route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        isActive: true,
    },
    {
        routePath: "/api-docs/html",
        method: "GET",
        description: "API documentation in HTML format",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        isActive: true,
    },
    {
        routePath: "/api-docs/test/*",
        method: "GET",
        description: "API testing route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        isActive: true,
    },
]