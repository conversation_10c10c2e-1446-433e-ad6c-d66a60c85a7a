import { AccessType, RouteType } from "../../../../types/pBAC";
import { AuthMapType } from "../authMap";

export const cartAuthMap: AuthMapType[] = [
    {
        routePath: "/cart/staff",
        description: "Get or create active cart for staff",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:get"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/cart/list",
        description: "List all carts with optional filters",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/cart/:cartId",
        description: "Get specific cart by ID",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:get"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/cart/staff/items",
        description: "Add item to cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "POST",
        isActive: true,
    },
    {
        routePath: "/cart/staff/items/:itemId",
        description: "Update cart item quantity",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PUT",
        isActive: true,
    },
    {
        routePath: "/cart/staff/add-details/:cartId",
        description: "Add details to cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/cart/staff/update-misc/:cartId",
        description: "Update miscellaneous item in cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/cart/staff/update-alert/:cartId",
        description: "Update alert in cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/cart/staff/delete-misc/:cartId/:miscItemId",
        description: "Delete miscellaneous item from cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "DELETE",
        isActive: true,
    },
    {
        routePath: "/cart/staff/delete-alert/:cartId/:alertId",
        description: "Delete alert from cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "DELETE",
        isActive: true,
    },
    {
        routePath: "/cart/staff/items/:itemId",
        description: "Remove item from cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "DELETE",
        isActive: true,
    },
    {
        routePath: "/cart/staff/delete-cart/:cartId",
        description: "Delete whole cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "DELETE",
        isActive: true,
    },
    {
        routePath: "/cart/staff/clear",
        description: "Clear all items from cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:clear"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "DELETE",
        isActive: true,
    },
    {
        routePath: "/cart/staff/hold",
        description: "Hold cart (change status to HOLD)",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:hold"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/cart/staff/:cartId/activate",
        description: "Activate held cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:activate"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/cart/staff/confirm",
        description: "Confirm cart (convert to order)",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:confirm"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "POST",
        isActive: true,
    },
    {
        routePath: "/cart/staff/update/:itemId",
        description: "Update item customization in cart",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["cart:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
];