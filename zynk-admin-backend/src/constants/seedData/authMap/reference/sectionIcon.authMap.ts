import { AccessType, RouteType } from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const sectionIconAuthMap: AuthMapType[] = [
  {
    routePath: "/section-icon/create",
    description: "Section icon create route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["section-icon:create"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: 'POST',
    isActive: true,
  },
  {
    routePath: "/section-icon/view/*",
    description: "Section icon view by ID route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["section-icon:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: 'GET',
    isActive: true,
  },
  {
    routePath: "/section-icon/view_all",
    description: "Section icon view all route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["section-icon:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: 'GET',
    isActive: true,
  },
  {
    routePath: "/section-icon/patch/*",
    description: "Section icon patch route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["section-icon:update"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: 'PATCH',
    isActive: true,
  },
  {
    routePath: "/section-icon/delete/*",
    description: "Section icon delete route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["section-icon:delete"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    method: 'DELETE',
    isActive: true,
  }
];