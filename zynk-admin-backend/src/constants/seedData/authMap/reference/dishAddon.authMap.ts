import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const dishAddonAuthMap: AuthMapType[] = [
    {
        routePath: "/dish-addon/create",
        description: "Dish addon create route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-addon:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/dish-addon/view/*",
        description: "Dish addon view by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-addon:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish-addon/view_all",
        description: "Dish addon view all route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-addon:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish-addon/patch/*",
        description: "Dish addon patch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-addon:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/dish-addon/delete/*",
        description: "Dish addon delete route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-addon:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    }
];