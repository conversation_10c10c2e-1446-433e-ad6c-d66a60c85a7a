import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const allergyAuthMap: AuthMapType[] = [
    {
        routePath: "/allergy/create",
        description: "Allergy create route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["allergy:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/allergy/view/*",
        description: "Allergy view by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["allergy:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/allergy/view_all",
        description: "Allergy view all route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["allergy:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/allergy/patch/*",
        description: "Allergy patch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["allergy:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/allergy/delete/*",
        description: "Allergy delete route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["allergy:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
];
