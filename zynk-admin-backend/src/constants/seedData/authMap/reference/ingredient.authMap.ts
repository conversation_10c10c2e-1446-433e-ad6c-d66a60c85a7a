import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const ingredientAuthMap: AuthMapType[] = [
    {
        routePath: "/ingredient/create",
        description: "Ingredient create route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["ingredient:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/ingredient/view/*",
        description: "Ingredient view by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["ingredient:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/ingredient/view_all",
        description: "Ingredient view all route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["ingredient:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/ingredient/patch/*",
        description: "Ingredient patch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["ingredient:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/ingredient/delete/*",
        description: "Ingredient delete route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["ingredient:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
];
