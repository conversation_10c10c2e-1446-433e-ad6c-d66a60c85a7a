import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const dishExtraAuthMap: AuthMapType[] = [
    {
        routePath: "/dish-extra/create",
        description: "Dish extra create route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-extra:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'POST',
        isActive: true,
    },
    {
        routePath: "/dish-extra/view/*",
        description: "Dish extra view by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-extra:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish-extra/view_all",
        description: "Dish extra view all route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-extra:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'GET',
        isActive: true,
    },
    {
        routePath: "/dish-extra/patch/*",
        description: "Dish extra patch route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-extra:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/dish-extra/delete/*",
        description: "Dish extra delete route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["dish-extra:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'DELETE',
        isActive: true,
    },
];
