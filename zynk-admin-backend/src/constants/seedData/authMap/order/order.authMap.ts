import { AccessType, RouteType } from "../../../../types/pBAC";
import { AuthMapType } from "../authMap";

export const orderAuthMap: AuthMapType[] = [
    {
        routePath: "/order/create",
        description: "Create a new order route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "POST",
        isActive: true,
    },
    {
        routePath: "/order/update-status/*",
        description: "Update order status and approval route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/order/update/*",
        description: "Partial update of order details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/order/items/*",
        description: "Add items to an existing order route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "POST",
        isActive: true,
    },
    {
        routePath: "/order/get-order/*",
        description: "Get order details by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/order/view_all",
        description: "Get all orders with filters route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/order/delete/*",
        description: "Hard delete order details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "DELETE",
        isActive: true,
    },
    {
        routePath: "/order/queue",
        description: "Get queue view route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order-queue:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/order/queue-status/*",
        description: "Update queue status route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order-queue:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/order/queue/get-waiter-orders",
        description: "Get active queue orders by waiter route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order-queue:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/order/active-order-details",
        description: "Get active order details by waiter route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/order/verify-cancellation",
        description: "Verify manager/admin pin before order cancellation!",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["order:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "POST",
        isActive: true,
    },
];