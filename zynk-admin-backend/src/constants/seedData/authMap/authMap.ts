import {branchAuthMap} from "./company/branch.authMap";
import {subTierAuthMap} from "./subscription/subTier.authMap";
import {superAdminAuthMap} from "./admin/superadmin.authMap";
import {tenantManageAuthMap} from "./admin/tenantManage.authMap";
import {companyInfoAuthMap} from "./company/company.authMap";
import {dishAuthMap} from "./foodmenu/dish.authMap";
import {foodMenuAuthMap} from "./foodmenu/foodmenu.authMap";
import {sectionAuthMap} from "./foodmenu/section.authMap";
import {miscAuthMap} from "./misc/misc.authMap";
import {allergyAuthMap} from "./reference/allergy.authMap";
import {dishAddonAuthMap} from "./reference/dishAddon.authMap";
import {dishExtraAuthMap} from "./reference/dishExtra.authMap";
import {ingredientAuthMap} from "./reference/ingredient.authMap";
import {sectionIconAuthMap} from "./reference/sectionIcon.authMap";
import {staffManageAuthMap} from "./staff/staff.authMap";
import {tenantAuthMap} from "./tenant/tenant.authMap";
import {userPreferencesAuthMap} from "./userPref/userPref.authMap";
import {customerAuthMap} from "./customer/customer.authMap";
import {AccessType, RouteType} from "../../../types/pBAC";
import {ZodTypeAny} from "zod";
import {actionAuthMap} from "./pBAC/actionAuthMap";
import {permissionAuthMap} from "./pBAC/permissionAuthMap";
import {moduleAuthMap} from "./pBAC/moduleAuthMap";
import {roleAuthMap} from "./pBAC/roleAuthMap";
import {attendanceAuthMap} from "./attendance/attendance.authMap";
import {testAuthMap} from "./test/testAuthMap";
import {orderAuthMap} from "./order/order.authMap";
import {tenantSettingsAuthMap} from "./tenantSettings/tenantSettings.authMap";
import {cartAuthMap} from "./cart/cart.authMap";
import {dineInAuthMap} from "./dineIn/dineIn.authMap";
import {taxAuthMap} from "./subscription/tax.authMap";
import {countryAuthMap} from "./subscription/country.authMap";
import {subscriptionAuthMap} from "./subscription/subscriptionAuthMap";
import {blockTenantAuthMap} from "./admin/blockTenant.authMap";
import {commonAuthMap} from "./admin/common.authMap";
import {featureAuthMap} from "./subscription/feature.authMap";
import { tableCombReservAuthMap } from "./dineIn/tableCombReserv.authMap";
import { attachmentAuthMap } from "./attachment/attachment.authMap";

export interface AuthMapType {
    routePath: string;
    method: string;
    description: string;
    accessType: AccessType;
    permissionActions: string[];
    isOrOperation: boolean;
    routeType: RouteType;
    isActive: boolean;
    requestParams?: ZodTypeAny;
    requestBody?: ZodTypeAny;
    queryParams?: ZodTypeAny;
}

export const authMapSeedData: AuthMapType[] = [
    ...testAuthMap,
    ...commonAuthMap,
    ...miscAuthMap,
    ...actionAuthMap,
    ...permissionAuthMap,
    ...moduleAuthMap,
    ...roleAuthMap,
    ...countryAuthMap,
    ...superAdminAuthMap,
    ...subTierAuthMap,
    ...subscriptionAuthMap,
    ...featureAuthMap,
    ...taxAuthMap,
    ...blockTenantAuthMap,
    ...tenantManageAuthMap,
    ...sectionIconAuthMap,
    ...dishAddonAuthMap,
    ...dishExtraAuthMap,
    ...ingredientAuthMap,
    ...allergyAuthMap,
    ...tenantAuthMap,
    ...companyInfoAuthMap,
    ...branchAuthMap,
    ...staffManageAuthMap,
    ...userPreferencesAuthMap,
    ...attendanceAuthMap,
    ...foodMenuAuthMap,
    ...sectionAuthMap,
    ...dishAuthMap,
    ...customerAuthMap,
    ...orderAuthMap,
    ...tenantSettingsAuthMap,
    ...cartAuthMap,
    ...dineInAuthMap,
    ...tableCombReservAuthMap,
    ...attachmentAuthMap,
];
