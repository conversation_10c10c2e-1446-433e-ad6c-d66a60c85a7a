import { AccessType, RouteType } from "../../../../types/pBAC";

export const attachmentAuthMap = [
  {
    routePath: "/attachment/upload",
    method: "POST",
    description: "Upload attachment route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["attachment:create"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    isActive: true,
  },
  {
    routePath: "/attachment/view",
    method: "GET",
    description: "Attachment view route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["attachment:read"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    isActive: true,
  },
  {
    routePath: "/attachment/delete",
    method: "DELETE",
    description: "Attachment delete route",
    accessType: AccessType.TENANT_ONLY,
    permissionActions: ["attachment:delete"],
    isOrOperation: false,
    routeType: RouteType.PRIVATE,
    isActive: true,
  },
]