import { AccessType, RouteType } from "../../../../types/pBAC";
import { AuthMapType } from "../authMap";

export const customerAuthMap: AuthMapType[] = [
    {
        routePath: "/customer/create",
        description: "Create a new customer route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:create"],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        method: "POST",
        isActive: true,
    },
    {
        routePath: "/customer/update-customer/*",
        description: "Partial update of customer details route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/customer/delete-customer/*",
        description: "Delete a customer route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "DELETE",
        isActive: true,
    },
    {
        routePath: "/customer/get-customer/*",
        description: "Get customer by ID route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/customer/view_all",
        description: "Get all customers with filters route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/customer/get-customer-email-phone",
        description: "Get customer by email or phone number route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:search"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "GET",
        isActive: true,
    },
    {
        routePath: "/customer/add-address/*",
        description: "Add a new address for a customer route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "POST",
        isActive: true,
    },
    {
        routePath: "/customer/update-address/*",
        description: "Partial update of a customer address route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "PATCH",
        isActive: true,
    },
    {
        routePath: "/customer/delete-address/*",
        description: "Delete a customer address route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: "DELETE",
        isActive: true,
    },
    {
        routePath: "/customer/login",
        description: "Customer login route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:create"],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        method: "POST",
        isActive: true,
    },
    {
        routePath: "/customer/logout",
        description: "Customer logout route",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["customer:create"],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        method: "POST",
        isActive: true,
    },
    {
        routePath: "/customer/revoke/*",
        description: "Revoke customer login from an IP",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
    {
        routePath: "/customer/reinstate/*",
        description: "Reinstate customer login from an IP",
        accessType: AccessType.TENANT_ONLY,
        permissionActions: ["staff-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        method: 'PATCH',
        isActive: true,
    },
];