import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";

export const moduleAuthMap: AuthMapType[] = [
    {
        routePath: "/pBAC/module/view_all",
        method: "GET",
        description: "list all actions",
        accessType: AccessType.ALL,
        permissionActions: ["module:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
];