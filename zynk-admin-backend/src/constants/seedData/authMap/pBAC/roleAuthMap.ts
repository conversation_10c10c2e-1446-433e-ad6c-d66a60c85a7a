import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";

export const roleAuthMap: AuthMapType[] = [
    {
        routePath: "/pBAC/role/view_all",
        method: "GET",
        description: "list all actions",
        accessType: AccessType.ALL,
        permissionActions: ["role:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
];