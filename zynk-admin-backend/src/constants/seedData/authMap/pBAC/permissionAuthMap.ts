import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";

export const permissionAuthMap: AuthMapType[] = [
    {
        routePath: "/pBAC/permission/view_all",
        method: "GET",
        description: "list all actions",
        accessType: AccessType.ALL,
        permissionActions: ["permission:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
];