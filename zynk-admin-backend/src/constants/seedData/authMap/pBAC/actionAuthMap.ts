import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";

export const actionAuthMap: AuthMapType[] = [
    {
        routePath: "/pBAC/action/view_all",
        method: "GET",
        description: "list all actions",
        accessType: AccessType.ALL,
        permissionActions: ["action:list"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
];