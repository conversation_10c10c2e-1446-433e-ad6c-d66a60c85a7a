import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";
import {
    blockTenantFilterSchema,
    blockTenantSchema, blockTypeFilterSchema,
    unblockTenantSchema
} from "../../../../validation/admin/settings/blockTenant.validation";

export const blockTenantAuthMap: AuthMapType[] = [
    // Tenant Block List
    {
        routePath: "/tenant-blocker/list",
        method: "GET",
        description: "Retrieve all blocked tenants entries with optional filters",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["manage-tenant-blocker:list", "manage-tenant-blocker:view", "manage-tenant-blocker:filter", "manage-tenant-blocker:search"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        queryParams: blockTenantFilterSchema

    },
    {
        routePath: "/tenant-blocker/block",
        method: "POST",
        description: "block a tenant",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["manage-tenant-blocker:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: blockTenantSchema
    },
    {
        routePath: "/tenant-blocker/unblock",
        method: "DELETE",
        description: "unblock a tenant",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["manage-tenant-blocker:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        queryParams: unblockTenantSchema
    },
    // Block Type
    {
        routePath: "/tenant-blocker/block_type/list",
        method: "GET",
        description: "list all block types",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["manage-block-type:list", "manage-block-type:view", "manage-block-type:filter", "manage-block-type:search"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        queryParams: blockTypeFilterSchema
    },
    {
        routePath: "/tenant-blocker/block_type/create",
        method: "POST",
        description: "create a block type",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["manage-block-type:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: blockTenantSchema
    },
    {
        routePath: "/tenant-blocker/block_type/patch",
        method: "PATCH",
        description: "patch a block type",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["manage-block-type:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: blockTenantSchema
    },
    {
        routePath: "/tenant-blocker/block_type/delete/*",
        method: "DELETE",
        description: "delete a block type",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["manage-block-type:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: blockTenantSchema
    },
];