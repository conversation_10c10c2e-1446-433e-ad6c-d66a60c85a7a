import {AuthMapType} from "../authMap";
import {AccessType, RouteType} from "../../../../types/pBAC";

export const commonAuthMap: AuthMapType[] = [
    {
        routePath: "/common/response_types/list",
        method: "GET",
        description: "list of response types",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/common/lock_types/list",
        method: "GET",
        description: "list of lock types",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    }
]