import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";

export const tenantManageAuthMap: AuthMapType[] = [
    // Tenant manage routes
    {
        routePath: "/tenant-manage/create",
        method: "POST",
        description: "Tenant create route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tenant-manage:create"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/tenant-manage/patch/*",
        method: "PATCH",
        description: "Tenant patch route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tenant-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/tenant-manage/delete/*",
        method: "DELETE",
        description: "Tenant delete route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tenant-manage:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/tenant-manage/view_all",
        method: "GET",
        description: "Tenant view all route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tenant-manage:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/tenant-manage/view/*",
        method: "GET",
        description: "Tenant view route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tenant-manage:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/tenant-manage/view_sessions/*",
        method: "GET",
        description: "Tenant view sessions route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tenant-manage:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/tenant-manage/revoke/*",
        method: "PATCH",
        description: "Tenant revoke route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tenant-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/tenant-manage/reinstate/*",
        method: "PATCH",
        description: "Tenant reinstate route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["tenant-manage:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
]