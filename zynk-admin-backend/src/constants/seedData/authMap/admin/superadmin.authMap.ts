import {AccessType, RouteType} from "../../../../types/pBAC";
import {AuthMapType} from "../authMap";
import {
    createSuperAdminSchema,
    deleteSuperAdminSchema,
    loginSuperAdminSchema,
    patchSuperAdminSchema,
    revokeSuperAdminSchema,
    viewSuperSessionParams
} from "../../../../validation/admin/superAdmin.validation";

export const superAdminAuthMap: AuthMapType[] = [
    {
        routePath: "/super-admin/login",
        method: "POST",
        description: "Super admin login route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        isActive: true,
        requestBody: loginSuperAdminSchema
    },
    {
        routePath: "/super-admin/create",
        method: "POST",
        description: "Super admin create route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        isActive: true,
        requestBody: createSuperAdminSchema
    },
    {
        routePath: "/super-admin/logout",
        method: "POST",
        description: "Super admin logout route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: [],
        isOrOperation: false,
        routeType: RouteType.PUBLIC,
        isActive: true,
    },
    {
        routePath: "/super-admin/patch/*",
        method: "PATCH",
        description: "Super admin patch route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["superadmin:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestParams: viewSuperSessionParams,
        requestBody: patchSuperAdminSchema,
    },
    {
        routePath: "/super-admin/delete/*",
        method: "DELETE",
        description: "Super admin delete route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["superadmin:delete"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestParams: viewSuperSessionParams,
        requestBody: deleteSuperAdminSchema
    },
    {
        routePath: "/super-admin/revoke",
        method: "PATCH",
        description: "Super admin revoke route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["superadmin:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: revokeSuperAdminSchema,
    },
    {
        routePath: "/super-admin/reinstate",
        method: "PATCH",
        description: "Super admin reinstate route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["superadmin:update"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestBody: revokeSuperAdminSchema,
    },
    {
        routePath: "/super-admin/view_all",
        method: "GET",
        description: "Super admin view all route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["superadmin:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
    },
    {
        routePath: "/super-admin/view_sessions/*",
        method: "GET",
        description: "Super admin view session route",
        accessType: AccessType.ADMIN_ONLY,
        permissionActions: ["superadmin:read"],
        isOrOperation: false,
        routeType: RouteType.PRIVATE,
        isActive: true,
        requestParams: viewSuperSessionParams,
    },
];
