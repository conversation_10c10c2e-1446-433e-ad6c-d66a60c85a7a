import {QueryRunner, Repository} from "typeorm";
import * as argon2 from "argon2";
import {getRepositories} from "../helpers/system/RepositoryHelper.helper";
import {LoginAttempt} from "../models/security/loginAttempt.model";
import {RATE_LIMITED} from "../constants/security/err";

export class LoginRateLimitService {
    private static readonly ATTEMPT_LIMIT = 5;
    private static readonly BLOCK_DURATION_MINUTES = 5;

    static async hashIp(ip: string): Promise<string> {
        return await argon2.hash(ip, {
            salt: Buffer.from(
                process.env.IPLOGATTEMPTSALT || "default_salt_16_bytes"
            ),
        });
    }

    static async isIpRateLimited(
        queryRunner: QueryRunner,
        ip: string
    ): Promise<{ blocked: boolean; remainingMinutes: number; message: string }> {
        try {
            const hashedIp = await this.hashIp(ip);
            const {LoginAttempt} = getRepositories(queryRunner) as {
                LoginAttempt: Repository<LoginAttempt>;
            };

            const blockDurationMs = this.BLOCK_DURATION_MINUTES * 60 * 1000;
            const now = new Date();

            const existingRecord = await LoginAttempt.findOne({
                where: {ipAddress: hashedIp},
            });

            if (!existingRecord) {
                return {blocked: false, remainingMinutes: 0, message: ""};
            }

            const timeSinceLastAttempt =
                now.getTime() - existingRecord.attemptTime.getTime();
            const blockExpired = timeSinceLastAttempt >= blockDurationMs;

            if (
                blockExpired &&
                (existingRecord.failureCount >= this.ATTEMPT_LIMIT ||
                    existingRecord.blocked)
            ) {
                existingRecord.failureCount = 0;
                existingRecord.blocked = false;
                existingRecord.attemptTime = now;
                await LoginAttempt.save(existingRecord);
                return {blocked: false, remainingMinutes: 0, message: ""};
            }

            const isBlocked =
                existingRecord.failureCount >= this.ATTEMPT_LIMIT ||
                existingRecord.blocked;

            if (isBlocked && !blockExpired) {
                const remainingMs = blockDurationMs - timeSinceLastAttempt;
                const remainingMinutes = Math.max(1, remainingMs / (60 * 1000));

                return {
                    blocked: true,
                    remainingMinutes,
                    message: `IP blocked: ${existingRecord.failureCount} failures. Try again after 5 minutes`,
                };
            }

            return {blocked: false, remainingMinutes: 0, message: ""};
        } catch (error) {
            throw error;
        }
    }

    static async checkAndLogAttempt(
        queryRunner: QueryRunner,
        ip: string,
        emailAddress: string,
        loginType: "TENANT" | "STAFF" | "USER" | "SUPERADMIN",
        userAgent?: string,
        success: boolean = false,
        userId?: string,
        reason?: string
    ): Promise<{ blocked: boolean; message?: string }> {
        try {
            if (!ip) return {blocked: false};

            const hashedIp = await this.hashIp(ip);

            if (!success) {
                const blockCheck = await this.isIpBlocked(queryRunner, hashedIp);
                if (blockCheck.blocked) {
                    await this.updateAttemptRecord(
                        queryRunner,
                        hashedIp,
                        emailAddress,
                        loginType,
                        false,
                        true,
                        userAgent,
                        RATE_LIMITED,
                        undefined,
                        blockCheck.message
                    );

                    await queryRunner.commitTransaction();

                    return {
                        blocked: true,
                        message: `Too many failed attempts. Try again in ${Math.ceil(
                            blockCheck.remainingMinutes
                        )} minute(s).`,
                    };
                } else {
                    await this.updateAttemptRecord(
                        queryRunner,
                        hashedIp,
                        emailAddress,
                        loginType,
                        success,
                        false,
                        userAgent,
                        reason,
                        userId
                    );
                    await queryRunner.commitTransaction();

                    return {blocked: false};
                }
            }

            await this.updateAttemptRecord(
                queryRunner,
                hashedIp,
                emailAddress,
                loginType,
                success,
                false,
                userAgent,
                reason,
                userId
            );

            return {blocked: false};
        } catch (e) {
            throw e;
        }
    }

    private static async isIpBlocked(
        queryRunner: QueryRunner,
        hashedIp: string
    ): Promise<{ blocked: boolean; remainingMinutes: number; message: string }> {
        try {
            const {LoginAttempt} = getRepositories(queryRunner) as {
                LoginAttempt: Repository<LoginAttempt>;
            };

            const blockDurationMs = this.BLOCK_DURATION_MINUTES * 60 * 1000;
            const now = new Date();

            const existingRecord = await LoginAttempt.findOne({
                where: {ipAddress: hashedIp},
            });

            if (!existingRecord) {
                return {blocked: false, remainingMinutes: 0, message: ""};
            }

            const timeSinceLastAttempt =
                now.getTime() - existingRecord.attemptTime.getTime();
            const blockExpired = timeSinceLastAttempt >= blockDurationMs;

            if (
                blockExpired &&
                (existingRecord.failureCount >= this.ATTEMPT_LIMIT ||
                    existingRecord.blocked)
            ) {
                existingRecord.failureCount = 0;
                existingRecord.blocked = false;
                existingRecord.attemptTime = now;
                await LoginAttempt.save(existingRecord);
                return {blocked: false, remainingMinutes: 0, message: ""};
            }

            const isBlocked =
                existingRecord.failureCount >= this.ATTEMPT_LIMIT ||
                existingRecord.blocked;

            if (isBlocked && !blockExpired) {
                const remainingMs = blockDurationMs - timeSinceLastAttempt;
                const remainingMinutes = Math.max(1, remainingMs / (60 * 1000));

                return {
                    blocked: true,
                    remainingMinutes,
                    message: `IP blocked: ${existingRecord.failureCount} failures`,
                };
            }

            return {blocked: false, remainingMinutes: 0, message: ""};
        } catch (error) {
            throw error;
        }
    }

    public static async updateAttemptRecord(
        queryRunner: QueryRunner,
        hashedIp: string,
        emailAddress: string,
        loginType: "TENANT" | "STAFF" | "USER" | "SUPERADMIN",
        success: boolean,
        blocked: boolean,
        userAgent?: string,
        reason?: string,
        userId?: string,
        metadata?: string
    ): Promise<void> {
        try {
            const {LoginAttempt} = getRepositories(queryRunner) as {
                LoginAttempt: Repository<LoginAttempt>;
            };

            let existingRecord = await LoginAttempt.findOne({
                where: {ipAddress: hashedIp},
            });

            const now = new Date();

            if (existingRecord) {
                existingRecord.emailAddress = emailAddress || "UNKNOWN";
                existingRecord.loginType = loginType;
                existingRecord.success = success;
                existingRecord.blocked = blocked;
                existingRecord.userAgent = userAgent;
                existingRecord.reason = reason;
                existingRecord.userId = userId;
                existingRecord.metadata = metadata;
                existingRecord.attemptTime = now;

                if (success) {
                    existingRecord.failureCount = 0;
                    existingRecord.blocked = false;
                    existingRecord.reason = "";
                } else if (!blocked) {
                    existingRecord.failureCount = (existingRecord.failureCount || 0) + 1;
                }

                await LoginAttempt.save(existingRecord);
            } else {
                const newRecord = LoginAttempt.create({
                    ipAddress: hashedIp,
                    emailAddress: emailAddress || "UNKNOWN",
                    loginType,
                    success,
                    blocked,
                    userAgent,
                    reason,
                    userId,
                    metadata,
                    attemptTime: now,
                    failureCount: success ? 0 : 1,
                });

                await LoginAttempt.save(newRecord);
            }
        } catch (error) {
            throw error;
        }
    }

    static async resetIpAttempts(
        queryRunner: QueryRunner,
        ip: string
    ): Promise<void> {
        if (!ip) return;

        try {
            const hashedIp = await this.hashIp(ip);

            const {LoginAttempt} = getRepositories(queryRunner) as {
                LoginAttempt: Repository<LoginAttempt>;
            };

            const existingRecord = await LoginAttempt.findOne({
                where: {ipAddress: hashedIp},
            });

            if (existingRecord) {
                existingRecord.failureCount = 0;
                existingRecord.blocked = false;
                existingRecord.attemptTime = new Date();
                await LoginAttempt.save(existingRecord);
            }
        } catch (error) {
            console.error("Error resetting IP attempts:", error);
        }
    }
}
