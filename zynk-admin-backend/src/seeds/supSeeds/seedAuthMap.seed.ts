import {QueryRunner, Repository} from "typeorm";
import log from "../../helpers/system/logger.helper"
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {AuthenticationMapper} from "../../models/authMap/AuthenticationMapper.model";
import {authMapSeedData} from "../../constants/seedData/authMap/authMap";
import {AccessType, RouteType} from "../../types/pBAC";
import {APIError} from "../../utils/errorHandler";
import {zodToJsonSchema} from "zod-to-json-schema";
import {PermActionNotFoundDS} from "../../exceptions/seeding/supImpl/PermActionNotFoundDS";
import {SeedingException} from "../../exceptions/seeding/SeedingException";
import {INTERNAL_SERVER_ERROR} from "../../constants/STATUS_CODES";
import {PermissionAction} from "../../models/pBAC/PermissionAction";
import {PermNotFoundDS} from "../../exceptions/seeding/supImpl/PermNotFoundDS";
import {ActionNotFoundDS} from "../../exceptions/seeding/supImpl/ActionNotFoundDS";
import {Permission} from "../../models/pBAC/Permission.model";
import {Action} from "../../models/pBAC/Action.model";

export const seedAuthMap = async (qRunner: QueryRunner) => {
    try {
        log.info("Seeding Authentication Mapper...");
        const {AuthenticationMapper: AuthMapRepo, Permission, Action, PermissionAction} = getRepositories(qRunner) as {
            AuthenticationMapper: Repository<AuthenticationMapper>;
            Permission: Repository<Permission>;
            Action: Repository<Action>;
            PermissionAction: Repository<PermissionAction>;
        };

        const authMapCount = await AuthMapRepo.count();
        if (authMapCount > 0) {
            log.warn("AuthenticationMapper data already exists in the database. Existing entries will be updated.");
        }

        const authMapsToSave: AuthenticationMapper[] = [];

        for (const seed of authMapSeedData) {
            let authMap = await AuthMapRepo.findOne({
                where: {routePath: seed.routePath, method: seed.method.toUpperCase() || "GET"},
            });

            if (seed.permissionActions && seed.permissionActions.length > 0) {
                for (const permAction of seed.permissionActions) {
                    const [permissionName, actionName] = permAction.split(':');
                    if (!permissionName || !actionName) {
                        throw new PermActionNotFoundDS(permAction);
                    }

                    const permission = await Permission.findOne({
                        where: {name: permissionName},
                    });
                    if (!permission) {
                        throw new PermNotFoundDS(permissionName);
                    }

                    const action = await Action.findOne({
                        where: {name: actionName},
                    });
                    if (!action) {
                        throw new ActionNotFoundDS(actionName);
                    }

                    const permissionAction = await PermissionAction.findOne({
                        where: {
                            permission: {permissionId: permission.permissionId},
                            action: {actionId: action.actionId},
                        },
                        relations: ['permission', 'action'],
                    });

                    if (!permissionAction) {
                        throw new PermActionNotFoundDS(`${permissionName}:${actionName}`);
                    }
                }
            }

            if (authMap) {
                authMap.description = seed.description;
                authMap.accessType = seed.accessType || AccessType.ALL;
                authMap.permissionActions = seed.permissionActions;
                authMap.isOrOperation = seed.isOrOperation || false;
                authMap.routeType = seed.routeType || RouteType.PRIVATE;
                authMap.isActive = seed.isActive !== undefined ? seed.isActive : true;
                authMap.requestParams = seed.requestParams ? zodToJsonSchema(seed.requestParams) : null;
                authMap.requestBody = seed.requestBody ? zodToJsonSchema(seed.requestBody) : null;
                authMap.queryParams = seed.queryParams ? zodToJsonSchema(seed.queryParams) : null;
                log.info(`AuthenticationMapper ${seed.routePath} (${seed.method}): Updated`);
            } else {
                authMap = AuthMapRepo.create({
                    routePath: seed.routePath,
                    method: seed.method.toUpperCase() || "GET",
                    description: seed.description,
                    accessType: seed.accessType || AccessType.ALL,
                    permissionActions: seed.permissionActions,
                    isOrOperation: seed.isOrOperation || false,
                    routeType: seed.routeType || RouteType.PRIVATE,
                    isActive: seed.isActive !== undefined ? seed.isActive : true,
                    requestParams: seed.requestParams ? zodToJsonSchema(seed.requestParams) : null,
                    requestBody: seed.requestBody ? zodToJsonSchema(seed.requestBody) : null,
                    queryParams: seed.queryParams ? zodToJsonSchema(seed.queryParams) : null,
                });
                log.info(`AuthenticationMapper ${seed.routePath} (${seed.method}): Created`);
            }

            authMapsToSave.push(authMap);
        }

        if (authMapsToSave.length > 0) {
            await AuthMapRepo.save(authMapsToSave);
        }

        log.info("AuthenticationMapper data seeded successfully.");
    } catch (error) {
        log.error("Error seeding AuthenticationMapper:", error);
        if (error instanceof SeedingException) throw error;
        throw new APIError(500, "Failed to seed AuthenticationMapper data.");
    }
};