import {Query<PERSON><PERSON><PERSON>, Repository} from "typeorm";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Action} from "../../models/pBAC/Action.model";
import {Permission} from "../../models/pBAC/Permission.model";
import log from "../../helpers/system/logger.helper";
import {actionsSeedData} from "../../constants/seedData/pBAC/actionSeedData";
import {permissionSeedsData} from "../../constants/seedData/pBAC/permissionSeedData";
import {APIError} from "../../utils/errorHandler";
import {INTERNAL_SERVER_ERROR} from "../../constants/STATUS_CODES";
import {SeedingException} from "../../exceptions/seeding/SeedingException";
import {Module} from "../../models/pBAC/Module.model";
import {moduleSeedData} from "../../constants/seedData/pBAC/moduleSeedData";
import {AccessType, hasSufficientAccess} from "../../types/pBAC";
import {PermissionAction} from "../../models/pBAC/PermissionAction";
import {rolesData} from "../../constants/seedData/pBAC/roleSeedData";
import {Role} from "../../models/pBAC/role/Role.model";
import {RolePermissionAction} from "../../models/pBAC/role/RolePermissionAction";
import {ModuleNotFoundDS} from "../../exceptions/seeding/supImpl/ModuleNotFoundDS";
import {LowAccessPMTypeDS} from "../../exceptions/seeding/supImpl/LowAccessPMTypeDS";
import {ActionNotFoundDS} from "../../exceptions/seeding/supImpl/ActionNotFoundDS";
import {LowAccessPATypeDS} from "../../exceptions/seeding/supImpl/LowAccessPATypeDS";
import {PermNotFoundDS} from "../../exceptions/seeding/supImpl/PermNotFoundDS";
import {MissingPermissionActionDS} from "../../exceptions/seeding/supImpl/MissingPermissionActionDS";
import {LowAccessRPTypeDS} from "../../exceptions/seeding/supImpl/LowAccessRPTypeDS";

export const seedPermissionBAC = async (qRunner: QueryRunner) => {
    try {
        log.info("Seeding pBAC data...");
        await createModules(qRunner);
        log.info("pBAC(Module) data seeded successfully.");
        await createActions(qRunner);
        log.info("pBAC(Action) data seeded successfully.");
        await createPermissions(qRunner);
        log.info("pBAC(Permission) data seeded successfully.");
        await createRoles(qRunner);
        log.info("pBAC(Role) data seeded successfully.");
        log.info("pBAC seed completed.");
    } catch (error) {
        log.error("Error seeding pBAC data:", error);
        throw error;
    }
};

const createModules = async (qRunner: QueryRunner) => {
    try {
        log.info('Seeding pBAC(Module) data...');
        const {Module} = getRepositories(qRunner) as { Module: Repository<Module> };

        const moduleCount = await Module.count();
        if (moduleCount > 0) {
            log.warn("pBAC(Module) data already exists in the database. Existing modules will be updated.");
        }

        const moduleMap = new Map<string, Module>();

        const flattenByLevels = (data: any, parentName: string | null = null, level: number = 0): Array<{
            data: any,
            parentName: string | null,
            level: number
        }> => {
            const result: Array<{ data: any, parentName: string | null, level: number }> = [];

            if (Array.isArray(data)) {
                for (const item of data) {
                    result.push(...flattenByLevels(item, parentName, level));
                }
            } else {
                result.push({data, parentName, level});

                if (data.subModules?.length > 0) {
                    for (const subModule of data.subModules) {
                        result.push(...flattenByLevels(subModule, data.moduleName, level + 1));
                    }
                }
            }

            return result;
        };

        const flatModules = flattenByLevels(moduleSeedData);
        const modulesByLevel = new Map<number, Array<{ data: any, parentName: string | null }>>();

        flatModules.forEach(item => {
            if (!modulesByLevel.has(item.level)) {
                modulesByLevel.set(item.level, []);
            }
            modulesByLevel.get(item.level)!.push({data: item.data, parentName: item.parentName});
        });

        const maxLevel = Math.max(...Array.from(modulesByLevel.keys()));

        for (let level = 0; level <= maxLevel; level++) {
            const levelModules = modulesByLevel.get(level) || [];
            if (levelModules.length === 0) continue;

            const modulesToSave: Module[] = [];

            for (const {data, parentName} of levelModules) {
                let module = await Module.findOne({
                    where: {moduleName: data.moduleName},
                    relations: ['parentModule'],
                });

                if (module) {
                    module.description = data.moduleDescription;
                    module.accessType = data.accessType;
                    module.isActive = true;

                    if (parentName) {
                        const parent = moduleMap.get(parentName);
                        if (parent && module.parentModule?.moduleName !== parentName) {
                            module.parentModule = parent;
                        }
                    }
                    // else if (module.parentModule) {
                    //     //     module.parentModule = null;
                    //     // }

                    log.info(`Module ${data.moduleName}: Updated`);
                } else {
                    module = Module.create();
                    module.moduleName = data.moduleName;
                    module.description = data.moduleDescription;
                    module.accessType = data.accessType;
                    module.isActive = true;

                    if (parentName) {
                        const parent = moduleMap.get(parentName);
                        if (parent) {
                            module.parentModule = parent;
                        }
                    }

                    log.info(`Module ${data.moduleName}: Created`);
                }

                modulesToSave.push(module);
                moduleMap.set(data.moduleName, module);
            }

            if (modulesToSave.length > 0) {
                await Module.save(modulesToSave);
            }
        }

    } catch (error) {
        log.error('Error seeding pBAC(Module) data:', error);
        if (error instanceof SeedingException) throw error;
        throw new APIError(INTERNAL_SERVER_ERROR, "Failed to seed pBAC(Module) data.");
    }
};

const createActions = async (qRunner: QueryRunner) => {
    try {
        log.info("Seeding pBAC(Action) data...");
        const {Action} = getRepositories(qRunner) as { Action: Repository<Action> };

        const actionCount = await Action.count();
        if (actionCount > 0) {
            log.warn("pBAC(Action) data already exists in the database. Existing actions will be updated.");
        }

        const actionsToSave: Action[] = [];

        for (const actionData of actionsSeedData) {
            let action = await Action.findOne({
                where: {name: actionData.name},
            });

            if (action) {
                action.description = actionData.description;
                log.info(`Action ${actionData.name}: Updated`);
            } else {
                action = Action.create({
                    name: actionData.name,
                    description: actionData.description,
                });
                log.info(`Action ${actionData.name}: Created`);
            }

            actionsToSave.push(action);
        }

        if (actionsToSave.length > 0) {
            await Action.save(actionsToSave);
        }

    } catch (error) {
        log.error("Error seeding pBAC(Action) data:", error);
        if (error instanceof SeedingException) throw error;
        throw new APIError(INTERNAL_SERVER_ERROR, "Failed to seed pBAC(Action) data.");
    }
};

const createPermissions = async (
    qRunner: QueryRunner,
) => {
    try {
        log.info("Seeding pBAC(Permission) data...");
        const {Permission, Module, Action} = getRepositories(qRunner) as {
            Permission: Repository<Permission>;
            Module: Repository<Module>;
            Action: Repository<Action>;
        };

        const permissionCount = await Permission.count();
        if (permissionCount > 0) {
            log.warn("pBAC(Permission) data already exists in the database. Existing permissions will be updated.");
        }

        const permissionsToSave: Permission[] = [];

        for (const perm of permissionSeedsData) {
            let permission = await Permission.findOne({
                where: {name: perm.name},
                relations: ['module', 'actions'],
            });

            if (permission) {
                permission.description = perm.description;
                permission.accessType = perm.accessType || AccessType.ALL;
                log.info(`Permission ${perm.name}: Updated`);
            } else {
                permission = Permission.create({
                    name: perm.name,
                    description: perm.description,
                    accessType: perm.accessType || AccessType.ALL,
                });
                log.info(`Permission ${perm.name}: Created`);
            }

            if (perm.moduleName) {
                const module = await Module.findOne({where: {moduleName: perm.moduleName}});
                if (!module) {
                    throw new ModuleNotFoundDS(perm.moduleName);
                }
                if (!hasSufficientAccess(permission.accessType, module.accessType)) {
                    throw new LowAccessPMTypeDS(
                        perm.name,
                        permission.accessType,
                        module.moduleName,
                        module.accessType
                    );
                }
                permission.module = module;
            }
            // else if (permission.module) {
            //     permission.module = null;
            // }

            if (perm.actions && perm.actions.length > 0) {
                permission.actions = await Promise.all(perm.actions.map(async (actionName) => {
                    const action = await Action.findOne({
                        where: {name: actionName},
                    });
                    if (!action) {
                        throw new ActionNotFoundDS(actionName);
                    }
                    if (!hasSufficientAccess(permission.accessType, action.accessType)) {
                        throw new LowAccessPATypeDS(
                            perm.name,
                            permission.accessType,
                            action.name,
                            action.accessType
                        );
                    }
                    return action;
                }));
            } else if (permission.actions && permission.actions.length > 0) {
                permission.actions = [];
            }

            permissionsToSave.push(permission);
        }

        if (permissionsToSave.length > 0) {
            await Permission.save(permissionsToSave);
        }

    } catch (error) {
        log.error("Error seeding pBAC(Permission) data:", error);
        if (error instanceof SeedingException) throw error;
        throw new APIError(INTERNAL_SERVER_ERROR, "Failed to seed pBAC(Permission) data.");
    }
};

const createRoles = async (
    qRunner: QueryRunner,
): Promise<void> => {
    try {
        log.info("Seeding pBAC(Role) data...");
        const {Role, RolePermissionAction, PermissionAction, Permission, Action} = getRepositories(qRunner) as {
            Role: Repository<Role>;
            RolePermissionAction: Repository<RolePermissionAction>;
            PermissionAction: Repository<PermissionAction>;
            Permission: Repository<Permission>;
            Action: Repository<Action>;
        };

        const roleCount = await Role.count();
        if (roleCount > 0) {
            log.warn("pBAC(Role) data already exists in the database. Existing roles will be updated.");
        }

        for (const roleData of rolesData) {
            let role = await Role.findOne({
                where: {name: roleData.name},
                relations: ['rolePermissionActions', 'rolePermissionActions.permissionAction']
            });

            if (role) {
                role.description = roleData.description;
                role.accessType = roleData.accessType || AccessType.ALL;
                log.info(`Role ${roleData.name}: Updated`);
            } else {
                role = Role.create({
                    name: roleData.name,
                    description: roleData.description,
                    accessType: roleData.accessType || AccessType.ALL,
                });
                log.info(`Role ${roleData.name}: Created`);
            }

            const savedRole = await Role.save(role);

            if (role.rolePermissionActions && role.rolePermissionActions.length > 0) {
                await RolePermissionAction.delete({
                    role: {roleId: savedRole.roleId},
                });
                savedRole.rolePermissionActions = [];
            }

            for (const permData of roleData.permissions) {
                const basePerm = await Permission.findOne({
                    where: {name: permData.permission},
                    relations: ['actions'],
                });
                if (!basePerm) throw new PermNotFoundDS(permData.permission);

                for (const actionName of permData.actions) {
                    const actionExists = basePerm.actions.some(
                        action => action.name === actionName
                    );
                    if (!actionExists) {
                        throw new MissingPermissionActionDS(permData.permission, actionName);
                    }
                }

                if (!hasSufficientAccess(savedRole.accessType, basePerm.accessType)) {
                    throw new LowAccessRPTypeDS(
                        basePerm.name,
                        basePerm.accessType,
                        savedRole.name,
                        savedRole.accessType,
                    );
                }

                for (const actionName of permData.actions) {
                    const action = await Action.findOne({
                        where: {name: actionName},
                    });
                    if (!action) throw new ActionNotFoundDS(actionName);

                    let permissionAction = await PermissionAction.findOne({
                        where: {
                            permission: {permissionId: basePerm.permissionId},
                            action: {actionId: action.actionId},
                        },
                        relations: ['permission', 'action'],
                    });

                    if (!permissionAction) {
                        permissionAction = PermissionAction.create({
                            permission: basePerm,
                            action: action,
                        });
                        permissionAction = await PermissionAction.save(permissionAction);
                    }

                    const rolePermissionAction = RolePermissionAction.create({
                        role: savedRole,
                        permissionAction: permissionAction,
                    });

                    await RolePermissionAction.save(rolePermissionAction);
                }
            }
        }
    } catch (error) {
        log.error("Error seeding pBAC(Role) data:", error);
        if (error instanceof SeedingException) throw error;
        throw new APIError(INTERNAL_SERVER_ERROR, "Failed to seed pBAC(Role) data.");
    }
};