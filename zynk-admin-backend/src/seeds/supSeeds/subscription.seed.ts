import {Query<PERSON><PERSON><PERSON>, Repository} from "typeorm";
import {Permission} from "../../models/pBAC/Permission.model";
import {Feature} from "../../models/subscription/feature/feature.model";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {featuresSeedData} from "../../constants/seedData/subscription/featuresSeedData";
import {AccessType, hasSufficientAccess, OperationType, PermActionModelType} from "../../types/pBAC";
import {Action} from "../../models/pBAC/Action.model";
import {PermissionAction} from "../../models/pBAC/PermissionAction";
import {FeaturePermissionAction} from "../../models/subscription/feature/FeaturePermissionAction.model";
import log from "../../helpers/system/logger.helper";
import {FeaturePAFailedEx} from "../../exceptions/seeding/supImpl/FeaturePAFailedEx";
import {SeedingException} from "../../exceptions/seeding/SeedingException";
import {PermNotFoundDS} from "../../exceptions/seeding/supImpl/PermNotFoundDS";
import {LowAccessFeatureTypeDS} from "../../exceptions/seeding/supImpl/LowAccessFeatureTypeDS";
import {ActionNotFoundDS} from "../../exceptions/seeding/supImpl/ActionNotFoundDS";
import {APIError} from "../../utils/errorHandler";
import {INTERNAL_SERVER_ERROR} from "../../constants/STATUS_CODES";
import {SubscriptionTier} from "../../models/subscription/subTier/subscriptiontier.model";
import {subscriptionTierSeedData} from "../../constants/seedData/subscription/subscriptionTierSeedData";
import {FeatureNotFoundEx} from "../../exceptions/seeding/supImpl/FeatureNotFoundEx";
import {Tax} from "../../models/subscription/tax/tax.model";
import {taxSeedData} from "../../constants/seedData/subscription/taxSeedData";
import {PermActionNotFoundDS} from "../../exceptions/seeding/supImpl/PermActionNotFoundDS";

export const seedSubscription = async (qRunner: QueryRunner) => {
    await seedFeatures(qRunner);
    await seedSubscriptionTier(qRunner);
    await seedTax(qRunner);
};
const seedFeatures = async (qRunner: QueryRunner) => {
    try {
        const {
            Feature,
            PermissionAction,
            FeaturePermissionAction,
            Permission,
        } = getRepositories(qRunner) as {
            Feature: Repository<Feature>,
            PermissionAction: Repository<PermissionAction>,
            FeaturePermissionAction: Repository<FeaturePermissionAction>,
            Permission: Repository<Permission>,
        };

        const featureCount = await Feature.count();
        if (featureCount > 0) {
            log.warn("Feature data already exists in the database. Existing features will be updated.");
        }

        const featureMap = new Map<string, Feature>();

        for (const featureData of featuresSeedData) {
            let feature = await Feature.findOne({
                where: {name: featureData.name},
                relations: [
                    'featurePermissionActions',
                    'featurePermissionActions.permissionAction',
                    'featurePermissionActions.permissionAction.permission',
                    'featurePermissionActions.permissionAction.action'
                ],
            });

            if (feature) {
                feature.description = featureData.description;
                feature.isActive = true;
                log.info(`Feature ${featureData.name}: Updated`);
            } else {
                feature = Feature.create({
                    name: featureData.name,
                    description: featureData.description,
                    isActive: true,
                });
                log.info(`Feature ${featureData.name}: Created`);
            }

            const savedFeature = await Feature.save(feature);

            if (featureData.permissions && featureData.permissions.length > 0) {
                const permActions: PermActionModelType[] = [];

                for (const perm of featureData.permissions) {
                    const foundPermission = await Permission.findOne({
                        where: {name: perm.permission},
                        relations: ['actions'],
                    });
                    if (!foundPermission) {
                        throw new PermNotFoundDS(perm.permission);
                    }

                    if (!hasSufficientAccess(AccessType.TENANT_ONLY, foundPermission.accessType)) {
                        throw new LowAccessFeatureTypeDS(
                            featureData.name,
                            AccessType.TENANT_ONLY,
                            foundPermission.name,
                            foundPermission.accessType
                        );
                    }

                    for (const actionName of perm.actions) {
                        const foundAction = foundPermission.actions?.find((a: Action) => a.name === actionName);
                        if (!foundAction) {
                            throw new ActionNotFoundDS(actionName);
                        }

                        const permissionAction = await PermissionAction.findOne({
                            where: {
                                permission: {permissionId: foundPermission.permissionId},
                                action: {actionId: foundAction.actionId},
                            },
                            relations: ['permission', 'action'],
                        });

                        if (!permissionAction) {
                            throw new PermActionNotFoundDS(`${perm.permission}:${actionName}`);
                        }

                        permActions.push({
                            actionId: foundAction.actionId,
                            permissionId: foundPermission.permissionId,
                            operationType: OperationType.PLUS,
                        });
                    }
                }

                const {success, ...rest} = await savedFeature.updatePermissionAction(
                    permActions,
                    PermissionAction,
                    FeaturePermissionAction,
                );
                if (!success) {
                    throw new FeaturePAFailedEx(savedFeature.name);
                }

                await Feature.save(savedFeature);
            } else if (feature.featurePermissionActions && feature.featurePermissionActions.length > 0) {
                await FeaturePermissionAction.delete({
                    feature: {featureId: savedFeature.featureId},
                });
                savedFeature.featurePermissionActions = [];
                await Feature.save(savedFeature);
            }

            featureMap.set(savedFeature.name, savedFeature);
        }

        log.info("Feature data seeded successfully.");
        return featureMap;
    } catch (error) {
        log.error("Error seeding Subscription(feature)", error);
        if (error instanceof SeedingException) throw error;
        throw new APIError(INTERNAL_SERVER_ERROR, "Failed to seed Subscription(feature) data.");
    }
};

const seedSubscriptionTier = async (qRunner: QueryRunner) => {
    try {
        const {SubscriptionTier, Feature} = getRepositories(qRunner) as {
            SubscriptionTier: Repository<SubscriptionTier>;
            Feature: Repository<Feature>;
        };

        const subTierCount = await SubscriptionTier.count();
        if (subTierCount > 0) {
            log.warn("SubscriptionTier data already exists in the database. Existing subscription tiers will be updated.");
        }

        const subscriptionTiersToSave: SubscriptionTier[] = [];

        for (const subscription of subscriptionTierSeedData) {
            let subscriptionTier = await SubscriptionTier.findOne({
                where: {name: subscription.name},
                relations: ['features'],
            });

            if (subscriptionTier) {
                subscriptionTier.description = subscription.description;
                subscriptionTier.isActive = true;
                subscriptionTier.price = subscription.price;
                subscriptionTier.tierType = subscription.subscriptionTierType;
                subscriptionTier.validityDuration = subscription.validityDuration;
                log.info(`SubscriptionTier ${subscription.name}: Updated`);
            } else {
                subscriptionTier = SubscriptionTier.create({
                    name: subscription.name,
                    description: subscription.description,
                    isActive: true,
                    price: subscription.price,
                    tierType: subscription.subscriptionTierType,
                    validityDuration: subscription.validityDuration,
                    features: [],
                });
                log.info(`SubscriptionTier ${subscription.name}: Created`);
            }

            subscriptionTier.features = [];
            if (subscription.features && subscription.features.length > 0) {
                for (const featureName of subscription.features) {
                    const foundFeature = await Feature.findOne({
                        where: {name: featureName},
                    });
                    if (!foundFeature) {
                        throw new FeatureNotFoundEx(featureName);
                    }
                    subscriptionTier.features.push(foundFeature);
                }
            }

            subscriptionTiersToSave.push(subscriptionTier);
        }

        if (subscriptionTiersToSave.length > 0) {
            await SubscriptionTier.save(subscriptionTiersToSave);
        }

        log.info("SubscriptionTier data seeded successfully.");
    } catch (error) {
        log.error("Error seeding Subscription(SubscriptionTier)", error);
        if (error instanceof SeedingException) throw error;
        throw new APIError(INTERNAL_SERVER_ERROR, "Failed to seed Subscription(SubscriptionTier) data.");
    }
};

export const seedTax = async (qRunner: QueryRunner) => {
    try {
        const {Tax} = getRepositories(qRunner) as { Tax: Repository<Tax> };

        const taxCount = await Tax.count();
        if (taxCount > 0) {
            log.warn("Tax data already exists in the database. Existing tax entries will be updated.");
        }

        const taxesToSave: Tax[] = [];

        for (const taxData of taxSeedData) {
            let tax = await Tax.findOne({
                where: {
                    countryCode: taxData.country,
                    stateCode: taxData.state,
                    upperLimit: taxData.upperLimit,
                    lowerLimit: taxData.lowerLimit,
                    isCentral: taxData.isCentral,
                },
            });

            if (tax) {
                tax.description = taxData.description;
                tax.taxGovId = taxData.taxGovId;
                tax.isActive = taxData.isActive !== undefined ? taxData.isActive : true;
                tax.isCentral = taxData.isCentral;
                tax.countryCode = taxData.country;
                tax.stateCode = taxData.state;
                tax.taxPercentage = taxData.taxPercentage;
                tax.upperLimit = taxData.upperLimit;
                tax.lowerLimit = taxData.lowerLimit;
                log.info(`Tax ${taxData.name}: Updated`);
            } else {
                tax = Tax.create({
                    name: taxData.name,
                    description: taxData.description,
                    taxGovId: taxData.taxGovId,
                    isActive: taxData.isActive !== undefined ? taxData.isActive : true,
                    isCentral: taxData.isCentral,
                    countryCode: taxData.country,
                    stateCode: taxData.state,
                    taxPercentage: taxData.taxPercentage,
                    upperLimit: taxData.upperLimit,
                    lowerLimit: taxData.lowerLimit,
                });
                log.info(`Tax ${taxData.name}: Created`);
            }

            taxesToSave.push(tax);
        }

        if (taxesToSave.length > 0) {
            await Tax.save(taxesToSave);
        }

        log.info("Tax data seeded successfully.");
    } catch (error) {
        log.error("Error seeding Subscription(Tax)", error);
        if (error instanceof SeedingException) throw error;
        throw new APIError(INTERNAL_SERVER_ERROR, "Failed to seed Subscription(Tax) data.");
    }
};