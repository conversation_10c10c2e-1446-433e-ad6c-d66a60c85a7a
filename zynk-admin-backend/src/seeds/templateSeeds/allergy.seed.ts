import {QueryRunner, Repository} from "typeorm";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Allergy, ColorPrefs} from "../../models/reference/allergy.model";
import log from "../../helpers/system/logger.helper";
import {SeedingAllergiesEx} from "../../exceptions/seeding/templateImp/SeedingAllergiesEx";
import { Branch } from "../../models/company/branch.model";

export const seedAllergies = async (qRunner: QueryRunner, branch: Branch) => {
    try {
        const {Allergy} = getRepositories(qRunner) as {
            Allergy: Repository<Allergy>;
        };

        const existingAllergies = await Allergy.count();
        if (existingAllergies > 0) {
            log.warn(
                `Allergies already exist (${existingAllergies} found). Skipping seeding.`
            );
            return;
        }

        const allergies = [
            {
                name: "Peanuts",
                description: "Allergic reaction to peanuts or peanut-based products.",
                colorPreference: ColorPrefs.PRIMARY,
                branch
            },
            {
                name: "Tree Nuts",
                description: "Allergic reaction to almonds, walnuts, cashews, and other tree nuts.",
                colorPreference: ColorPrefs.SECONDARY,
                branch
            },
            {
                name: "Milk",
                description: "Allergic reaction to dairy products containing milk proteins.",
                colorPreference: ColorPrefs.TERTIARY,
                branch
            },
            {
                name: "Eggs",
                description: "Allergic reaction to egg proteins, often found in baked goods.",
                colorPreference: ColorPrefs.PRIMARY,
                branch
            },
            {
                name: "Wheat",
                description: "Allergic reaction to wheat proteins, including gluten.",
                colorPreference: ColorPrefs.SECONDARY,
                branch
            },
            {
                name: "Soy",
                description: "Allergic reaction to soy or soy-based products.",
                colorPreference: ColorPrefs.TERTIARY,
                branch
            },
            {
                name: "Fish",
                description: "Allergic reaction to fish like salmon, tuna, or cod.",
                colorPreference: ColorPrefs.PRIMARY,
                branch
            },
            {
                name: "Shellfish",
                description: "Allergic reaction to crustaceans like shrimp, crab, or lobster.",
                colorPreference: ColorPrefs.SECONDARY,
                branch
            },
            {
                name: "Sesame",
                description: "Allergic reaction to sesame seeds or sesame oil.",
                colorPreference: ColorPrefs.TERTIARY,
                branch
            },
        ];

        const result = await Allergy.save(allergies);
        log.info(`Successfully seeded ${result.length} allergies.`);
    } catch (error) {
        log.error("Error seeding allergies:", error);
        throw new SeedingAllergiesEx();
    }
};