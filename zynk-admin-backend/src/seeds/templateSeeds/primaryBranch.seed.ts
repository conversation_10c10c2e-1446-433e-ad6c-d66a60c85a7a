import {QueryRunner, Repository} from "typeorm";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Branch} from "../../models/company/branch.model";
import {BranchContacts} from "../../models/company/branchcontact.model";
import {BusinessHour} from "../../models/common/businesshour.model";
import {BusinessHourSlot} from "../../models/common/businesshourslot.model";
import {SpecialDay} from "../../models/common/specialday.model";
import {branchSettingsHelper} from "../../helpers/settings/tenant/branchSettings.helper";
import log from "../../helpers/system/logger.helper";
import {DuplicateDayDS} from "../../exceptions/seeding/templateImp/DuplicateDayDS";
import {SeedingException} from "../../exceptions/seeding/SeedingException";
import {APIError} from "../../utils/errorHandler";
import {INTERNAL_SERVER_ERROR} from "../../constants/STATUS_CODES";
import { PRIMARY_BRANCH_NOT_FOUND } from "../../constants/tenant/company/err";

export const seedPrimaryBranch = async (qRunner: QueryRunner) => {
    try {
        const {
            Branch,
            BranchContacts,
            BusinessHour,
            BusinessHourSlot,
            SpecialDay,
        } = getRepositories(qRunner) as {
            Branch: Repository<Branch>;
            BranchContacts: Repository<BranchContacts>;
            BusinessHour: Repository<BusinessHour>;
            BusinessHourSlot: Repository<BusinessHourSlot>;
            SpecialDay: Repository<SpecialDay>;
        };

        const existingBranches = await Branch.count();
        if (existingBranches > 0) {
            log.warn(
                `Branches already exist (${existingBranches} found). Skipping seeding.`
            );

            const primaryBranch = await Branch.findOne({
                where: {
                    isPrimary: true
                }
            })

            if(!primaryBranch)
                throw new Error(PRIMARY_BRANCH_NOT_FOUND)
            
            return primaryBranch;
        }

        const branchData = {
            name: "Portsmouth Branch",
            isActive: true,
            location: {lat: 50, lng: 50},
            country: "United Kingdom",
            city: "Portsmouth",
            state: "Hampshire",
            postalCode: "PO123456",
            streetName: "123 Street, 456 Lane",
            houseNumber: "H-1234",
            apartment: "AP-123",
            locationName: "Portsmouth",
            isPrimary: true,
        };

        const newBranch = Branch.create(branchData);
        await Branch.save(newBranch);

        const branchContacts = [
            {
                name: "Sam Wilson",
                emailAddress: "<EMAIL>",
                phoneNumber: "+44 123456789",
                faxNumber: "FX1234",
                branch: newBranch,
            },
            {
                name: "Jane Wilson",
                emailAddress: "<EMAIL>",
                phoneNumber: "+************",
                faxNumber: "FX1234S",
                branch: newBranch,
            },
        ];

        const newBranchContacts = branchContacts.map((contact) =>
            BranchContacts.create(contact)
        );
        await BranchContacts.save(newBranchContacts);
        newBranch.branchContacts = newBranchContacts;

        const newBusinessHour = BusinessHour.create();
        await BusinessHour.save(newBusinessHour);

        const businessHourSlots = [
            {
                days: {
                    M: true,
                    T: true,
                    W: true,
                    Th: true,
                    F: true,
                    Sa: false,
                    Su: false,
                } as Partial<Record<string, boolean>>,
                is24Hours: false,
                firstSeating: "09:30:00",
                lastSeating: "20:00:00",
                businessHour: newBusinessHour,
            },
            {
                days: {
                    M: false,
                    T: false,
                    W: false,
                    Th: false,
                    F: false,
                    Sa: true,
                    Su: true,
                } as Partial<Record<string, boolean>>,
                is24Hours: false,
                firstSeating: "09:30:00",
                lastSeating: "20:00:00",
                businessHour: newBusinessHour,
            },
        ];

        const businessHourDays = new Map<string, boolean>();
        businessHourSlots.forEach((slot) => {
            Object.entries(slot.days).forEach(([day, isActive]) => {
                if (isActive && businessHourDays.get(day)) {
                    log.error(`Duplicate day ${day} in business hours`);
                    throw new DuplicateDayDS(day);
                }
                if (isActive) {
                    businessHourDays.set(day, true);
                }
            });
        });

        const newBusinessHourSlots = businessHourSlots.map((slot) =>
            BusinessHourSlot.create(slot)
        );
        await BusinessHourSlot.save(newBusinessHourSlots);
        newBusinessHour.slots = newBusinessHourSlots;

        const specialDays = [
            {
                eventName: "Branch Holiday",
                startTime: "2025-05-06T08:00:00Z",
                endTime: "2025-05-06T10:00:00Z",
                availability: false,
                businessHour: newBusinessHour,
            },
            {
                eventName: "Some function",
                startTime: "2025-05-07T14:00:00Z",
                endTime: "2025-05-07T16:30:00Z",
                availability: true,
                businessHour: newBusinessHour,
            },
        ];

        const newSpecialDays = specialDays.map((sDay) => SpecialDay.create(sDay));
        await SpecialDay.save(newSpecialDays);
        newBusinessHour.specialDays = newSpecialDays;

        await BusinessHour.save(newBusinessHour);
        newBranch.businessHour = newBusinessHour;

        await Branch.save(newBranch);
        await branchSettingsHelper(newBranch, qRunner);

        log.info(`Successfully seeded Primary Branch`);

        return newBranch
    } catch (error) {
        log.error('Error seeding Primary Branch data:', error);
        if (error instanceof SeedingException) throw error;
        throw new APIError(INTERNAL_SERVER_ERROR, "Failed to seed primary branch data.");
    }
};