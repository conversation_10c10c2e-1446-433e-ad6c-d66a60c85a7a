import {QueryRunner} from "typeorm";
import {seedPrimaryBranch} from "./primaryBranch.seed";
import {seedSectionIcons} from "./sectionIcon.seed";
import {seedAllergyColors} from "./allergyColor.seed";
import {seedAllergies} from "./allergy.seed";
import {seedDishAddons} from "./addon.seed";
import {seedDishExtras} from "./extra.seed";
import {seedIngredients} from "./ingredient.seed";
import { seedOptionalFoodMenu } from "./optional/foodMenu.seed";
import { seedOptionalFloorsAndTables } from "./optional/floorTable.seed";

export const SeedTemplateDB = async (qRunner: QueryRunner) => {
    const branch = await seedPrimaryBranch(qRunner)
    await seedSectionIcons(qRunner, branch);
    await seedAllergyColors(qRunner, branch);
    await seedAllergies(qRunner, branch);
    await seedDishAddons(qRunner, branch)
    await seedDishExtras(qRunner, branch)
    await seedIngredients(qRunner, branch)
    await seedOptionalFoodMenu(qRunner)
    await seedOptionalFloorsAndTables(qRunner, branch)
};