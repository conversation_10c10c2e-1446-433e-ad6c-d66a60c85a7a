import { Query<PERSON><PERSON>ner, Repository } from "typeorm";
import { Section } from "../../../models/foodmenu/section.model";
import { Dish } from "../../../models/foodmenu/dish.model";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Ingredient } from "../../../models/reference/ingredient.model";
import { Allergy } from "../../../models/reference/allergy.model";
import log from "../../../helpers/system/logger.helper";
import { DishIngredient } from "../../../models/foodmenu/dish_ingredient.model";
import { DishSide } from "../../../models/foodmenu/custom/dish_side.model";
import { DishBeverage } from "../../../models/foodmenu/custom/dish_bev.model";
import { DishDessert } from "../../../models/foodmenu/custom/dish_dessert.model";
import { DishSize } from "../../../models/foodmenu/custom/dishsize.model";
import { Customization } from "../../../models/foodmenu/customization.model";
import { DishExclusion } from "../../../models/foodmenu/custom/dishexclusion.model";
import { CookingStyle } from "../../../models/foodmenu/custom/cookingstyle.model";
import { Spiciness } from "../../../models/foodmenu/custom/spiciness.model";
import { DishAddon } from "../../../models/foodmenu/custom/addon.model";
import { DishExtra } from "../../../models/foodmenu/custom/extra.model";

export const seedDishesIntoSections = async (
  Dish: Repository<Dish>,
  newSections: Section[],
  queryRunner: QueryRunner
) => {
  try {
    const {
      Ingredient,
      Allergy,
      DishAddon,
      DishExtra,
      DishSide,
      DishBeverage,
      DishDessert,
    } = getRepositories(queryRunner) as {
      Ingredient: Repository<Ingredient>;
      Allergy: Repository<Allergy>;
      DishAddon: Repository<DishAddon>;
      DishExtra: Repository<DishExtra>;
      DishSide: Repository<DishSide>;
      DishBeverage: Repository<DishBeverage>;
      DishDessert: Repository<DishDessert>;
    };

    const starterSection = newSections[0];
    const burgerSection = newSections[1];
    const pizzaSection = newSections[2];

    const dessertSection = newSections[3];
    const mandiSection = newSections[4];
    const drinksSection = newSections[5];

    const commonIngredients = await Ingredient.find();

    if (!commonIngredients) {
      log.error("Could not find Ingredient model?");
      throw new Error("Could not find Ingredient model?");
    }

    if (commonIngredients && commonIngredients.length < 1) {
      log.error("No Ingredients found, were ingredients seeded?");
      throw new Error("No Ingredients found, were ingredients seeded?");
    }

    const commonAllergies = await Allergy.find();

    if (!commonAllergies) {
      log.error("Could not find Allergy model?");
      throw new Error("Could not find Allergy model?");
    }

    if (commonAllergies && commonAllergies.length < 1) {
      log.error("No Allergies found, were allergies seeded?");
      throw new Error("No Allergies found, were allergies seeded?");
    }

    await seedStarterSection(
      starterSection,
      commonIngredients,
      commonAllergies,
      Dish,
      queryRunner
    );

    const commonAddons = await DishAddon.find();

    if (!commonAddons) {
      log.error("Addon model not found?");
      throw new Error("Addon model not found?");
    }

    if (commonAddons && commonAddons.length < 1) {
      log.error("No addons found, were addons seeded?");
      throw new Error("No addons found, were addons seeded?");
    }

    const commonExtras = await DishExtra.find();

    if (!commonExtras) {
      log.error("Extra model not found?");
      throw new Error("Extra model not found?");
    }

    if (commonExtras && commonExtras.length < 1) {
      log.error("No extras found, were extras seeded?");
      throw new Error("No extras found, were extras seeded?");
    }

    await seedDessertSection(
      dessertSection,
      commonIngredients,
      commonAllergies,
      Dish,
      queryRunner
    );

    await seedDrinksSection(
      drinksSection,
      commonIngredients,
      commonAllergies,
      Dish,
      queryRunner
    );

    // do this at last
    const allDishSides = await DishSide.find();
    const allDishBeverages = await DishBeverage.find();
    const allDishDesserts = await DishDessert.find();

    await seedBurgerSection(
      burgerSection,
      commonIngredients,
      commonAllergies,
      commonAddons,
      commonExtras,
      Dish,
      queryRunner,
      allDishSides,
      allDishBeverages,
      allDishDesserts
    );

    await seedPizzaSection(
      pizzaSection,
      commonIngredients,
      commonAllergies,
      commonAddons,
      commonExtras,
      Dish,
      queryRunner,
      allDishSides,
      allDishBeverages,
      allDishDesserts
    );

    await seedMandiSection(
      mandiSection,
      commonIngredients,
      commonAllergies,
      commonAddons,
      commonExtras,
      Dish,
      queryRunner,
      allDishSides,
      allDishBeverages,
      allDishDesserts
    );
  } catch (error) {
    throw error;
  }
};

export const seedStarterSection = async (
  section: Section,
  commonIngredients: Ingredient[],
  commonAllergies: Allergy[],
  Dish: Repository<Dish>,
  qRunner: QueryRunner
) => {
  try {
    const { DishSide, DishBeverage, DishDessert, DishIngredient} = getRepositories(
      qRunner
    ) as {
      DishSide: Repository<DishSide>;
      DishBeverage: Repository<DishBeverage>;
      DishDessert: Repository<DishDessert>;
      DishIngredient: Repository<DishIngredient>
    };

    const starterDishes: Partial<Dish>[] = [
      {
        pictureUrl: "https://cdn.imgchest.com/files/7kzcanv9po7.jpg",
        name: "Tomato Basil Soup",
        description:
          "A creamy, velvety soup made with ripe tomatoes, fresh basil, and a touch of cream.",
        price: 5.0,
        dietaryInfo: ["vegetarian"],
        tags: ["side"],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: false,
        preparationTime: 15,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/4nec85ebze4.jpeg",
        name: "Caprese Salad",
        description:
          "Fresh mozzarella, ripe tomatoes, and basil drizzled with balsamic glaze.",
        price: 5.2,
        dietaryInfo: ["vegetarian", "gluten-free"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: false,
        preparationTime: 10,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/7ogcbvlrary.jpg",
        name: "Stuffed Mushrooms",
        description:
          "Button mushrooms filled with a savory blend of breadcrumbs, garlic, and herbs.",
        price: 5.2,
        dietaryInfo: ["vegetarian"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: false,
        preparationTime: 20,
        section,
      },
    ];

    for (const starterDish of starterDishes) {
      const newDish = Dish.create({
        ...starterDish,
      });

      await Dish.save(newDish);

      await dishTagHelper(DishSide, DishBeverage, DishDessert, newDish);
    }
  } catch (error) {
    throw error;
  }
};

export const seedBurgerSection = async (
  section: Section,
  commonIngredients: Ingredient[],
  commonAllergies: Allergy[],
  commonAddons: DishAddon[],
  commonExtras: DishExtra[],
  Dish: Repository<Dish>,
  qRunner: QueryRunner,
  allDishSides: DishSide[],
  allDishBeverages: DishBeverage[],
  allDishDesserts: DishDessert[]
) => {
  try {
    const { Customization, DishSize, DishExclusion, CookingStyle, Spiciness, DishIngredient} =
      getRepositories(qRunner) as {
        Customization: Repository<Customization>;
        DishSize: Repository<DishSize>;
        DishExclusion: Repository<DishExclusion>;
        CookingStyle: Repository<CookingStyle>;
        Spiciness: Repository<Spiciness>;
        DishIngredient: Repository<DishIngredient>
      };

    const burgerDishes: Partial<Dish>[] = [
      {
        pictureUrl: "https://cdn.imgchest.com/files/7bwckb936j7.jpg",
        name: "Classic Beef Burger",
        description:
          "Juicy beef patty with lettuce, tomato, onion, and special sauce on a toasted bun.",
        price: 6.49,
        dietaryInfo: [],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: true,
        preparationTime: 12,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/y2pck6p3qn7.jpg",
        name: "Veggie Delight Burger",
        description:
          "Flavorful veggie patty with avocado, sprouts, and chipotle mayo on a whole-grain bun.",
        price: 6.99,
        dietaryInfo: ["vegetarian"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: true,
        preparationTime: 10,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/7pjcq6abnx7.jpg",
        name: "Spicy Chicken Burger",
        description:
          "Crispy chicken patty with spicy slaw, pickles, and sriracha mayo on a brioche bun.",
        price: 6.79,
        dietaryInfo: ["spicy"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: true,
        preparationTime: 15,
        section,
      },
    ];

    for (const burgerDish of burgerDishes) {
      const customization = Customization.create({
        maxSelection: 4,
        enforceMaxSelection: true,
        dishAddons: commonAddons.slice(0, 2),
        dishExtras: commonExtras.slice(0, 2),
        dishSizes: [
          DishSize.create({
            name: "Regular",
            price: 0.0,
            isDefault: true,
          }),
          DishSize.create({
            name: "Large",
            price: 2.0,
            isDefault: false,
          }),
        ],
        dishExclusions: [
          DishExclusion.create({
            name: "No Onion",
          }),
          DishExclusion.create({
            name: "No Pickles",
          }),
        ],
        cookingStyles: [
          CookingStyle.create({
            name: "Medium",
          }),
          CookingStyle.create({
            name: "Well Done",
          }),
        ],
        spiciness: [
          Spiciness.create({
            name: "Mild",
            price: 0.0,
          }),
          Spiciness.create({
            name: "Hot",
            price: 0.5,
          }),
        ],
        dishSides: allDishSides,
        dishBeverages: allDishBeverages,
        dishDesserts: allDishDesserts,
      });

      await Customization.save(customization);

      const newDish = Dish.create({
        ...burgerDish,
        customization,
      });

      await Dish.save(newDish);
    }
  } catch (error) {
    log.error("Error seeding burger section:", error);
    throw error;
  }
};

export const seedPizzaSection = async (
  section: Section,
  commonIngredients: Ingredient[],
  commonAllergies: Allergy[],
  commonAddons: DishAddon[],
  commonExtras: DishExtra[],
  Dish: Repository<Dish>,
  qRunner: QueryRunner,
  allDishSides: DishSide[],
  allDishBeverages: DishBeverage[],
  allDishDesserts: DishDessert[]
) => {
  try {
    const { Customization, DishSize, DishExclusion, CookingStyle, Spiciness, DishIngredient} =
      getRepositories(qRunner) as {
        DishSide: Repository<DishSide>;
        DishBeverage: Repository<DishBeverage>;
        DishDessert: Repository<DishDessert>;
        Customization: Repository<Customization>;
        DishSize: Repository<DishSize>;
        DishExclusion: Repository<DishExclusion>;
        CookingStyle: Repository<CookingStyle>;
        Spiciness: Repository<Spiciness>;
        DishIngredient: Repository<DishIngredient>
      };

    const pizzaDishes: Partial<Dish>[] = [
      {
        pictureUrl: "https://cdn.imgchest.com/files/yxkcz2nvgw7.jpg",
        name: "Margherita Pizza",
        description: "Tomato sauce, mozzarella, and basil on a thin crust.",
        price: 6.49,
        dietaryInfo: ["vegetarian"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: true,
        preparationTime: 12,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/yvdcw6rzdgy.jpeg",
        name: "Pepperoni Pizza",
        description:
          "Tomato sauce, mozzarella, and pepperoni on a hand-tossed crust.",
        price: 6.99,
        dietaryInfo: ["spicy"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: true,
        preparationTime: 14,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/49zc2zaebqy.jpg",
        name: "Veggie Pizza",
        description:
          "Tomato sauce, mozzarella, peppers, mushrooms, and olives.",
        price: 6.79,
        dietaryInfo: ["vegetarian"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: true,
        preparationTime: 13,
        section,
      },
    ];

    for (const pizzaDish of pizzaDishes) {
      const customization = Customization.create({
        maxSelection: 4,
        enforceMaxSelection: true,
        dishAddons: commonAddons.slice(0, 2),
        dishExtras: commonExtras.slice(0, 2),
        dishSizes: [
          DishSize.create({
            name: "Medium",
            price: 0.0,
            isDefault: true,
          }),
          DishSize.create({
            name: "Large",
            price: 1.5,
            isDefault: false,
          }),
        ],
        dishExclusions: [
          DishExclusion.create({
            name: "No Onions",
          }),
          DishExclusion.create({
            name: "No Olives",
          }),
        ],
        cookingStyles: [
          CookingStyle.create({
            name: "Regular Bake",
          }),
          CookingStyle.create({
            name: "Crispy Bake",
          }),
        ],
        spiciness: [
          Spiciness.create({
            name: "Mild",
            price: 0.0,
          }),
          Spiciness.create({
            name: "Spicy",
            price: 0.3,
          }),
        ],
        dishSides: allDishSides,
        dishBeverages: allDishBeverages,
        dishDesserts: allDishDesserts,
      });

      await Customization.save(customization);

      const newDish = Dish.create({
        ...pizzaDish,
        customization,
      });

      await Dish.save(newDish);
    }
  } catch (error) {
    log.error("Error seeding pizza section:", error);
    throw error;
  }
};

export const seedDessertSection = async (
  section: Section,
  commonIngredients: Ingredient[],
  commonAllergies: Allergy[],
  Dish: Repository<Dish>,
  qRunner: QueryRunner
) => {
  try {
    const { DishSide, DishBeverage, DishDessert, DishIngredient} = getRepositories(
      qRunner
    ) as {
      DishSide: Repository<DishSide>;
      DishBeverage: Repository<DishBeverage>;
      DishDessert: Repository<DishDessert>;
      DishIngredient: Repository<DishIngredient>
    };

    const dessertDishes: Partial<Dish>[] = [
      {
        pictureUrl: "https://cdn.imgchest.com/files/y2pck6px257.jpg",
        name: "Vanilla Ice Cream",
        description: "Creamy vanilla soft serve, perfect for a sweet treat.",
        price: 2.29,
        dietaryInfo: ["vegetarian"],
        tags: ["dessert"],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: false,
        preparationTime: 5,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/4jdcvbgrke4.jpg",
        name: "Chocolate Brownie",
        description: "Rich, fudgy brownie with a gooey center, served warm.",
        price: 2.49,
        dietaryInfo: ["vegetarian"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: false,
        preparationTime: 8,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/7lxcp69wer7.jpg",
        name: "Apple Pie",
        description: "Crispy pastry filled with sweet, spiced apple filling.",
        price: 2.19,
        dietaryInfo: ["vegetarian"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: false,
        preparationTime: 10,
        section,
      },
    ];

    for (const dessertDish of dessertDishes) {
      const newDish = Dish.create({
        ...dessertDish,
      });

      await Dish.save(newDish);

      await dishTagHelper(DishSide, DishBeverage, DishDessert, newDish);
    }
  } catch (error) {
    log.error("Error seeding dessert section:", error);
    throw error;
  }
};

export const dishTagHelper = async (
  DishSide: Repository<DishSide>,
  DishBeverage: Repository<DishBeverage>,
  DishDessert: Repository<DishDessert>,
  actualDish: Dish
) => {
  try {
    if (actualDish.tags && actualDish.tags.length > 0) {
      if (actualDish.tags.includes("side")) {
        const dishSide = DishSide.create({
          dish: actualDish,
        });
        actualDish.asSide = dishSide;

        await DishSide.save(dishSide);
      }

      if (actualDish.tags.includes("beverage")) {
        const dishBeverage = DishBeverage.create({
          dish: actualDish,
        });
        actualDish.asBeverage = dishBeverage;

        await DishBeverage.save(dishBeverage);
      }

      if (actualDish.tags.includes("dessert")) {
        const dishDessert = DishDessert.create({
          dish: actualDish,
        });
        actualDish.asDessert = dishDessert;

        await DishDessert.save(dishDessert);
      }
    }
  } catch (error) {
    throw error;
  }
};

export const seedMandiSection = async (
  section: Section,
  commonIngredients: Ingredient[],
  commonAllergies: Allergy[],
  commonAddons: DishAddon[],
  commonExtras: DishExtra[],
  Dish: Repository<Dish>,
  qRunner: QueryRunner,
  allDishSides: DishSide[],
  allDishBeverages: DishBeverage[],
  allDishDesserts: DishDessert[]
) => {
  try {
    const { Customization, DishSize, DishExclusion, CookingStyle, Spiciness, DishIngredient} =
      getRepositories(qRunner) as {
        Customization: Repository<Customization>;
        DishSize: Repository<DishSize>;
        DishExclusion: Repository<DishExclusion>;
        CookingStyle: Repository<CookingStyle>;
        Spiciness: Repository<Spiciness>;
        DishIngredient: Repository<DishIngredient>
      };

    const mandiDishes: Partial<Dish>[] = [
      {
        pictureUrl: "https://cdn.imgchest.com/files/yrgcn68wwx4.jpg",
        name: "Chicken Mandi",
        description:
          "Tender chicken with fragrant rice, spiced with traditional Yemeni flavors, served with yogurt sauce.",
        price: 6.99,
        dietaryInfo: ["spicy"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: true,
        preparationTime: 20,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/yd5cebkx9d4.jpg",
        name: "Lamb Mandi",
        description:
          "Succulent lamb with aromatic rice, cooked with Middle Eastern spices, served with tomato chutney.",
        price: 7.49,
        dietaryInfo: ["spicy"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: true,
        preparationTime: 25,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/7lxcp69wwp7.jpg",
        name: "Chicken Biryani",
        description:
          "Spiced chicken layered with basmati rice, infused with saffron and herbs, served with raita.",
        price: 6.49,
        dietaryInfo: ["spicy"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: true,
        preparationTime: 18,
        section,
      },
    ];

    for (const mandiDish of mandiDishes) {
      const customization = Customization.create({
        maxSelection: 4,
        enforceMaxSelection: true,
        dishAddons: commonAddons.slice(0, 2),
        dishExtras: commonExtras.slice(0, 2),
        dishSizes: [
          DishSize.create({
            name: "Regular",
            price: 0.0,
            isDefault: true,
          }),
          DishSize.create({
            name: "Large",
            price: 1.5,
            isDefault: false,
          }),
        ],
        dishExclusions: [
          DishExclusion.create({
            name: "No Yogurt Sauce",
          }),
          DishExclusion.create({
            name: "No Chutney",
          }),
        ],
        cookingStyles: [
          CookingStyle.create({
            name: "Traditional",
          }),
          CookingStyle.create({
            name: "Extra Spiced",
          }),
        ],
        spiciness: [
          Spiciness.create({
            name: "Mild",
            price: 0.0,
          }),
          Spiciness.create({
            name: "Hot",
            price: 0.3,
          }),
        ],
        dishSides: allDishSides,
        dishBeverages: allDishBeverages,
        dishDesserts: allDishDesserts,
      });

      await Customization.save(customization);

      const newDish = Dish.create({
        ...mandiDish,
        customization,
      });

      await Dish.save(newDish);
    }
  } catch (error) {
    log.error("Error seeding mandi section:", error);
    throw error;
  }
};

export const seedDrinksSection = async (
  section: Section,
  commonIngredients: Ingredient[],
  commonAllergies: Allergy[],
  Dish: Repository<Dish>,
  qRunner: QueryRunner
) => {
  try {
    const { DishSide, DishBeverage, DishDessert, DishIngredient} = getRepositories(
      qRunner
    ) as {
      DishSide: Repository<DishSide>;
      DishBeverage: Repository<DishBeverage>;
      DishDessert: Repository<DishDessert>;
      DishIngredient: Repository<DishIngredient>
    };

    const drinkDishes: Partial<Dish>[] = [
      {
        pictureUrl: "https://cdn.imgchest.com/files/yvdcw6renly.jpg",
        name: "Cola",
        description: "Refreshing carbonated cola served ice-cold.",
        price: 1.79,
        dietaryInfo: [],
        tags: ["beverage"],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: false,
        preparationTime: 2,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/7ogcbvlqvly.jpg",
        name: "Iced Coffee",
        description: "Chilled coffee with a splash of milk, served over ice.",
        price: 2.29,
        dietaryInfo: [],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: false,
        preparationTime: 5,
        section,
      },
      {
        pictureUrl: "https://cdn.imgchest.com/files/4jdcvbgrbr4.jpg",
        name: "Vanilla Milkshake",
        description: "Creamy vanilla milkshake blended to perfection.",
        price: 2.49,
        dietaryInfo: ["vegetarian"],
        tags: [],
        dishIngredients: [
          DishIngredient.create({
            amount: 0.1,
            ingredient: commonIngredients[0]
          }),
          DishIngredient.create({
            amount: 0.5,
            ingredient: commonIngredients[1]
          })
        ],
        allergies: [commonAllergies[0], commonAllergies[1]],
        isCustomizable: false,
        preparationTime: 4,
        section,
      },
    ];

    for (const drinkDish of drinkDishes) {
      const newDish = Dish.create({
        ...drinkDish,
      });

      await Dish.save(newDish);

      await dishTagHelper(DishSide, DishBeverage, DishDessert, newDish);
    }
  } catch (error) {
    log.error("Error seeding drinks section:", error);
    throw error;
  }
};
