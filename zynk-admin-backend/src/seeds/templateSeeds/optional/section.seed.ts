import { QueryRunner, Repository } from "typeorm";
import { Section } from "../../../models/foodmenu/section.model";
import { FoodMenu } from "../../../models/foodmenu/foodmenu.model";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { SectionIcon } from "../../../models/reference/sectionicon.model";

export const seedSectionsIntoFoodMenu = async (
  SectionRepo: Repository<Section>,
  foodMenu: FoodMenu,
  queryRunner: QueryRunner
) => {
  try {
    const { SectionIcon } = getRepositories(queryRunner) as {
      SectionIcon: Repository<SectionIcon>;
    };

    const starterIcon = await SectionIcon.findOne({
      where: {
        iconName: "Starters",
      },
    });

    const burgerIcon = await SectionIcon.findOne({
        where: {
            iconName: "Burgers & Sandwiches"
        }
    })

    const pizzaIcon = await SectionIcon.findOne({
        where: {
            iconName: "Pizzas & Pastas"
        }
    })

    const dessertIcon = await SectionIcon.findOne({
        where: {
            iconName: "Desserts & Sweets"
        }
    })

    const mandiIcon = await SectionIcon.findOne({
        where: {
            iconName: "Mandi & Biryani"
        }
    })

    const drinksIcon = await SectionIcon.findOne({
        where: {
            iconName: "Drinks & Refreshments"
        }
    })

    const sectionSeedData: Partial<Section>[] = [
      {
      pictureUrls: [
        "https://placehold.co/256x256",
        "https://placehold.co/300x300",
      ],
      name: "Starters",
      description:
        "A selection of light dishes to begin your meal, including soups, salads, and appetizers.",
      visibility: true,
      foodMenu,
      sectionIcon: starterIcon!,
      },
      {
      pictureUrls: [
        "https://placehold.co/256x256",
        "https://placehold.co/300x300",
      ],
      name: "Burgers & Sandwiches",
      description:
        "Enjoy a variety of burgers and sandwiches with options for customization, including choice of bread, toppings, sauces, and sides.",
      visibility: true,
      foodMenu,
      sectionIcon: burgerIcon!,
      },
      {
      pictureUrls: [
        "https://placehold.co/256x256",
        "https://placehold.co/300x300",
      ],
      name: "Pizzas & Pastas",
      description:
        "A delicious assortment of classic and specialty pizzas, along with a variety of pasta dishes prepared with rich sauces and fresh ingredients.",
      visibility: true,
      foodMenu,
      sectionIcon: pizzaIcon!,
      },
      {
      pictureUrls: [
        "https://placehold.co/256x256",
        "https://placehold.co/300x300",
      ],
      name: "Desserts",
      description:
        "A sweet selection of desserts including cakes, pastries, ice creams, and traditional treats to end your meal on a delightful note.",
      visibility: true,
      foodMenu,
      sectionIcon: dessertIcon!,
      },
      {
      pictureUrls: [
        "https://placehold.co/256x256",
        "https://placehold.co/300x300",
      ],
      name: "Mandi & Biriyani",
      description:
        "Savor aromatic rice dishes such as Mandi and Biriyani, featuring tender meats, fragrant spices, and traditional accompaniments.",
      visibility: true,
      foodMenu,
      sectionIcon: mandiIcon!,
      },
      {
      pictureUrls: [
        "https://placehold.co/256x256",
        "https://placehold.co/300x300",
      ],
      name: "Drinks",
      description:
        "A refreshing selection of beverages including soft drinks, juices, mocktails, and specialty refreshments to complement your meal.",
      visibility: true,
      foodMenu,
      sectionIcon: drinksIcon!,
      },
    ];

    let sampleSections: Section[] = [];

    for (const sectionData of sectionSeedData){
        const newSection = SectionRepo.create({
            ...sectionData,
        });

        await SectionRepo.save(newSection)
        sampleSections.push(newSection)
    }

    return sampleSections

  } catch (error) {
    throw error;
  }
};
