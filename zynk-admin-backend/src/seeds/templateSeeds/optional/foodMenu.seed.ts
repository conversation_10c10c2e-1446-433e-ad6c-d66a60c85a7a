import { NextFunction, Request, Response } from "express";
import { QueryRunner, Repository } from "typeorm";
import log from "../../../helpers/system/logger.helper";
import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { FoodMenu } from "../../../models/foodmenu/foodmenu.model";
import { Section } from "../../../models/foodmenu/section.model";
import { Dish } from "../../../models/foodmenu/dish.model";
import { Branch } from "../../../models/company/branch.model";
import { seedSectionsIntoFoodMenu } from "./section.seed";
import { seedDishesIntoSections } from "./dish.seed";

export const seedOptionalFoodMenu = async (queryRunner: QueryRunner) => {
  try {
    const { FoodMenu, Section, Dish, Branch } = getRepositories(
      queryRunner
    ) as {
      FoodMenu: Repository<FoodMenu>;
      Section: Repository<Section>;
      Dish: Repository<Dish>;
      Branch: Repository<Branch>;
    };

    const primaryBranch = await Branch.findOneBy({
      isPrimary: true,
    });

    if (!primaryBranch) throw new Error("Could not find primary branch!");

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        branch: {
          branchId: primaryBranch.branchId
        }
      }
    })

    if(exFoodMenu){
      await FoodMenu.remove(exFoodMenu)
    }

    const newSampleMenu = FoodMenu.create({
      pictureUrl: "https://placehold.co/256x256",
      isDefault: true,
      name: "Test Menu",
      description:
        "Sample food menu for testing. Contains sample sections and dishes.",
      visibility: true,
      branch: primaryBranch,
    });

    await FoodMenu.save(newSampleMenu);

    log.info("FoodMenu: Test Menu Created!")

    const newSections = await seedSectionsIntoFoodMenu(Section, newSampleMenu, queryRunner);

    log.info("Sample sections seeded successfully!")

    await seedDishesIntoSections(Dish, newSections, queryRunner);

    log.info("Sample dishes seeded successfully!")

  } catch (error) {
    log.error("Error seeding optional food menu!");
    throw error;
  }
};
