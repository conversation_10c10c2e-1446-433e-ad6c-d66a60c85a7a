import { QueryRunner, Repository } from "typeorm";

import { getRepositories } from "../../../helpers/system/RepositoryHelper.helper";
import { Branch } from "../../../models/company/branch.model";
import { Floor } from "../../../models/tenantSettings/tableReservation/management/floor.model";
import { TableModel } from "../../../models/tenantSettings/tableReservation/management/table.model";
import { TableCombination } from "../../../models/tenantSettings/tableReservation/management/tableCombination.model";
import log from "../../../helpers/system/logger.helper";

export const seedOptionalFloorsAndTables = async (
  qRunner: QueryRunner,
  branch: Branch
) => {
  try {
    const { Floor, TableModel, TableCombination } = getRepositories(
      qRunner
    ) as {
      Floor: Repository<Floor>;
      TableModel: Repository<TableModel>;
      TableCombination: Repository<TableCombination>;
    };
    const exTables = await TableModel.findAndCount({
      where: {
        branch: {
          branchId: branch.branchId,
        },
      },
    });

    const exTableCombinations = await TableCombination.find({
      where: {
        branch: {
          branchId: branch.branchId,
        },
      },
    });

    const exFloors = await Floor.find({
      where: {
        branch: {
          branchId: branch.branchId,
        },
      },
    });

    await Floor.remove(exFloors);

    await TableModel.remove(exTables[0]);

    await TableCombination.remove(exTableCombinations);

    const floorData: Partial<Floor>[] = [
      {
        name: "Floor-1",
        branch,
      },
      {
        name: "Floor-2",
        branch,
      },
      {
        name: "Floor-3",
        branch,
      },
    ];

    const newFloors: Floor[] = [];

    for (const floor of floorData) {
      const exFloor = Floor.create({
        ...floor,
      });

      await Floor.save(exFloor);

      newFloors.push(exFloor);
    }

    const tableData: Partial<TableModel>[] = [
      {
        name: "T1",
        minSeats: 1,
        maxSeats: 2,
        enabled: true,
        availableOnline: true,
        branch,
        location: "Main Area",
        floor: newFloors[0],
      },
      {
        name: "T2",
        minSeats: 1,
        maxSeats: 2,
        enabled: true,
        availableOnline: true,
        branch,
        location: "Main Area",
        floor: newFloors[0],
      },
      {
        name: "P1",
        minSeats: 1,
        maxSeats: 6,
        enabled: true,
        availableOnline: true,
        branch,
        location: "Premium Area",
        floor: newFloors[0],
      },
      {
        name: "T4",
        minSeats: 1,
        maxSeats: 2,
        enabled: true,
        availableOnline: true,
        branch,
        location: "Corner",
        floor: newFloors[0],
      },
      {
        name: "VIP1",
        minSeats: 1,
        maxSeats: 6,
        enabled: true,
        availableOnline: true,
        branch,
        location: "VIP Area",
        floor: newFloors[0],
      },
      {
        name: "T6",
        minSeats: 1,
        maxSeats: 4,
        enabled: true,
        availableOnline: true,
        branch,
        location: "Center",
        floor: newFloors[0],
      },
      {
        name: "T7",
        minSeats: 1,
        maxSeats: 4,
        enabled: true,
        availableOnline: true,
        branch,
        location: "Side Area",
        floor: newFloors[0],
      },
      // Floor 2 Table
      {
        name: "T8",
        minSeats: 1,
        maxSeats: 4,
        enabled: true,
        availableOnline: true,
        branch,
        location: "Upper Floor",
        floor: newFloors[1],
      },

      // Floor 3 Table
      {
        name: "T9",
        minSeats: 1,
        maxSeats: 4,
        enabled: true,
        availableOnline: true,
        branch,
        location: "Top Floor",
        floor: newFloors[2],
      },
    ];

    let newTableCombinationList: TableModel[] = [];

    let iter = 0;

    for (const table of tableData) {
      const newTable = TableModel.create({
        ...table,
      });

      await TableModel.save(newTable);

      if (table.floor === newFloors[0] && iter < 2) {
        newTableCombinationList.push(newTable);
        iter++;
      }
    }

    const newTableCombination = TableCombination.create({
      minSeats: 2,
      maxSeats: 4,
      enabled: true,
      availableOnline: true,
      tables: newTableCombinationList,
      branch,
      floor: newFloors[0],
    });

    await TableCombination.save(newTableCombination);

    log.info("Successfully seeded floors and tables!");

    // if (exTables[1] < 1) {
    //   const tableData: Partial<TableModel>[] = [
    //     {
    //       name: "1",

    //       minSeats: 1,

    //       maxSeats: 4,

    //       enabled: true,

    //       availableOnline: true,
    //       branch
    //     },
    //     {
    //       name: "2",

    //       minSeats: 1,

    //       maxSeats: 4,

    //       enabled: true,

    //       availableOnline: true,
    //       branch
    //     },
    //   ];

    //   let newTableCombinationList: TableModel[] = [];

    //   for (const table of tableData) {
    //     const newTable = TableModel.create({
    //       ...table,
    //     });

    //     await TableModel.save(newTable);

    //     newTableCombinationList.push(newTable);
    //   }

    //   const newTableCombination = TableCombination.create({
    //     minSeats: 2,

    //     maxSeats: 8,

    //     enabled: true,

    //     availableOnline: true,

    //     tables: newTableCombinationList,
    //     branch
    //   });

    //   await TableCombination.save(newTableCombination)
    // } else {
    //   log.info("Tables and Table combinations already exist!");
    // }
  } catch (error) {
    log.error("Error seeding currency units", error);
    throw error;
  }
};
