import {QueryRunner, Repository} from "typeorm";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {Ingredient} from "../../models/reference/ingredient.model";
import log from "../../helpers/system/logger.helper";
import {SeedingIngredientsEx} from "../../exceptions/seeding/templateImp/SeedingIngredientsEx";
import { Branch } from "../../models/company/branch.model";

export const seedIngredients = async (qRunner: QueryRunner, branch: Branch) => {
    try {
        const {Ingredient} = getRepositories(qRunner) as {
            Ingredient: Repository<Ingredient>;
        };

        const existingIngredients = await Ingredient.count();
        if (existingIngredients > 0) {
            log.warn(
                `Ingredients already exist (${existingIngredients} found). Skipping seeding.`
            );
            return;
        }

        const ingredients = [
            {
                name: "Flour",
                description: "All-purpose wheat flour used in baking and cooking.",
                unit: "g",
                branch
            },
            {
                name: "Sugar",
                description: "Granulated white sugar for sweetening dishes.",
                unit: "g",
                branch
            },
            {
                name: "Olive Oil",
                description: "Extra virgin olive oil for cooking and dressings.",
                unit: "ml",
                branch
            },
            {
                name: "Chicken Breast",
                description:
                    "Boneless, skinless chicken breast for grilling or baking.",
                unit: "g",
                branch
            },
            {
                name: "Tomato",
                description: "Fresh tomatoes used in sauces and salads.",
                unit: "unit",
                branch
            },
            {
                name: "Salt",
                description: "Table salt for seasoning dishes.",
                unit: "g",
                branch
            },
            {
                name: "Black Pepper",
                description: "Ground black pepper for seasoning.",
                unit: "g",
                branch
            },
            {
                name: "Milk",
                description: "Whole milk used in cooking and beverages.",
                unit: "ml",
                branch
            },
            {
                name: "Egg",
                description: "Large chicken eggs for baking and cooking.",
                unit: "unit",
                branch
            },
            {
                name: "Butter",
                description: "Unsalted butter for cooking and baking.",
                unit: "g",
                branch
            },
        ];

        const result = await Ingredient.save(ingredients);
        log.info(`Successfully seeded ${result.length} ingredients.`);
    } catch (error) {
        log.error("Error seeding ingredients:", error);
        throw new SeedingIngredientsEx();
    }
};
