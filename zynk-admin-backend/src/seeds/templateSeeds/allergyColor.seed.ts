import {QueryRunner, Repository} from "typeorm";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {AllergyColor} from "../../models/reference/allergyColor.model";
import log from "../../helpers/system/logger.helper";
import {SeedingAllergyColorsEx} from "../../exceptions/seeding/templateImp/SeedingAllergyColorsEx";
import { Branch } from "../../models/company/branch.model";

export const seedAllergyColors = async (qRunner: QueryRunner, branch: Branch) => {
    try {
        const {AllergyColor} = getRepositories(qRunner) as {
            AllergyColor: Repository<AllergyColor>;
        };

        const existingAllergyColors = await AllergyColor.count();
        if (existingAllergyColors > 0) {
            log.warn(
                `Allergy colors already exist (${existingAllergyColors} found). Skipping seeding.`
            );
            return;
        }

        const allergyColors = [
            {
                name: "primary",
                color: "#FF0000", // Red
                branch
            },
            {
                name: "secondary",
                color: "#00FF00", // Green
                branch
            },
            {
                name: "tertiary",
                color: "#0000FF", // Blue
                branch
            },
            {
                name: "morethanone",
                color: "#964B00", // Brown
                branch
            },
        ];

        const result = await AllergyColor.save(allergyColors);
        log.info(`Successfully seeded ${result.length} allergy colors.`);
    } catch (error) {
        log.error("Error seeding allergy colors:", error);
        throw new SeedingAllergyColorsEx();
    }
};