import {QueryRunner, Repository} from "typeorm";
import {SectionIcon} from "../../models/reference/sectionicon.model";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import log from "../../helpers/system/logger.helper";
import {SectionIconsSeedingEx} from "../../exceptions/seeding/templateImp/SectionIconsSeedingEx";
import { Branch } from "../../models/company/branch.model";

export const seedSectionIcons = async (qRunner: QueryRunner, branch: Branch) => {
    try {
        const {SectionIcon} = getRepositories(qRunner) as {
            SectionIcon: Repository<SectionIcon>;
        };

        const existingIcons = await SectionIcon.count()
        if (existingIcons > 0) {
            log.warn(
                `Section icons already exist (${existingIcons} found). Skipping seeding.`
            );
            return;
        }

        const sectionIcons = [
            {
                iconName: "Starters",
                iconImg: "https://cdn.imgchest.com/files/4nec85ew6e4.png",
                branch
            },
            {
                iconName: "Non-Veg Delights",
                iconImg: "https://cdn.imgchest.com/files/yxkcz2nj9z7.png",
                branch
            },
            {
                iconName: "Breads & Flatbreads",
                iconImg: "https://cdn.imgchest.com/files/45xcvezk5o7.png",
                branch
            },
            {
                iconName: "Mandi & Biryani",
                iconImg: "https://cdn.imgchest.com/files/y8xcnpxl6o4.png",
                branch
            },
            {
                iconName: "Burgers & Sandwiches",
                iconImg: "https://cdn.imgchest.com/files/4apc5bzd884.png",
                branch
            },
            {
                iconName: "Pizzas & Pastas",
                iconImg: "https://cdn.imgchest.com/files/49zc2zag8my.png",
                branch
            },
            {
                iconName: "Drinks & Refreshments",
                iconImg: "https://cdn.imgchest.com/files/yvdcw6rb2xy.png",
                branch
            },
            {
                iconName: "Desserts & Sweets",
                iconImg: "https://cdn.imgchest.com/files/46acqzoadm7.png",
                branch
            },
            {
                iconName: "Veg Specials",
                iconImg: "https://cdn.imgchest.com/files/7bwckb9gnj7.png",
                branch
            },
        ];

        await SectionIcon.save(sectionIcons);
    } catch (error) {
        log.error("Error seeding section icons:", error);
        throw new SectionIconsSeedingEx();
    }
};
