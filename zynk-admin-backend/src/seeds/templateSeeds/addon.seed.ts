import {QueryRunner, Repository} from "typeorm";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {DishAddon} from "../../models/foodmenu/custom/addon.model";
import log from "../../helpers/system/logger.helper";
import {SeedingDishAddonsEx} from "../../exceptions/seeding/templateImp/SeedingDishAddonsEx";
import { Branch } from "../../models/company/branch.model";

export const seedDishAddons = async (qRunner: QueryRunner, branch: Branch) => {
    try {
        const {DishAddon} = getRepositories(qRunner) as {
            DishAddon: Repository<DishAddon>;
        };

        const existingDishAddons = await DishAddon.count();
        if (existingDishAddons > 0) {
            log.warn(
                `Dish addons already exist (${existingDishAddons} found). Skipping seeding.`
            );
            return;
        }

        const dishAddons = [
            {
                name: "Extra Cheese",
                price: 2.50,
                imgUrl: 'https://cdn.imgchest.com/files/739cx6z9qd7.jpg',
                branch
            },
            {
                name: "Avocado",
                price: 1.75,
                imgUrl: 'https://cdn.imgchest.com/files/yrgcnzxgql4.jpg',
                branch
            },
            {
                name: "Bacon",
                price: 3.00,
                imgUrl: 'https://cdn.imgchest.com/files/7mmc9ravbx7.jpg',
                branch
            },
            {
                name: "Grilled Chicken",
                price: 4.50,
                imgUrl: 'https://cdn.imgchest.com/files/ye3c2dvam24.jpg',
                branch
            },
            {
                name: "Sautéed Mushrooms",
                price: 2.00,
                imgUrl: 'https://cdn.imgchest.com/files/y8xcnka3og4.jpg',
                branch
            },
            {
                name: "Truffle Oil",
                price: 3.25,
                imgUrl: 'https://cdn.imgchest.com/files/4apc539gaq4.jpg',
                branch
            },
            {
                name: "Jalapeños",
                price: 1.50,
                imgUrl: 'https://cdn.imgchest.com/files/4gdcxp35q24.jpg',
                branch
            },
            {
                name: "Guacamole",
                price: 2.25,
                imgUrl: 'https://cdn.imgchest.com/files/4z9cvwqo5e7.jpg',
                branch
            },
            {
                name: "Fried Egg",
                price: 1.80,
                imgUrl: 'https://cdn.imgchest.com/files/7w6c2v9ld9y.jpg',
                branch
            },
        ];

        const result = await DishAddon.save(dishAddons);
        log.info(`Successfully seeded ${result.length} dish addons.`);
    } catch (error) {
        log.error("Error seeding dish addons:", error);
        throw new SeedingDishAddonsEx();
    }
};