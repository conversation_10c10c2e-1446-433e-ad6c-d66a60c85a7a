import {QueryRunner, Repository} from "typeorm";
import {getRepositories} from "../../helpers/system/RepositoryHelper.helper";
import {DishExtra} from "../../models/foodmenu/custom/extra.model";
import log from "../../helpers/system/logger.helper";
import {SeedingDishExtrasEx} from "../../exceptions/seeding/templateImp/SeedingDishExtrasEx";
import { Branch } from "../../models/company/branch.model";

export const seedDishExtras = async (qRunner: QueryRunner, branch: Branch) => {
    try {
        const {DishExtra} = getRepositories(qRunner) as {
            DishExtra: Repository<DishExtra>;
        };

        const existingDishExtras = await DishExtra.count();
        if (existingDishExtras > 0) {
            log.warn(
                `Dish extras already exist (${existingDishExtras} found). Skipping seeding.`
            );
            return;
        }

        const dishExtras = [
            {
                name: "Side Salad",
                price: 3.50,
                imgUrl: 'https://cdn.imgchest.com/files/yd5cer5pz94.jpeg',
                branch
            },
            {
                name: "French Fries",
                price: 2.75,
                imgUrl: 'https://cdn.imgchest.com/files/7pjcqwnrmp7.jpg',
                branch
            },
            {
                name: "Garlic Bread",
                price: 2.00,
                imgUrl: 'https://cdn.imgchest.com/files/7kzcabp2ea7.jpg',
                branch
            },
            {
                name: "Mashed Potatoes",
                price: 3.00,
                imgUrl: 'https://cdn.imgchest.com/files/yq9c3ekm5m4.jpg',
                branch
            },
            {
                name: "Steamed Vegetables",
                price: 2.50,
                imgUrl: 'https://cdn.imgchest.com/files/yd5cer5pzl4.jpg',
                branch
            },
            {
                name: "Onion Rings",
                price: 3.25,
                imgUrl: 'https://cdn.imgchest.com/files/4jdcvjx8wz4.jpg',
                branch
            },
            {
                name: "Coleslaw",
                price: 1.75,
                imgUrl: 'https://cdn.imgchest.com/files/7bwckd6wjq7.jpg',
                branch
            },
            {
                name: "Mac and Cheese",
                price: 3.75,
                imgUrl: 'https://cdn.imgchest.com/files/y2pckoql927.jpg',
                branch
            },
            {
                name: "Soup of the Day",
                price: 4.00,
                imgUrl: 'https://cdn.imgchest.com/files/7lxcpdalgz7.jpg',
                branch
            },
        ];

        const result = await DishExtra.save(dishExtras);
        log.info(`Successfully seeded ${result.length} dish extras.`);
    } catch (error) {
        log.error("Error seeding dish extras:", error);
        throw new SeedingDishExtrasEx();
    }
};