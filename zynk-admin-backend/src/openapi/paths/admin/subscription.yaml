/subscription/create:
  post:
    tags:
      - Subscription
    summary: Create a new subscription tier
    description: Creates a new subscription tier with the provided name, description, price, and validity period.
    parameters:
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    security:
      - cookieAuth: []
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              name:
                type: string
                minLength: 1
                description: The name of the subscription tier.
              description:
                type: string
                description: A description of the subscription tier.
              price:
                type: number
                description: The price of the subscription tier.
              validityInDays:
                type: number
                description: The validity period of the subscription tier in days.
            required:
              - name
              - description
              - price
              - validityInDays
    responses:
      '201':
        description: Subscription tier created successfully.
      '400':
        description: Invalid request body (e.g., missing or invalid fields).
      '401':
        description: Unauthorized (no valid session or token).
      '409':
        description: Subscription tier name already exists.
      '500':
        description: Server error.
/subscription/view_all:
  get:
    tags:
      - Subscription
    summary: Get all subscription tiers
    description: Retrieves a list of all subscription tiers.
    parameters:
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    security:
      - cookieAuth: []
      - bearerAuth: []
    responses:
      '200':
        description: A list of subscription tiers.
      '401':
        description: Unauthorized (no valid session or token).
      '500':
        description: Server error.
/subscription/view/{subTierId}:
  get:
    tags:
      - Subscription
    summary: Get a subscription tier by ID
    description: Retrieves details of a specific subscription tier by its ID.
    parameters:
      - in: path
        name: subTierId
        required: true
        schema:
          type: string
          format: uuid
        description: The UUID of the subscription tier to retrieve.
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    security:
      - cookieAuth: []
      - bearerAuth: []
    responses:
      '200':
        description: Subscription tier details retrieved successfully.
      '400':
        description: Invalid subTierId.
      '401':
        description: Unauthorized (no valid session or token).
      '404':
        description: Subscription tier not found.
      '500':
        description: Server error.
/subscription/patch/{subTierId}:
  patch:
    tags:
      - Subscription
    summary: Update a subscription tier
    description: Updates the specified subscription tier's name, description, price, or validity period.
    parameters:
      - in: path
        name: subTierId
        required: true
        schema:
          type: string
          format: uuid
        description: The UUID of the subscription tier to update.
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    security:
      - cookieAuth: []
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              name:
                type: string
                minLength: 1
                description: The new name of the subscription tier (optional).
              description:
                type: string
                description: The new description of the subscription tier (optional).
              price:
                type: number
                description: The new price of the subscription tier (optional).
              validityInDays:
                type: number
                description: The new validity period of the subscription tier in days (optional).
    responses:
      '200':
        description: Subscription tier updated successfully.
      '400':
        description: Invalid request body or parameters.
      '401':
        description: Unauthorized (no valid session or token).
      '404':
        description: Subscription tier not found.
      '409':
        description: Subscription tier name already exists.
      '500':
        description: Server error.
/subscription/delete/{subTierId}:
  delete:
    tags:
      - Subscription
    summary: Delete a subscription tier
    description: Deletes a specific subscription tier by its ID.
    parameters:
      - in: path
        name: subTierId
        required: true
        schema:
          type: string
          format: uuid
        description: The UUID of the subscription tier to delete.
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    security:
      - cookieAuth: []
      - bearerAuth: []
    responses:
      '204':
        description: Subscription tier deleted successfully.
      '400':
        description: Invalid subTierId.
      '401':
        description: Unauthorized (no valid session or token).
      '404':
        description: Subscription tier not found.
      '500':
        description: Server error.