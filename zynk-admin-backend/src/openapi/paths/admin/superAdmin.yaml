/super-admin/create:
  post:
    tags:
      - SuperAdmin
    summary: Create a new super admin
    description: Creates a new super admin account with the provided username, email address, and password.
    parameters:
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              username:
                type: string
                minLength: 1
                description: The username for the super admin.
              emailAddress:
                type: string
                format: email
                description: The email address for the super admin.
              password:
                type: string
                minLength: 1
                description: The password for the super admin account.
            required:
              - username
              - emailAddress
              - password
    responses:
      '201':
        description: Super admin created successfully.
      '400':
        description: Invalid request body (e.g., missing or invalid fields).
      '409':
        description: Email address or username already exists.
      '500':
        description: Server error.
/super-admin/login:
  post:
    tags:
      - SuperAdmin
    summary: Log in a super admin
    description: Authenticates a super admin using their email address and password, returning a session token.
    parameters:
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              emailAddress:
                type: string
                format: email
                description: The email address of the super admin.
              password:
                type: string
                minLength: 1
                description: The password of the super admin.
            required:
              - emailAddress
              - password
    responses:
      '200':
        description: Login successful, returns session token.
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: Session token for the authenticated super admin.
      '401':
        description: Invalid email or password.
      '500':
        description: Server error.
/super-admin/logout:
  post:
    tags:
      - SuperAdmin
    summary: Log out a super admin
    description: Invalidates the current session for the authenticated super admin.
    parameters:
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    security:
      - cookieAuth: []
      - bearerAuth: []
    responses:
      '204':
        description: Logout successful, session invalidated.
      '401':
        description: Unauthorized (no valid session).
      '500':
        description: Server error.
/super-admin/patch/{superAdminId}:
  patch:
    tags:
      - SuperAdmin
    summary: Update a super admin
    description: Updates the super admin's email address, username, or password. Requires the old password for verification.
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - in: path
        name: superAdminId
        required: true
        schema:
          type: string
          format: uuid
        description: The UUID of the super admin to update.
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              oldPassword:
                type: string
                minLength: 1
                description: The current password for verification.
              newPassword:
                type: string
                minLength: 1
                description: The new password (optional).
              emailAddress:
                type: string
                format: email
                description: The new email address (optional).
              username:
                type: string
                minLength: 1
                description: The new username (optional).
            required:
              - oldPassword
    responses:
      '200':
        description: Super admin updated successfully.
      '400':
        description: Invalid request body or parameters.
      '401':
        description: Invalid old password or unauthorized.
      '404':
        description: Super admin not found.
      '500':
        description: Server error.
/super-admin/delete/{superAdminId}:
  delete:
    tags:
      - SuperAdmin
    summary: Delete a super admin
    description: Deletes a super admin account after verifying their password.
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - in: path
        name: superAdminId
        required: true
        schema:
          type: string
          format: uuid
        description: The UUID of the super admin to delete.
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              password:
                type: string
                minLength: 1
                description: The password for verification.
            required:
              - password
    responses:
      '204':
        description: Super admin deleted successfully.
      '400':
        description: Invalid request body or parameters.
      '401':
        description: Invalid password or unauthorized.
      '404':
        description: Super admin not found.
      '500':
        description: Server error.
/super-admin/view_all:
  get:
    tags:
      - SuperAdmin
    summary: Get all super admins
    description: Retrieves a list of all super admin accounts.
    parameters:
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    security:
      - cookieAuth: []
      - bearerAuth: []
    responses:
      '200':
        description: A list of super admins.
      '401':
        description: Unauthorized (no valid session).
      '500':
        description: Server error.
/super-admin/view_sessions/{superAdminId}:
  get:
    tags:
      - SuperAdmin
    summary: Get all sessions for a super admin
    description: Retrieves all active sessions for the specified super admin.
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - in: path
        name: superAdminId
        required: true
        schema:
          type: string
          format: uuid
        description: The UUID of the super admin whose sessions are to be retrieved.
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    responses:
      '200':
        description: A list of sessions for the super admin.
      '400':
        description: Invalid superAdminId.
      '401':
        description: Unauthorized (no valid session).
      '404':
        description: Super admin not found.
      '500':
        description: Server error.
/super-admin/revoke:
  patch:
    tags:
      - SuperAdmin
    summary: Revoke a super admin session
    description: Revokes a specific super admin session based on email, password, and IP address.
    parameters:
      - in: header
        name: x-host
        required: false
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    security:
      - cookieAuth: []
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              emailAddress:
                type: string
                format: email
                description: The email address of the super admin.
              password:
                type: string
                minLength: 1
                description: The password for verification.
              ipAddress:
                type: string
                minLength: 1
                description: The IP address associated with the session to revoke.
            required:
              - emailAddress
              - password
              - ipAddress
    responses:
      '204':
        description: Session revoked successfully.
      '400':
        description: Invalid request body.
      '401':
        description: Invalid credentials or unauthorized.
      '404':
        description: Session or super admin not found.
      '500':
        description: Server error.