/pBAC/action/view_all:
  get:
    tags:
      - PermissionBasedAccessControl
    summary: Get all actions
    description: Retrieves a list of all actions in the permission-based access control system.
    parameters:
      - in: header
        name: x-host
        required: true
        schema:
          type: string
        description: A custom header to specify the host or origin of the request (e.g., 'example.com').
    security:
      - cookieAuth: [ ]
      - bearerAuth: [ ]
    responses:
      '200':
        description: A list of actions.
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                    description: The UUID of the action.
                  name:
                    type: string
                    description: The name of the action.
                  description:
                    type: string
                    description: A description of the action.
      '401':
        description: Unauthorized (no valid session or token).
      '500':
        description: Server error.