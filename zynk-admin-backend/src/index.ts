import "reflect-metadata";
import express, {Express, Request, Response} from "express";
import {loadEnv} from "./config/envConfig";
import {initializeSuperAdminDS, SuperAdminDS} from "./config/data-source";
import serverCors from "./config/corsConfig";
import cookieParser from "cookie-parser";
import {compressionMiddleware} from "./config/compressionConfig";
import {getPingEmoji} from "./utils/configUtils";
import {loadRoutes} from "./config/server";
import {serverErrorHandler} from "./utils/errorHandler";
import {queryInitializer} from "./middlewares/initializer.middleware";
import {requestInfoInitializer} from "./middlewares/reqInfoInitializer.middleware";
import {rootAuthenticator} from "./middlewares/rootAuthenticator.middleware";
import {resolveTenant} from "./middlewares/resolveTenant.middleware";
import {displayServerBanner} from "./utils/serverBanner";
import {initializeTemplateDatabase} from "./config/templateConfig";
import log from "./helpers/system/logger.helper";
import {SeedingException} from "./exceptions/seeding/SeedingException";
import {crashLogger} from "./utils/crashLogger";
import {engine} from "express-handlebars";
import path from "path";
import {checkForUpdates} from "./utils/updateChecker";
import {getApiDocsHtml, testApiDocs} from "./controllers/misc/apiDocs.controller";
import {handlebarsHelpers} from "./helpers/system/handleBarHelpers";
import {tokenDirector} from "./middlewares/tokenDirector.middleware";


process.on('uncaughtException', (error: Error) => {
    crashLogger(error, 'uncaughtException');
    process.exit(1);
});

process.on('unhandledRejection', (reason: unknown) => {
    crashLogger(reason, 'unhandledRejection');
    process.exit(1);
});

process.on('SIGTERM', () => {
    crashLogger(new Error('SIGTERM received'));
    process.exit(0);
});

const startServer = async () => {
    try {
        await loadEnv();
        log.info("[✓] Environment variables loaded successfully!");
    } catch (err) {
        log.error(`[x] Error loading environment variables,`, err);
        return;
    }

    try {
        await initializeSuperAdminDS().then(() => {
            log.info("[✓] SuperAdmin Datasource initialized successfully!");
        });
        await initializeTemplateDatabase().then(() => {
            log.info("[✓] Tenant template initialized successfully!");
        });
    } catch (error) {
        if (error instanceof SeedingException) {
            log.error("[x] Failed to initialize data source");
            crashLogger(error);
            return;
        }
        log.error("[x] Failed to initialize data source", error);
        crashLogger(error);
        return;
    }

    const app: Express = express();
    const hostname: string = (process.env.HOSTNAME as string) || "localhost";
    const port: string | number = process.env.PORT || 3000;

    app.use(serverCors);
    app.use(express.json());
    app.use(cookieParser(process.env.SIGNED_COOKIE_KEY as string));
    app.use(compressionMiddleware);
    app.use(resolveTenant());
    app.use(queryInitializer());
    app.set("trust proxy", true);
    app.use(requestInfoInitializer());
    app.engine("hbs", engine({
        extname: ".hbs",
        defaultLayout: "",
        helpers: handlebarsHelpers,
    }));
    app.set("view engine", "hbs");
    app.set("views", path.join(__dirname, "templates"));
    app.use(rootAuthenticator());
    app.use(tokenDirector)
    await loadRoutes(app);

    app.get("/", async (req: Request, res: Response) => {
        try {
            const serverStart = Date.now();
            const pingUrl = `http://${hostname}:${port}/ping-test`;
            await fetch(pingUrl);
            const serverPing = Date.now() - serverStart;
            const serverEmoji = getPingEmoji(serverPing);

            const dbStart = Date.now();
            await SuperAdminDS.query("SELECT 1");
            const dbPing = Date.now() - dbStart;
            const dbEmoji = getPingEmoji(dbPing);

            res.send(
                `Welcome to EasyDine.\n` +
                `Self Ping: ${serverPing.toFixed(2)}ms ${serverEmoji} \n` +
                `Database Ping: ${dbPing.toFixed(2)}ms ${dbEmoji}`
            );
        } catch (error: unknown) {
            log.error(`Error during performance measurement: ${error}`);
            res.send("Welcome to EasyDine. (Performance measurement failed)");
        }
    });
    app.get("/api-docs/html", getApiDocsHtml)
    app.get("/api-docs/test/:authMapId", testApiDocs)

    app.use(serverErrorHandler);

    app.listen(port, () => {
        displayServerBanner(app, SuperAdminDS);

        log.info(`[server]: Server has started running on http://${hostname}:${port}`);
        log.info(`View documentation at http://${hostname}:${port}/reference`);
        log.info(`API Docs in HTML format: http://${hostname}:${port}/api-docs/html`);

        if (process.env.NODE_ENV === "development") {
            setTimeout(async () => {
                await checkForUpdates()
            }, 2000)
        }
    });
};

startServer();
