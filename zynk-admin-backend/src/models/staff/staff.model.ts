import {
    <PERSON>umn,
    <PERSON>reateDate<PERSON><PERSON>umn,
    DeleteDateColumn,
    Entity,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    Repository,
    UpdateDateColumn,
} from "typeorm";
import {EasyDine} from "../base/easydine.base";
import {StaffAuth} from "./staffAuth.model";
import {StaffSession} from "./staffSession.model";
import {DaysAvailable} from "./availability/days_available.model";
import {DefaultShiftTiming} from "./availability/default_shift_timing.model";
import {StaffCertification} from "./certs/staff_certification.model";
import {Branch} from "../company/branch.model";
import {AttendanceRecord} from "../attendance/attendance.model";
import {TimeEntry} from "../attendance/time_entry.model";
import {TRole} from "../pBAC/tRole/TRole.model";
import {StaffPermissionAction} from "./StaffPermissionAction";
import {ModelType, PermActionModelType} from "../../types/pBAC";
import {PermissionAction} from "../pBAC/PermissionAction";
import {InSufficientUserAssociation} from "../../exceptions/model/impl/InSufficientUserAssociation";
import {FormatType} from "../../types/pBAC/formatterTypes";
import {processPermissionActions, updatePermissionAction} from "../../helpers/pBAC/model.helper";

@Entity("staff_members")
export class Staff extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    staffId: string;

    @Column("text", {
        nullable: false,
    })
    firstName: string;

    @Column("text", {
        nullable: true,
    })
    middleName: string;

    @Column("text", {
        nullable: false,
    })
    lastName: string;

    @Column({
        nullable: false,
        unique: true,
    })
    emailAddress: string;

    @Column({
        nullable: false,
        unique: true,
    })
    phoneNumber: string;

    @Column("text", {
        nullable: false,
    })
    address: string;

    @Column("text", {
        nullable: true,
        default: "https://placehold.co/100x100/png",
    })
    profileUrl: string;

    @Column({
        nullable: false
    })
    pin: string;


    @OneToMany(() => StaffPermissionAction, spa => spa.staff)
    staffPermissionActions: StaffPermissionAction[];

    @ManyToOne(() => TRole, TRole => TRole.staff, {
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
    })
    role: TRole;

    // Until staff role is complete and perfect, use this 
    @Column({
        nullable: false,
        default: 'Waiter'
    })
    tempRole: string

    @OneToOne(() => StaffAuth, (staffAuth) => staffAuth.staff, {
        cascade: true,
    })
    staffAuth: StaffAuth;

    @OneToOne(
        () => DaysAvailable,
        (staffDaysAvailable) => staffDaysAvailable.staff,
        {
            cascade: true,
        }
    )
    staffDaysAvailable: DaysAvailable;

    @OneToOne(
        () => DefaultShiftTiming,
        (defaultShiftTiming) => defaultShiftTiming.staff,
        {
            cascade: true,
        }
    )
    defaultShiftTiming: DefaultShiftTiming;

    @OneToMany(
        () => StaffCertification,
        (staffCertification) => staffCertification.staff,
        {
            cascade: true,
        }
    )
    staffCertifications: StaffCertification[];

    @OneToMany(() => StaffSession, (staffsession) => staffsession.staff, {
        cascade: true,
    })
    staffSessions: StaffSession[];

    @ManyToMany(() => Branch)
    @JoinTable({
        name: "staff_reserved_branches",
    })
    reservedBranches: Branch[];

    @OneToMany(
        () => AttendanceRecord,
        (attendanceRecord) => attendanceRecord.staff,
        {
            cascade: true,
        }
    )
    attendanceRecords: AttendanceRecord[];

    @OneToMany(
        () => TimeEntry,
        (timeEntry) => timeEntry.staff,
        {
            cascade: true,
        }
    )
    timeEntries: TimeEntry[];

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;

    @DeleteDateColumn({type: "timestamptz"})
    deletedAt?: Date;

    async updatePermissionAction(
        permissionActionIds: PermActionModelType[],
        permissionAction: Repository<PermissionAction>,
        StaffPermissionAction: Repository<StaffPermissionAction>
    ) {
        try {
            if (!this.role || !this.role.rolePermissionActions)
                throw new InSufficientUserAssociation(ModelType.STAFF)
            const result = await updatePermissionAction(
                permissionActionIds,
                this.role.rolePermissionActions,
                this.staffPermissionActions,
                this.staffId,
                "staff",
                "staffId",
                StaffPermissionAction,
                permissionAction,
            );
            const {updatedUnknownPermissions, ...rest} = result
            return rest;
        } catch (e) {
            throw e;
        }
    }

    async getPermissionAction(formatType: FormatType) {
        try {
            if (!this.role || !this.role.rolePermissionActions)
                throw new InSufficientUserAssociation(ModelType.STAFF)
            return processPermissionActions(
                this.role.rolePermissionActions,
                this.staffPermissionActions,
                {
                    formatType: formatType,
                    requiredActive: true,
                }
            );
        } catch (e) {
            throw e;
        }
    }

}
