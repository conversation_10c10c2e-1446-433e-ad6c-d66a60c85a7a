import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Staff } from "./staff.model";

@Entity("staffauths")
export class StaffAuth extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  staffAuthId: string;

  @Column({
    nullable: false,
  })
  password: string;

  @Column({
    type: "text",
    nullable: true,
  })
  refreshToken: string;

  @Column({ nullable: true })
  staffId: string;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @OneToOne(() => Staff, (staff) => staff.staffAuth, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "staffId" })
  staff: Staff;
}
