import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Staff } from "./staff.model";

@Entity("staffsessions")
export class StaffSession extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  staffSessionId: string;

  @Column({
    type: "text",
    nullable: true,
  })
  ipAddress: string;

  @Column({
    type: "text",
    nullable: true,
  })
  userAgent: string;

  @Column({
    type: "timestamp",
    nullable: true,
  })
  lastLogin: Date;

  @Column({
    type: "boolean",
    nullable: false,
    default: false,
  })
  isRevoked: boolean;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @ManyToOne(() => Staff, (staff) => staff.staffSessions, {
    onDelete: "CASCADE",
  })
  staff: Staff;
}
