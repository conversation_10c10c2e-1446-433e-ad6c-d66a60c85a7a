import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  <PERSON><PERSON><PERSON>,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Staff } from "../staff.model";
import { EasyDine } from "../../base/easydine.base";

export enum DAY_AVAILABILITY {
  OFF = "off",
  ACTIVE = "active",
  LEAVE = "leave",
}

type DayAvailType = "M" | "T" | "W" | "Th" | "F" | "Sa" | "Su";

@Entity("days_available")
export class DaysAvailable extends EasyDine{
  @PrimaryGeneratedColumn("uuid")
  daysAvailableId: string;

  @Column("jsonb", {
    default: {
      M: DAY_AVAILABILITY.ACTIVE,
      T: DAY_AVAILABILITY.ACTIVE,
      W: DAY_AVAILABILITY.ACTIVE,
      Th: DAY_AVAILABILITY.ACTIVE,
      F: DAY_AVAILABILITY.ACTIVE,
      Sa: DAY_AVAILABILITY.OFF,
      Su: DAY_AVAILABILITY.OFF,
    },
  })
  days: Partial<Record<DayAvailType, DAY_AVAILABILITY>>;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @Column({ nullable: true })
  staffId: string;

  @OneToOne(() => Staff, (staff) => staff.staffDaysAvailable, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "staffId" })
  staff: Staff;
}
