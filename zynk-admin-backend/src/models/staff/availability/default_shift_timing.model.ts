import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Staff } from "../staff.model";
import { EasyDine } from "../../base/easydine.base";

export enum DayOfWeek {
  SUNDAY = 'Su',
  MONDAY = 'M', 
  TUESDAY = 'T',
  WEDNESDAY = 'W',
  THURSDAY = 'Th',
  FRIDAY = 'F',
  SATURDAY = 'Sa'
}

export enum ShiftType {
  MORNING = 'morning',
  NOON = 'noon', 
  NIGHT = 'night'
}

export interface ShiftTiming {
  isActive: boolean;
  clockInTime?: string;
  clockOutTime?: string;
}

export interface DailyShiftTimings {
  morning: ShiftTiming;
  noon: ShiftTiming;
  night: ShiftTiming;
}

export interface WeeklyShiftTimings {
  sunday: DailyShiftTimings;
  monday: DailyShiftTimings;
  tuesday: DailyShiftTimings;
  wednesday: DailyShiftTimings;
  thursday: DailyShiftTimings;
  friday: DailyShiftTimings;
  saturday: DailyShiftTimings;
}

@Entity("default_shift_timings")
export class DefaultShiftTiming extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  defaultShiftTimingId: string;

  @Column({ nullable: true })
  staffId: string;

  @OneToOne(() => Staff, (staff) => staff.defaultShiftTiming, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "staffId" })
  staff: Staff;

  @Column({ 
    type: "jsonb", 
    nullable: true,
    default: {}
  })
  weeklyTimings: WeeklyShiftTimings;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}