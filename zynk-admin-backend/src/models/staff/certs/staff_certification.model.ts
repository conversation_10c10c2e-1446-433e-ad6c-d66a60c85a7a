import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Staff } from "../staff.model";

@Entity("staff_certifications")
export class StaffCertification extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  staffCertificationId: string;

  @Column("text", {
    nullable: false,
  })
  certId: string;

  @Column({
    nullable: false,
  })
  certName: string;

  @Column("text", {
    nullable: false,
  })
  attachmentUrl: string;

  @Column({
    nullable: true,
    default: null,
  })
  expiryDate: Date;

  @ManyToOne(() => Staff, (staff) => staff.staffCertifications, {
    onDelete: "CASCADE",
  })
  staff: Staff;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
