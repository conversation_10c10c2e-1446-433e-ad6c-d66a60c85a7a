import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn} from "typeorm";
import {OperationType} from "../../types/pBAC";
import {Staff} from "./staff.model";
import {TPermissionAction} from "../pBAC/tPBAC/TPermissionAction";

@Entity({name: 'staff_permission_action'})
export class StaffPermissionAction {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column({
        type: 'enum',
        enum: OperationType,
        default: OperationType.PLUS,
    })
    operationType: OperationType;

    @ManyToOne(() => Staff, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "staffId"})
    staff: Staff;

    @ManyToOne(() => TPermissionAction, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "pAId"})
    permissionAction: TPermissionAction;
}