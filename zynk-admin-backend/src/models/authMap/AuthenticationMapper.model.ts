import {Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {EasyDine} from "../base/easydine.base";
import {AccessType, RouteType} from "../../types/pBAC";

@Entity('authentication_mapper')
@Index(['routePath', "method"], {unique: true})
export class AuthenticationMapper extends EasyDine {
    @PrimaryGeneratedColumn('uuid')
    authMapId: string;

    @Column({nullable: false})
    routePath: string;

    @Column({nullable: false})
    method: string;

    @Column({type: 'text', nullable: true})
    description: string;

    @Column({
        type: 'enum',
        enum: AccessType,
        default: AccessType.ALL,
    })
    accessType: AccessType;

    @Column({type: 'text', array: true, nullable: true})
    permissionActions: string[];

    @Column({type: "boolean", nullable: false, default: false})
    isOrOperation: boolean;

    @Column({
        type: 'enum',
        enum: RouteType,
        default: RouteType.PRIVATE,
    })
    routeType: RouteType;

    @Column({type: 'json', nullable: true})
    responseData: any;

    @Column({type: 'json', nullable: true})
    requestBody: any;

    @Column({type: 'json', nullable: true})
    queryParams: any;

    @Column({type: 'json', nullable: true})
    requestParams: any;

    @Column({type: 'boolean', nullable: false, default: true})
    isActive: boolean;

    @CreateDateColumn({type: 'timestamptz'})
    createdAt: Date;

    @UpdateDateColumn({type: 'timestamptz'})
    updatedAt: Date;
}