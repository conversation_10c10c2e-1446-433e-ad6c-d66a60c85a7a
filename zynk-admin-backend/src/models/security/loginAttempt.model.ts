import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  UpdateDateColumn,
  DeleteDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";

@Entity("login_attempts")
@Index("IDX_login_attempts_ip_address", ["ipAddress"])
@Index("IDX_login_attempts_attempt_time", ["attemptTime"]) 
@Index("IDX_login_attempts_ip_time", ["ipAddress", "attemptTime"])
export class LoginAttempt extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  loginAttemptId: string;

  @Column({ type: "text" })
  @Index("IDX_login_attempts_ip_address_single")
  ipAddress: string;

  @Column({ length: 255 })
  emailAddress: string;

  @Column({ length: 20 })
  loginType: "SUPERADMIN" | "TENANT" | "STAFF" | "USER";

  @Column({ type: "uuid", nullable: true })
  userId?: string;

  @Column({ type: "boolean", default: false })
  success: boolean;

  @Column({ type: "boolean", default: false })
  blocked: boolean;

  @Column({ type: "integer", default: 0 })
  failureCount: number;

  @Column("text", { nullable: true })
  reason?: string;

  @Column({ type: "text", nullable: true })
  userAgent?: string;

  @CreateDateColumn()
  attemptTime: Date;

  @Column({ type: "varchar", length: 100, nullable: true })
  metadata?: string;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}