import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Branch } from "./branch.model";
import { EasyDine } from "../base/easydine.base";

@Entity("branchcontacts")
export class BranchContacts extends EasyDine{
  @PrimaryGeneratedColumn("uuid")
  branchContactId: string;

  @Column({
    nullable: false,
  })
  name: string;

  @Column({
    nullable: false,
    unique: true,
  })
  emailAddress: string;

  @Column({
    nullable: false,
    unique: true,
  })
  phoneNumber: string;

  @Column({
    nullable: true,
  })
  faxNumber: string;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @ManyToOne(() => Branch, (branch) => branch.branchContacts, {
    onDelete: "CASCADE",
  })
  branch: Branch;
}
