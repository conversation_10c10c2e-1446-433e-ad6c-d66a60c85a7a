import {
    <PERSON>umn,
    <PERSON>reateDate<PERSON>olumn,
    DeleteDateColumn,
    Entity,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from "typeorm";
import {CompanyInformation} from "./companyinformation.model";
import {BranchContacts} from "./branchcontact.model";
import {BusinessHour} from "../common/businesshour.model";
import {FoodMenu} from "../foodmenu/foodmenu.model";
import {EasyDine} from "../base/easydine.base";
import {OrderScheduling} from "../tenantSettings/onlineOrders/OrderScheduling.model";
import {WasteManagement} from "../tenantSettings/wasteManagement/waste_management.model";
import {SpecialInstructions} from "../tenantSettings/food/SpecialInstructions.model";
import {ReceiveOrders} from "../tenantSettings/onlineOrders/ReceiveOrders.model";
import {Pickup} from "../tenantSettings/onlineOrders/Pickup.model";
import { PreparationDuration } from "../tenantSettings/food/preparation_duration.model";
import { OrderType } from "../tenantSettings/general/orderType.model";
import { CurrencyUnit } from "../tenantSettings/general/currencyUnit.model";
import { TableModel } from "../tenantSettings/tableReservation/management/table.model";
import { TableCombination } from "../tenantSettings/tableReservation/management/tableCombination.model";
import { ParkingService } from "../tenantSettings/general/parkingService.model";
import { Allergy } from "../reference/allergy.model";
import { AllergyColor } from "../reference/allergyColor.model";
import { Ingredient } from "../reference/ingredient.model";
import { SectionIcon } from "../reference/sectionicon.model";
import { DishAddon } from "../foodmenu/custom/addon.model";
import { DishExtra } from "../foodmenu/custom/extra.model";
import { Floor } from "../tenantSettings/tableReservation/management/floor.model";
import { TurnoverTime } from "../tenantSettings/tableReservation/turnoverTime/turnover_time.model";
import { BufferTime } from "../tenantSettings/tableReservation/management/bufferTime.model";
import { OnlineReservationConfig } from "../tenantSettings/tableReservation/online/online_reserv.model";

@Entity("branches")
export class Branch extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    branchId: string;

    @Column("text", {
        nullable: false,
        unique: true,
    })
    name: string;

    @Column('boolean', {
        nullable: false,
        default: false
    })
    isPrimary: boolean;

    @ManyToOne(() => CompanyInformation, (companyInfo) => companyInfo.branches, {
        onDelete: "CASCADE",
    })
    companyInfo: CompanyInformation;

    @OneToMany(() => BranchContacts, (branchContact) => branchContact.branch, {
        cascade: true,
    })
    branchContacts: BranchContacts[];

    @OneToMany(() => FoodMenu, (foodmenu) => foodmenu.branch, {
        cascade: true,
    })
    foodMenus: FoodMenu[];

    @Column("boolean", {
        nullable: false,
        default: true
    })
    isActive: boolean;

    @Column("jsonb", {
        nullable: false,
    })
    location: {
        lat: number;
        lng: number;
    };

    @Column({
        nullable: false,
    })
    country: string;

    @Column({
        nullable: false,
    })
    city: string;

    @Column({
        nullable: false,
    })
    state: string;

    @Column({
        nullable: false,
    })
    postalCode: string;

    @Column({
        nullable: false,
    })
    streetName: string;

    @Column({
        nullable: false,
    })
    houseNumber: string;

    @Column({
        nullable: false,
    })
    apartment: string;

    @Column({
        nullable: false,
    })
    locationName: string;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;

    @DeleteDateColumn({type: "timestamptz"})
    deletedAt?: Date;

    @OneToOne(() => BusinessHour, (businessHour) => businessHour.branch, {
        cascade: true,
    })
    businessHour: BusinessHour;

    @OneToOne(() => PreparationDuration, (preparationDuration) => preparationDuration.branch, {
        cascade: true,
    })
    preparationDuration: PreparationDuration;

    @OneToOne(() => OrderScheduling, (orderScheduling) => orderScheduling.branch, {
        cascade: true,
    })
    orderScheduling: OrderScheduling;

    @OneToOne(() => CurrencyUnit, (currencyUnit) => currencyUnit.branch, {
        cascade: true,
    })
    currencyUnit: CurrencyUnit;

    @OneToOne(() => WasteManagement, (wasteManagement) => wasteManagement.branch, {
        cascade: true,
    })
    wasteManagement: WasteManagement;

    @OneToOne(() => SpecialInstructions, (specialInstructions) => specialInstructions.branch, {
        cascade: true,
    })
    specialInstructions: SpecialInstructions;

    @OneToOne(() => ReceiveOrders, (receiveOrders) => receiveOrders.branch, {
        cascade: true,
    })
    receiveOrders: ReceiveOrders;

    @OneToOne(() => Pickup, (pickup) => pickup.branch, {
        cascade: true,
    })
    pickup: Pickup;

    @OneToOne(() => ParkingService, (parkingService) => parkingService.branch, {
        cascade: true,
    })
    parkingService: ParkingService;

    @OneToMany(() => OrderType, (orderType) => orderType.branch, {
        cascade: true,
    })
    orderTypes: OrderType[];

    @OneToMany(() => Floor, (floor) => floor.branch, {
        cascade: true,
    })
    floors: Floor[];

    @OneToMany(() => TableModel, (table) => table.branch, {
        cascade: true,
    })
    tables: TableModel[];

    @OneToMany(() => TableCombination, (tableCombination) => tableCombination.branch, {
        cascade: true,
    })
    tableCombinations: TableCombination[];

    @OneToMany(() => Allergy, (allergy) => allergy.branch, {
        cascade: true,
    })
    allergies: Allergy[];

    @OneToMany(() => AllergyColor, (allergyColor) => allergyColor.branch, {
        cascade: true,
    })
    allergyColors: AllergyColor[];

    @OneToMany(() => Ingredient, (ingredient) => ingredient.branch, {
        cascade: true,
    })
    ingredients: Ingredient[];

    @OneToMany(() => SectionIcon, (sectionIcon) => sectionIcon.branch, {
        cascade: true,
    })
    sectionIcons: SectionIcon[];

    @OneToMany(() => DishAddon, (dishAddon) => dishAddon.branch, {
        cascade: true,
    })
    dishAddons: DishAddon[];

    @OneToMany(() => DishExtra, (dishExtra) => dishExtra.branch, {
        cascade: true,
    })
    dishExtras: DishExtra[];

    @OneToOne(() => TurnoverTime, (turnovertime) => turnovertime.branch, {
        cascade: true
    })
    turnoverTime: TurnoverTime

    @OneToOne(() => BufferTime, (bufferTime) => bufferTime.branch, {
        cascade: true
    })
    bufferTime: BufferTime

     @OneToOne(() => OnlineReservationConfig, (onlineResConfig) => onlineResConfig.branch, {
        cascade: true
    })
    onlineResConfig: OnlineReservationConfig
}
