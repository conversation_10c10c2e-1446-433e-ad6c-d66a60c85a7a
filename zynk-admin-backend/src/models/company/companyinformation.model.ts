import { <PERSON><PERSON><PERSON>, <PERSON>ToMany, <PERSON>To<PERSON>ne } from "typeorm";
import { Branch } from "./branch.model";
import { CompanyInformationBase } from "../base/companyinformation.base";
import { BankDetail } from "./bankdetail.model";
import { AccountRep } from "./accountrep.model";

@Entity("companyinformation")
export class CompanyInformation extends CompanyInformationBase {
  @OneToMany(() => Branch, (branch) => branch.companyInfo, {
    cascade: true
  })
  branches: Branch[];

  @OneToOne(() => BankDetail, (bankdetail) => bankdetail.companyInfo, {
    cascade: true,
  })
  bankdetail: BankDetail;

  @OneToMany(() => AccountRep, (accountRep) => accountRep.companyInfo)
  accountReps: AccountRep[];
}
