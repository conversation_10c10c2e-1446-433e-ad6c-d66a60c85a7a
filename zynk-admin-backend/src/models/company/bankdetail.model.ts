import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { CompanyInformation } from "./companyinformation.model";
import { EasyDine } from "../base/easydine.base";

@Entity("bankdetails")
export class BankDetail extends EasyDine{
  @PrimaryGeneratedColumn("uuid")
  bankDetId: string;

  @Column({
    nullable: false,
  })
  bankName: string

  @Column({
    nullable: false,
  })
  sortCode: string;

  @Column({
    nullable: false,
  })
  accountNumber: string;

  @Column({ nullable: true })
  companyInfoId: string;

  @OneToOne(() => CompanyInformation, (companyInfo) => companyInfo.bankdetail, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "companyInfoId" })
  companyInfo: CompanyInformation;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
