import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { CompanyInformation } from "./companyinformation.model";
import { EasyDine } from "../base/easydine.base";

@Entity("account_representatives")
export class AccountRep extends EasyDine{
  @PrimaryGeneratedColumn("uuid")
  accountRepId: string;

  @Column({
    nullable: true,
    default: "https://placehold.co/100x100/png",
  })
  photoUrl: string;

  @Column({
    nullable: false,
  })
  firstName: string;

  @Column({
    nullable: true,
  })
  middleName: string;

  @Column({
    nullable: false,
  })
  lastName: string;

  @Column({
    nullable: false,
  })
  dob: Date;

  @Column({
    nullable: false,
    unique: true,
  })
  emailAddress: string;

  @Column({
    nullable: false,
    unique: true,
  })
  phoneNumber: string;

  @Column({
    nullable: false,
  })
  country: string;

  @Column({
    nullable: false,
  })
  address: string;

  @Column({
    nullable: false,
  })
  city: string;

  @Column({
    nullable: false,
  })
  state: string;

  @Column({
    nullable: false,
  })
  postalCode: string;

  @Column({
    nullable: false,
  })
  jobTitle: string;

  @Column({
    nullable: false,
  })
  highOwnership: boolean;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @ManyToOne(() => CompanyInformation, (companyInfo) => companyInfo.accountReps, {
    onDelete: "CASCADE",
  })
  companyInfo: CompanyInformation
}
