import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Staff } from "../staff/staff.model";

@Entity("time_entries")
export class TimeEntry extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  timeEntryId: string;

  @Column({ type: "time" })
  clockInTime: string;

  @Column({
    type: "date",
    nullable: false,
  })
  date: string;

  @Column({ type: "time", nullable: true })
  clockOutTime: string;

  @ManyToOne(() => Staff, (staff) => staff.timeEntries, {
    onDelete: "CASCADE",
  })
  staff: Staff;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
