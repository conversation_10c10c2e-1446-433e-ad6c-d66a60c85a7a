import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Staff } from "../staff/staff.model";

@Entity("attendance_records")
export class AttendanceRecord extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  attendanceRecordId: string;

  @Column({ type: "time" })
  checkInTime: string;

  @Column({
    type: "date",
    nullable: false,
  })
  date: string;

  @Column({ type: "time", nullable: true })
  checkOutTime: string;

  @ManyToOne(() => Staff, (staff) => staff.attendanceRecords, {
    onDelete: "CASCADE",
  })
  staff: Staff;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
