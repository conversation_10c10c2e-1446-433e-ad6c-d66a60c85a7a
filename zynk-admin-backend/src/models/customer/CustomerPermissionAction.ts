import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn} from "typeorm";
import {OperationType} from "../../types/pBAC";
import {TPermissionAction} from "../pBAC/tPBAC/TPermissionAction";
import {Customer} from "./customer.model";

@Entity({name: 'customer_permission_action'})
export class CustomerPermissionAction {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column({
        type: 'enum',
        enum: OperationType,
        default: OperationType.PLUS,
    })
    operationType: OperationType;

    @ManyToOne(() => Customer, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "customerId"})
    customer: Customer;

    @ManyToOne(() => TPermissionAction, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "pAId"})
    permissionAction: TPermissionAction;
}