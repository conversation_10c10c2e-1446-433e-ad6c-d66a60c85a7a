import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Customer } from "./customer.model";

@Entity("customer_auths")
export class CustomerAuth extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  customerAuthId: string;

  @Column({
    nullable: false,
  })
  password: string;

  @Column({
    nullable: true,
  })
  refreshToken: string;

  @Column({
    nullable: true
  })
  customerId: string

  @OneToOne(() => Customer, (customer) => customer.customerAuth, {
    onDelete: "CASCADE"
  })
  @JoinColumn({name: "customerId"})
  customer: Customer

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
