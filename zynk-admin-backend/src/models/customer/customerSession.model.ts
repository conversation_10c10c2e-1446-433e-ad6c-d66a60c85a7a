import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Customer } from "./customer.model";

@Entity("customersessions")
export class CustomerSession extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  customerSessionId: string;

  @Column({
    type: "text",
    nullable: true,
  })
  ipAddress: string;

  @Column({
    type: "text",
    nullable: true,
  })
  userAgent: string;

  @Column({
    type: "timestamp",
    nullable: true,
  })
  lastLogin: Date;

  @Column({
    type: "boolean",
    nullable: false,
    default: false,
  })
  isRevoked: boolean;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @ManyToOne(() => Customer, (customer) => customer.customerSessions, {
    onDelete: "CASCADE",
  })
  customer: Customer;
}
