import { Column, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Customer } from "./customer.model";

@Entity('customer_addresses')
export class CustomerAddress extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    addressId: string;

    @Column()
    customerId: string;

    @ManyToOne(() => Customer, customer => customer.addresses, {
        onDelete: "CASCADE"
    })
    @JoinColumn({ name: 'customerId' })
    customer: Customer;

    @Column({
        type: 'enum',
        enum: ['HOME', 'WORK', 'OTHER'],
        default: 'HOME'
    })
    addressType: string;

    @Column()
    addressLine1: string;

    @Column({ nullable: true })
    addressLine2: string;

    @Column()
    city: string;

    @Column()
    state: string;

    @Column()
    postalCode: string;

    @Column({ default: 'UK' })
    country: string;

    @Column({ default: false })
    isDefault: boolean;

    @CreateDateColumn()
    createdAt: Date;
}