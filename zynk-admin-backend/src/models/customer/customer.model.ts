import { <PERSON>umn, CreateDate<PERSON><PERSON><PERSON>n, <PERSON><PERSON>ty, OneToMany, PrimaryGeneratedColumn, Repository, UpdateDateColumn, ManyToOne, OneToOne } from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { CustomerAddress } from "./customerAddress.model";
import { OrderDetails } from "../order/orderDetail.model";
import { ModelType, PermActionModelType } from "../../types/pBAC";
import { PermissionAction } from "../pBAC/PermissionAction";
import { InSufficientUserAssociation } from "../../exceptions/model/impl/InSufficientUserAssociation";
import { FormatType } from "../../types/pBAC/formatterTypes";
import { processPermissionActions, updatePermissionAction } from "../../helpers/pBAC/model.helper";
import { CustomerPermissionAction } from "./CustomerPermissionAction";
import { TRole } from "../pBAC/tRole/TRole.model";
import { CustomerAuth } from "./customerAuth.model";
import { CustomerSession } from "./customerSession.model";

export enum Status {
    ACTIVE = 'active',
    UNACTIVE = 'unactive',
}

@Entity('customers')
export class Customer extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    customerId: string;

    @Column({
        type: 'varchar',
        unique: true,
    })
    customerCode: string;

    @Column()
    firstName: string;

    @Column({ nullable: true })
    middleName: string;

    @Column()
    lastName: string;

    @Column({
        unique: true,
        nullable: true
    })
    email: string;

    @Column({
        type: 'varchar',
        length: 15,
        unique: true,
        nullable: true
    })
    phoneNumber: string;

    @Column({
        type: 'enum',
        enum: Status,
        default: Status.ACTIVE
    })
    status: Status;

    @OneToMany(() => CustomerPermissionAction, cpa => cpa.customer)
    customerPermissionActions: CustomerPermissionAction[];

    @ManyToOne(() => TRole, TRole => TRole.customer, {
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
    })
    role: TRole;

    @OneToMany(() => CustomerAddress, address => address.customer, { cascade: true, eager: true })
    addresses: CustomerAddress[];

    @OneToMany(() => OrderDetails, orderDetails => orderDetails.customer, { cascade: true, eager: true })
    orderDetails: OrderDetails[];

    @OneToOne(() => CustomerAuth, (customerAuth) => customerAuth.customer, {
        cascade: true
    })
    customerAuth: CustomerAuth

    @OneToMany(() => CustomerSession, (customerSession) => customerSession.customer, {
        cascade: true
    })
    customerSessions: CustomerSession[]

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    // Helper method to get default address
    get defaultAddress(): CustomerAddress | null {
        return this.addresses?.find(addr => addr.isDefault) || null;
    }

    // Helper method to get active orders
    get activeOrders(): OrderDetails[] {
        return this.orderDetails?.filter(order => order.isInQueue()) || [];
    }

    // Helper method to get completed orders
    get completedOrders(): OrderDetails[] {
        return this.orderDetails?.filter(order => order.isCompleted()) || [];
    }

    async updatePermissionAction(
        permissionActionIds: PermActionModelType[],
        permissionAction: Repository<PermissionAction>,
        CustomerPermissionAction: Repository<CustomerPermissionAction>
    ) {
        try {
            if (!this.role || !this.role.rolePermissionActions)
                throw new InSufficientUserAssociation(ModelType.STAFF)
            const result = await updatePermissionAction(
                permissionActionIds,
                this.role.rolePermissionActions,
                this.customerPermissionActions,
                this.customerId,
                "customer",
                "customerId",
                CustomerPermissionAction,
                permissionAction,
            );
            const { updatedUnknownPermissions, ...rest } = result
            return rest;
        } catch (e) {
            throw e;
        }
    }

    async getPermissionAction(formatType: FormatType) {
        try {
            if (!this.role || !this.role.rolePermissionActions)
                throw new InSufficientUserAssociation(ModelType.STAFF)
            return processPermissionActions(
                this.role.rolePermissionActions,
                this.customerPermissionActions,
                {
                    formatType: formatType,
                    requiredActive: true,
                }
            );
        } catch (e) {
            throw e;
        }
    }
}