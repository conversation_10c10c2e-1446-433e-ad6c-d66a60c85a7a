/**
 * Top level base class for all models
 * Provides a method to update properties of the model
 */
export abstract class EasyDine {
  update<T extends Record<string, any>>(data: Partial<T>): this {
    if (!data || Object.keys(data).length === 0) {
      return this;
    }

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        (this as any)[key] = value;
      }
    });

    return this;
  }
}
