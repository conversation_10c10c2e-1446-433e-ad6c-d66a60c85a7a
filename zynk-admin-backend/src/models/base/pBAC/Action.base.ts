import {<PERSON>umn, CreateDate<PERSON>olumn, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {EasyDine} from "../easydine.base";
import {AccessType} from "../../../types/pBAC";

export class ActionBase extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    actionId: string;

    @Column({nullable: false, unique: true})
    name: string;

    @Column({type: "text", nullable: true})
    description: string;

    @Column({type: "boolean", nullable: false, default: true})
    isActive: boolean;

    @Column({
        type: 'enum',
        enum: AccessType,
        default: AccessType.ALL,
    })
    accessType: AccessType;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;
}