import {<PERSON>um<PERSON>, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {EasyDine} from "../easydine.base";
import {AccessType} from "../../../types/pBAC";

@Entity("modules")
export class ModuleBase extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    moduleId: string;

    @Column({nullable: false, unique: true})
    moduleName: string;

    @Column({type: "text", nullable: true})
    description: string;

    @Column({type: "boolean", default: true})
    isActive: boolean;

    @Column({
        type: 'enum',
        enum: AccessType,
        default: AccessType.ALL,
    })
    accessType: AccessType;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;
}