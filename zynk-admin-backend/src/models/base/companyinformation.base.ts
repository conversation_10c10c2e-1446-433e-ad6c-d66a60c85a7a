import {
  Column,
  CreateDate<PERSON>olumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "./easydine.base";

export enum BusinessType{
  individual = 'individual',
  soleproprietor = 'soleproprietor',
  corporation = 'corporation'
}

export abstract class CompanyInformationBase extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  companyInfoId: string;

  @Column({
    nullable: false,
    unique: true,
  })
  regNumber: string;

  @Column({
    nullable: false,
    unique: true,
  })
  name: string;

  @Column('text', {
    nullable: false,
  })
  description: string;

  @Column({
    nullable: false,
    unique: true,
  })
  businessAlias: string;

  @Column("text", {
    nullable: false,
    default: "https://placehold.co/100x100/png"
  })
  regDocument: string

  @Column("text", {
    nullable: true,
    default: "https://placehold.co/100x100/png",
  })
  logoUrl: string;

  @Column("text", {
    nullable: false,
  })
  addrLine1: string;

  @Column("text", {
    nullable: true,
    default: null,
  })
  addrLine2: string;

  @Column({
    nullable: false,
  })
  city: string;

  @Column({
    nullable: false,
  })
  state: string;

  @Column({
    nullable: false,
  })
  postalCode: string;

  @Column({
    nullable: false,
  })
  country: string;

  @Column({
    nullable: false,
  })
  taxIDNumber: string;

  @Column({
    type: "enum",
    enum: BusinessType,
    default: BusinessType.corporation
  })
  businessType: string;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
