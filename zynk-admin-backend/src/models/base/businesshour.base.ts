import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "./easydine.base";

type Weekday = "M" | "T" | "W" | "Th" | "F" | "Sa" | "Su";

export abstract class BusinessHourSlotBase extends EasyDine {
  @Column({
    type: "jsonb",
    default: {
      M: true,
      T: true,
      W: true,
      Th: true,
      F: true,
      Sa: false,
      Su: false,
    },
  })
  days: Partial<Record<Weekday, boolean>>;

  @Column({ default: false })
  is24Hours: boolean;

  @Column("boolean", {
    default: true,
    nullable: false,
  })
  isActive: boolean;

  @Column({ type: "time", nullable: true })
  firstSeating: string;

  @Column({ type: "time", nullable: true })
  lastSeating: string;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
