import { Entity, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { EasyDine } from '../base/easydine.base';
import { Staff } from '../staff/staff.model';
import { CartItem, PriceNamedEntity } from './cartItem.model';

export enum CartStatus {
    ACTIVE = 'ACTIVE',
    HOLD = 'HOLD',
    CONFIRMED = 'CONFIRMED',
    CANCELLED = 'CANCELLED'
}

// Interface for JSONB objects - reusing from OrderDetails
export interface NamedEntity {
    id: string;
    name: string;
}

export interface Alert {
    id: string;
    note: string;
}

@Entity('cart')
@Index(['staffId', 'status'])
@Index(['status'])
export class Cart extends EasyDine {
    @PrimaryGeneratedColumn('uuid')
    cartId: string;

    @Column('uuid')
    staffId: string;

    @ManyToOne(() => Staff, { onDelete: "CASCADE" })
    @JoinColumn({ name: 'staffId' })
    staff: Staff;

    @Column({
        type: 'enum',
        enum: CartStatus,
        default: CartStatus.ACTIVE
    })
    status: CartStatus;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Table information with id and name'
    })
    table: NamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Order type information with id and name'
    })
    orderType: NamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Customer information with id and name if available'
    })
    customerInfo: NamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Branch information with id and name'
    })
    branch: NamedEntity;

    @Column({
        type: 'text',
        nullable: true,
        comment: 'Special instructions or notes'
    })
    notes: string;

    @Column({
        type: 'decimal',
        precision: 10,
        scale: 2,
        default: 0,
        comment: 'Calculated total of all cart items'
    })
    total: number;

    @Column({
        type: 'jsonb',
        nullable: true,
        default: () => "'[]'",
        comment: 'Array of miscellaneous items with id, name, price, and addedAt'
    })
    miscItems: PriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        default: () => "'[]'",
        comment: 'Array of alerts with id, note, and addedAt'
    })
    alerts: Alert[];

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @OneToMany(() => CartItem, cartItem => cartItem.cart, { cascade: true, eager: true })
    cartItems: CartItem[];


    // Helper method to check if cart is empty
    isEmpty(): boolean {
        return !this.cartItems || this.cartItems.length === 0;
    }

    // Helper method to check if cart is active
    isActive(): boolean {
        return this.status === CartStatus.ACTIVE;
    }

    // Helper method to check if cart is on hold
    isOnHold(): boolean {
        return this.status === CartStatus.HOLD;
    }

    // Helper method to hold cart
    holdCart(): void {
        this.status = CartStatus.HOLD;
    }

    // Helper method to activate cart
    activateCart(): void {
        this.status = CartStatus.ACTIVE;
    }

    // Helper method to confirm cart (convert to order)
    confirmCart(): void {
        this.status = CartStatus.CONFIRMED;
    }

    // Helper method to cancel cart
    cancelCart(): void {
        this.status = CartStatus.CANCELLED;
    }

    // Helper method to validate and set entities (reused from OrderDetails)
    static validateAndSetEntity(entity: any): NamedEntity | null {
        if (!entity) return null;

        if (typeof entity === 'string') {
            throw new Error('Name is required along with ID');
        }

        if (entity.id && entity.name) {
            return { id: entity.id, name: entity.name };
        }

        throw new Error('Both id and name are required');
    }

    // Convert cart to OrderItemInput format for createOrderDetails
    toOrderItemInputs(): any[] {
        if (!this.cartItems || this.cartItems.length === 0) {
            return [];
        }

        return this.cartItems.map(cartItem => ({
            name: cartItem.name,
            price: cartItem.totalPrice,
            quantity: cartItem.quantity,
            dishId: cartItem.baseItem?.id,
            type: cartItem.type,
            baseItemId: cartItem.baseItem?.id,
            notes: cartItem.notes,
            addedOn: cartItem.createdAt.toISOString(),

            // Customization arrays - convert back to ID arrays
            allergyIds: cartItem.allergies?.map((a: any) => a.id),
            dishSizeId: cartItem.dishSizes?.id,
            dishExclusionIds: cartItem.dishExclusions?.map((e: any) => e.id),
            cookingStyleId: cartItem.cookingStyles?.id,
            spicinessId: cartItem.spiciness?.id,
            dishAddons: cartItem.dishAddons?.map((addon: any) => ({
                id: addon.id,
                quantity: addon.quantity
            })),
            dishExtras: cartItem.dishExtras?.map((extra: any) => ({
                id: extra.id,
                quantity: extra.quantity
            })),
            dishSides: cartItem.dishSides?.map((side: any) => ({
                id: side.id,
                quantity: side.quantity
            })),
            dishBeverages: cartItem.dishBeverages?.map((beverage: any) => ({
                id: beverage.id,
                quantity: beverage.quantity
            })),
            dishDesserts: cartItem.dishDesserts?.map((dessert: any) => ({
                id: dessert.id,
                quantity: dessert.quantity
            }))
        }));
    }

    // Getter methods for backward compatibility
    get tableId(): string | null {
        return this.table?.id || null;
    }

    get orderTypeId(): string | null {
        return this.orderType?.id || null;
    }

    get customerId(): string | null {
        return this.customerInfo?.id || null;
    }

    get branchId(): string | null {
        return this.branch?.id || null;
    }
}