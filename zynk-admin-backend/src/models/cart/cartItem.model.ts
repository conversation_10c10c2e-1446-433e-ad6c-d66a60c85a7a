import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { EasyDine } from '../base/easydine.base';
import { Cart } from './cart.model';
import { ItemType } from '../order/orderItem.model';

// Reusing interfaces from OrderItem
export interface NamedEntity {
    id: string;
    name: string;
}

export interface PriceNamedEntity extends NamedEntity {
    price: number;
}

export interface QuantityPriceNamedEntity extends PriceNamedEntity {
    quantity: number;
}

@Entity('cart_items')
export class CartItem extends EasyDine {
    @PrimaryGeneratedColumn('uuid')
    cartItemId: string;

    @Column()
    name: string;

    @Column('decimal', { precision: 10, scale: 2 })
    price: number;

    @Column('decimal', { precision: 10, scale: 2 })
    totalPrice: number;

    @Column('int')
    quantity: number;

    // Customization fields - identical to OrderItem
    @Column({
        type: 'enum',
        enum: ItemType,
        default: ItemType.STANDARD
    })
    type: ItemType;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Base item information with id and name for customized items'
    })
    baseItem: PriceNamedEntity;

    // Allergies stored as array of JSONB objects
    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected allergy options with id and name'
    })
    allergies: NamedEntity[];

    // Dish customization arrays - identical to OrderItem
    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish sizes with id and name'
    })
    dishSizes: PriceNamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish exclusions with id and name'
    })
    dishExclusions: NamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected cooking styles with id and name'
    })
    cookingStyles: NamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected spiciness levels with id and name'
    })
    spiciness: PriceNamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish addons with id and name'
    })
    dishAddons: QuantityPriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish extras with id and name'
    })
    dishExtras: QuantityPriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish sides with id and name'
    })
    dishSides: QuantityPriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish beverages with id and name'
    })
    dishBeverages: QuantityPriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish desserts with id and name'
    })
    dishDesserts: QuantityPriceNamedEntity[];

    @Column({
        type: 'text',
        nullable: true,
        comment: 'Special notes or customization instructions'
    })
    notes: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @ManyToOne(() => Cart, cart => cart.cartItems, {
        onDelete: "CASCADE",
    })
    cart: Cart;

    // Helper method to calculate total price for this item
    getTotalPrice(): number {
        return this.price * this.quantity;
    }

    // Helper method to check if item has customizations
    hasCustomizations(): boolean {
        return !!(
            this.allergies?.length ||
            this.dishSizes ||
            this.dishExclusions?.length ||
            this.cookingStyles ||
            this.spiciness ||
            this.dishAddons?.length ||
            this.dishExtras?.length ||
            this.dishSides?.length ||
            this.dishBeverages?.length ||
            this.dishDesserts?.length
        );
    }

    // Helper method to get customization summary
    getCustomizationSummary(): string[] {
        const customizations: string[] = [];

        if (this.dishSizes) {
            customizations.push(`Size: ${this.dishSizes.name}`);
        }
        if (this.spiciness) {
            customizations.push(`Spiciness: ${this.spiciness.name}`);
        }
        if (this.cookingStyles) {
            customizations.push(`Cooking Style: ${this.cookingStyles.name}`);
        }
        if (this.allergies?.length) {
            customizations.push(`Allergies: ${this.allergies.map(a => a.name).join(', ')}`);
        }
        if (this.dishExclusions?.length) {
            customizations.push(`Exclusions: ${this.dishExclusions.map(e => e.name).join(', ')}`);
        }
        if (this.dishAddons?.length) {
            customizations.push(`Addons: ${this.dishAddons.map(a => `${a.name} (${a.quantity})`).join(', ')}`);
        }
        if (this.dishExtras?.length) {
            customizations.push(`Extras: ${this.dishExtras.map(e => `${e.name} (${e.quantity})`).join(', ')}`);
        }
        if (this.dishSides?.length) {
            customizations.push(`Sides: ${this.dishSides.map(s => `${s.name} (${s.quantity})`).join(', ')}`);
        }
        if (this.dishBeverages?.length) {
            customizations.push(`Beverages: ${this.dishBeverages.map(b => `${b.name} (${b.quantity})`).join(', ')}`);
        }
        if (this.dishDesserts?.length) {
            customizations.push(`Desserts: ${this.dishDesserts.map(d => `${d.name} (${d.quantity})`).join(', ')}`);
        }

        return customizations;
    }
}