import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from "typeorm";
import { Dish } from "./dish.model";
import { Ingredient } from "../reference/ingredient.model";
import { EasyDine } from "../base/easydine.base";

@Entity("dish_ingredients")
export class DishIngredient extends EasyDine{
  @PrimaryGeneratedColumn("uuid")
  dishIngredientId: string;

  @Column("decimal", { scale: 2, nullable: false })
  amount: number;

  @ManyToOne(() => Dish, (dish) => dish.dishIngredients, {
    onDelete: "CASCADE",
  })
  dish: Dish;

  @ManyToOne(() => Ingredient, {
    eager: true,
    onDelete: "CASCADE",
  })
  ingredient: Ingredient;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
