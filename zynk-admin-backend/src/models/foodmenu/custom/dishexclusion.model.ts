import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Customization } from "../customization.model";

@Entity("dish_exclusions")
export class DishExclusion extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  dishExclusionId: string;

  @Column({
    nullable: false,
    length: 100,
  })
  name: string;

  @ManyToOne(
    () => Customization,
    (customization) => customization.dishExclusions,
    {
      onDelete: "CASCADE",
    }
  )
  customization: Customization;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
