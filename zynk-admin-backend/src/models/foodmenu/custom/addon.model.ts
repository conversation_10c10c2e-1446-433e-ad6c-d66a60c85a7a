import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Customization } from "../customization.model";
import { Branch } from "../../company/branch.model";

@Entity("dish_addons")
export class DishAddon extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  addonId: string;

  @Column({
    nullable: false,
    length: 100,
  })
  name: string;

  @Column({
    nullable: false,
    default: '',
  })
  imgUrl: string;

  @Column("decimal", { scale: 2, nullable: false })
  price: number;

  @ManyToOne(() => Branch, (branch) => branch.dishAddons, {
    onDelete: "CASCADE"
  })
  branch: Branch

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
