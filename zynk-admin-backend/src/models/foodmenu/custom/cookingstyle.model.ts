import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Customization } from "../customization.model";

@Entity("cooking_styles")
export class CookingStyle extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  cookingStyleId: string;

  @Column({
    nullable: false,
    length: 100,
  })
  name: string;

  @ManyToOne(
    () => Customization,
    (customization) => customization.cookingStyles,
    {
      onDelete: "CASCADE",
    }
  )
  customization: Customization;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
