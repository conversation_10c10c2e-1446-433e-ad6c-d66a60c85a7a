import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Customization } from "../customization.model";
import { Branch } from "../../company/branch.model";

@Entity("dish_extras")
export class DishExtra extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  extraId: string;

  @Column({
    nullable: false,
    length: 100,
  })
  name: string;

  @Column({
    nullable: false,
    default: '',
  })
  imgUrl: string;

  @Column("decimal", { scale: 2, nullable: false })
  price: number;

  @ManyToOne(() => Branch, (branch) => branch.dishExtras, {
    onDelete: "CASCADE",
  })
  branch: Branch;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
