import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Customization } from "../customization.model";
import { Dish } from "../dish.model";

@Entity("dish_sides")
export class DishSide extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  dishSideId: string;

  @Column({
    nullable: true,
  })
  dishId: string;

  @OneToOne(() => Dish, (dish) => dish.asSide, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "dishId" })
  dish: Dish;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
