import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Customization } from "../customization.model";

@Entity("dish_spiciness")
export class Spiciness extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  spicinessId: string;

  @Column({
    nullable: false,
    length: 100,
  })
  name: string;

  @Column("decimal", { scale: 2, nullable: false })
  price: number;

  @ManyToOne(() => Customization, (customization) => customization.spiciness, {
    onDelete: "CASCADE",
  })
  customization: Customization;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
