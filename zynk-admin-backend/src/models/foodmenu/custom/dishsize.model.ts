import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Customization } from "../customization.model";

@Entity("dish_sizes")
export class DishSize extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  dishSizeId: string;

  @Column({
    nullable: false,
    length: 100,
  })
  name: string;

  @Column("decimal", { scale: 2, nullable: false })
  price: number;

  @Column("boolean", {
    nullable: false,
    default: false,
  })
  isDefault: boolean;

  @ManyToOne(() => Customization, (customization) => customization.dishSizes, {
    onDelete: "CASCADE",
  })
  customization: Customization;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
