import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { FoodMenu } from "./foodmenu.model";
import { EasyDine } from "../base/easydine.base";
import { SectionIcon } from "../reference/sectionicon.model";
import { CustomHourSlot } from "../common/customhourslot.model";
import { SpecialDay } from "../common/specialday.model";
import { Dish } from "./dish.model";

@Entity("sections")
export class Section extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  menuSectionId: string;

  @Column("simple-array", {
    nullable: true,
  })
  pictureUrls: string[];

  @Column({
    length: 100,
    nullable: false,
  })
  name: string;

  @Column("text", {
    nullable: true,
  })
  description: string;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @Column("jsonb", {
    nullable: false,
    default: {
      delivery: true,
      pickup: true,
      dineIn: true,
      takeAway: true,
      contactlessDineIn: true,
      site: true,
      mobile: true,
      phoneOrder: true,
    },
  })
  availability: {
    delivery: boolean;
    pickup: boolean;
    dineIn: boolean;
    takeAway: boolean;
    contactlessDineIn: boolean;
    site: boolean;
    mobile: boolean;
    phoneOrder: boolean;
  };

  @Column({
    type: "boolean",
    nullable: false,
    default: true,
  })
  visibility: boolean;

  @OneToMany(() => SpecialDay, (spDay) => spDay.section, {
    cascade: true,
  })
  specialDays: SpecialDay[];

  @ManyToOne(() => FoodMenu, (foodMenu) => foodMenu.sections, {
    onDelete: "CASCADE",
  })
  foodMenu: FoodMenu;

  @OneToMany(() => Dish, (dish) => dish.section, { cascade: true })
  dishes: Dish[];

  @ManyToOne(() => SectionIcon, (sectionIcon) => sectionIcon.sections)
  sectionIcon: SectionIcon;

  @Column("boolean", {
    nullable: false,
    default: false,
  })
  customTimes: boolean;

  @OneToMany(() => CustomHourSlot, (slot) => slot.section, {
    cascade: true,
  })
  customSlots: CustomHourSlot[];

  @ManyToMany(() => CustomHourSlot)
  @JoinTable({
    name: "section_globalhours"
  })
  globalCustomSlots: CustomHourSlot[];

  // /**
  //  * @constructor for the Section class.
  //  * When creating a new Section instance, it initializes the preparationDuration property
  //  * with a new PreparationDuration instance if it is not already set.
  //  * This ensures that every section has a preparation duration associated with it.
  //  */

  // constructor() {
  //     super();
  //     if (!this.preparationDuration) {
  //         const prep = new PreparationDuration();
  //         prep.section = this;
  //         this.preparationDuration = prep;
  //     }
  // }
}
