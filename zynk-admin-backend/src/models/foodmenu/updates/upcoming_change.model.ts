import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON>inColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { FoodMenu } from '../foodmenu.model';
import { DishUpcomingChange } from './dish_upcoming_change.model';

@Entity("upcoming_changes")
export class UpcomingChange {
  @PrimaryGeneratedColumn("uuid")
  changeId: string;

  @ManyToOne(() => FoodMenu, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "foodMenuId"})
  foodMenu: FoodMenu;

  @Column()
  foodMenuId: string;

  @Column("date")
  activeFrom: Date;

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => DishUpcomingChange, dishChange => dishChange.upcomingChange, { cascade: true })
  dishChanges: DishUpcomingChange[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}