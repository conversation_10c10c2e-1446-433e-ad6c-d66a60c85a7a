import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, <PERSON>umn, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { dietaryInfoLabels, Dish } from '../dish.model';
import { UpcomingChange } from './upcoming_change.model';

@Entity("dish_upcoming_changes")
export class DishUpcomingChange {
  @PrimaryGeneratedColumn("uuid")
  dishChangeId: string;

  @ManyToOne(() => UpcomingChange, upcomingChange => upcomingChange.dishChanges, { onDelete: 'CASCADE' })
  @JoinColumn({ name: "changeId" })
  upcomingChange: UpcomingChange;

  @Column()
  changeId: string;

  @ManyToOne(() => Dish, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "dishId" })
  dish: Dish;

  @Column()
  dishId: string;

  @Column({ length: 100, nullable: true })
  newName?: string;

  @Column("decimal", { scale: 2, nullable: true })
  newPrice?: number;

  @Column("simple-array", { nullable: true })
  newDietaryInfo?: dietaryInfoLabels[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}