import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { CustomHourSlot } from "../common/customhourslot.model";
import { DishIngredient } from "./dish_ingredient.model";
import { Allergy } from "../reference/allergy.model";
import { DishSide } from "./custom/dish_side.model";
import { DishBeverage } from "./custom/dish_bev.model";
import { DishDessert } from "./custom/dish_dessert.model";
import { Customization } from "./customization.model";
import { SpecialDay } from "../common/specialday.model";
import { Section } from "./section.model";

export type dietaryInfoLabels =
  | "special"
  | "vegan"
  | "vegetarian"
  | "gluten-free"
  | "organic"
  | "spicy"
  | "hot"
  | "extra-hot";

type dishTagLabels = "side" | "beverage" | "dessert";

@Entity("dishes")
export class Dish extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  dishId: string;

  @Column("text", {
    nullable: true,
  })
  pictureUrl: string;

  @Column({
    length: 100,
    nullable: false,
  })
  name: string;

  @Column("text", {
    nullable: true,
  })
  description: string;

  @Column("decimal", { scale: 2, nullable: false })
  price: number;

  @Column("simple-array", {
    nullable: true,
  })
  dietaryInfo: dietaryInfoLabels[];

  @Column("simple-array", {
    nullable: true,
  })
  tags: dishTagLabels[];

  @OneToMany(() => DishIngredient, (di) => di.dish, {
    cascade: true,
  })
  dishIngredients: DishIngredient[];

  @ManyToMany(() => Allergy)
  @JoinTable({
    name: "dish_allergies",
  })
  allergies: Allergy[];

  @Column("boolean", {
    nullable: false,
    default: false,
  })
  isCustomizable: boolean;

  @Column("int", {
    nullable: true,
    comment: "Preparation time in minutes",
  })
  preparationTime: number;

  @OneToOne(() => Customization, (customization) => customization.dish, {
    cascade: true,
    nullable: true,
  })
  customization: Customization;

  @OneToOne(() => DishSide, (dishSide) => dishSide.dish)
  asSide: DishSide;

  @OneToOne(() => DishBeverage, (dishBeverage) => dishBeverage.dish)
  asBeverage: DishBeverage;

  @OneToOne(() => DishDessert, (dishDessert) => dishDessert.dish)
  asDessert: DishDessert;

  @Column("jsonb", {
    nullable: false,
    default: {
      global: true,
      delivery: true,
      pickup: true,
      dineIn: true,
      takeAway: true,
      contactlessDineIn: true,
      site: true,
      mobile: true,
      phoneOrder: true,
    },
  })
  availability: {
    global: boolean;
    delivery: boolean;
    pickup: boolean;
    dineIn: boolean;
    takeAway: boolean;
    contactlessDineIn: boolean;
    site: boolean;
    mobile: boolean;
    phoneOrder: boolean;
  };

  @Column({
    type: "boolean",
    nullable: false,
    default: true,
  })
  visibility: boolean;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @ManyToOne(() => Section, (section) => section.dishes, {
    onDelete: "CASCADE",
  })
  section: Section;

  @Column("boolean", {
    nullable: false,
    default: false,
  })
  customTimes: boolean;

  @Column("boolean", {
    nullable: false,
    default: true,
  })
  specialRequests: boolean;

  @OneToMany(() => CustomHourSlot, (slot) => slot.dish, {
    cascade: true,
  })
  customSlots: CustomHourSlot[];

  @ManyToMany(() => CustomHourSlot)
  @JoinTable({
    name: "dish_globalhours"
  })
  globalCustomSlots: CustomHourSlot[];

  @OneToMany(() => SpecialDay, (spDay) => spDay.dish, {
    cascade: true,
  })
  specialDays: SpecialDay[];
}
