import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Section } from "./section.model";
import { Branch } from "../company/branch.model";
import { EasyDine } from "../base/easydine.base";
import { CustomHourSlot } from "../common/customhourslot.model";
import { SpecialDay } from "../common/specialday.model";

@Entity("food_menus")
export class FoodMenu extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  foodMenuId: string;

  @Column("text", {
    nullable: true,
  })
  pictureUrl: string;

  @Column("boolean", {
    nullable: false,
    default: false,
  })
  isDefault: boolean;

  @Column({
    unique: true,
    length: 100,
  })
  name: string;

  @Column("text", {
    nullable: true,
  })
  description: string;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @Column("jsonb", {
    nullable: false,
    default: {
      delivery: true,
      pickup: true,
      dineIn: true,
      takeAway: true,
      contactlessDineIn: true,
      site: true,
      mobile: true,
      phoneOrder: true,
    },
  })
  availability: {
    delivery: boolean;
    pickup: boolean;
    dineIn: boolean;
    takeAway: boolean;
    contactlessDineIn: boolean;
    site: boolean;
    mobile: boolean;
    phoneOrder: boolean;
  };

  @Column({
    type: "boolean",
    nullable: false,
    default: true,
  })
  visibility: boolean;

  @OneToMany(() => SpecialDay, (spDay) => spDay.foodMenu, {
    cascade: true,
  })
  specialDays: SpecialDay[];

  @ManyToOne(() => Branch, (branch) => branch.foodMenus, {
    onDelete: "CASCADE",
  })
  branch: Branch;

  @OneToMany(() => Section, (section) => section.foodMenu, { cascade: true })
  sections: Section[];

  @Column("boolean", {
    nullable: false,
    default: false,
  })
  customTimes: boolean;

  @OneToMany(() => CustomHourSlot, (slot) => slot.foodMenu, {
    cascade: true,
  })
  customSlots: CustomHourSlot[];

  @ManyToMany(() => CustomHourSlot)
  @JoinTable({
    name: "foodmenu_globalhours"
  })
  globalCustomSlots: CustomHourSlot[];
}
