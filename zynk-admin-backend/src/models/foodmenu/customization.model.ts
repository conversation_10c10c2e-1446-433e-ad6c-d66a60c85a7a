import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { DishSize } from "./custom/dishsize.model";
import { DishExclusion } from "./custom/dishexclusion.model";
import { CookingStyle } from "./custom/cookingstyle.model";
import { Spiciness } from "./custom/spiciness.model";
import { DishAddon } from "./custom/addon.model";
import { DishExtra } from "./custom/extra.model";
import { DishSide } from "./custom/dish_side.model";
import { DishBeverage } from "./custom/dish_bev.model";
import { DishDessert } from "./custom/dish_dessert.model";
import { Dish } from "./dish.model";

@Entity("customizations")
export class Customization extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  customizationId: string;

  @Column("int", { nullable: false, default: 4})
  maxSelection: number;

  @Column("boolean", {
    nullable: false,
    default: true,
  })
  enforceMaxSelection: boolean;

  @Column({
    nullable: true,
  })
  dishId: string;

  @OneToOne(() => Dish, (dish) => dish.customization, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "dishId" })
  dish: Dish;

  @OneToMany(() => DishSize, (dishSize) => dishSize.customization, {
    cascade: true,
  })
  dishSizes: DishSize[];

  @OneToMany(
    () => DishExclusion,
    (dishExclusion) => dishExclusion.customization,
    {
      cascade: true,
    }
  )
  dishExclusions: DishExclusion[];

  @OneToMany(() => CookingStyle, (cookingStyle) => cookingStyle.customization, {
    cascade: true,
  })
  cookingStyles: CookingStyle[];

  @OneToMany(() => Spiciness, (spiciness) => spiciness.customization, {
    cascade: true,
  })
  spiciness: Spiciness[];

  @ManyToMany(() => DishAddon)
  @JoinTable({
    name: "customization_dish_addons",
  })
  dishAddons: DishAddon[];

  @ManyToMany(() => DishExtra)
  @JoinTable({
    name: "customization_dish_extras",
  })
  dishExtras: DishExtra[];

  @ManyToMany(() => DishSide)
  @JoinTable({
    name: "customization_dish_sides",
  })
  dishSides: DishSide[];

  @ManyToMany(() => DishBeverage)
  @JoinTable({
    name: "customization_dish_beverages",
  })
  dishBeverages: DishBeverage[];

  @ManyToMany(() => DishDessert)
  @JoinTable({
    name: "customization_dish_desserts",
  })
  dishDesserts: DishDessert[];

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
