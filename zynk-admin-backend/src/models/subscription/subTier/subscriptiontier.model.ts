import {
    <PERSON>umn,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinTable,
    ManyToMany,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from "typeorm";
import {Subscription} from "../subscriptions.model";
import {EasyDine} from "../../base/easydine.base";
import {Feature} from "../feature/feature.model";
import {Duration} from "../../../types/subscription";
import {deflateRaw} from "node:zlib";

@Entity("subscription_tiers")
export class SubscriptionTier extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    subTierId: string;

    @Column({
        nullable: false,
        unique: true,
    })
    name: string;

    @Column({
        type: "text",
        nullable: true,
    })
    description: string;

    @Column({type: "boolean", default: false, nullable: false})
    isActive: boolean;

    @Column({type: "boolean", default: false, nullable: false})
    isUpdated: boolean

    @Column({type: "boolean", default: false, nullable: true})
    isReflectToNight: boolean;

    @Column({
        type: "decimal",
        scale: 2
    })
    price: number;

    @Column({
        type: "enum",
        enum: Duration,
        default: Duration.MONTHLY
    })
    tierType: Duration;

    @Column({
        type: "int",
        nullable: false,
        default: 0
    })
    validityDuration: number;

    @OneToMany(() => Subscription, (sub) => sub.subTier)
    subscriptions: Subscription[];

    @ManyToMany(() => Feature, (feature) => feature.subscriptionTiers, {
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    })
    @JoinTable({
        name: "subscription_tiers_features",
        joinColumns: [{name: "subTierId", referencedColumnName: "subTierId"}],
        inverseJoinColumns: [{name: "featureId", referencedColumnName: "featureId"}],
    })
    features: Feature[];

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;

    @DeleteDateColumn({type: "timestamptz"})
    deletedAt?: Date;
}
