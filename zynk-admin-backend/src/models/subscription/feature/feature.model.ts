import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    ManyToMany,
    OneToMany,
    PrimaryGeneratedColumn,
    Repository,
    UpdateDateColumn
} from "typeorm";
import {EasyDine} from "../../base/easydine.base";
import {SubscriptionTier} from "../subTier/subscriptiontier.model";
import {FeaturePermissionAction} from "./FeaturePermissionAction.model";
import {FormatType} from "../../../types/pBAC/formatterTypes";
import {processPermissionActions, updatePermissionAction} from "../../../helpers/pBAC/model.helper";
import {PermActionModelType} from "../../../types/pBAC";
import {PermissionAction} from "../../pBAC/PermissionAction";

@Entity("features")
export class Feature extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    featureId: string;

    @Column({nullable: false, unique: true})
    name: string;

    @Column({type: "text", nullable: true})
    description: string;

    @Column({type: "boolean", nullable: false, default: true})
    isActive: boolean;

    @OneToMany(() => FeaturePermissionAction, fpa => fpa.feature)
    featurePermissionActions: FeaturePermissionAction[];

    @ManyToMany(() => SubscriptionTier, (subscriptionTier) => subscriptionTier.features)
    subscriptionTiers: SubscriptionTier[];

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;

    async updatePermissionAction(
        permissionActionIds: PermActionModelType[],
        permissionAction: Repository<PermissionAction>,
        FeaturePermissionAction: Repository<FeaturePermissionAction>
    ) {
        try {
            const result = await updatePermissionAction(
                permissionActionIds,
                [],
                this.featurePermissionActions ?? [],
                this.featureId,
                "feature",
                "featureId",
                FeaturePermissionAction,
                permissionAction,
            );
            const {updatedUnknownPermissions, ...rest} = result
            return rest;
        } catch (e) {
            throw e;
        }
    }

    async getPermissionAction(formatType: FormatType) {
        try {
            return processPermissionActions(
                [],
                this.featurePermissionActions,
                {
                    formatType: formatType,
                    requiredActive: true,
                }
            );
        } catch (e) {
            throw e;
        }
    }
}