import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn} from "typeorm";
import {OperationType} from "../../../types/pBAC";
import {PermissionAction} from "../../pBAC/PermissionAction";
import {Feature} from "./feature.model";

@Entity({name: 'feature_permission_action'})
export class FeaturePermissionAction {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column({
        type: 'enum',
        enum: OperationType,
        default: OperationType.PLUS,
    })
    operationType: OperationType;

    @ManyToOne(() => Feature, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "featureId"})
    feature: Feature;

    @ManyToOne(() => PermissionAction, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "pAId"})
    permissionAction: PermissionAction;
}