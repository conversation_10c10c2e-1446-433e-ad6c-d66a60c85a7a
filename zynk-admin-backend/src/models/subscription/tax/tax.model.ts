import {EasyDine} from "../../base/easydine.base";
import {Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

@Entity("taxes")
@Index(["countryCode", "stateCode", "upperLimit", "lowerLimit", "isCentral"], {unique: true})
export class Tax extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    taxId: string;

    @Column({nullable: false})
    name: string;

    @Column({nullable: true})
    description: string;

    @Column({nullable: true})
    taxGovId: string;

    @Column({type: "boolean", nullable: false, default: false})
    isActive: boolean

    @Column({type: "boolean", nullable: false, default: false})
    isCentral: boolean

    @Column({nullable: false})
    countryCode: string;

    @Column({nullable: true})
    stateCode: string;

    @Column({type: "decimal", nullable: false, default: 0})
    taxPercentage: number;

    @Column({type: "decimal", nullable: false, default: 0})
    upperLimit: number;

    @Column({type: "decimal", nullable: false, default: 0})
    lowerLimit: number;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;
}