import {Column, CreateDateColumn, Entity, Index, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {Tenant} from "../admin/Tenant/tenant.model";
import {SubscriptionTier} from "./subTier/subscriptiontier.model";
import {EasyDine} from "../base/easydine.base";
import {Duration, SubscriptionStatus, SubsOperationType} from "../../types/subscription";

@Entity("subscriptions")
@Index(['tenant.tenantId', 'status'], {unique: true, where: `status = '${SubscriptionStatus.ACTIVE}'`})
export class Subscription extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    subscriptionId: string;


    // Subscription Tier
    @ManyToOne(() => SubscriptionTier, (subTier) => subTier.subscriptions, {
        onDelete: "CASCADE",
    })
    subTier: SubscriptionTier;
    @Column({type: "json", nullable: false, default: {}})
    activatedFeatures: any

    @Column({type: "decimal", scale: 2})
    subTiersPurchasePricePerDay: number;


    // Tenant
    @ManyToOne(() => Tenant, (tenant) => tenant.subscriptions, {
        onDelete: 'SET NULL',
        nullable: true,
    })
    tenant: Tenant;

    @Column({type: "enum", enum: Duration, default: Duration.MONTHLY})
    durationType: Duration

    @Column({type: "integer", default: 1, nullable: false})
    durationValue: number;

    @Column({type: "decimal", scale: 2})
    paidPrice: number;

    @Column({type: "decimal", scale: 2, default: 0})
    duePrice: number;

    @Column({type: "enum", enum: SubsOperationType, default: SubsOperationType.ADD_NEW})
    performedOperation: SubsOperationType

    @Column({type: "timestamptz", nullable: false, default: new Date()})
    activeFrom: Date;

    @Column({type: "timestamptz", nullable: false})
    activeTill: Date;

    //Tax
    @Column({nullable: true})
    taxName: string;

    @Column({type: "decimal", scale: 2})
    presentTaxPercentage: number;

    @Column({nullable: false})
    countryCode: string;

    @Column({type: "boolean", default: true, nullable: false})
    isCentral: boolean;

    @Column({nullable: true})
    stateCode: string;

    @Column({nullable: true})
    taxGovId: string;

    // Subscription
    @Column({type: "decimal", scale: 2, nullable: false})
    totalCost: number;

    @Column({
        type: "enum",
        enum: SubscriptionStatus,
        default: SubscriptionStatus.ACTIVE
    })
    status: SubscriptionStatus;

    @Column({
        nullable: true
    })
    reasonForCancellation: string;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;
}
