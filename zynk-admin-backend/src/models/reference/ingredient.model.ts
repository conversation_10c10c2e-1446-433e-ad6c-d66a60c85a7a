import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Branch } from "../company/branch.model";

@Entity("ingredients")
export class Ingredient extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  ingredientId: string;

  @Column({
    length: 100,
    nullable: false,
  })
  name: string;

  @Column("text", {
    nullable: true,
  })
  description: string;

  @Column({
    length: 50,
    nullable: false,
    default: "g",
  })
  unit: string;

  @ManyToOne(() => Branch, (branch) => branch.ingredients, {
    onDelete: "CASCADE",
  })
  branch: Branch;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
