import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Section } from "../foodmenu/section.model";
import { Branch } from "../company/branch.model";

@Entity("section_icons")
export class SectionIcon extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  sectionIconId: string;

  @Column({
    nullable: false,
    unique: true,
    length: 100,
  })
  iconName: string;

  @Column("text", {
    nullable: false,
    unique: true,
  })
  iconImg: string;

  @ManyToOne(() => Branch, (branch) => branch.sectionIcons, {
    onDelete: "CASCADE",
  })
  branch: Branch;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @OneToMany(() => Section, (section) => section.sectionIcon)
  sections: Section[];
}
