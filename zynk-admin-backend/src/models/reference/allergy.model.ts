import { Column, CreateDateColumn, DeleteDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Branch } from "../company/branch.model";

export enum ColorPrefs {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  TERTIARY = 'tertiary',
}

@Entity("allergies")
export class Allergy extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  allergyId: string;

  @Column({
    length: 100,
    nullable: false,
  })
  name: string;

  @Column("text", {
    nullable: true,
  })
  description: string;

  @Column("enum", {
    enum: ColorPrefs,
    default: ColorPrefs.PRIMARY,
    nullable: false,
  })
  colorPreference: string

  @ManyToOne(() => Branch, (branch) => branch.allergies, {
    onDelete: "CASCADE"
  })
  branch: Branch

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}