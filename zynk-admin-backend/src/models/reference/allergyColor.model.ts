import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { Branch } from "../company/branch.model";

@Entity("allergy_colors")
export class AllergyColor extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  allergyColorId: string;

  @Column({
    length: 100,
    nullable: false,
    unique: true,
  })
  name: string;

  @Column({
    length: 100,
    nullable: false,
  })
  color: string;

  @ManyToOne(() => Branch, (branch) => branch.allergyColors, {
    onDelete: "CASCADE",
  })
  branch: Branch;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
