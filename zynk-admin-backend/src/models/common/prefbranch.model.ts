
import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";

export enum PrefUserType{
    TENANT = 'tenant',
    STAFF = 'staff',
    CUSTOMER = 'customer'
}

@Entity("preferred_branches")
export class PrefBranch extends EasyDine{
  @PrimaryGeneratedColumn("uuid")
  prefBranchIndexId: string;

  @Column("uuid", {
    nullable: true,
  })
  prefBranchId: string | null;

  @Column({
    nullable: true
  })
  userId: string

  @Column({
    type: 'enum',
    enum: PrefUserType,
    default: PrefUserType.STAFF,
    nullable: true
  })
  userType: string

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
