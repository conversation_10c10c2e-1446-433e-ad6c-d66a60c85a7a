import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToOne,
  Column,
  JoinColumn,
} from "typeorm";
import { BusinessHourSlot } from "./businesshourslot.model";
import { CustomHourSlot } from "./customhourslot.model";
import { Branch } from "../company/branch.model";
import { SpecialDay } from "./specialday.model";
import { EasyDine } from "../base/easydine.base";

@Entity("business_hours")
export class BusinessHour extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  businessHourId: string;

  @Column('boolean',{
    nullable: false,
    default: true
  })
  useDefault: boolean

  @OneToMany(() => BusinessHourSlot, (slot) => slot.businessHour, {
    cascade: true,
    eager: true,
  })
  slots: BusinessHourSlot[];

  @OneToMany(() => CustomHourSlot, (slot) => slot.businessHour, {
    cascade: true,
    eager: true,
  })
  customSlots: CustomHourSlot[];

  @OneToMany(() => SpecialDay, (spDay) => spDay.businessHour, {
    cascade: true,
    eager: true
  })
  specialDays: SpecialDay[];

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @Column({ nullable: true })
  branchId: string;

  @OneToOne(() => Branch, (branch) => branch.businessHour, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "branchId" })
  branch: Branch;
}
