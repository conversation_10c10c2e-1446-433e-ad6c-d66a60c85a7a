import {
  Entity,
  PrimaryGeneratedColumn,
  ManyToOne,
} from "typeorm";
import { BusinessHour } from "./businesshour.model";
import { BusinessHourSlotBase } from "../base/businesshour.base";

@Entity("business_hour_slots")
export class BusinessHourSlot extends BusinessHourSlotBase {
  @PrimaryGeneratedColumn("uuid")
  slotId: string;

  @ManyToOne(() => BusinessHour, (businessHour) => businessHour.slots, {
    onDelete: "CASCADE",
  })
  businessHour: BusinessHour;
}
