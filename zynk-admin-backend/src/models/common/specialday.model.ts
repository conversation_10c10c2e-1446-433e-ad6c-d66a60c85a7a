import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
} from "typeorm";
import { BusinessHour } from "./businesshour.model";
import { EasyDine } from "../base/easydine.base";
import { FoodMenu } from "../foodmenu/foodmenu.model";
import { Section } from "../foodmenu/section.model";
import { Dish } from "../foodmenu/dish.model";
import { OnlineReservationConfig } from "../tenantSettings/tableReservation/online/online_reserv.model";

@Entity("special_days")
export class SpecialDay extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  specialDayId: string;

  @Column("text")
  eventName: string;

  @Column("timestamptz")
  startTime: Date;

  @Column("timestamptz")
  endTime: Date;

  @Column("boolean", {
    default: false,
    nullable: false,
  })
  availability: boolean;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @ManyToOne(() => BusinessHour, (businessHour) => businessHour.specialDays, {
    onDelete: "CASCADE",
  })
  businessHour: BusinessHour;

  @ManyToOne(() => FoodMenu, (foodMenu) => foodMenu.specialDays, {
    onDelete: "CASCADE",
  })
  foodMenu: FoodMenu;

  @ManyToOne(() => Section, (section) => section.specialDays, {
    onDelete: "CASCADE",
  })
  section: Section;

  @ManyToOne(() => Dish, (dish) => dish.specialDays, {
    onDelete: "CASCADE",
  })
  dish: Dish;

  @ManyToOne(() => OnlineReservationConfig, (online_reservation) => online_reservation.specialDays, {
    onDelete: "CASCADE",
  })
  online_reservation: OnlineReservationConfig;
}
