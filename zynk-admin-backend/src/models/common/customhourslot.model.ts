import { Entity, PrimaryGeneratedColumn, ManyToOne, Column } from "typeorm";
import { BusinessHour } from "./businesshour.model";
import { BusinessHourSlotBase } from "../base/businesshour.base";
import { FoodMenu } from "../foodmenu/foodmenu.model";
import { Section } from "../foodmenu/section.model";
import { Dish } from "../foodmenu/dish.model";
import { OnlineReservationConfig } from "../tenantSettings/tableReservation/online/online_reserv.model";

@Entity("custom_hour_slots")
export class CustomHourSlot extends BusinessHourSlotBase {
  @PrimaryGeneratedColumn("uuid")
  slotId: string;

  @Column({
    nullable: false
  })
  name: string;

  @ManyToOne(() => BusinessHour, (businessHour) => businessHour.customSlots, {
    onDelete: "CASCADE",
  })
  businessHour: BusinessHour;

  @ManyToOne(() => FoodMenu, (foodMenu) => foodMenu.customSlots, {
    onDelete: "CASCADE",
  })
  foodMenu: FoodMenu;

  @ManyToOne(() => Section, (section) => section.customSlots, {
    onDelete: "CASCADE",
  })
  section: Section;

  @ManyToOne(() => Dish, (dish) => dish.customSlots, {
    onDelete: "CASCADE",
  })
  dish: Dish;

  @ManyToOne(() => OnlineReservationConfig, (online_reservation) => online_reservation.customSlots, {
    onDelete: "CASCADE",
  })
  online_reservation: OnlineReservationConfig;
}
