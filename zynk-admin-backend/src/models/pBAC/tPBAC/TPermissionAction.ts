import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn} from "typeorm";
import {TPermissions} from "./TPermissions.model";
import {TAction} from "./TAction.entity";

@Entity({name: 'permission_action'})
export class TPermissionAction {
    @PrimaryGeneratedColumn("uuid")
    pAId: string;

    @ManyToOne(() => TPermissions, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({
        name: 'permissionId',
        referencedColumnName: 'permissionId'
    })
    permission: TPermissions;

    @ManyToOne(() => TAction, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({
        name: 'actionId',
        referencedColumnName: 'actionId'
    })
    action: TAction;
}