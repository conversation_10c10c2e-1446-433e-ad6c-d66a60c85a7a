import {<PERSON><PERSON><PERSON>, <PERSON>To<PERSON>any, <PERSON>, <PERSON><PERSON><PERSON>, TreeParent} from "typeorm";
import {ModuleBase} from "../../base/pBAC/Module.base";
import {TPermissions} from "./TPermissions.model";

@Entity("modules")
@Tree("closure-table")
export class TModule extends ModuleBase {

    @OneToMany(() => TPermissions, (TPermissions) => TPermissions.module)
    permissions: TPermissions[];

    @TreeChildren()
    subModules: TModule[];

    @TreeParent()
    parentModule: TModule;

}