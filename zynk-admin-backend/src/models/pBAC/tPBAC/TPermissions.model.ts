import {PermissionBase} from "../../base/pBAC/Permission.base";
import {Entity, JoinTable, ManyToMany, ManyToOne} from "typeorm";
import {TAction} from "./TAction.entity";
import {TModule} from "./TModule.model";

@Entity("permissions")
export class TPermissions extends PermissionBase {
    
    @ManyToOne(() => TModule, (module) => module.permissions, {
        nullable: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        cascade: true,
    })
    module: TModule;

    @ManyToMany(() => TAction, (action) => action.permissions)
    @JoinTable({
        name: "permission_actions",
        joinColumn: {
            name: "permissionId",
            referencedColumnName: "permissionId",
        },
        inverseJoinColumn: {
            name: "actionId",
            referencedColumnName: "actionId",
        }
    })
    actions: TAction[];
}