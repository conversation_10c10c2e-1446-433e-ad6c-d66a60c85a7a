import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn} from "typeorm";
import {Permission} from "./Permission.model";
import {Action} from "./Action.model";

@Entity({name: 'permission_action'})
export class PermissionAction {
    @PrimaryGeneratedColumn("uuid")
    pAId: string;

    @ManyToOne(() => Permission, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({
        name: 'permissionId',
        referencedColumnName: 'permissionId'
    })
    permission: Permission;

    @ManyToOne(() => Action, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({
        name: 'actionId',
        referencedColumnName: 'actionId'
    })
    action: Action;
}