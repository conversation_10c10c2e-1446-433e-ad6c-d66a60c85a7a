import {Entity, JoinTable, <PERSON>To<PERSON>any, ManyToOne} from "typeorm";
import {PermissionBase} from "../base/pBAC/Permission.base";
import {Action} from "./Action.model";
import {Module} from "./Module.model";

@Entity("permissions")
export class Permission extends PermissionBase {

    @ManyToOne(() => Module, (module) => module.permissions, {
        nullable: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        cascade: true,
    })
    module: Module;

    @ManyToMany(() => Action, (action) => action.permissions)
    @JoinTable({
        name: "permission_actions",
        joinColumn: {
            name: "permissionId",
            referencedColumnName: "permissionId",
        },
        inverseJoinColumn: {
            name: "actionId",
            referencedColumnName: "actionId",
        }
    })
    actions: Action[];
}