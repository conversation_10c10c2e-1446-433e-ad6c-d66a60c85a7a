import {PermissionAction} from "../PermissionAction";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn} from "typeorm";
import {Role} from "./Role.model";

@Entity({name: 'role_permission_action'})
export class RolePermissionAction {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @ManyToOne(() => Role, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "roleId"})
    role: Role;

    @ManyToOne(() => PermissionAction, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "pAId"})
    permissionAction: PermissionAction;
}