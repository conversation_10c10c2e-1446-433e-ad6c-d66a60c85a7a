import {RoleBase} from "../../base/pBAC/Role.base";
import {<PERSON>tity, OneToMany} from "typeorm";
import {RolePermissionAction} from "./RolePermissionAction";
import {SuperAdmin} from "../../admin/superadmin.model";
import {Tenant} from "../../admin/Tenant/tenant.model";

@Entity("roles")
export class Role extends RoleBase {
    @OneToMany(() => RolePermissionAction, rpa => rpa.role)
    rolePermissionActions: RolePermissionAction[];

    @OneToMany(() => SuperAdmin, superAdmin => superAdmin.role, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    superAdmins: SuperAdmin[];

    @OneToMany(() => Tenant, tenant => tenant.role, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    tenants: Tenant[];
}