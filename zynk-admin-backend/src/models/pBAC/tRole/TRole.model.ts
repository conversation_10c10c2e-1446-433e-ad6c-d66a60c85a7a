import {<PERSON>ti<PERSON>, <PERSON>To<PERSON>any} from "typeorm";
import {RoleBase} from "../../base/pBAC/Role.base";
import {Staff} from "../../staff/staff.model";
import {TRolePermissionAction} from "./TRolePermissionAction";
import {Customer} from "../../customer/customer.model";

@Entity("roles")
export class TRole extends RoleBase {
    @OneToMany(() => TRolePermissionAction, rpa => rpa.role)
    rolePermissionActions: TRolePermissionAction[];

    @OneToMany(() => Staff, Staff => Staff.role, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    staff: Staff[];

    @OneToMany(() => Customer, Customer => Customer.role, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    customer: Customer[];

}