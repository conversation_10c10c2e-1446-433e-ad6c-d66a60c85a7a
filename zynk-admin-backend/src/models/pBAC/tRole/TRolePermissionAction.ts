import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn} from "typeorm";
import {TRole} from "./TRole.model";
import {TPermissionAction} from "../tPBAC/TPermissionAction";

@Entity({name: 'role_permission_action'})
export class TRolePermissionAction {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @ManyToOne(() => TRole, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "roleId"})
    role: TRole;

    @ManyToOne(() => TPermissionAction, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "pAId"})
    permissionAction: TPermissionAction;
}