import {<PERSON><PERSON><PERSON>, <PERSON>To<PERSON>any, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, TreeParent} from "typeorm";
import {ModuleBase} from "../base/pBAC/Module.base";
import {Permission} from "./Permission.model";

@Entity("modules")
@Tree("closure-table")
export class Module extends ModuleBase {

    @OneToMany(() => Permission, (Permission) => Permission.module)
    permissions: Permission[];

    @TreeChildren()
    subModules: Module[];

    @TreeParent()
    parentModule: Module;

}