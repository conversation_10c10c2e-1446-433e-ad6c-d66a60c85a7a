import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../../base/easydine.base";
import { TableModel } from "./table.model";
import { Branch } from "../../../company/branch.model";
import { TableCombination } from "./tableCombination.model";

@Entity("floors")
export class Floor extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  floorId: string;

  @Column({
    nullable: false,
  })
  name: string;

  @OneToMany(() => TableModel, (table) => table.floor, {
    cascade: true,
  })
  tables: TableModel[];

  @OneToMany(() => TableCombination, (tableCombination) => tableCombination.floor, {
    cascade: true,
  })
  tableCombinations: TableCombination[];

  @ManyToOne(() => Branch, (branch) => branch.floors, {
    onDelete: "CASCADE",
  })
  branch: Branch;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
