import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../../base/easydine.base";
import { TableModel } from "./table.model";
import { Branch } from "../../../company/branch.model";
import { Floor } from "./floor.model";

@Entity("table_combinations")
export class TableCombination extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  tableCombinationId: string;

  @Column({
    type: "int",
    nullable: false,
    default: 1,
  })
  minSeats: number;

  @Column({
    type: "int",
    nullable: false,
    default: 4,
  })
  maxSeats: number;

  @Column({
    type: "boolean",
    default: true,
  })
  enabled: boolean;

  @Column({
    type: "boolean",
    default: true,
  })
  availableOnline: boolean;

  @ManyToMany(() => TableModel, (table) => table.tableCombinations, {
    cascade: true,
    onDelete: "CASCADE"
  })
  @JoinTable({
    name: "table_combination_tables",
    joinColumn: {
      name: "tableCombinationId",
      referencedColumnName: "tableCombinationId",
    },
    inverseJoinColumn: {
      name: "tableId",
      referencedColumnName: "tableId",
    },
  })
  tables: TableModel[];

  @ManyToOne(() => Branch, (branch) => branch.tableCombinations, {
    onDelete: "CASCADE",
  })
  branch: Branch;

  @ManyToOne(() => Floor, (floor) => floor.tableCombinations, {
    onDelete: "CASCADE",
  })
  floor: Floor;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
