import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../../base/easydine.base";
import { TableCombination } from "./tableCombination.model";
import { Branch } from "../../../company/branch.model";
import { Floor } from "./floor.model";

export enum TableStatus{
  AVAILABLE = 'available',
  OCCUPIED = 'occupied',
  RESERVED = 'reserved'
}

export enum TableCleaningStatus{
  CLEAN = "clean",
  NEEDSCLEANING = "needscleaning",
  DIRTY = "dirty"
}

@Entity("tables")
export class TableModel extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  tableId: string;

  @Column({
    nullable: false,
  })
  name: string;

  @Column({
    type: "int",
    nullable: false,
    default: 1,
  })
  minSeats: number;

  @Column({
    type: "int",
    nullable: false,
    default: 4,
  })
  maxSeats: number;

  @Column({
    type: "int",
    nullable: false,
    default: 0,
  })
  bookedSeats: number

  @Column({
    type: "boolean",
    default: true,
  })
  enabled: boolean;

  @Column({
    type: "enum",
    enum: TableStatus,
    default: TableStatus.AVAILABLE,
  })
  status: TableStatus;

  @Column({
    type: "enum",
    enum: TableCleaningStatus,
    default: TableCleaningStatus.CLEAN,
  })
  cleaning: TableCleaningStatus;

  @Column({
    nullable: true,
    default: ''
  })
  location: string;

  @Column({
    type: "boolean",
    default: true,
  })
  availableOnline: boolean;

  @ManyToMany(() => TableCombination, (tableCombination) => tableCombination.tables)
  tableCombinations: TableCombination[];

  @ManyToOne(() => Branch, (branch) => branch.tables, {
    onDelete: "CASCADE"
  })
  branch: Branch

  @ManyToOne(() => Floor, (floor) => floor.tables, {
    onDelete: "CASCADE"
  })
  floor: Floor

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
