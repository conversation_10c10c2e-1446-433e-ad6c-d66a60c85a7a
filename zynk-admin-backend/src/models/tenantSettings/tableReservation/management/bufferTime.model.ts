import {
    Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../../base/easydine.base";
import { Branch } from "../../../company/branch.model";

@Entity("buffer_times")
export class BufferTime extends EasyDine {
  @PrimaryColumn("uuid")
  bufferTimeId: string;

  @OneToOne(() => Branch, (branch) => branch.bufferTime, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "bufferTimeId" })
  branch: Branch;

  @Column({
    nullable: false,
    default: 30
  })
  time: number

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
