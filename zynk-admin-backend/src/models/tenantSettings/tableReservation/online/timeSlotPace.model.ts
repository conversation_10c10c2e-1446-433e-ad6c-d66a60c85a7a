import {
    Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../../base/easydine.base";
import { OnlineReservationConfig } from "./online_reserv.model";

@Entity("time_slot_pacings")
export class TimeSlotPace extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  timeSlotPaceId: string

  @Column({
    nullable: false,
    type: 'time'
  })
  time: string

  @Column({
    nullable: false,
    default: 6
  })
  maxGuests: number

  @ManyToOne(() => OnlineReservationConfig, (online_reserv) => online_reserv.timeSlotPacings, {
    onDelete: "CASCADE"
  })
  online_reservation: OnlineReservationConfig

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
