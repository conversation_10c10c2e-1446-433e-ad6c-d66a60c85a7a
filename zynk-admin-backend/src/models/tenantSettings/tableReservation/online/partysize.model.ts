import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../../base/easydine.base";
import { OnlineReservationConfig } from "./online_reserv.model";

@Entity("party_sizes")
export class PartySize extends EasyDine {
  @PrimaryColumn("uuid")
  partySizeId: string;

  @Column({
    nullable: false,
    default: 0,
  })
  minGuests: number;

  @Column({
    nullable: false,
    default: 6,
  })
  maxGuests: number;

  @Column({
    nullable: false,
    default: true,
  })
  displayPhone: boolean;

  @Column({
    nullable: false,
    default: true,
  })
  reqManualApproval: boolean;

  @Column({
    nullable: false,
    default: 6,
  })
  reqManualGuests: number;

  @OneToOne(
    () => OnlineReservationConfig,
    (online_reserv) => online_reserv.partySize,
    {
      onDelete: "CASCADE",
    }
  )
  @JoinColumn({ name: "partySizeId" })
  onlineReservation: OnlineReservationConfig;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
