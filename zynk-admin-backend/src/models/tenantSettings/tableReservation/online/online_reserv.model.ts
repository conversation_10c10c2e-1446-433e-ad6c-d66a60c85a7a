import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../../base/easydine.base";
import { Branch } from "../../../company/branch.model";
import { CustomHourSlot } from "../../../common/customhourslot.model";
import { SpecialDay } from "../../../common/specialday.model";
import { PartySize } from "./partysize.model";
import { TimeSlotPace } from "./timeSlotPace.model";

@Entity("online_reservation_configs")
export class OnlineReservationConfig extends EasyDine {
  @PrimaryColumn("uuid")
  onlineResConfigId: string;

  @Column({
    nullable: false,
    default: true,
  })
  enabled: boolean;

  @OneToOne(() => Branch, (branch) => branch.onlineResConfig, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "onlineResConfigId" })
  branch: Branch;

  @OneToOne(() => PartySize, (party_size) => party_size.onlineReservation, {
    cascade: true,
  })
  partySize: PartySize;

  @OneToOne(
    () => OnlineReservationConfig,
    (online_reserv) => online_reserv.partySize,
    {
      onDelete: "CASCADE",
    }
  )
  onlineReservation: OnlineReservationConfig;

  @Column("boolean", {
    nullable: false,
    default: false,
  })
  customTimes: boolean;

  @OneToMany(() => CustomHourSlot, (slot) => slot.online_reservation, {
    cascade: true,
  })
  customSlots: CustomHourSlot[];

  @ManyToMany(() => CustomHourSlot)
  @JoinTable({
    name: "onlinereserv_globalhours",
  })
  globalCustomSlots: CustomHourSlot[];

  @OneToMany(() => SpecialDay, (spDay) => spDay.online_reservation, {
    cascade: true,
  })
  specialDays: SpecialDay[];

  @OneToMany(
    () => TimeSlotPace,
    (timeslotPace) => timeslotPace.online_reservation,
    {
      cascade: true,
    }
  )
  timeSlotPacings: TimeSlotPace[];

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
