import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../../base/easydine.base";
import { Branch } from "../../../company/branch.model";
import { TurnoverRule } from "./turnover_rules.model";
import { DefaultTurnoverDurations } from "./turnover.def";

@Entity("turnover_times")
export class TurnoverTime extends EasyDine {
  @PrimaryColumn("uuid")
  turnoverTimeId: string;

  @Column("enum", {
    nullable: false,
    enum: DefaultTurnoverDurations,
    default: DefaultTurnoverDurations.THIRTYMIN,
  })
  defaultDuration: DefaultTurnoverDurations;

  @OneToOne(() => Branch, (branch) => branch.turnoverTime, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "turnoverTimeId" })
  branch: Branch;

  @OneToMany(() => TurnoverRule, (turnoverRule) => turnoverRule.turnoverTime, {
    cascade: true
  })
  turnoverRules: TurnoverRule[];

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
