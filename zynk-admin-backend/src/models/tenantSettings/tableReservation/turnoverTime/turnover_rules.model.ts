import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../../base/easydine.base";
import { TurnoverTime } from "./turnover_time.model";
import { DefaultTurnoverDurations } from "./turnover.def";

@Entity("turnover_rules")
export class TurnoverRule extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  turnoverRuleId: string;

  @Column({
    nullable: false,
    default: 0,
  })
  minGuests: number;

  @Column({
    nullable: false,
  })
  maxGuests: number;

  @Column("enum", {
    nullable: false,
    enum: DefaultTurnoverDurations,
    default: DefaultTurnoverDurations.THIRTYMIN,
  })
  duration: DefaultTurnoverDurations;
  
  @ManyToOne(() => TurnoverTime, (turnoverTime) => turnoverTime.turnoverRules, {
    onDelete: "CASCADE"
  })
  turnoverTime: TurnoverTime

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;
}
