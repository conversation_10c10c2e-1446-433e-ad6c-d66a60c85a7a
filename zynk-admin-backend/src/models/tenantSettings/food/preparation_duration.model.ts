import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  VersionColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Branch } from "../../company/branch.model";

// Preparation time is now global for all menus in a branch (client change)
@Entity("preparation_duration")
export class PreparationDuration extends EasyDine {
  @PrimaryColumn("uuid")
  preparationDurationId: string;

  @OneToOne(() => Branch, (branch) => branch.preparationDuration, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "preparationDurationId" })
  branch: Branch;

  /**
   * The duration of the preparation in minutes.
   * @type {number}
   * @default 5 ->(5 minutes)
   * @description The default preparation duration for a dish.
   * This value can be overridden for specific dishes.
   * It is stored in the database as an integer.
   */
  @Column({ type: "int", nullable: false, default: 5 })
  duration: number;

  /**
   * OptimisticLockVersionMismatchError-> if multiple users try to modify this unknowingly
   */
  @VersionColumn()
  version: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;
}
