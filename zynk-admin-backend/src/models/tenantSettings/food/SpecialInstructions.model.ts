import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
  VersionColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Branch } from "../../company/branch.model";

export enum InstructionType {
  NOTES_AND_ALLERGIES = "Notes and Allergies",
  ONLY_NOTES = "Only notes",
  ONLY_ALLERGIES = "Only allergies",
  DISABLED = "Disabled",
}

@Entity("special_instructions")
export class SpecialInstructions extends EasyDine {
  @PrimaryColumn({ type: "uuid" })
  specialInstructionsId: string;

  @OneToOne(() => Branch, (branch) => branch.specialInstructions, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "specialInstructionsId" })
  branch: Branch;

  /**
   * The current setting for displaying special instructions.
   * Options: Notes and Allergies, Only notes, Only allergies, Disabled.
   */
  @Column({
    type: "enum",
    enum: InstructionType,
    default: InstructionType.NOTES_AND_ALLERGIES,
    nullable: false,
  })
  currentInstructions: InstructionType;

  /**
   * Optimistic locking to prevent conflicts when multiple users edit.
   */
  @VersionColumn()
  version: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;
}
