import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Branch } from "../../company/branch.model";

@Entity("parking_services")
export class ParkingService extends EasyDine {
  @PrimaryColumn("uuid")
  parkingServiceId: string;

  @Column({
    nullable: false
  })
  enabled: boolean

  @OneToOne(() => Branch, (branch) => branch.parkingService, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "parkingServiceId" })
  branch: Branch;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;
}
