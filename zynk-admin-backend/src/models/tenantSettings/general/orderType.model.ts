import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>inC<PERSON>umn,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Branch } from "../../company/branch.model";

export enum OrderTypeName {
  DINE_IN = "Dine In",
  TAKEAWAY = "Takeaway",
  PICKUP = "Pickup",
  CONTACTLESS_DINE_IN = "Contactless Dine In",
  DELIVERY = "Delivery",
  SITE = "Site",
  MOBILE = "Mobile",
  PHONE_ORDER = "Phone Order",
}

@Entity("order_types")
export class OrderType extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  orderTypeId: string;

  @Column({
    nullable: false,
  })
  name: string;

  @Column({
    type: "enum",
    enum: OrderTypeName,
    nullable: false,
  })
  reservedName: OrderTypeName;

  @ManyToOne(() => Branch, (branch) => branch.orderTypes, {
    onDelete: "CASCADE",
  })
  branch: Branch;

  @Column({
    nullable: false,
    default: true,
  })
  enabled: boolean;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;
}
