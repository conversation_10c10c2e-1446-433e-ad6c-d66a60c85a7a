import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { nullable } from "zod";
import { Branch } from "../../company/branch.model";

@Entity("currency_units")
export class CurrencyUnit extends EasyDine {
  @PrimaryColumn("uuid")
  currencyUnitId: string;

  @Column({
    nullable: false,
  })
  currentUnit: string;

  @OneToOne(() => Branch, (branch) => branch.currencyUnit, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "currencyUnitId" })
  branch: Branch;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;
}
