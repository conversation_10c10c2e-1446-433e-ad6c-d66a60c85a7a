import {
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  Entity,
  <PERSON><PERSON><PERSON><PERSON>umn,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
  VersionColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Branch } from "../../company/branch.model";

@Entity("waste_management")
export class WasteManagement extends EasyDine {
  @PrimaryColumn({ type: "uuid" })
  wasteManagementId: string;

  @OneToOne(() => Branch, (branch) => branch.wasteManagement, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "wasteManagementId" })
  branch: Branch;

  @Column({ type: "boolean", nullable: false, default: false })
  isEnabled: boolean;

  /**
   * OptimisticLockVersionMismatchError-> if multiple users try to modify this unknowingly
   */
  @VersionColumn()
  version: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;
}
