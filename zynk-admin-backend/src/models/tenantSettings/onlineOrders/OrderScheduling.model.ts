import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
  VersionColumn,
} from "typeorm";
import { Branch } from "../../company/branch.model";

export enum OrderSchedulingDurationType {
  Days = "Days",
  Weeks = "Weeks",
}

@Entity("order_scheduling")
export class OrderScheduling {
  @PrimaryColumn({ type: "uuid" })
  orderSchedulingId: string;

  @OneToOne(() => Branch, (branch) => branch.orderScheduling, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "orderSchedulingId" })
  branch: Branch;

  @Column({ type: "boolean", nullable: false, default: false })
  asapEnabled: boolean;

  @Column({ type: "boolean", nullable: false, default: false })
  scheduledOrdersEnabled: boolean;

  @Column({
    type: "enum",
    enum: OrderSchedulingDurationType,
    default: OrderSchedulingDurationType.Days,
    nullable: false,
  })
  minimumSchedulingDuration: OrderSchedulingDurationType;

  @Column({ type: "int", nullable: false, default: 1 })
  minimumSchedulingDurationValue: number;

  @Column({
    type: "enum",
    enum: OrderSchedulingDurationType,
    default: OrderSchedulingDurationType.Weeks,
    nullable: false,
  })
  maximumSchedulingDuration: OrderSchedulingDurationType;

  @Column({ type: "int", nullable: false, default: 1 })
  maximumSchedulingDurationValue: number;

  /**
   * OptimisticLockVersionMismatchError-> if multiple users try to modify this unknowingly
   */
  @VersionColumn()
  version: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;
}
