import { EasyDine } from "../../base/easydine.base";
import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
  VersionColumn,
} from "typeorm";
import { Branch } from "../../company/branch.model";

@Entity("receive_orders")
export class ReceiveOrders extends EasyDine {
  @PrimaryColumn({ type: "uuid" })
  receiveOrdersId: string;

  @OneToOne(() => Branch, (branch) => branch.receiveOrders, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "receiveOrdersId" })
  branch: Branch;

  @Column({ type: "boolean", nullable: false, default: false })
  isEnabled: boolean;

  /**
   * OptimisticLockVersionMismatchError-> if multiple users try to modify this unknowingly
   */
  @VersionColumn()
  version: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;
}
