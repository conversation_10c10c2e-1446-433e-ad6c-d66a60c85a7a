import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
  VersionColumn,
} from "typeorm";
import { EasyDine } from "../../base/easydine.base";
import { Branch } from "../../company/branch.model";

export enum PickupOrderReadyDurationType {
  Minutes = "Minutes",
  Hours = "Hours",
  Days = "Days",
  Weeks = "Weeks",
}

@Entity("pickup")
export class Pickup extends EasyDine {
  @PrimaryColumn({ type: "uuid" })
  pickupId: string;

  @OneToOne(() => Branch, (branch) => branch.pickup, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "pickupId" })
  branch: Branch;

  @Column({
    type: "enum",
    enum: PickupOrderReadyDurationType,
    default: PickupOrderReadyDurationType.Minutes,
    nullable: false,
  })
  orderReadyDurationType: PickupOrderReadyDurationType;

  @PrimaryColumn({ type: "int", nullable: false, default: 1 })
  orderReadyDurationValue: number;

  @Column({ type: "int", nullable: false, default: 0 })
  minimumPickupOrderPrice: number;

  /**
   * OptimisticLockVersionMismatchError-> if multiple users try to modify this unknowingly
   */
  @VersionColumn()
  version: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;
}
