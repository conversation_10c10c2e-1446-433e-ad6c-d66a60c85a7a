import {BeforeInsert, BeforeUpdate, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn} from "typeorm";
import {EasyDine} from "../../base/easydine.base";
import {SecureEncryption} from "../../../utils/SecureEncryption";

// Don't name it `DataSource`, this can cause import conflict with typeORM's DataSource class
// and will require using alias everywhere, instead use a different name.
@Entity("conn_configs")
export class ConnectionConfig extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    connConfigId: string;

    @Column({type: "int", nullable: false, default: 10})
    maxTenant: number;

    @Column({type: "boolean", default: true})
    isActive: boolean;

    @Column({
        type: "text",
        nullable: false,
        unique: true,
        name: "connection_string"
    })
    private _connectionString: string;

    async setConnectionString(value: string): Promise<void> {
        if (!value) {
            throw new Error("Connection string cannot be empty");
        }
        this._connectionString = await SecureEncryption.encrypt(value);
    }

    async getConnectionString(): Promise<string> {
        if (!this._connectionString) {
            throw new Error("Connection string is not set");
        }
        return await SecureEncryption.decrypt(this._connectionString);
    }

    get encryptedConnectionString(): string {
        return this._connectionString;
    }

    isConnectionStringEncrypted(): boolean {
        return SecureEncryption.isEncrypted(this._connectionString);
    }

    @BeforeInsert()
    @BeforeUpdate()
    async validateEncryption(): Promise<void> {
        if (this._connectionString && !SecureEncryption.isEncrypted(this._connectionString)) {
            this._connectionString = await SecureEncryption.encrypt(this._connectionString);
        }
    }

    async create(connectionString: string, maxTenant = 10): Promise<ConnectionConfig> {
        const dataSource = new ConnectionConfig();
        await dataSource.setConnectionString(connectionString);
        dataSource.maxTenant = maxTenant;
        return dataSource;
    }
}