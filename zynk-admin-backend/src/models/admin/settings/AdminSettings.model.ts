import {Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn, VersionColumn} from "typeorm";
import {EasyDine} from "../../base/easydine.base";

@Entity("admin_settings")
export class AdminSettings extends EasyDine {
    @PrimaryColumn("varchar", {length: 255})
    superSettingId: string = "AdminSettingsSingletonId";

    @Column("boolean", {
        nullable: false,
        default: true,
    })
    allowNewSuperAdmin: boolean;

    @VersionColumn()
    version: number;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;
}