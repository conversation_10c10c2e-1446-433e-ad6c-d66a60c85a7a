import {EasyDine} from "../../../base/easydine.base";
import {<PERSON>umn, CreateDateColumn, Entity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {LockedType} from "../../../../types/system";
import {TenantBlockList} from "./TenantBlockList";

@Entity("block_type")
export class BlockType extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    blockTypeId: string;

    @Column({type: "varchar", nullable: false, unique: true})
    name: string;

    @Column({type: "text", nullable: false})
    blockMessage: string;

    @Column({type: "boolean", nullable: false, default: true})
    isActive: boolean;

    @OneToMany(() => TenantBlockList, tenantBlockList => tenantBlockList.blockType)
    tenantBlockLists: TenantBlockList[];
    
    @Column({
        type: 'enum',
        enum: LockedType,
        default: LockedType.NOT_SYSTEM_MANAGER
    })
    lockedType: LockedType;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;
}