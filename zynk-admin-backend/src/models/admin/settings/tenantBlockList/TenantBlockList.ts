import {<PERSON>um<PERSON>, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {EasyDine} from "../../../base/easydine.base";
import {Tenant} from "../../Tenant/tenant.model";
import {LockedType} from "../../../../types/system";
import {BlockType} from "./BlockType";

@Entity("tenant_block_list")
export class TenantBlockList extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    tenantBlockListId: string;

    @ManyToOne(() => Tenant, tenant => tenant.tenantBlockList, {
        eager: true,
    })
    tenant: Tenant;

    @ManyToOne(() => BlockType, BlockType => BlockType.tenantBlockLists, {
        eager: true,
    })
    blockType: BlockType

    @Column({
        type: 'enum',
        enum: LockedType,
        default: LockedType.NOT_SYSTEM_MANAGER
    })
    lockedType: LockedType;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;

}