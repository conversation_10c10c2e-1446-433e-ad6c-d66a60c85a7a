import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>} from "typeorm";
import {Tenant} from "./tenant.model";
import {CompanyInformationBase} from "../../base/companyinformation.base";

@Entity("companyinformation")
export class CompanyInformation extends CompanyInformationBase {
    @Column({nullable: true})
    tenantId: string;

    @OneToOne(() => Tenant, (tenant) => tenant.tenantInfo, {
        onDelete: "CASCADE",
    })
    @JoinColumn({name: "tenantId"})
    tenant: Tenant;
}
