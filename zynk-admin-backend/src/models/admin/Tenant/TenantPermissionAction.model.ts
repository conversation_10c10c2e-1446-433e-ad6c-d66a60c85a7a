import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn} from "typeorm";
import {PermissionAction} from "../../pBAC/PermissionAction";
import {Tenant} from "./tenant.model";
import {OperationType} from "../../../types/pBAC";

@Entity({name: 'tenant_permission_action'})
export class TenantPermissionAction {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column({
        type: 'enum',
        enum: OperationType,
        default: OperationType.PLUS,
    })
    operationType: OperationType;

    @ManyToOne(() => Tenant, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "tenantId"})
    tenant: Tenant;

    @ManyToOne(() => PermissionAction, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "pAId"})
    permissionAction: PermissionAction;
}