import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import {Tenant} from "./tenant.model";
import {EasyDine} from "../../base/easydine.base";

@Entity("tenantsessions")
export class TenantSession extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    tenantSessionId: string;

    @Column({
        type: "text",
        nullable: true,
    })
    ipAddress: string;

    @Column({
        type: "text",
        nullable: true,
    })
    userAgent: string;

    @Column({
        type: "timestamp",
        nullable: true,
    })
    lastLogin: Date;

    @Column({
        type: "boolean",
        nullable: false,
        default: false,
    })
    isRevoked: boolean;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;

    @DeleteDateColumn({type: "timestamptz"})
    deletedAt?: Date;

    @ManyToOne(() => Tenant, (tenant) => tenant.tenantSessions, {
        onDelete: "CASCADE",
    })
    tenant: Tenant;
}
