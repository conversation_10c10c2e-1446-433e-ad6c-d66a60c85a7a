import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import {Tenant} from "./tenant.model";
import {EasyDine} from "../../base/easydine.base";

@Entity("tenantauths")
export class TenantAuth extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    tenantAuthId: string;

    @Column({
        nullable: false,
    })
    password: string;

    @Column({
        nullable: false,
    })
    pin: string;

    @Column({
        type: "text",
        nullable: true,
    })
    refreshToken: string;

    @Column({nullable: true})
    tenantId: string;

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;

    @DeleteDateColumn({type: "timestamptz"})
    deletedAt?: Date;

    @OneToOne(() => Tenant, (tenant) => tenant.tenantAuth, {
        onDelete: "CASCADE",
    })
    @JoinColumn({name: "tenantId"})
    tenant: Tenant;
}
