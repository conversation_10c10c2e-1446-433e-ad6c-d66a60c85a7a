import {
    <PERSON>umn,
    <PERSON>reateDate<PERSON>olumn,
    DeleteDateColumn,
    Entity,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    Repository,
    UpdateDateColumn,
} from "typeorm";
import {TenantAuth} from "./tenantAuth.model";
import {CompanyInformation} from "./companyinformation.model";
import {TenantSession} from "./tenantsession.model";
import {Subscription} from "../../subscription/subscriptions.model";
import {EasyDine} from "../../base/easydine.base";
import {TenantPermissionAction} from "./TenantPermissionAction.model";
import {ModelType, PermActionModelType} from "../../../types/pBAC";
import {PermissionAction} from "../../pBAC/PermissionAction";
import {InSufficientUserAssociation} from "../../../exceptions/model/impl/InSufficientUserAssociation";
import {FormatType} from "../../../types/pBAC/formatterTypes";
import {processPermissionActions, updatePermissionAction} from "../../../helpers/pBAC/model.helper";
import {Role} from "../../pBAC/role/Role.model";
import {TenantBlockList} from "../settings/tenantBlockList/TenantBlockList";

export enum TenantStatus {
    DRAFT = "draft",
    ACTIVE = "active",
}

@Entity("tenants")
export class Tenant extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    tenantId: string;

    @Column({
        nullable: false,
        unique: true,
    })
    name: string;

    @Column("text", {
        nullable: false,
        unique: true,
    })
    subDomain: string;

    @Column({
        nullable: false,
        unique: true,
    })
    emailAddress: string;

    @Column({
        nullable: false,
        unique: true,
    })
    phoneNumber: string;

    /**
     *  Duplicate Field coz Already in Subscription. change the logic to -
     *  if any active Subscription Found then validate its date using cron for deployment like vm -
     *  and write an external api for serverless deployment -
     *  and make that api public and that api should only accept a request from only one ip -
     */

    @Column({
        nullable: true,
        default: null,
    })
    activeFrom: Date;

    @Column({
        nullable: true,
        default: null,
    })
    activeTill: Date;

    @Column({
        type: "enum",
        enum: TenantStatus,
        default: TenantStatus.DRAFT,
    })
    status: string;

    @OneToOne(() => TenantAuth, (tenantAuth) => tenantAuth.tenant, {
        cascade: true,
    })
    tenantAuth: TenantAuth;

    @OneToOne(() => CompanyInformation, (companyInfo) => companyInfo.tenant, {
        cascade: true,
    })
    tenantInfo: CompanyInformation;

    @OneToMany(() => TenantSession, (tenantSessions) => tenantSessions.tenant, {
        cascade: true,
    })
    tenantSessions: TenantSession[];

    @OneToMany(() => TenantPermissionAction, tpa => tpa.tenant)
    tenantPermissionAction: TenantPermissionAction[];

    @OneToMany(() => Subscription, (subscription) => subscription.tenant)
    subscriptions: Subscription[];

    @ManyToOne(() => Role, role => role.superAdmins, {
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
    })
    role: Role;

    @OneToMany(() => TenantBlockList, (tenantBlockList) => tenantBlockList.tenant, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    tenantBlockList: TenantBlockList[]

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;

    @DeleteDateColumn({type: "timestamptz"})
    deletedAt?: Date;

    async updatePermissionAction(
        permissionActionIds: PermActionModelType[],
        permissionAction: Repository<PermissionAction>,
        TenantPermissionAction: Repository<TenantPermissionAction>
    ) {
        try {
            if (!this.role || !this.role.rolePermissionActions)
                throw new InSufficientUserAssociation(ModelType.TENANT)
            const result = await updatePermissionAction(
                permissionActionIds,
                this.role.rolePermissionActions,
                this.tenantPermissionAction,
                this.tenantId,
                "tenant",
                "tenantId",
                TenantPermissionAction,
                permissionAction,
            );
            const {updatedUnknownPermissions, ...rest} = result
            return rest;
        } catch (e) {
            throw e;
        }
    }

    async getPermissionAction(formatType: FormatType) {
        try {
            if (!this.role || !this.role.rolePermissionActions)
                throw new InSufficientUserAssociation(ModelType.TENANT)
            return processPermissionActions(
                this.role.rolePermissionActions,
                this.tenantPermissionAction,
                {
                    formatType: formatType,
                    requiredActive: true,
                }
            );
        } catch (e) {
            throw e;
        }
    }

}
