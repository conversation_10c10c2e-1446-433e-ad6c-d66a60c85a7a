import {
    <PERSON>umn,
    <PERSON>reateDateColumn,
    DeleteDateColumn,
    Entity,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    Repository,
    UpdateDateColumn,
} from "typeorm";
import {SuperSession} from "./supersession.model";
import {EasyDine} from "../base/easydine.base";
import {SupPermissionAction} from "./SupAdminPermissionAction.model";
import {Role} from "../pBAC/role/Role.model";
import {processPermissionActions, updatePermissionAction} from "../../helpers/pBAC/model.helper";
import {ModelType, PermActionModelType} from "../../types/pBAC";
import {PermissionAction} from "../pBAC/PermissionAction";
import {FormatType} from "../../types/pBAC/formatterTypes";
import {InSufficientUserAssociation} from "../../exceptions/model/impl/InSufficientUserAssociation";

@Entity("superadmins")
export class SuperAdmin extends EasyDine {
    @PrimaryGeneratedColumn("uuid")
    superAdminId: string;

    @Column({
        length: 100,
        nullable: false,
        unique: true,
    })
    username: string;

    @Column({
        nullable: false,
        unique: true,
    })
    emailAddress: string;

    @Column({
        nullable: false,
    })
    password: string;

    @Column({
        type: "text",
        nullable: true,
    })
    refreshToken: string;

    @OneToMany(() => SupPermissionAction, spa => spa.superAdmin)
    supPermissionAction: SupPermissionAction[];

    @ManyToOne(() => Role, role => role.superAdmins, {
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
    })
    role: Role;

    @OneToMany(() => SuperSession, (superSession) => superSession.superAdmin)
    superSessions: SuperSession[];

    @CreateDateColumn({type: "timestamptz"})
    createdAt: Date;

    @UpdateDateColumn({type: "timestamptz"})
    updatedAt: Date;

    @DeleteDateColumn({type: "timestamptz"})
    deletedAt?: Date;

    async updatePermissionAction(
        permissionActionIds: PermActionModelType[],
        permissionAction: Repository<PermissionAction>,
        SupPermissionAction: Repository<SupPermissionAction>
    ) {
        try {
            if (!this.role || !this.role.rolePermissionActions)
                throw new InSufficientUserAssociation(ModelType.ADMIN)
            const result = await updatePermissionAction(
                permissionActionIds,
                this.role.rolePermissionActions,
                this.supPermissionAction,
                this.superAdminId,
                "superAdmin",
                "superAdminId",
                SupPermissionAction,
                permissionAction,
            );
            const {updatedUnknownPermissions, ...rest} = result
            return rest;
        } catch (e) {
            throw e;
        }
    }

    async getPermissionAction(formatType: FormatType) {
        try {
            if (!this.role || !this.role.rolePermissionActions)
                throw new InSufficientUserAssociation(ModelType.ADMIN)
            return processPermissionActions(
                this.role.rolePermissionActions,
                this.supPermissionAction,
                {
                    formatType: formatType,
                    requiredActive: true,
                }
            );
        } catch (e) {
            throw e;
        }
    }

}
