import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { SuperAdmin } from "./superadmin.model";
import { EasyDine } from "../base/easydine.base";

@Entity("supersessions")
export class SuperSession extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  suSessionId: string;

  @Column({
    type: "text",
    nullable: true,
    unique: true,
  })
  ipAddress: string;

  @Column({
    type: "text",
    nullable: true,
  })
  userAgent: string;

  @Column({
    type: "timestamp",
    nullable: true,
  })
  lastLogin: Date;

  @Column({
    type: "boolean",
    nullable: false,
    default: false,
  })
  isRevoked: boolean;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  @ManyToOne(() => SuperAdmin, (superAdmin) => superAdmin.superSessions, {
    onDelete: "CASCADE",
  })
  superAdmin: SuperAdmin;
}
