import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn} from "typeorm";
import {PermissionAction} from "../pBAC/PermissionAction";
import {SuperAdmin} from "./superadmin.model";
import {OperationType} from "../../types/pBAC";

@Entity({name: 'sup_permission_action'})
export class SupPermissionAction {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column({
        type: 'enum',
        enum: OperationType,
        default: OperationType.PLUS,
    })
    operationType: OperationType;

    @ManyToOne(() => SuperAdmin, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "superAdminId"})
    superAdmin: SuperAdmin;

    @ManyToOne(() => PermissionAction, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    })
    @JoinColumn({name: "pAId"})
    permissionAction: PermissionAction;
}