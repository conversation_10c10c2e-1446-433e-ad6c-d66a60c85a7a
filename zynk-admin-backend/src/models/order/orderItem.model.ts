import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn, ManyToMany } from 'typeorm';
import { EasyDine } from '../base/easydine.base';
import { OrderDetails } from './orderDetail.model';
import { Dish } from '../foodmenu/dish.model';
import { OrderQueue } from './orderQueue.model';

export enum ItemType {
    STANDARD = 'standard',
    CUSTOMIZED = 'customized'
}


// Interface for JSONB objects
export interface NamedEntity {
    id: string;
    name: string;
}

// Interface for side dish with price
export interface PriceNamedEntity extends NamedEntity {
    price: number;
}

export interface QuantityPriceNamedEntity extends PriceNamedEntity {
    quantity: number;
}

@Entity('order_items')
export class OrderItem extends EasyDine {
    @PrimaryGeneratedColumn('uuid')
    orderItemId: string;

    @Column()
    name: string;

    @Column('decimal', { precision: 10, scale: 2 })
    price: number;

    @Column('int')
    quantity: number;

    // Customization fields
    @Column({
        type: 'enum',
        enum: ItemType,
        default: ItemType.STANDARD
    })
    type: ItemType;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Base item information with id and name for customized items'
    })
    baseItem: PriceNamedEntity;

    // Allergies stored as array of JSONB objects
    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected allergy options with id and name'
    })
    allergies: NamedEntity[];

    // Dish customization arrays
    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish sizes with id and name'
    })
    dishSizes: PriceNamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish exclusions with id and name'
    })
    dishExclusions: NamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected cooking styles with id and name'
    })
    cookingStyles: NamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected spiciness levels with id and name'
    })
    spiciness: PriceNamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish addons with id and name'
    })
    dishAddons: QuantityPriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish extras with id and name'
    })
    dishExtras: QuantityPriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish sides with id and name'
    })
    dishSides: QuantityPriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish beverages with id and name'
    })
    dishBeverages: QuantityPriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Selected dish desserts with id and name'
    })
    dishDesserts: QuantityPriceNamedEntity[];

    @Column({
        type: 'text',
        nullable: true,
        comment: 'Special notes or customization instructions'
    })
    notes: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @ManyToOne(() => OrderDetails, orderDetails => orderDetails.orderItems, {
        onDelete: "CASCADE",
    })
    orderDetails: OrderDetails;

    @ManyToOne(() => Dish)
    dishEntity: Dish;

    // Fixed inverse side of many-to-many relationship
    @ManyToMany(() => OrderQueue, queue => queue.orderItems)
    queues: OrderQueue[];
}