import { Entity, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { EasyDine } from '../base/easydine.base';
import { OrderItem, PriceNamedEntity } from './orderItem.model';
import { Customer } from '../customer/customer.model';
import { Alert } from '../cart/cart.model';

export enum OrderStatus {
    PENDING = 'PENDING',
    IN_PREPARATION = 'IN_PREPARATION',
    READY = 'READY',
    SERVED = 'SERVED',
    CANCELLED = 'CANCELLED',
    CHECKOUT = 'CHECKOUT',
    COMPLETED = 'COMPLETED'
}

export enum AppprovalStatus {
    PENDING = 'PENDING',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED'
}

// Interface for JSONB objects
export interface NamedEntity {
    id: string;
    name: string;
}

@Entity('order_details')
@Index(['status'])
export class OrderDetails extends EasyDine {
    @PrimaryGeneratedColumn('uuid')
    orderDetailId: string;

    @Column({
        type: 'varchar',
        unique: true,
    })
    orderCode: string;

    @Column({
        type: 'jsonb',
        comment: 'Table information with id and name'
    })
    table: NamedEntity;

    @Column('decimal', { precision: 10, scale: 2 })
    total: number;

    @Column({
        type: 'enum',
        enum: OrderStatus,
        default: OrderStatus.PENDING
    })
    status: OrderStatus;

    @Column({
        type: 'int',
        default: 1,
        comment: 'Number of people for this order'
    })
    numberOfPeople: number;

    @Column({
        type: 'jsonb',
        comment: 'Order type information with id and name'
    })
    orderType: NamedEntity;

    @Column({
        type: 'enum',
        enum: AppprovalStatus,
        default: AppprovalStatus.PENDING
    })
    orderApproval: AppprovalStatus;

    // Timing fields
    @Column({
        type: 'int',
        nullable: true,
        comment: 'Estimated preparation time in minutes'
    })
    estimatedPrepTime: number;

    @Column({
        type: 'timestamp',
        nullable: true,
        comment: 'When preparation started'
    })
    prepStartTime: Date;

    @Column({
        type: 'timestamp',
        nullable: true,
        comment: 'When order was marked as ready'
    })
    readyTime: Date;

    @Column({
        type: 'timestamp',
        nullable: true,
        comment: 'When order was served to customer'
    })
    servedTime: Date;

    // Assignment fields
    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Chef assigned to prepare this order with id and name'
    })
    assignedChef: NamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Waiter assigned to serve this order with id and name'
    })
    assignedWaiter: NamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Staff who ordered this with id and name'
    })
    orderedBy: NamedEntity;

    @Column({
        type: 'jsonb',
        nullable: true,
        comment: 'Customer information with id and name if available'
    })
    customerInfo: NamedEntity;

    @Column({
        type: 'uuid',
        nullable: true,
        comment: 'Branch ID'
    })
    branchId: string;

    // Notes
    @Column({
        type: 'text',
        nullable: true,
        comment: 'Special instructions or notes for kitchen'
    })
    notes: string;

    @Column({
        type: 'jsonb',
        nullable: true,
        default: () => "'[]'",
        comment: 'Array of miscellaneous items with id, name, price, and addedAt'
    })
    miscItems: PriceNamedEntity[];

    @Column({
        type: 'jsonb',
        nullable: true,
        default: () => "'[]'",
        comment: 'Array of alerts with id, note, and addedAt'
    })
    alerts: Alert[];

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @OneToMany(() => OrderItem, orderItem => orderItem.orderDetails, { cascade: true, eager: true })
    orderItems: OrderItem[];

    // Keep the relation with Customer for backward compatibility if needed
    @ManyToOne(() => Customer, customer => customer.orderDetails)
    @JoinColumn({ name: 'customerId' })
    customer: Customer;

    // Getter for customerId to maintain backward compatibility
    get customerId(): string | null {
        return this.customerInfo?.id || null;
    }

    // Getter for tableId to maintain backward compatibility
    get tableId(): string | null {
        return this.table?.id || null;
    }

    // Getter for orderTypeId to maintain backward compatibility
    get orderTypeId(): string | null {
        return this.orderType?.id || null;
    }

    get actualPrepTime(): number | null {
        if (this.prepStartTime && this.readyTime) {
            return Math.floor((this.readyTime.getTime() - this.prepStartTime.getTime()) / (1000 * 60));
        }
        return null;
    }

    // Virtual field to calculate total time from order to ready
    get totalProcessingTime(): number | null {
        if (this.createdAt && this.readyTime) {
            return Math.floor((this.readyTime.getTime() - this.createdAt.getTime()) / (1000 * 60));
        }
        return null;
    }

    // Helper method to check if order is overdue
    isOverdue(): boolean {
        if (!this.estimatedPrepTime || !this.prepStartTime) return false;

        const expectedReadyTime = new Date(this.prepStartTime.getTime() + (this.estimatedPrepTime * 60 * 1000));
        return new Date() > expectedReadyTime &&
            this.status !== OrderStatus.READY &&
            this.status !== OrderStatus.SERVED;
    }

    // Helper method to check if order is in active queue (not completed)
    isInQueue(): boolean {
        return [
            OrderStatus.PENDING,
            OrderStatus.IN_PREPARATION,
            OrderStatus.READY
        ].includes(this.status);
    }

    // Helper method to check if order is completed
    isCompleted(): boolean {
        return [
            OrderStatus.SERVED,
            OrderStatus.CANCELLED,
            OrderStatus.CHECKOUT
        ].includes(this.status);
    }

    // Helper method to start preparation
    startPreparation(chef?: NamedEntity): void {
        this.status = OrderStatus.IN_PREPARATION;
        this.prepStartTime = new Date();
        if (chef) {
            this.assignedChef = chef;
        }
    }

    // Helper method to mark as ready
    markAsReady(): void {
        this.status = OrderStatus.READY;
        this.readyTime = new Date();
    }

    // Helper method to mark as served
    markAsServed(waiter?: NamedEntity): void {
        this.status = OrderStatus.SERVED;
        this.servedTime = new Date();
        if (waiter) {
            this.assignedWaiter = waiter;
        }
    }

    // Helper method to validate and set entities
    static validateAndSetEntity(entity: any): NamedEntity | null {
        if (!entity) return null;

        if (typeof entity === 'string') {
            // If only ID is provided, you'll need to fetch the name from your service
            throw new Error('Name is required along with ID');
        }

        if (entity.id && entity.name) {
            return { id: entity.id, name: entity.name };
        }

        throw new Error('Both id and name are required');
    }
}