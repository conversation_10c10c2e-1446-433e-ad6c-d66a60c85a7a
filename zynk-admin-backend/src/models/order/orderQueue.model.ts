import { Entity, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, Index, ManyToMany, JoinTable } from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { OrderDetails } from "./orderDetail.model";
import { OrderItem } from "./orderItem.model";

export enum QueueStatus {
    PENDING = 'PENDING',
    IN_PREPARATION = 'IN_PREPARATION',
    READY = 'READY',
    SERVED = 'SERVED',
    CANCELLED = 'CANCELLED',
    CHECKOUT = 'CHECKOUT',
    COMPLETED = 'COMPLETED'
}

export enum QueuePriority {
    NORMAL = 1,
    HIGH = 2,
    URGENT = 3,
    EMERGENCY = 4
}

@Entity('order_queue')
@Index(['status', 'priority', 'queuePosition'])
export class OrderQueue extends EasyDine {
    @PrimaryGeneratedColumn('uuid')
    orderQueueId: string;

    @Column('uuid')
    orderDetailId: string;

    @ManyToOne(() => OrderDetails, { onDelete: "CASCADE", eager: true })
    @JoinColumn({ name: 'orderDetailId' })
    orderDetails: OrderDetails;

    // Fixed many-to-many relationship
    @ManyToMany(() => OrderItem, orderItem => orderItem.queues, { cascade: true })
    @JoinTable({
        name: 'order_queue_items',
        joinColumn: { name: 'orderQueueId', referencedColumnName: 'orderQueueId' },
        inverseJoinColumn: { name: 'orderItemId', referencedColumnName: 'orderItemId' }
    })
    orderItems: OrderItem[];

    @Column({
        type: 'enum',
        enum: QueueStatus,
        default: QueueStatus.PENDING
    })
    status: QueueStatus;

    @Column({
        type: 'int',
        nullable: true,
        comment: 'Estimated preparation time in minutes'
    })
    estimatedPrepTime: number;

    @Column({
        type: 'int',
        default: QueuePriority.NORMAL
    })
    priority: QueuePriority;

    @Column({
        type: 'int',
        nullable: true,
        comment: 'Position in queue for ordering, lower numbers are processed first'
    })
    queuePosition: number;

    @Column({
        type: 'text',
        nullable: true,
        comment: 'Special instructions or notes for kitchen'
    })
    notes: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    // Queue-specific helper methods
    isOverdue(): boolean {
        return this.orderDetails?.isOverdue() || false;
    }

    shouldBeRemoved(): boolean {
        return this.orderDetails?.isCompleted() || false;
    }
}
