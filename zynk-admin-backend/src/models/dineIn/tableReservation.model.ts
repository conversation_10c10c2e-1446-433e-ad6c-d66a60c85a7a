import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from "typeorm";
import { EasyDine } from "../base/easydine.base";
import { TableModel } from "../tenantSettings/tableReservation/management/table.model";
import { Branch } from "../company/branch.model";
import { randomBytes } from "crypto";

export enum ReservationStatus {
  PENDING = "pending",
  CONFIRMED = "confirmed",
  CANCELLED = "cancelled",
  COMPLETED = "completed",
  NO_SHOW = "no_show",
}

@Entity("table_reservations")
export class TableReservation extends EasyDine {
  @PrimaryGeneratedColumn("uuid")
  reservationId: string;

  @Column({ unique: false })
  confirmationCode: string;

  @Column({
    nullable: false,
  })
  customerName: string;

  @Column({
    nullable: false,
  })
  phoneNumber: string;

  @Column({
    type: "int",
    nullable: false,
  })
  numberOfGuests: number;

  @Column({
    type: "timestamptz",
    nullable: false,
  })
  reservationTime: Date;

  @Column({
    type: "text",
    nullable: true,
  })
  specialNotes: string;

  @Column({
    type: "enum",
    enum: ReservationStatus,
    default: ReservationStatus.PENDING,
  })
  status: ReservationStatus;

  @Column({
    type: "timestamptz",
    nullable: true,
  })
  arrivedAt: Date;

  @Column({
    type: "int",
    default: 120, // 2 hours default
  })
  durationMinutes: number;

  @ManyToOne(() => TableModel, {
    onDelete: "CASCADE",
  })
  table: TableModel;

  @Column({
    nullable: true
  })
  tableCombinationId: string

  @ManyToOne(() => Branch, {
    onDelete: "CASCADE",
  })
  branch: Branch;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ type: "timestamptz" })
  deletedAt?: Date;

  static async generateConfirmationCode() {
    const chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    const bytes = randomBytes(6);
    let code = "";

    for (let i = 0; i < 6; i++) {
      code += chars.charAt(bytes[i] % chars.length);
    }

    return `RES${code}`;
  }
}
