{"name": "easydine-backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "migration:cli": "ts-node src/scripts/migration-cli.ts", "migration:create": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:create", "migration:generate:superadmin": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:generate -d src/db/migrations/MigrationManager.ts#getSuperAdminMigrationDS", "migration:run:superadmin": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:run -d src/db/migrations/MigrationManager.ts#getSuperAdminMigrationDS", "migration:revert:superadmin": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:revert -d src/db/migrations/MigrationManager.ts#getSuperAdminMigrationDS", "typeorm": "typeorm-ts-node-commonjs", "bundle-swagger": "ts-node src/scripts/bundle-swagger.ts", "update-seeds": "ts-node src/scripts/update-seeds.ts", "tenant-seeder": "ts-node src/scripts/tenant-seeder.ts", "diagram-gen": "ts-node src/scripts/diagram-gen.ts", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@types/multer": "^2.0.0", "argon2": "^0.43.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "country-state-city": "^3.2.1", "countrycitystatejson": "^25.5.2908", "dotenv": "^16.5.0", "express": "^5.1.0", "express-handlebars": "^8.0.3", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "pg": "^8.14.1", "reflect-metadata": "^0.2.2", "swagger-jsdoc": "^6.2.8", "swagger-themes": "^1.4.3", "swagger-ui-express": "^5.0.1", "tldts": "^7.0.5", "typeorm": "^0.3.22", "winston": "^3.17.0", "zod": "^3.24.2", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/glob": "^8.1.0", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.33", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/winston": "^2.4.4", "glob": "^11.0.2", "jest": "^29.7.0", "nodemon": "^3.1.10", "swagger-cli": "^4.0.4", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}