module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/tests/**',
  ],
  setupFilesAfterEnv: ['<rootDir>/src/tests/setup.ts'],
  // Increase timeout for database operations
  testTimeout: 30000,
  // Load environment variables
  setupFiles: ['<rootDir>/jest.setup.js'],
  // Don't exit immediately
  forceExit: true,
  // Detect open handles
  detectOpenHandles: true,
};