import {QueryRunner} from "typeorm";
import {AuthMap} from "../../src/types/authMap/AuthMap";
import {UserType} from "../../src/types/pBAC";

declare module "express-serve-static-core" {
    interface Request {
        /**
         * Query runner for the super admin database.
         *
         * This property provides access to the query runner specifically
         * configured for operations on the super admin database. It is
         * typically used for administrative tasks and operations that
         * require access to the root database.
         */
        rootQueryRunner: QueryRunner;

        /**
         * The query runner for tenant-specific database connections.
         * Used for operations on individual tenant databases.
         */
        queryRunner: QueryRunner;

        /**
         * Root query runner only meant to be used for token verification
         * DO NOT USE FOR ANY OTHER PURPOSE!!!
         */
        pr_rootQueryRunner: QueryRunner;

        /**
         * Tenant query runner only meant to be used for token verification
         * DO NOT USE FOR ANY OTHER PURPOSE!!!
         */
        pr_queryRunner: QueryRunner;

        /**
         * Holds tenant name found by subdomain if in production or by x-host if in development.
         * Check `resolveTenant.middleware.ts` for more details.
         * If it's a superadmin that is currently accessing, the tenantName will be `null`.
         */
        tenantName: string | null;

        /**
         Indicates whether this is tenant itself making request.
         Why this is required and why not just tenantName?

         - A tenant could login/a user could login, `verifyToken.middleware.ts`
         identifies that this user is authenticated, but what about authorization, sure we have
         tenantName, but that may only work if we have a hostname or if x-host is present, in
         situations such as this, an extra attribute to indicate whether it is tenant or not can
         be used in the request from the info of their token.
         */
        isTenant: boolean | null;

        /**
         * Indicated weather this request is from a customer which belongs to a tenant or not.
         * if isCustomer is true, then UserType is "CUSTOMER"
         * if isCustomer is false, then UserType is "STAFF"
         */

        isCustomer: boolean | null;

        /**
         * Object containing route authentication information.
         * @see AuthMap
         */

        authMap: AuthMap;

        /**
         * The type of the current user making the request.
         * This can be one of the following:
         * - 'superadmin'
         * - 'tenant'
         * - 'user'
         */
        USER_TYPE: UserType;

        /**
         * User ID of the logged in user
         */
        userId: string;

        /**
         * Branch ID of the logged in user
         */
        branchId: string;

        /**
         * User ID of the checked in user
         */
        checkedInStaffId: string;

        /**
         * Contain's Array of usersPermissionActions
         * for further processing or inCase if there is any need for -
         * usersPermissionAction in the controller
         */
        userPermissionAction: string[]

        /**
         * The IP address of the client making the request.
         */
        clientIP: string | null;

        /**
         * The User agent of the client making the request.
         */
        userAgent: string;

        /**
         * Validated query object for requests with query
         */
        validatedQuery?: any;
    }
}
